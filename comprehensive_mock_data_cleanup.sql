-- COMPREHENSIVE MOCK DATA CLEANUP SCRIPT
-- Run this in Supabase SQL Editor to remove all mock data

-- First, let's see what mock data exists
SELECT 'MOCK DATA ANALYSIS' as section;

-- Check for mock videos
SELECT 
    'Mock Videos Found:' as info,
    COUNT(*) as count
FROM videos 
WHERE title LIKE '%React Server Components%'
   OR title LIKE '%React Hooks Complete Guide%'
   OR title LIKE '%Building a Full Stack React App%'
   OR title LIKE '%React State Management%'
   OR title LIKE '%Home Office Setup%'
   OR title LIKE '%AI Tools Revolutionizing%'
   OR title LIKE '%Boost Your Work From Home%'
   OR title LIKE '%How to Grow Your Channel%'
   OR title LIKE '%Beginner''s Guide to YouTube%'
   OR title LIKE '%Top 10 Camera Tips%'
   OR title LIKE '%YouTube Algorithm Explained%'
   OR title LIKE '%Camera Settings for Beginners%'
   OR title LIKE '%mock%'
   OR title LIKE '%demo%'
   OR title LIKE '%test%'
   OR youtube_video_id IS NULL 
   OR youtube_video_id = '';

-- Check for mock content pillars
SELECT 
    'Mock Pillars Found:' as info,
    COUNT(*) as count
FROM content_pillars 
WHERE name IN ('Entertainment', 'Education', 'Behind The Scenes')
   OR name LIKE '%mock%'
   OR name LIKE '%demo%'
   OR name LIKE '%test%'
   OR description LIKE '%mock%'
   OR description LIKE '%demo%'
   OR description LIKE '%example%';

-- Check for mock goals (if any)
SELECT 
    'Mock Goals Found:' as info,
    COUNT(*) as count
FROM goals 
WHERE type LIKE '%mock%'
   OR type LIKE '%demo%'
   OR type LIKE '%test%';

-- Check for orphaned content pillars (no associated videos)
SELECT 
    'Orphaned Pillars Found:' as info,
    COUNT(*) as count
FROM content_pillars cp
LEFT JOIN videos v ON cp.id = v.pillar_id
WHERE v.id IS NULL;

-- Now perform the cleanup
SELECT 'STARTING CLEANUP' as section;

-- 1. Delete mock videos
DELETE FROM videos 
WHERE title LIKE '%React Server Components%'
   OR title LIKE '%React Hooks Complete Guide%'
   OR title LIKE '%Building a Full Stack React App%'
   OR title LIKE '%React State Management%'
   OR title LIKE '%Home Office Setup%'
   OR title LIKE '%AI Tools Revolutionizing%'
   OR title LIKE '%Boost Your Work From Home%'
   OR title LIKE '%How to Grow Your Channel%'
   OR title LIKE '%Beginner''s Guide to YouTube%'
   OR title LIKE '%Top 10 Camera Tips%'
   OR title LIKE '%YouTube Algorithm Explained%'
   OR title LIKE '%Camera Settings for Beginners%'
   OR title LIKE '%mock%'
   OR title LIKE '%demo%'
   OR title LIKE '%test%'
   OR youtube_video_id IS NULL 
   OR youtube_video_id = '';

-- 2. Delete mock content pillars
DELETE FROM content_pillars 
WHERE name IN ('Entertainment', 'Education', 'Behind The Scenes')
   OR name LIKE '%mock%'
   OR name LIKE '%demo%'
   OR name LIKE '%test%'
   OR description LIKE '%mock%'
   OR description LIKE '%demo%'
   OR description LIKE '%example%';

-- 3. Delete mock goals (if any)
DELETE FROM goals 
WHERE type LIKE '%mock%'
   OR type LIKE '%demo%'
   OR type LIKE '%test%';

-- 4. Clean up orphaned content pillars (optional - be careful with this)
-- Uncomment the next section if you want to remove pillars with no videos
/*
DELETE FROM content_pillars 
WHERE id IN (
    SELECT cp.id 
    FROM content_pillars cp
    LEFT JOIN videos v ON cp.id = v.pillar_id
    WHERE v.id IS NULL
);
*/

-- 5. Clean up any AI usage logs for mock content
DELETE FROM ai_usage_logs 
WHERE metadata::text LIKE '%mock%'
   OR metadata::text LIKE '%demo%'
   OR metadata::text LIKE '%test%';

-- 6. Clean up any whiteboard projects that might be mock data
DELETE FROM whiteboard_projects 
WHERE name LIKE '%mock%'
   OR name LIKE '%demo%'
   OR name LIKE '%test%'
   OR description LIKE '%mock%'
   OR description LIKE '%demo%'
   OR description LIKE '%test%';

-- Final verification - show what's left
SELECT 'CLEANUP COMPLETE - VERIFICATION' as section;

-- Count remaining videos
SELECT 
    'Remaining Videos:' as info,
    COUNT(*) as count
FROM videos;

-- Count remaining pillars
SELECT 
    'Remaining Pillars:' as info,
    COUNT(*) as count
FROM content_pillars;

-- Count remaining goals
SELECT 
    'Remaining Goals:' as info,
    COUNT(*) as count
FROM goals;

-- Show remaining pillar names to verify they're real
SELECT 
    'Remaining Pillar Names:' as info,
    string_agg(name, ', ') as pillar_names
FROM content_pillars;

-- Show remaining video titles (first 5) to verify they're real
SELECT 
    'Sample Remaining Video Titles:' as info,
    title
FROM videos 
ORDER BY created_at DESC 
LIMIT 5;

SELECT 'Mock data cleanup completed successfully!' as result;
