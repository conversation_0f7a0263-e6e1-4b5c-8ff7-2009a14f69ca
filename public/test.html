<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Supabase Connection Test</title>
  <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
  <style>
    body { font-family: Arial, sans-serif; padding: 20px; }
    button { padding: 10px 20px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; }
    button:disabled { background-color: #ccc; cursor: not-allowed; }
    #result { margin-top: 20px; padding: 10px; border: 1px solid #ddd; }
  </style>
</head>
<body>
  <h1>Supabase Connection Test</h1>
  
  <button id="testButton">Test Connection</button>
  
  <div id="result">No test run yet</div>

  <script>
    document.getElementById('testButton').addEventListener('click', async function() {
      const resultEl = document.getElementById('result');
      const buttonEl = document.getElementById('testButton');
      
      buttonEl.disabled = true;
      resultEl.textContent = 'Testing connection...';
      
      try {
        // Replace with your actual Supabase URL and anon key
        const supabaseUrl = prompt('Enter your Supabase URL:');
        const supabaseAnonKey = prompt('Enter your Supabase anon key:');
        
        if (!supabaseUrl || !supabaseAnonKey) {
          resultEl.textContent = 'Error: URL and anon key are required';
          buttonEl.disabled = false;
          return;
        }
        
        const supabase = supabase.createClient(supabaseUrl, supabaseAnonKey);
        
        // Test connection with a simple query
        const { data, error } = await supabase
          .from('videos')
          .select('count', { count: 'exact', head: true });
          
        if (error) {
          resultEl.textContent = `Error: ${error.message}`;
        } else {
          resultEl.textContent = `Success! Connected to Supabase. Found ${data.count} videos.`;
        }
      } catch (error) {
        resultEl.textContent = `Exception: ${error.message}`;
      } finally {
        buttonEl.disabled = false;
      }
    });
  </script>
</body>
</html>