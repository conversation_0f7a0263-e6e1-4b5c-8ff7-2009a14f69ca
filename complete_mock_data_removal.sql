-- COMPLETE MOCK DATA REMOVAL SCRIPT
-- This will remove ALL mock data including Entertainment, Education, Behind The Scenes

-- 1. First check what exists
SELECT 'CURRENT MOCK DATA IN DATABASE:' as status;

-- Show all pillars that look like mock data
SELECT 
    'MOCK PILLARS:' as type,
    id, name, description, user_id, target_percentage, color, created_at
FROM content_pillars 
WHERE name IN ('Entertainment', 'Education', 'Behind The Scenes', 'Behind the Scenes')
   OR name ILIKE '%mock%'
   OR name ILIKE '%demo%'
   OR name ILIKE '%test%'
   OR name ILIKE '%sample%'
   OR description ILIKE '%mock%'
   OR description ILIKE '%demo%'
   OR description ILIKE '%example%'
   OR description ILIKE '%sample%';

-- Show all videos that look like mock data
SELECT 
    'MOCK VIDEOS:' as type,
    COUNT(*) as total_count
FROM videos 
WHERE title ILIKE '%React Server Components%'
   OR title ILIKE '%React Hooks Complete Guide%'
   OR title ILIKE '%Building a Full Stack React App%'
   OR title ILIKE '%React State Management%'
   OR title ILIKE '%Home Office Setup%'
   OR title ILIKE '%AI Tools Revolutionizing%'
   OR title ILIKE '%Boost Your Work From Home%'
   OR title ILIKE '%How to Grow Your Channel%'
   OR title ILIKE '%Beginner''s Guide to YouTube%'
   OR title ILIKE '%Top 10 Camera Tips%'
   OR title ILIKE '%YouTube Algorithm Explained%'
   OR title ILIKE '%Camera Settings for Beginners%'
   OR title ILIKE '%mock%'
   OR title ILIKE '%demo%'
   OR title ILIKE '%test%'
   OR title ILIKE '%sample%'
   OR youtube_video_id IS NULL 
   OR youtube_video_id = ''
   OR youtube_video_id = 'mock'
   OR youtube_video_id ILIKE '%mock%';

-- 2. DELETE ALL MOCK DATA
SELECT 'DELETING ALL MOCK DATA...' as status;

-- Delete all mock videos first (to avoid foreign key issues)
DELETE FROM videos 
WHERE title ILIKE '%React Server Components%'
   OR title ILIKE '%React Hooks Complete Guide%'
   OR title ILIKE '%Building a Full Stack React App%'
   OR title ILIKE '%React State Management%'
   OR title ILIKE '%Home Office Setup%'
   OR title ILIKE '%AI Tools Revolutionizing%'
   OR title ILIKE '%Boost Your Work From Home%'
   OR title ILIKE '%How to Grow Your Channel%'
   OR title ILIKE '%Beginner''s Guide to YouTube%'
   OR title ILIKE '%Top 10 Camera Tips%'
   OR title ILIKE '%YouTube Algorithm Explained%'
   OR title ILIKE '%Camera Settings for Beginners%'
   OR title ILIKE '%mock%'
   OR title ILIKE '%demo%'
   OR title ILIKE '%test%'
   OR title ILIKE '%sample%'
   OR youtube_video_id IS NULL 
   OR youtube_video_id = ''
   OR youtube_video_id = 'mock'
   OR youtube_video_id ILIKE '%mock%';

-- Delete all mock pillars
DELETE FROM content_pillars 
WHERE name IN ('Entertainment', 'Education', 'Behind The Scenes', 'Behind the Scenes')
   OR name ILIKE '%mock%'
   OR name ILIKE '%demo%'
   OR name ILIKE '%test%'
   OR name ILIKE '%sample%'
   OR description ILIKE '%mock%'
   OR description ILIKE '%demo%'
   OR description ILIKE '%example%'
   OR description ILIKE '%sample%';

-- Delete mock goals
DELETE FROM goals 
WHERE type ILIKE '%mock%'
   OR type ILIKE '%demo%'
   OR type ILIKE '%test%'
   OR type ILIKE '%sample%';

-- Clean up AI usage logs for mock content
DELETE FROM ai_usage_logs 
WHERE metadata::text ILIKE '%mock%'
   OR metadata::text ILIKE '%demo%'
   OR metadata::text ILIKE '%test%'
   OR metadata::text ILIKE '%sample%'
   OR metadata::text ILIKE '%Entertainment%'
   OR metadata::text ILIKE '%Education%'
   OR metadata::text ILIKE '%Behind The Scenes%';

-- Clean up whiteboard projects that might be mock data
DELETE FROM whiteboard_projects 
WHERE name ILIKE '%mock%'
   OR name ILIKE '%demo%'
   OR name ILIKE '%test%'
   OR name ILIKE '%sample%'
   OR description ILIKE '%mock%'
   OR description ILIKE '%demo%'
   OR description ILIKE '%test%'
   OR description ILIKE '%sample%';

-- 3. FINAL VERIFICATION
SELECT 'VERIFICATION - WHAT REMAINS:' as status;

-- Show remaining pillars
SELECT 
    'REMAINING PILLARS:' as type,
    COUNT(*) as count,
    string_agg(name, ', ') as pillar_names
FROM content_pillars;

-- Show remaining videos
SELECT 
    'REMAINING VIDEOS:' as type,
    COUNT(*) as count
FROM videos;

-- Show remaining goals
SELECT 
    'REMAINING GOALS:' as type,
    COUNT(*) as count
FROM goals;

-- Show any remaining videos without YouTube IDs (potential mock data)
SELECT 
    'VIDEOS WITHOUT YOUTUBE IDS:' as type,
    COUNT(*) as count
FROM videos 
WHERE youtube_video_id IS NULL OR youtube_video_id = '';

SELECT 'MOCK DATA REMOVAL COMPLETED!' as result;
