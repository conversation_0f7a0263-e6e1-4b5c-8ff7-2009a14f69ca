<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MyContentHub - YouTube Strategy Platform</title>
    <meta name="description" content="Transform your YouTube content strategy with data-driven insights. Track content pillars, analyze performance, and grow your channel strategically." />
    <meta name="author" content="MyContentHub" />
    <link rel="icon" type="image/png" href="/MCH-Logo-Transparent2.png" />
    <link rel="apple-touch-icon" href="/MCH-Logo-Transparent2.png" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#14b8a6" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="MyContentHub" />

    <meta property="og:title" content="MyContentHub - YouTube Strategy Platform" />
    <meta property="og:description" content="Transform your YouTube content strategy with data-driven insights. Track content pillars, analyze performance, and grow your channel strategically." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/MCH-Logo-Transparent2.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@mycontenthub" />
    <meta name="twitter:image" content="/MCH-Logo-Transparent2.png" />
    <meta name="twitter:title" content="MyContentHub - YouTube Strategy Platform" />
    <meta name="twitter:description" content="Transform your YouTube content strategy with data-driven insights. Track content pillars, analyze performance, and grow your channel strategically." />
  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
