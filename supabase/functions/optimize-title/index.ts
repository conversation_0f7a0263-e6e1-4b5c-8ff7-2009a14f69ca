
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: user } = await supabase.auth.getUser(token);

    if (!user.user) {
      throw new Error('User not authenticated');
    }

    const { title, description, pillarName } = await req.json();

    console.log('[OPTIMIZE-TITLE] Optimizing title for:', title);

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are an expert YouTube content strategist. Generate 3 optimized video titles that are catchy, SEO-friendly, and likely to get more clicks and views. Each title should be under 60 characters and include relevant keywords. Return only the 3 titles, each on a new line, numbered 1-3.'
          },
          {
            role: 'user',
            content: `Original title: "${title}"
Description: "${description || 'No description provided'}"
Content pillar: "${pillarName || 'General'}"

Generate 3 improved titles that would perform better on YouTube.`
          }
        ],
        max_tokens: 200,
        temperature: 0.8,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('[OPTIMIZE-TITLE] OpenAI API error:', response.status, errorData);
      throw new Error(`OpenAI API error: ${response.status} - ${errorData}`);
    }

    const data = await response.json();
    const suggestions = data.choices[0].message.content
      .split('\n')
      .filter((line: string) => line.trim())
      .slice(0, 3)
      .map((line: string) => line.replace(/^\d+\.\s*/, '').replace(/^["']|["']$/g, '').trim());

    // Track AI usage
    await supabase.from('ai_usage_logs').insert({
      user_id: user.user.id,
      feature_type: 'title_optimization',
      credits_used: 1,
      prompt_used: title,
      response_received: suggestions.join('; ')
    });

    console.log('[OPTIMIZE-TITLE] Generated suggestions:', suggestions);

    return new Response(JSON.stringify({ suggestions }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('[OPTIMIZE-TITLE] Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
