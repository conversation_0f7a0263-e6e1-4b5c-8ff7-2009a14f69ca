
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";
import { SubscriptionTierId, subscriptionTiers } from '../../src/config/subscriptionTiers';

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[CHECK-SUBSCRIPTION] ${step}${detailsStr}`);
};

// Create a mapping of Stripe price IDs to tier IDs from the centralized config
const buildStripePriceMapping = (): Record<string, SubscriptionTierId> => {
  const mapping: Record<string, SubscriptionTierId> = {};
  
  subscriptionTiers.forEach(tier => {
    mapping[tier.stripePriceIds.monthly] = tier.id;
    mapping[tier.stripePriceIds.annual] = tier.id;
  });
  
  return mapping;
};

const STRIPE_PRICES = buildStripePriceMapping();

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  const supabaseClient = createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    { auth: { persistSession: false } }
  );

  try {
    logStep("Function started");

    const stripeKey = Deno.env.get("STRIPE_SECRET_KEY");
    if (!stripeKey) throw new Error("STRIPE_SECRET_KEY is not set");
    logStep("Stripe key verified");

    const authHeader = req.headers.get("Authorization");
    if (!authHeader) throw new Error("No authorization header provided");
    logStep("Authorization header found");

    const token = authHeader.replace("Bearer ", "");
    logStep("Authenticating user with token");
    
    const { data: userData, error: userError } = await supabaseClient.auth.getUser(token);
    if (userError) throw new Error(`Authentication error: ${userError.message}`);
    const user = userData.user;
    if (!user?.email) throw new Error("User not authenticated or email not available");
    logStep("User authenticated", { userId: user.id, email: user.email });

    const stripe = new Stripe(stripeKey, { apiVersion: "2023-10-16" });
    const customers = await stripe.customers.list({ email: user.email, limit: 1 });
    
    if (customers.data.length === 0) {
      logStep("No customer found, updating unsubscribed state");
      
      // Update profiles table
      await supabaseClient
        .from('profiles')
        .update({
          subscription_tier: 'analytics_only',
          subscription_status: 'trialing'
        })
        .eq('id', user.id);

      // Update users table
      await supabaseClient
        .from('users')
        .update({
          subscription_tier: 'analytics_only',
          subscription_status: 'trialing',
          subscription_credits: 20
        })
        .eq('id', user.id);

      return new Response(JSON.stringify({ subscribed: false }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      });
    }

    const customerId = customers.data[0].id;
    logStep("Found Stripe customer", { customerId });

    // Get ALL subscriptions for this customer (including trialing)
    const allSubscriptions = await stripe.subscriptions.list({
      customer: customerId,
      limit: 10,
    });
    logStep("All subscriptions found", { 
      count: allSubscriptions.data.length,
      subscriptions: allSubscriptions.data.map(sub => ({
        id: sub.id,
        status: sub.status,
        priceId: sub.items.data[0]?.price.id,
        current_period_end: sub.current_period_end
      }))
    });

    // Check for any active or trialing subscription
    const activeSubscriptions = allSubscriptions.data.filter(sub => 
      sub.status === "active" || sub.status === "trialing"
    );
    const hasActiveSub = activeSubscriptions.length > 0;
    let subscriptionTier = 'analytics_only';
    let subscriptionCredits = 20;
    let subscriptionEnd = null;

    if (hasActiveSub) {
      const subscription = activeSubscriptions[0];
      subscriptionEnd = new Date(subscription.current_period_end * 1000).toISOString();
      logStep("Active/trialing subscription found", { 
        subscriptionId: subscription.id, 
        status: subscription.status,
        endDate: subscriptionEnd 
      });
      
      // Determine subscription tier from price - Updated mapping for new tiers
      const priceId = subscription.items.data[0].price.id;
      logStep("Processing price ID", { priceId });
      
      subscriptionTier = STRIPE_PRICES[priceId] || 'analytics_only';
      
      // Set credits based on tier
      const creditsByTier = {
        'analytics_only': 50, // MCH Starter
        'ai_lite': 200,       // MCH Lite
        'ai_pro': 500         // MCH Pro
      };
      
      subscriptionCredits = creditsByTier[subscriptionTier as keyof typeof creditsByTier] || 20;
      
      logStep("Determined subscription tier", { 
        priceId, 
        subscriptionTier,
        subscriptionCredits,
        foundInMapping: !!STRIPE_PRICES[priceId]
      });
    } else {
      logStep("No active or trialing subscription found");
    }

    // Update profiles table
    await supabaseClient
      .from('profiles')
      .update({
        stripe_customer_id: customerId,
        subscription_tier: subscriptionTier,
        subscription_status: hasActiveSub ? (activeSubscriptions[0].status === 'trialing' ? 'trialing' : 'active') : 'trialing',
        current_period_end: subscriptionEnd,
        subscription_credits: subscriptionCredits
      })
      .eq('id', user.id);

    // Update users table
    await supabaseClient
      .from('users')
      .update({
        stripe_customer_id: customerId,
        subscription_tier: subscriptionTier,
        subscription_status: hasActiveSub ? (activeSubscriptions[0].status === 'trialing' ? 'trialing' : 'active') : 'trialing',
        subscription_end_date: subscriptionEnd,
        subscription_credits: subscriptionCredits
      })
      .eq('id', user.id);

    logStep("Updated database with subscription info", { 
      subscribed: hasActiveSub, 
      subscriptionTier,
      subscriptionCredits,
      subscriptionStatus: hasActiveSub ? activeSubscriptions[0].status : 'trialing'
    });
    
    return new Response(JSON.stringify({
      subscribed: hasActiveSub,
      subscription_tier: subscriptionTier,
      subscription_end: subscriptionEnd
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logStep("ERROR in check-subscription", { message: errorMessage });
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
