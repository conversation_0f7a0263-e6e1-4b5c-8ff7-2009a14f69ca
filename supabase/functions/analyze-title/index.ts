
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: user } = await supabase.auth.getUser(token);

    if (!user.user) {
      throw new Error('User not authenticated');
    }

    const { title, pillarName } = await req.json();

    console.log('[ANALYZE-TITLE] Analyzing title:', title);

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: `You are an expert YouTube title analyzer. Analyze titles for click-through rate optimization and provide detailed scoring.

Return your analysis as a JSON object with this exact structure:
{
  "score": number (0-100),
  "keywordPlacement": {"score": number (0-20), "status": "good|warning|poor", "message": "brief explanation"},
  "length": {"score": number (0-10), "status": "good|warning|poor", "message": "brief explanation"},
  "emotional": {"score": number (0-20), "status": "good|warning|poor", "message": "brief explanation"},
  "specificity": {"score": number (0-20), "status": "good|warning|poor", "message": "brief explanation"},
  "curiosity": {"score": number (0-20), "status": "good|warning|poor", "message": "brief explanation"},
  "clarity": {"score": number (0-10), "status": "good|warning|poor", "message": "brief explanation"},
  "improvements": ["improvement 1", "improvement 2", "improvement 3"],
  "suggestedFix": "one improved version of the title"
}

Scoring criteria:
- Keyword placement (20 points): Front-loaded keywords score higher
- Emotional triggers (20 points): Power words, urgency, curiosity
- Specificity/numbers (20 points): Specific numbers, timeframes, quantities
- Curiosity gap (20 points): Creates intrigue without giving away everything
- Length optimization (10 points): 40-60 characters ideal, penalize too long/short
- Clarity (10 points): Clear topic understanding

Status levels:
- "good": 80%+ of category points
- "warning": 50-79% of category points  
- "poor": <50% of category points`
          },
          {
            role: 'user',
            content: `Analyze this YouTube title: "${title}"
${pillarName ? `Content pillar: "${pillarName}"` : ''}

Provide a comprehensive analysis with scoring and actionable improvements.`
          }
        ],
        max_tokens: 800,
        temperature: 0.3,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('[ANALYZE-TITLE] OpenAI API error:', response.status, errorData);
      throw new Error(`OpenAI API error: ${response.status} - ${errorData}`);
    }

    const data = await response.json();
    let analysisText = data.choices[0].message.content;

    // Clean up JSON if it's wrapped in markdown
    analysisText = analysisText.replace(/```json\n?|\n?```/g, '').trim();

    let analysis;
    try {
      analysis = JSON.parse(analysisText);
    } catch (parseError) {
      console.error('[ANALYZE-TITLE] Failed to parse analysis JSON:', parseError);
      throw new Error('Failed to parse analysis results');
    }

    // Track AI usage
    await supabase.from('ai_usage_logs').insert({
      user_id: user.user.id,
      feature_type: 'title_analysis',
      credits_used: 1,
      prompt_used: title,
      response_received: JSON.stringify(analysis)
    });

    console.log('[ANALYZE-TITLE] Analysis completed with score:', analysis.score);

    return new Response(JSON.stringify({ analysis }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('[ANALYZE-TITLE] Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
