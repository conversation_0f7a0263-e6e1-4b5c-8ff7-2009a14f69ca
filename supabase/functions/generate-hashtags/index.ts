import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('[GENERATE-HASHTAGS] Function started');

    if (!openAIApiKey) {
      console.error('[GENERATE-HASHTAGS] OpenAI API key not configured');
      throw new Error('OpenAI API key not configured');
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    const authHeader = req.headers.get('Authorization')!;
    if (!authHeader) {
      console.error('[GENERATE-HASHTAGS] No authorization header');
      throw new Error('No authorization header');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      console.error('[GENERATE-HASHTAGS] User authentication failed:', userError);
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const { title, content } = await req.json();

    if (!title && !content) {
      console.error('[GENERATE-HASHTAGS] No title or content provided');
      throw new Error('Either title or content is required');
    }

    console.log('[GENERATE-HASHTAGS] Processing request for user:', user.id, 'Title:', title?.substring(0, 50));

    const prompt = `Generate optimized hashtags for a YouTube video based on the following:

Title: "${title || 'No title provided'}"
Content/Description: "${content || 'No content provided'}"

Please generate exactly 10 relevant hashtags that will help with YouTube discoverability. Include:
1. Broad category hashtags (e.g., #contentcreator, #youtube)
2. Specific topic hashtags related to the content
3. Trending hashtags that are relevant
4. Platform-specific hashtags (e.g., #youtubeshorts, #reels)

Return as a JSON array of exactly 10 hashtags with # symbols included.

Example format:
{
  "hashtags": ["#contentcreator", "#youtube", "#tutorial", "#tips", "#viral", "#trending", "#fyp", "#shorts", "#creator", "#howto"]
}`;

    console.log('[GENERATE-HASHTAGS] Making OpenAI API request...');

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are an expert YouTube SEO specialist who creates optimized hashtags for maximum discoverability. Always return valid JSON with the exact structure requested.'
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 400,
      }),
    });

    console.log('[GENERATE-HASHTAGS] OpenAI API response status:', response.status);

    if (!response.ok) {
      const errorData = await response.text();
      console.error('[GENERATE-HASHTAGS] OpenAI API error:', response.status, errorData);

      // Handle specific OpenAI errors
      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or expired');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded. Please try again later.');
      } else if (response.status === 500) {
        throw new Error('OpenAI API is experiencing issues. Please try again later.');
      } else {
        throw new Error(`OpenAI API error: ${response.status} - ${errorData}`);
      }
    }

    const data = await response.json();
    let result;
    
    try {
      // Clean up JSON if it's wrapped in markdown
      let content = data.choices[0].message.content;
      content = content.replace(/```json\n?|\n?```/g, '').trim();
      result = JSON.parse(content);
    } catch (parseError) {
      console.error('[GENERATE-HASHTAGS] Failed to parse JSON:', parseError);
      // Fallback: extract hashtags from text
      const content = data.choices[0].message.content;
      const hashtagMatches = content.match(/#\w+/g) || [];
      result = {
        hashtags: hashtagMatches.slice(0, 10)
      };
    }

    // Ensure we have hashtags array
    if (!result.hashtags || !Array.isArray(result.hashtags)) {
      result = { hashtags: [] };
    }

    // Log AI usage
    await supabase.from('ai_usage_logs').insert({
      user_id: user.id,
      feature_type: 'hashtag_generation',
      credits_used: 1,
      prompt_used: `Title: ${title || 'N/A'}, Content: ${(content || '').substring(0, 100)}...`,
      response_received: `Generated ${result.hashtags.length} hashtags`
    });

    console.log('[GENERATE-HASHTAGS] Generated', result.hashtags.length, 'hashtags for user:', user.id);

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('[GENERATE-HASHTAGS] Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
