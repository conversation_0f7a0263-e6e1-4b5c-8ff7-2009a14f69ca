
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);
    
    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const { prompt, type, user_id } = await req.json();

    console.log(`[GENERATE-AI-CONTENT] Processing request: {
  type: "${type}",
  userId: "${user_id}"
}`);

    let systemPrompt = 'You are a helpful assistant that generates content based on user prompts.';
    let temperature = 0.7;
    let maxTokens = 1000;

    // Customize prompts based on type
    switch (type) {
      case 'topic-generation':
        systemPrompt = 'You are an expert YouTube content strategist. Generate creative, engaging video topic ideas that will perform well on YouTube. Always return valid JSON array format.';
        temperature = 0.8;
        maxTokens = 800;
        break;
      case 'title-generation':
        systemPrompt = 'You are an expert YouTube title writer. Create compelling, click-worthy titles that follow YouTube best practices. Keep titles under 60 characters for optimal display.';
        temperature = 0.8;
        maxTokens = 400;
        break;
      case 'hook-generation':
        systemPrompt = 'You are an expert YouTube script writer specializing in creating engaging video hooks that capture viewer attention in the first 15 seconds.';
        temperature = 0.8;
        maxTokens = 600;
        break;
      case 'seo-generation':
        systemPrompt = 'You are a YouTube SEO expert. Generate relevant tags and hashtags that will help videos rank better and reach the right audience. Always return valid JSON format.';
        temperature = 0.3;
        maxTokens = 500;
        break;
      case 'description-generation':
        systemPrompt = 'You are an expert YouTube description writer. Create engaging, SEO-optimized descriptions that encourage engagement and provide value to viewers.';
        temperature = 0.7;
        maxTokens = 800;
        break;
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: prompt }
        ],
        temperature,
        max_tokens: maxTokens,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    let content = data.choices[0].message.content;

    // Handle SEO generation specifically
    if (type === 'seo-generation') {
      try {
        // Try to parse as JSON first
        const cleanedContent = content.replace(/```json\n?|\n?```/g, '').trim();
        const parsedContent = JSON.parse(cleanedContent);
        
        console.log('[GENERATE-AI-CONTENT] Successfully parsed SEO JSON:', parsedContent);
        
        return new Response(JSON.stringify({ content: parsedContent }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      } catch (parseError) {
        console.log('[GENERATE-AI-CONTENT] Could not parse SEO as JSON, creating fallback structure');
        
        // Create fallback structure for SEO
        const fallbackSEO = {
          tags: content.split('\n').filter(line => line.trim() && !line.includes('#')).slice(0, 15),
          hashtags: content.split('\n').filter(line => line.includes('#')).join(' ')
        };
        
        return new Response(JSON.stringify({ content: fallbackSEO }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }
    }

    // Handle other content types
    if (type === 'topic-generation') {
      try {
        const cleanedContent = content.replace(/```json\n?|\n?```/g, '').trim();
        const parsedContent = JSON.parse(cleanedContent);
        console.log('[GENERATE-AI-CONTENT] Successfully parsed topics JSON');
        return new Response(JSON.stringify({ content: parsedContent }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      } catch (parseError) {
        console.log('[GENERATE-AI-CONTENT] Could not parse topics as JSON, returning as text');
      }
    }

    // For other types, try to split into array if it's a list
    if (type === 'title-generation' || type === 'hook-generation') {
      const lines = content.split('\n').filter(line => line.trim());
      if (lines.length > 1) {
        content = lines.map(line => line.replace(/^\d+\.\s*/, '').trim());
      } else {
        content = [content];
      }
    } else {
      console.log('[GENERATE-AI-CONTENT] Could not parse as JSON, returning as text');
    }

    console.log('[GENERATE-AI-CONTENT] Generated content successfully');

    return new Response(JSON.stringify({ content }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in generate-ai-content function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
