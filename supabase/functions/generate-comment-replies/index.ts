
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { comment, tone, user_id } = await req.json();

    if (!comment || !tone || !user_id) {
      throw new Error('Missing required parameters');
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Log AI usage
    const creditsUsed = 2; // Comment replies cost 2 credits
    await supabase.from('ai_usage_logs').insert({
      user_id,
      feature_used: 'comment_replies',
      credits_used: creditsUsed,
      request_data: { comment_length: comment.length, tone }
    });

    console.log(`Comment replies requested for user: ${user_id}, tone: ${tone}`);

    // Call OpenAI API
    const openAIResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: `You are a YouTube creator's assistant. Generate 3 different reply variations to viewer comments.
            
            Guidelines:
            - Be authentic and engaging
            - ${tone === 'friendly' ? 'Use warm, approachable language with emojis' : ''}
            - ${tone === 'professional' ? 'Use professional but personable language' : ''}
            - ${tone === 'casual' ? 'Use relaxed, conversational language' : ''}
            - Keep replies under 100 characters when possible
            - Encourage further engagement
            - Show appreciation for the comment
            
            Respond in JSON format:
            {
              "replies": [
                {"text": "reply1", "tone": "${tone}"},
                {"text": "reply2", "tone": "${tone}"},
                {"text": "reply3", "tone": "${tone}"}
              ]
            }`
          },
          {
            role: 'user',
            content: `Generate ${tone} replies to this viewer comment: "${comment}"`
          }
        ],
        max_tokens: 400,
        temperature: 0.8,
      }),
    });

    if (!openAIResponse.ok) {
      throw new Error(`OpenAI API error: ${openAIResponse.statusText}`);
    }

    const openAIData = await openAIResponse.json();
    const repliesText = openAIData.choices[0].message.content;

    // Parse the JSON response
    let repliesData;
    try {
      repliesData = JSON.parse(repliesText);
    } catch (parseError) {
      console.error('Failed to parse OpenAI response:', repliesText);
      throw new Error('Failed to parse reply results');
    }

    console.log('Comment replies generated successfully');

    return new Response(
      JSON.stringify({ replies: repliesData.replies }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Error in generate-comment-replies function:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
