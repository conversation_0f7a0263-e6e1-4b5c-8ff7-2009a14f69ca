
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: user } = await supabase.auth.getUser(token);

    if (!user.user) {
      throw new Error('User not authenticated');
    }

    const { analyticsData } = await req.json();

    console.log('[GET-INSIGHTS] Analyzing data for user:', user.user.id);

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are an expert YouTube analytics consultant. Analyze the provided channel data and provide 3 specific, actionable recommendations to improve performance. Each recommendation should be practical and based on the data provided.'
          },
          {
            role: 'user',
            content: `Channel Analytics Data:
- Total Views: ${analyticsData.totalViews || 0}
- Total Videos: ${analyticsData.totalVideos || 0}
- Average Views per Video: ${analyticsData.avgViewsPerVideo || 0}
- Best Performing Content Pillar: ${analyticsData.bestPerformingPillar || 'Unknown'}
- Growth Rate: ${analyticsData.growthRate || 0}%

Top Videos: ${analyticsData.topVideos?.slice(0, 5).map((v: any) => `"${v.title}" (${v.views} views)`).join(', ') || 'No data'}

Content Pillar Performance: ${analyticsData.pillarPerformance?.map((p: any) => `${p.name}: ${p.avgViews} avg views, ${p.videoCount} videos`).join('; ') || 'No data'}

Based on this data, provide 3 specific recommendations to improve channel performance. Format as:
1. [Recommendation Title]: [Detailed explanation]
2. [Recommendation Title]: [Detailed explanation]
3. [Recommendation Title]: [Detailed explanation]`
          }
        ],
        max_tokens: 500,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('[GET-INSIGHTS] OpenAI API error:', response.status, errorData);
      throw new Error(`OpenAI API error: ${response.status} - ${errorData}`);
    }

    const data = await response.json();
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('Invalid response structure from OpenAI');
    }
    
    const content = data.choices[0].message.content;
    
    // Parse recommendations
    const recommendations = content.split(/\d+\.\s/).slice(1).map((rec: string) => {
      const [title, ...descParts] = rec.split(':');
      return {
        title: title?.trim() || 'Recommendation',
        description: descParts.join(':').trim()
      };
    }).slice(0, 3);

    // Track AI usage
    await supabase.from('ai_usage_logs').insert({
      user_id: user.user.id,
      feature_type: 'analytics_insights',
      credits_used: 2,
      prompt_used: 'Analytics data analysis',
      response_received: `Generated ${recommendations.length} insights`
    });

    console.log('[GET-INSIGHTS] Generated recommendations:', recommendations);

    return new Response(JSON.stringify({ recommendations }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('[GET-INSIGHTS] Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
