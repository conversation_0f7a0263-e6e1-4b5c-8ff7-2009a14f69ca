
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";
import { subscriptionTiers, legacyTierMapping } from '../../src/config/subscriptionTiers';

const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
  apiVersion: "2023-10-16",
});

const supabaseClient = createClient(
  Deno.env.get("SUPABASE_URL") ?? "",
  Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
);

// Create a mapping of Stripe price IDs to tier IDs from the centralized config
const buildStripePriceMapping = (): Record<string, string> => {
  const mapping: Record<string, string> = {};
  
  subscriptionTiers.forEach(tier => {
    mapping[tier.stripePriceIds.monthly] = tier.id;
    mapping[tier.stripePriceIds.annual] = tier.id;
  });
  
  return mapping;
};

const STRIPE_PRICES = buildStripePriceMapping();

serve(async (req) => {
  const signature = req.headers.get("stripe-signature");

  if (!signature) {
    console.error("No stripe signature found");
    return new Response("No signature", { status: 400 });
  }

  let event: Stripe.Event;

  try {
    // Get the raw body as Uint8Array for proper signature verification
    const body = await req.arrayBuffer();
    const bodyString = new TextDecoder().decode(body);
    
    // Use async webhook signature verification
    event = await stripe.webhooks.constructEventAsync(
      bodyString,
      signature,
      Deno.env.get("STRIPE_WEBHOOK_SECRET") || ""
    );

    console.log('Webhook event:', event.type);
  } catch (err) {
    console.error(`Webhook signature verification failed: ${err.message}`);
    return new Response(`Webhook Error: ${err.message}`, { status: 400 });
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        console.log('Checkout completed for user:', session.metadata?.user_id);

        if (session.metadata?.user_id) {
          // Check if this is a team seats purchase
          if (session.metadata?.type === 'team_seats') {
            const additionalSeats = parseInt(session.metadata.additional_seats || '0');
            console.log('Team seats purchase:', { user_id: session.metadata.user_id, additional_seats: additionalSeats });

            // Update users table with additional seats
            await supabaseClient
              .from('users')
              .update({
                additional_seats: additionalSeats
              })
              .eq('id', session.metadata.user_id);

            console.log('Updated user with additional seats:', additionalSeats);
          } else {
            // Regular subscription checkout
            // Map old tier names to new ones
            const tierMapping = legacyTierMapping;

            const newTier = tierMapping[session.metadata.tier as keyof typeof tierMapping] || 'analytics_only';

            // Set credits based on tier
            const creditsByTier = {
              'analytics_only': 50, // MCH Starter
              'ai_lite': 200,       // MCH Lite
              'ai_pro': 500         // MCH Pro
            };

            const subscriptionCredits = creditsByTier[newTier as keyof typeof creditsByTier] || 50;

            // Update profiles table with subscription info
            await supabaseClient
              .from('profiles')
              .upsert({
                id: session.metadata.user_id,
                email: session.customer_email!,
                stripe_customer_id: session.customer as string,
                subscription_tier: newTier,
                subscription_status: 'trialing',
                subscription_credits: subscriptionCredits
              }, { onConflict: 'id' });

            // Also update users table for backward compatibility
            await supabaseClient
              .from('users')
              .update({
                stripe_customer_id: session.customer as string,
                subscription_tier: newTier,
                subscription_status: 'trialing',
                subscription_credits: subscriptionCredits
              })
              .eq('id', session.metadata.user_id);
          }
        }
        break;
      }

      case 'customer.subscription.updated':
      case 'customer.subscription.created': {
        const subscription = event.data.object as Stripe.Subscription;
        const priceId = subscription.items.data[0]?.price.id;
        const tier = STRIPE_PRICES[priceId as keyof typeof STRIPE_PRICES] || 'analytics_only';

        // Set credits based on tier
        const creditsByTier = {
          'analytics_only': 50, // MCH Starter
          'ai_lite': 200,       // MCH Lite
          'ai_pro': 500         // MCH Pro
        };
        
        const subscriptionCredits = creditsByTier[tier as keyof typeof creditsByTier] || 50;

        console.log('Subscription updated:', subscription.id, 'Status:', subscription.status, 'Tier:', tier);

        // Update profiles table
        await supabaseClient
          .from('profiles')
          .update({
            subscription_tier: tier,
            subscription_status: subscription.status,
            current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            subscription_credits: subscriptionCredits
          })
          .eq('stripe_customer_id', subscription.customer as string);

        // Also update users table for backward compatibility
        await supabaseClient
          .from('users')
          .update({
            subscription_tier: tier,
            subscription_status: subscription.status,
            subscription_end_date: new Date(subscription.current_period_end * 1000).toISOString(),
            subscription_credits: subscriptionCredits
          })
          .eq('stripe_customer_id', subscription.customer as string);
        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;
        console.log('Subscription deleted:', subscription.id);

        // Update profiles table
        await supabaseClient
          .from('profiles')
          .update({
            subscription_tier: 'analytics_only',
            subscription_status: 'canceled',
            subscription_credits: 50
          })
          .eq('stripe_customer_id', subscription.customer as string);

        // Also update users table for backward compatibility
        await supabaseClient
          .from('users')
          .update({
            subscription_tier: 'analytics_only',
            subscription_status: 'canceled',
            subscription_credits: 50
          })
          .eq('stripe_customer_id', subscription.customer as string);
        break;
      }
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.error('Webhook processing error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { "Content-Type": "application/json" },
      status: 500,
    });
  }
});
