import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import Stripe from "https://esm.sh/stripe@14.21.0";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  console.log('=== CREATE-SEATS-CHECKOUT FUNCTION STARTED ===');
  console.log('Request method:', req.method);

  if (req.method === "OPTIONS") {
    console.log('Handling CORS preflight request');
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('1. Parsing request body...');
    const { additional_seats, customer_id } = await req.json();
    console.log('Request data:', { additional_seats, customer_id });

    // Validate input
    if (!additional_seats || additional_seats <= 0) {
      throw new Error('Invalid number of additional seats');
    }

    if (!customer_id) {
      throw new Error('Customer ID is required');
    }

    console.log('2. Getting auth user...');
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    );

    const { data: { user }, error: userError } = await supabaseClient.auth.getUser();
    if (userError || !user) {
      console.error('Auth error:', userError);
      throw new Error('Authentication failed');
    }

    console.log('3. Initializing Stripe...');
    const stripeKey = Deno.env.get("STRIPE_SECRET_KEY");
    if (!stripeKey) {
      console.error('ERROR: STRIPE_SECRET_KEY not found in environment');
      throw new Error("Stripe secret key not configured");
    }

    const stripe = new Stripe(stripeKey, {
      apiVersion: "2023-10-16",
    });

    console.log('4. Getting PUBLIC_URL for redirect URLs...');
    const publicUrl = Deno.env.get("PUBLIC_URL") || "http://localhost:8080";
    console.log('Public URL:', publicUrl);

    // Calculate pricing - $5 per additional seat per month
    const seatPrice = 500; // $5.00 in cents
    const totalAmount = additional_seats * seatPrice;

    console.log('5. Creating Stripe checkout session for team seats...');
    const sessionParams = {
      mode: 'subscription' as const,
      payment_method_types: ['card'],
      customer: customer_id,
      line_items: [{
        price_data: {
          currency: 'usd',
          product_data: {
            name: `Additional Team Seats (${additional_seats} seat${additional_seats > 1 ? 's' : ''})`,
            description: `Add ${additional_seats} team member seat${additional_seats > 1 ? 's' : ''} to your subscription`,
          },
          unit_amount: seatPrice,
          recurring: {
            interval: 'month',
          },
        },
        quantity: additional_seats,
      }],
      subscription_data: {
        metadata: {
          user_id: user.id,
          additional_seats: additional_seats.toString(),
          type: 'team_seats'
        }
      },
      success_url: `${publicUrl}/billing?seats=success`,
      cancel_url: `${publicUrl}/billing`,
      metadata: {
        user_id: user.id,
        additional_seats: additional_seats.toString(),
        type: 'team_seats'
      }
    };
    
    console.log('Session params:', sessionParams);
    
    const session = await stripe.checkout.sessions.create(sessionParams);
    console.log('Stripe session created successfully:', { 
      id: session.id, 
      url: session.url,
      mode: session.mode,
      status: session.status
    });

    console.log('6. Returning success response...');
    return new Response(
      JSON.stringify({ 
        url: session.url,
        session_id: session.id 
      }),
      { 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    );

  } catch (error) {
    console.error('=== SEATS CHECKOUT ERROR ===');
    console.error('Error type:', error.constructor.name);
    console.error('Error message:', error.message);
    console.error('Full error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Failed to create checkout session' 
      }),
      { 
        status: 400,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    );
  }
});
