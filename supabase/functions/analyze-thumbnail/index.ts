
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { image, user_id } = await req.json();

    if (!image || !user_id) {
      throw new Error('Missing required parameters');
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Log AI usage
    const creditsUsed = 3; // Thumbnail analysis costs 3 credits
    await supabase.from('ai_usage_logs').insert({
      user_id,
      feature_used: 'thumbnail_analysis',
      credits_used: creditsUsed,
      request_data: { action: 'analyze_thumbnail' }
    });

    console.log(`Thumbnail analysis requested for user: ${user_id}`);

    // Call OpenAI Vision API
    const openAIResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: `You are a YouTube thumbnail expert. Analyze the thumbnail and provide scores (1-10) for:
            1. Clickability Score - How likely people are to click
            2. Readability Score - How easy text is to read
            3. Contrast Score - Visual contrast and clarity
            4. Visual Impact Score - Overall visual appeal
            
            Also provide 3-5 specific improvement tips.
            
            Respond in JSON format:
            {
              "clickabilityScore": number,
              "readabilityScore": number,
              "contrastScore": number,
              "visualImpactScore": number,
              "improvementTips": ["tip1", "tip2", "tip3", "tip4", "tip5"]
            }`
          },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: 'Please analyze this YouTube thumbnail:'
              },
              {
                type: 'image_url',
                image_url: {
                  url: image
                }
              }
            ]
          }
        ],
        max_tokens: 500,
        temperature: 0.7,
      }),
    });

    if (!openAIResponse.ok) {
      throw new Error(`OpenAI API error: ${openAIResponse.statusText}`);
    }

    const openAIData = await openAIResponse.json();
    const analysisText = openAIData.choices[0].message.content;

    // Parse the JSON response
    let analysis;
    try {
      analysis = JSON.parse(analysisText);
    } catch (parseError) {
      console.error('Failed to parse OpenAI response:', analysisText);
      throw new Error('Failed to parse analysis results');
    }

    console.log('Thumbnail analysis completed successfully');

    return new Response(
      JSON.stringify({ analysis }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Error in analyze-thumbnail function:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
