
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

// Try both possible variations of the OpenAI API key name
const openAIApiKey = Deno.env.get('OPENAI_API_KEY') || Deno.env.get('OpenAI APi Key');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (!openAIApiKey) {
      console.error('OpenAI API key not found. Checked: OPENAI_API_KEY and "OpenAI APi Key"');
      throw new Error('OpenAI API key not configured');
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Get user from request
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);
    
    if (userError || !user) {
      console.error('User authentication failed:', userError);
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const { title, description, pillar, videoLength, tone } = await req.json();

    if (!title || !videoLength || !tone) {
      throw new Error('Missing required fields: title, videoLength, or tone');
    }

    console.log('[GENERATE-SCRIPT] Processing request for user:', user.id, { title, videoLength, tone });

    // Log AI usage
    await supabase.from('ai_usage_logs').insert({
      user_id: user.id,
      feature_type: 'script_generation',
      credits_used: 5
    });

    // Create detailed script prompt based on video length
    let detailedInstructions = '';
    let maxTokens = 1500;
    
    switch (videoLength) {
      case 'short':
        detailedInstructions = `Create a COMPLETE, WORD-FOR-WORD script for a 60-second YouTube video. Include:
        - Every single word the host should say
        - Detailed opening hook (15 seconds)
        - 2-3 main points with full explanations (30 seconds)
        - Strong call-to-action with exact wording (15 seconds)
        - Natural speech patterns, pauses indicated with [PAUSE]
        - Visual cues in [BRACKETS] for editing`;
        maxTokens = 800;
        break;
      case 'medium':
        detailedInstructions = `Create a COMPLETE, WORD-FOR-WORD script for a 5-10 minute YouTube video. Include:
        - Full introduction with hook and preview (1-2 minutes)
        - 3-4 detailed main sections with complete explanations and examples (6-7 minutes)
        - Smooth transitions between sections with exact wording
        - Call-to-action prompts throughout
        - Detailed conclusion and final CTA (1 minute)
        - Natural speech patterns, indicate pauses with [PAUSE]
        - Visual and editing cues in [BRACKETS]`;
        maxTokens = 2500;
        break;
      case 'long':
        detailedInstructions = `Create a COMPLETE, WORD-FOR-WORD script for a 10-20 minute YouTube video. Include:
        - Comprehensive introduction with hook, preview, and context (2-3 minutes)
        - 4-5 detailed main sections with full explanations, examples, and stories (12-15 minutes)
        - Multiple engagement points and CTAs throughout
        - Detailed transitions and natural conversation flow
        - Comprehensive conclusion with summary and strong final CTA (2-3 minutes)
        - Natural speech patterns, indicate pauses with [PAUSE]
        - Detailed visual and editing cues in [BRACKETS]`;
        maxTokens = 4000;
        break;
      default:
        detailedInstructions = detailedInstructions || `Create a COMPLETE, WORD-FOR-WORD script for a 5-10 minute YouTube video with all spoken content included.`;
        maxTokens = 2500;
    }

    const prompt = `You are a professional YouTube scriptwriter. Create a COMPLETE, DETAILED, WORD-FOR-WORD video script for:

Title: "${title}"
Topic: ${description || 'No specific description provided'}
Content Pillar: ${pillar || 'general'}
Tone: ${tone}
Length: ${detailedInstructions}

IMPORTANT: This must be a FULL SCRIPT with every word the host will say, not just an outline or summary.

Structure the complete script with these sections:

**HOOK (First 15 seconds):**
[Write the complete opening lines word-for-word]

**INTRODUCTION:**
[Complete introduction with what viewers will learn]

**MAIN CONTENT:**
[Write out the FULL content for each section with complete sentences, explanations, and examples]

**CALL TO ACTION:**
[Complete CTA with exact wording for subscribe, like, comment prompts]

**OUTRO:**
[Complete closing with exact words to thank viewers and tease next video]

Make it ${tone} and engaging. Include natural speech patterns, indicate pauses with [PAUSE], and add visual cues in [BRACKETS]. Write EVERY SINGLE WORD the host should say - this is not an outline, it's a complete script ready for filming.`;

    console.log('[GENERATE-SCRIPT] Sending detailed request to OpenAI...');

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { 
            role: 'system', 
            content: 'You are a professional YouTube script writer who creates complete, word-for-word video scripts. Never create outlines or summaries - always write complete scripts with every single word the host should say. Include natural speech patterns and detailed content.'
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: maxTokens,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('OpenAI API error:', response.status, errorData);
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      console.error('Invalid OpenAI response structure:', data);
      throw new Error('Invalid response from OpenAI API');
    }

    const script = data.choices[0].message.content;

    // Estimate read time (average 150 words per minute for natural speech)
    const wordCount = script.split(' ').length;
    const readTimeMinutes = Math.ceil(wordCount / 150);
    const estimatedReadTime = readTimeMinutes === 1 ? '1 minute' : `${readTimeMinutes} minutes`;

    console.log('[GENERATE-SCRIPT] Complete script generated successfully. Word count:', wordCount);

    return new Response(JSON.stringify({ 
      script,
      estimatedReadTime,
      wordCount 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in generate-script function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
