
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Strip<PERSON> from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";
import { getStripePriceId } from '../../src/config/subscriptionTiers';

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Content-Type": "application/json",
};

serve(async (req) => {
  console.log('=== CREATE-CHECKOUT FUNCTION STARTED ===');
  console.log('Request method:', req.method);

  if (req.method === "OPTIONS") {
    console.log('Handling CORS preflight request');
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('1. Setting up Supabase client...');
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    console.log('2. Checking authorization header...');
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) {
      console.error('ERROR: No authorization header found');
      throw new Error("No authorization header provided");
    }

    console.log('3. Extracting and validating user...');
    const token = authHeader.replace("Bearer ", "");
    const { data, error: authError } = await supabaseClient.auth.getUser(token);
    
    if (authError) {
      console.error('ERROR: Authentication failed:', authError);
      throw new Error(`Authentication failed: ${authError.message}`);
    }
    
    const user = data.user;
    if (!user?.email) {
      console.error('ERROR: User not authenticated or no email');
      throw new Error("User not authenticated or email not available");
    }
    console.log('User authenticated successfully:', { id: user.id, email: user.email });

    console.log('4. Parsing request body...');
    const requestBody = await req.json();
    console.log('Request body:', requestBody);
    
    const { tier, isAnnual } = requestBody;
    if (!tier) {
      console.error('ERROR: No tier provided in request body');
      throw new Error("Tier is required");
    }
    
    console.log('5. Building price key and validating...');
    const priceId = getStripePriceId(tier, isAnnual);
    console.log('Price lookup:', { tier, isAnnual, priceId });

    if (!priceId) {
      console.error('ERROR: Invalid tier:', tier);
      throw new Error(`Invalid tier (${tier}) or billing period (${isAnnual ? 'annual' : 'monthly'})`);
    }

    console.log('6. Initializing Stripe...');
    const stripeKey = Deno.env.get("STRIPE_SECRET_KEY");
    if (!stripeKey) {
      console.error('ERROR: STRIPE_SECRET_KEY not found in environment');
      throw new Error("Stripe secret key not configured");
    }
    
    const stripe = new Stripe(stripeKey, {
      apiVersion: "2023-10-16",
    });
    console.log('Stripe client initialized successfully');

    console.log('7. Getting PUBLIC_URL for redirect URLs...');
    const publicUrl = Deno.env.get("PUBLIC_URL") || "http://localhost:3000";
    console.log('Public URL:', publicUrl);

    console.log('8. Creating Stripe checkout session...');
    const sessionParams = {
      mode: 'subscription' as const,
      payment_method_types: ['card'],
      customer_email: user.email,
      line_items: [{
        price: priceId,
        quantity: 1
      }],
      subscription_data: {
        trial_period_days: 7,
        metadata: {
          user_id: user.id,
          tier: tier
        }
      },
      allow_promotion_codes: true,
      success_url: `${publicUrl}/dashboard?checkout=success`,
      cancel_url: `${publicUrl}/pricing`,
      metadata: {
        user_id: user.id,
        tier: tier
      }
    };
    
    console.log('Session params:', sessionParams);
    
    const session = await stripe.checkout.sessions.create(sessionParams);
    console.log('Stripe session created successfully:', { 
      id: session.id, 
      url: session.url,
      mode: session.mode,
      status: session.status
    });

    console.log('9. Returning success response...');
    const response = { url: session.url };
    console.log('Final response:', response);

    return new Response(JSON.stringify(response), {
      headers: corsHeaders,
      status: 200,
    });
  } catch (error) {
    console.error('=== CREATE-CHECKOUT ERROR ===');
    console.error('Error type:', error.constructor.name);
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);
    
    const errorResponse = { 
      error: error.message,
      type: error.constructor.name,
      timestamp: new Date().toISOString()
    };
    
    console.error('Error response:', errorResponse);
    
    return new Response(JSON.stringify(errorResponse), {
      headers: corsHeaders,
      status: 500,
    });
  }
});
