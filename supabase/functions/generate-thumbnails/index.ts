
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface ThumbnailRequest {
  title: string;
  category: string;
  colorScheme: string;
  user_id: string;
}

interface ThumbnailResult {
  id: string;
  text: string;
  template: string;
  imageUrl: string;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  const startTime = Date.now();
  console.log('[GENERATE-THUMBNAILS] Function started');

  try {
    const { title, category, colorScheme, user_id }: ThumbnailRequest = await req.json();

    if (!title || !category || !colorScheme || !user_id) {
      console.error('[GENERATE-THUMBNAILS] Missing required parameters:', { title: !!title, category: !!category, colorScheme: !!colorScheme, user_id: !!user_id });
      throw new Error('Missing required parameters: title, category, colorScheme, and user_id are required');
    }

    // Initialize Supabase client with retry logic
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Check rate limiting
    const { data: recentUsage, error: usageError } = await supabase
      .from('ai_usage_logs')
      .select('created_at')
      .eq('user_id', user_id)
      .eq('feature_used', 'thumbnail_generation')
      .gte('created_at', new Date(Date.now() - 60000).toISOString()) // Last minute
      .order('created_at', { ascending: false });

    if (usageError) {
      console.warn('[GENERATE-THUMBNAILS] Could not check rate limiting:', usageError);
    } else if (recentUsage && recentUsage.length >= 5) {
      console.warn('[GENERATE-THUMBNAILS] Rate limit exceeded for user:', user_id);
      throw new Error('Rate limit exceeded. Please wait a moment before generating more thumbnails.');
    }

    // Log AI usage with enhanced tracking
    const creditsUsed = 1;
    const { error: logError } = await supabase.from('ai_usage_logs').insert({
      user_id,
      feature_used: 'thumbnail_generation',
      credits_used: creditsUsed,
      request_data: { title, category, colorScheme, processing_time_start: startTime }
    });

    if (logError) {
      console.warn('[GENERATE-THUMBNAILS] Failed to log usage:', logError);
    }

    console.log(`[GENERATE-THUMBNAILS] Processing request for user: ${user_id}, title: "${title}"`);

    // Enhanced OpenAI API call with retry logic
    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    let openAIResponse;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        openAIResponse = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${openAIApiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'gpt-4o-mini',
            messages: [
              {
                role: 'system',
                content: `You are a YouTube thumbnail text expert. Create compelling, clickable text for thumbnails based on video titles.
                
                Guidelines:
                - Maximum 6 words per text variation
                - Use powerful, emotional words that drive clicks
                - Create urgency or curiosity
                - Make text scannable at small sizes
                - Match the ${category} category style
                - Consider ${colorScheme} color scheme for readability
                - Avoid clickbait that misleads
                - Use action words and numbers when appropriate
                
                Generate 3 different text variations optimized for maximum click-through rate.
                
                Respond in JSON format:
                {
                  "thumbnails": [
                    {"id": "1", "text": "text1", "template": "${category}", "appeal": "curiosity"},
                    {"id": "2", "text": "text2", "template": "${category}", "appeal": "urgency"},
                    {"id": "3", "text": "text3", "template": "${category}", "appeal": "benefit"}
                  ]
                }`
              },
              {
                role: 'user',
                content: `Generate thumbnail text for: "${title}"`
              }
            ],
            max_tokens: 400,
            temperature: 0.8,
          }),
        });

        if (openAIResponse.ok) {
          break; // Success, exit retry loop
        } else {
          throw new Error(`OpenAI API error: ${openAIResponse.status} - ${openAIResponse.statusText}`);
        }
      } catch (error) {
        retryCount++;
        console.warn(`[GENERATE-THUMBNAILS] Attempt ${retryCount} failed:`, error.message);
        
        if (retryCount >= maxRetries) {
          throw new Error(`Failed to generate thumbnails after ${maxRetries} attempts: ${error.message}`);
        }
        
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
      }
    }

    const openAIData = await openAIResponse!.json();
    const thumbnailsText = openAIData.choices[0].message.content;

    // Parse the JSON response with error handling
    let thumbnailsData;
    try {
      thumbnailsData = JSON.parse(thumbnailsText);
    } catch (parseError) {
      console.error('[GENERATE-THUMBNAILS] Failed to parse OpenAI response:', thumbnailsText);
      // Fallback to simple text-based thumbnails
      const fallbackText = openAIData.choices[0].message.content.split('\n').filter((line: string) => line.trim());
      thumbnailsData = {
        thumbnails: fallbackText.slice(0, 3).map((text: string, index: number) => ({
          id: (index + 1).toString(),
          text: text.replace(/^\d+\.\s*/, '').substring(0, 30),
          template: category,
          appeal: ['curiosity', 'urgency', 'benefit'][index]
        }))
      };
    }

    // Enhanced thumbnail generation with better image creation
    const colorSchemes = {
      'vibrant': { bg: '#FF6B35', text: '#FFFFFF', accent: '#F7931E' },
      'professional': { bg: '#2C3E50', text: '#FFFFFF', accent: '#3498DB' },
      'minimalist': { bg: '#FFFFFF', text: '#2C3E50', accent: '#E74C3C' },
      'dark': { bg: '#1A1A1A', text: '#FFFFFF', accent: '#37BEB0' },
      'bright': { bg: '#FFD700', text: '#000000', accent: '#FF4500' }
    };

    const selectedScheme = colorSchemes[colorScheme as keyof typeof colorSchemes] || colorSchemes.vibrant;

    const thumbnailsWithImages: ThumbnailResult[] = thumbnailsData.thumbnails.map((thumbnail: any) => {
      const encodedText = encodeURIComponent(thumbnail.text);
      const encodedBg = encodeURIComponent(selectedScheme.bg);
      const encodedTextColor = encodeURIComponent(selectedScheme.text);
      
      return {
        ...thumbnail,
        imageUrl: `https://via.placeholder.com/1280x720/${encodedBg.slice(1)}/${encodedTextColor.slice(1)}?text=${encodedText}&font=Arial%20Bold`
      };
    });

    // Update usage log with completion data
    const processingTime = Date.now() - startTime;
    await supabase.from('ai_usage_logs').insert({
      user_id,
      feature_used: 'thumbnail_generation_completed',
      credits_used: 0,
      request_data: { 
        processing_time_ms: processingTime,
        thumbnails_generated: thumbnailsWithImages.length,
        retry_count: retryCount
      }
    });

    console.log(`[GENERATE-THUMBNAILS] Successfully generated ${thumbnailsWithImages.length} thumbnails in ${processingTime}ms`);

    return new Response(
      JSON.stringify({ 
        thumbnails: thumbnailsWithImages,
        processingTime,
        retryCount 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('[GENERATE-THUMBNAILS] Error:', error);
    
    // Log the error for monitoring
    try {
      const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
      const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
      const supabase = createClient(supabaseUrl, supabaseKey);
      
      await supabase.from('ai_usage_logs').insert({
        user_id: 'system',
        feature_used: 'thumbnail_generation_error',
        credits_used: 0,
        request_data: { 
          error: error.message,
          processing_time_ms: processingTime
        }
      });
    } catch (logError) {
      console.error('[GENERATE-THUMBNAILS] Failed to log error:', logError);
    }

    return new Response(
      JSON.stringify({ 
        error: error.message,
        suggestion: error.message.includes('rate limit') 
          ? 'Please wait a moment before trying again.'
          : 'Please try again or contact support if the issue persists.'
      }),
      { 
        status: error.message.includes('rate limit') ? 429 : 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
