
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);
    
    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const { title, hookStyle, targetAudience, pillar } = await req.json();

    const prompt = `Generate 5 compelling video hooks for the YouTube video titled "${title}".

Hook Style: ${hookStyle}
Target Audience: ${targetAudience}
Content Pillar: ${pillar || 'General'}

Each hook should be:
- 15 seconds or less when spoken
- Compelling and attention-grabbing
- Matched to the ${hookStyle} style
- Appropriate for ${targetAudience} audience
- Designed to maximize viewer retention

Return ONLY a valid JSON array of 5 strings. No additional text, formatting, or explanations. Example format:
["Hook 1 text here", "Hook 2 text here", "Hook 3 text here", "Hook 4 text here", "Hook 5 text here"]`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { 
            role: 'system', 
            content: 'You are an expert YouTube content creator who specializes in creating compelling video hooks. You MUST return only valid JSON arrays of strings, with no additional formatting or text.'
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.8,
        max_tokens: 600,
      }),
    });

    const data = await response.json();
    let hooks;
    
    try {
      const content = data.choices[0].message.content.trim();
      console.log('Raw OpenAI response:', content);
      
      // Clean the response to ensure it's valid JSON
      let cleanedContent = content;
      
      // Remove any markdown formatting
      if (cleanedContent.startsWith('```json')) {
        cleanedContent = cleanedContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      }
      if (cleanedContent.startsWith('```')) {
        cleanedContent = cleanedContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      // Parse the JSON
      hooks = JSON.parse(cleanedContent);
      
      // Ensure we have an array of strings
      if (!Array.isArray(hooks)) {
        throw new Error('Response is not an array');
      }
      
      // Filter out any empty or invalid entries
      hooks = hooks.filter(hook => typeof hook === 'string' && hook.trim().length > 0);
      
      // Ensure we have exactly 5 hooks
      if (hooks.length < 5) {
        console.log('Not enough valid hooks, generating fallbacks');
        const fallbackHooks = [
          `${title} - Here's what you need to know!`,
          `The truth about ${title} might surprise you`,
          `I tried ${title} for 30 days - here's what happened`,
          `Stop scrolling! This ${title} insight will change everything`,
          `Warning: This ${title} revelation is not for everyone`
        ];
        
        // Add fallbacks to reach 5 hooks
        while (hooks.length < 5) {
          hooks.push(fallbackHooks[hooks.length] || `Discover the secrets of ${title}`);
        }
      }
      
      // Take only the first 5 hooks
      hooks = hooks.slice(0, 5);
      
    } catch (parseError) {
      console.error('JSON parsing failed:', parseError);
      console.log('Failed content:', data.choices[0].message.content);
      
      // Fallback to generating hooks manually
      hooks = [
        `${title} - Here's what you need to know!`,
        `The truth about ${title} might surprise you`,
        `I tried ${title} for 30 days - here's what happened`,
        `Stop scrolling! This ${title} insight will change everything`,
        `Warning: This ${title} revelation is not for everyone`
      ];
    }

    // Log AI usage
    await supabase.from('ai_usage_logs').insert({
      user_id: user.id,
      feature_type: 'hook_generation',
      credits_used: 3,
      prompt_used: title,
      response_received: Array.isArray(hooks) ? hooks.join('; ') : 'Generated hooks'
    });

    return new Response(JSON.stringify({ hooks }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in generate-hooks function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
