
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { getOptimizedFeatureConfig, estimateInputTokens, executeWithFallback } from '../_shared/openai-config.ts';
import {
  generateCacheKey,
  getCachedResponse,
  setCachedResponse,
  isCachingEnabled,
  checkPendingRequest,
  registerPendingRequest
} from '../_shared/cache-utils.ts';
import {
  logDetailedUsage,
  calculateCost,
  extractTokenCounts,
  analyzePromptComplexity
} from '../_shared/analytics-utils.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get user authentication
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      throw new Error('User not authenticated');
    }

    const { keyword, userTier } = await req.json();

    if (!keyword) {
      return new Response(JSON.stringify({ error: 'Keyword is required' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get optimized configuration with dynamic token allocation
    const featureConfig = getOptimizedFeatureConfig('keyword-strategy', userTier, keyword);
    const { model, temperature, maxTokens, systemPrompt } = featureConfig.config;

    console.log(`[KEYWORD-STRATEGY] Using model: ${model}, tokens: ${maxTokens} for keyword: "${keyword}"`);

    // Analytics setup
    const startTime = Date.now();
    const promptComplexity = analyzePromptComplexity(keyword);
    const estimatedInputTokens = estimateInputTokens(keyword + systemPrompt);
    let cacheHit = false;
    let fallbackUsed = false;
    let actualModelUsed = model;

    // Check cache first if caching is enabled
    const featureType = 'keyword-strategy';
    const cacheKey = generateCacheKey(featureType, keyword, userTier);

    if (isCachingEnabled(featureType)) {
      const cachedResponse = await getCachedResponse(cacheKey);

      if (cachedResponse) {
        console.log(`[KEYWORD-STRATEGY] Cache hit for keyword: ${keyword}`);
        cacheHit = true;

        // Log analytics for cache hit
        const responseTime = Date.now() - startTime;
        await logDetailedUsage({
          user_id: user.id,
          feature_type: 'keyword-strategy',
          model_used: model,
          model_requested: model,
          input_tokens: estimatedInputTokens,
          output_tokens: 0, // No tokens consumed for cache hit
          estimated_cost: 0, // No cost for cache hit
          response_time_ms: responseTime,
          cache_hit: true,
          fallback_used: false,
          user_tier: userTier,
          prompt_complexity: promptComplexity,
          success: true
        });

        return new Response(JSON.stringify(cachedResponse), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      console.log(`[KEYWORD-STRATEGY] Cache miss for keyword: ${keyword}`);
    }

    // Check for duplicate requests
    const pendingRequest = await checkPendingRequest(cacheKey, featureType, user.id);
    if (pendingRequest) {
      console.log(`[KEYWORD-STRATEGY] Duplicate request detected for keyword: ${keyword}`);
      return new Response(JSON.stringify({
        error: 'A similar request is already being processed. Please wait a moment and try again.'
      }), {
        status: 429,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const prompt = `As a YouTube keyword strategy expert, analyze the keyword "${keyword}" and provide comprehensive insights.

Return a JSON object with this exact structure:

{
  "keywordIntelligence": {
    "contentType": "Tutorial|Review|Entertainment|Educational|News|Comparison|How-to",
    "competitionLevel": "Low|Medium|High",
    "opportunityScore": 85,
    "bestApproach": "Educational tutorial focusing on beginners"
  },
  "smartKeywordVariations": {
    "low": ["keyword variant 1", "keyword variant 2"],
    "medium": ["keyword variant 3", "keyword variant 4"],
    "high": ["keyword variant 5", "keyword variant 6"]
  },
  "contentStrategy": {
    "titleAngles": ["angle 1", "angle 2", "angle 3"],
    "optimalLength": "8-12 minutes",
    "contentStructure": ["Hook (0-15s)", "Problem intro", "Solution steps", "Examples", "Call to action"],
    "differentiationAdvice": "Focus on practical examples and step-by-step guidance"
  },
  "longTailOpportunities": ["long tail 1", "long tail 2", "long tail 3", "long tail 4", "long tail 5"],
  "proTips": ["tip 1", "tip 2", "tip 3", "tip 4"],
  "competitiveEdge": {
    "missingContent": "Most creators skip the advanced troubleshooting section",
    "uniqueAngle": "Focus on common mistakes and how to avoid them"
  }
}

Guidelines:
- Opportunity score: 0-100 based on search volume vs competition
- Competition levels: Low (easy to rank), Medium (moderate effort), High (very competitive)
- Content structure should be 4-6 actionable steps
- Long-tail opportunities should be specific, searchable phrases
- Pro tips should be actionable and specific to this keyword
- Keep all responses concise but valuable`;

    // Create request configuration for fallback system
    const requestConfig = {
      model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt }
      ],
      temperature,
      max_tokens: maxTokens,
    };

    // Create and register the API call promise for deduplication
    const apiCallPromise = executeWithFallback(requestConfig, 2);

    // Register the pending request for deduplication
    registerPendingRequest(cacheKey, featureType, user.id, apiCallPromise);

    const { data, modelUsed } = await apiCallPromise;

    actualModelUsed = modelUsed;
    fallbackUsed = modelUsed !== model;

    console.log(`[KEYWORD-STRATEGY] Successfully used model: ${modelUsed} for keyword: "${keyword}"`);
    if (fallbackUsed) {
      console.log(`[KEYWORD-STRATEGY] Fallback occurred: ${model} -> ${modelUsed}`);
    }

    const content = data.choices[0].message.content;
    
    // Parse the JSON response
    let analysis;
    try {
      // Clean the response in case it has markdown code blocks
      const cleanContent = content.replace(/```json\n?|\n?```/g, '').trim();
      analysis = JSON.parse(cleanContent);
    } catch (parseError) {
      console.error('Failed to parse AI response:', content);
      throw new Error('Invalid response format from AI');
    }

    // Cache the response if caching is enabled
    if (isCachingEnabled(featureType)) {
      const cacheKey = generateCacheKey(featureType, keyword, userTier);
      await setCachedResponse(cacheKey, featureType, keyword, analysis, userTier);
    }

    // Log detailed analytics for successful request
    const responseTime = Date.now() - startTime;
    const tokenCounts = extractTokenCounts(data);
    const estimatedCost = calculateCost(actualModelUsed, tokenCounts.inputTokens || estimatedInputTokens, tokenCounts.outputTokens);

    await logDetailedUsage({
      user_id: user.id,
      feature_type: 'keyword-strategy',
      model_used: actualModelUsed,
      model_requested: model,
      input_tokens: tokenCounts.inputTokens || estimatedInputTokens,
      output_tokens: tokenCounts.outputTokens,
      estimated_cost: estimatedCost,
      response_time_ms: responseTime,
      cache_hit: cacheHit,
      fallback_used: fallbackUsed,
      user_tier: userTier,
      prompt_complexity: promptComplexity,
      success: true
    });

    console.log(`[KEYWORD-STRATEGY] Analytics logged: $${estimatedCost.toFixed(4)} cost, ${responseTime}ms response time`);

    return new Response(JSON.stringify(analysis), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in keyword-strategy function:', error);

    // Log error analytics if we have user context
    try {
      if (user?.id) {
        const responseTime = Date.now() - (startTime || Date.now());
        await logDetailedUsage({
          user_id: user.id,
          feature_type: 'keyword-strategy',
          model_used: actualModelUsed || model || 'unknown',
          model_requested: model || 'unknown',
          input_tokens: estimatedInputTokens || 0,
          output_tokens: 0,
          estimated_cost: 0,
          response_time_ms: responseTime,
          cache_hit: cacheHit || false,
          fallback_used: fallbackUsed || false,
          user_tier: userTier || 'unknown',
          prompt_complexity: promptComplexity || 'medium',
          success: false,
          error_type: error.message || 'unknown_error'
        });
      }
    } catch (analyticsError) {
      console.warn('Failed to log error analytics:', analyticsError);
    }

    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
