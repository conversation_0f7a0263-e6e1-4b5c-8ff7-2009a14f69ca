import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';
import { getUsageAnalytics, getUserCostTracking } from '../_shared/analytics-utils.ts';
import { getCacheStats } from '../_shared/cache-utils.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get user authentication
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      throw new Error('User not authenticated');
    }

    const url = new URL(req.url);
    const action = url.searchParams.get('action') || 'overview';
    const timeframe = url.searchParams.get('timeframe') || 'week';
    const userId = url.searchParams.get('userId') || user.id;

    // Calculate date range based on timeframe
    const now = new Date();
    let startDate: Date;
    
    switch (timeframe) {
      case 'day':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    let result;

    switch (action) {
      case 'overview':
        // Get comprehensive analytics overview
        const analytics = await getUsageAnalytics(
          startDate.toISOString(),
          now.toISOString(),
          userId === 'all' ? undefined : userId
        );
        
        const cacheStats = await getCacheStats();
        
        result = {
          timeframe,
          period: {
            start: startDate.toISOString(),
            end: now.toISOString()
          },
          analytics,
          cacheStats,
          summary: {
            totalCost: analytics.totalCost,
            totalRequests: analytics.totalRequests,
            avgCostPerRequest: analytics.totalRequests > 0 ? analytics.totalCost / analytics.totalRequests : 0,
            cacheHitRate: analytics.cacheHitRate,
            fallbackRate: analytics.fallbackRate,
            successRate: analytics.successRate,
            avgResponseTime: analytics.averageResponseTime
          }
        };
        break;

      case 'user-tracking':
        // Get detailed user cost tracking
        const userTracking = await getUserCostTracking(userId, timeframe as any);
        result = {
          userId,
          timeframe,
          ...userTracking
        };
        break;

      case 'cost-breakdown':
        // Get detailed cost breakdown
        const costAnalytics = await getUsageAnalytics(
          startDate.toISOString(),
          now.toISOString(),
          userId === 'all' ? undefined : userId
        );
        
        result = {
          timeframe,
          costByFeature: costAnalytics.costByFeature,
          costByModel: costAnalytics.costByModel,
          topCostlyFeatures: costAnalytics.topCostlyFeatures,
          recommendations: costAnalytics.recommendations,
          totalCost: costAnalytics.totalCost,
          totalRequests: costAnalytics.totalRequests
        };
        break;

      case 'performance':
        // Get performance metrics
        const { data: performanceData, error: perfError } = await supabase
          .from('ai_usage_analytics')
          .select('feature_type, response_time_ms, cache_hit, fallback_used, success, created_at')
          .gte('created_at', startDate.toISOString())
          .lte('created_at', now.toISOString());

        if (perfError) throw perfError;

        const performanceMetrics = {
          avgResponseTimeByFeature: {} as Record<string, number>,
          cacheHitRateByFeature: {} as Record<string, number>,
          fallbackRateByFeature: {} as Record<string, number>,
          successRateByFeature: {} as Record<string, number>
        };

        // Group by feature type
        const featureGroups = performanceData?.reduce((acc, record) => {
          if (!acc[record.feature_type]) {
            acc[record.feature_type] = [];
          }
          acc[record.feature_type].push(record);
          return acc;
        }, {} as Record<string, any[]>) || {};

        // Calculate metrics for each feature
        Object.entries(featureGroups).forEach(([feature, records]) => {
          const totalRecords = records.length;
          
          performanceMetrics.avgResponseTimeByFeature[feature] = 
            records.reduce((sum, r) => sum + r.response_time_ms, 0) / totalRecords;
          
          performanceMetrics.cacheHitRateByFeature[feature] = 
            records.filter(r => r.cache_hit).length / totalRecords;
          
          performanceMetrics.fallbackRateByFeature[feature] = 
            records.filter(r => r.fallback_used).length / totalRecords;
          
          performanceMetrics.successRateByFeature[feature] = 
            records.filter(r => r.success).length / totalRecords;
        });

        result = {
          timeframe,
          totalRecords: performanceData?.length || 0,
          ...performanceMetrics
        };
        break;

      case 'savings-report':
        // Calculate savings from optimizations
        const savingsData = await getUsageAnalytics(
          startDate.toISOString(),
          now.toISOString(),
          userId === 'all' ? undefined : userId
        );
        
        const cacheHits = savingsData.totalRequests * savingsData.cacheHitRate;
        const estimatedSavingsFromCache = cacheHits * 0.002; // Estimated $0.002 per cached request
        
        const fallbackSavings = savingsData.totalRequests * savingsData.fallbackRate * 0.01; // Estimated savings from fallbacks
        
        result = {
          timeframe,
          totalRequests: savingsData.totalRequests,
          totalCost: savingsData.totalCost,
          cacheHits: Math.round(cacheHits),
          estimatedSavingsFromCache: estimatedSavingsFromCache,
          estimatedSavingsFromFallbacks: fallbackSavings,
          totalEstimatedSavings: estimatedSavingsFromCache + fallbackSavings,
          optimizationImpact: {
            cacheHitRate: `${(savingsData.cacheHitRate * 100).toFixed(1)}%`,
            fallbackRate: `${(savingsData.fallbackRate * 100).toFixed(1)}%`,
            successRate: `${(savingsData.successRate * 100).toFixed(1)}%`
          },
          recommendations: savingsData.recommendations
        };
        break;

      default:
        throw new Error(`Unknown action: ${action}`);
    }

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in ai-analytics-dashboard function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
