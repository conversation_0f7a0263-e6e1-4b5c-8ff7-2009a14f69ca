
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);
    
    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const { title, longFormContent, pillar } = await req.json();

    const prompt = `Convert this long-form content into a compelling 60-second YouTube Shorts script:

Title: "${title}"
Content Pillar: ${pillar || 'General'}
Long-form Content: "${longFormContent}"

Create a YouTube Shorts script that:
- Is exactly 60 seconds or less when spoken
- Maintains the core message and value
- Is optimized for vertical video format
- Has a strong hook in the first 3 seconds
- Includes clear visual cues and transitions
- Ends with a strong call-to-action
- Is engaging and punchy throughout

Format the script with timing markers and visual directions.`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { 
            role: 'system', 
            content: 'You are an expert YouTube Shorts creator who specializes in converting long-form content into engaging, punchy vertical video scripts that maximize engagement and retention.'
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 600,
      }),
    });

    const data = await response.json();
    const shortsScript = data.choices[0].message.content;

    // Log AI usage
    await supabase.from('ai_usage_logs').insert({
      user_id: user.id,
      feature_type: 'shorts_conversion',
      credits_used: 4,
      prompt_used: title,
      response_received: shortsScript.substring(0, 200) + '...'
    });

    return new Response(JSON.stringify({ script: shortsScript }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in convert-to-shorts function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
