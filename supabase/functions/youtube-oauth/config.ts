// ⚠️ TEMPORARY HARDCODED SECRET DUE TO SUPABASE ENVIRONMENT VARIABLE BUG ⚠️
// Supabase environment variables are corrupted and won't save the correct value
// This is a temporary workaround until the Supabase bug is resolved
// TODO: Remove hardcoded secret and use environment variable when bug is fixed
export const CLIENT_SECRET = '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';

// Keep environment variable code commented out for when bug is fixed
// const CLIENT_SECRET = Deno.env.get('YT_CLIENT_SECRET');
// if (!CLIENT_SECRET) {
//   console.error('❌ YT_CLIENT_SECRET not configured in Supabase secrets');
//   throw new Error('YouTube Client Secret not configured. Please set YT_CLIENT_SECRET in Supabase secrets.');
// }

// Ensure we're using the same client ID as the frontend
export const GOOGLE_CLIENT_ID = '343487815633-rtm8h3ptunici9q3f384e2q66clfohu3.apps.googleusercontent.com';

export const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Validate that we have a proper Google client secret format
if (!CLIENT_SECRET.startsWith('GOCSPX-')) {
  console.error('❌ Client secret appears to be invalid format. Expected format: GOCSPX-...');
  console.error('Current value starts with:', CLIENT_SECRET.substring(0, 10) + '...');
  throw new Error('Invalid YouTube Client Secret format. Expected format: GOCSPX-...');
}
