
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';

import { CORS_HEADERS } from './config.ts';
import { validateAuthCode, validateRedirectUri } from './validation.ts';
import { exchangeCodeForTokens } from './tokenExchange.ts';
import { fetchYouTubeChannelInfo } from './youtubeApi.ts';
import { storeYouTubeConnectionData } from './database.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: CORS_HEADERS });
  }

  try {
    // Get the user from the Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      throw new Error('Invalid authorization token');
    }

    const { code, redirectUri } = await req.json();

    // Input validation
    validateAuthCode(code);
    validateRedirectUri(redirectUri);

    console.log('Processing OAuth for user:', user.id);

    // Exchange authorization code for tokens
    const tokenData = await exchangeCodeForTokens(code, redirectUri);

    // Fetch YouTube channel information
    const channel = await fetchYouTubeChannelInfo(tokenData.access_token);

    // Store tokens and channel info in the database
    const channelData = await storeYouTubeConnectionData(supabase, user.id, tokenData, channel);

    // Return channel info to frontend
    return new Response(
      JSON.stringify({
        success: true,
        channelData,
      }),
      {
        headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('=== YOUTUBE OAUTH ERROR ===');
    console.error('Error type:', error.constructor.name);
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);
    console.error('Note: Using hardcoded secret due to Supabase environment variable bug');
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        status: 400,
        headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' },
      }
    );
  }
});
