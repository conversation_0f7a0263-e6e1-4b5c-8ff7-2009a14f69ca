
import { CLIENT_SECRET, GOOGLE_CLIENT_ID } from './config.ts';

export async function exchangeCodeForTokens(code: string, redirectUri: string) {
  console.log('=== OAuth Token Exchange Debug Info ===');
  console.log('Using client ID:', GOOGLE_CLIENT_ID);
  console.log('Using redirect URI:', redirectUri);
  console.log('Authorization code received (first 20 chars):', code.substring(0, 20) + '...');
  console.log('Authorization code length:', code.length);
  
  console.log('⚠️ USING HARDCODED CLIENT SECRET DUE TO SUPABASE BUG ⚠️');
  console.log('🔧 Temporary workaround mode active');
  console.log('Client secret configured:', !!CLIENT_SECRET);
  console.log('Client secret format valid:', CLIENT_SECRET.startsWith('GOCSPX-'));

  // Exchange authorization code for tokens
  const tokenRequestBody = new URLSearchParams({
    client_id: GOOGLE_CLIENT_ID,
    client_secret: CLIENT_SECRET,
    code: code,
    grant_type: 'authorization_code',
    redirect_uri: redirectUri,
  });

  console.log('=== Token Exchange Request Details ===');
  console.log('Token request parameters:', {
    client_id: GOOGLE_CLIENT_ID,
    grant_type: 'authorization_code',
    redirect_uri: redirectUri,
    code_provided: !!code,
    code_length: code.length,
    has_secret: !!CLIENT_SECRET,
    secret_format_valid: CLIENT_SECRET.startsWith('GOCSPX-'),
    using_hardcoded_secret: true
  });

  const tokenUrl = 'https://oauth2.googleapis.com/token';
  console.log('Making token exchange request to:', tokenUrl);

  const tokenResponse = await fetch(tokenUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json',
      'User-Agent': 'MyContentHub/1.0'
    },
    body: tokenRequestBody,
  });

  console.log('=== Token Exchange Response ===');
  console.log('Token exchange response status:', tokenResponse.status);

  if (!tokenResponse.ok) {
    const errorText = await tokenResponse.text();
    console.error('=== TOKEN EXCHANGE FAILED ===');
    console.error('Status:', tokenResponse.status);
    console.error('Status Text:', tokenResponse.statusText);
    console.error('Error Response Body:', errorText);
    
    // Try to parse the error for more details
    try {
      const errorJson = JSON.parse(errorText);
      console.error('Parsed error object:', errorJson);
      
      if (errorJson.error === 'invalid_client') {
        console.error('=== INVALID_CLIENT DIAGNOSIS ===');
        console.error('This error means Google rejected our client credentials');
        console.error('Possible issues:');
        console.error('1. Client ID mismatch between Google Cloud Console and our app');
        console.error('2. Client secret is incorrect or expired');
        console.error('3. Redirect URI not properly configured in Google Cloud Console');
        console.error('4. Google Cloud Console OAuth settings need verification');
        
        throw new Error(`Invalid client credentials. Please verify:
          - OAuth Client ID in Google Cloud Console: ${GOOGLE_CLIENT_ID}
          - Client Secret is hardcoded correctly (temporary workaround)
          - Authorized redirect URIs include: ${redirectUri}
          - OAuth consent screen is properly configured`);
      }
      
      if (errorJson.error === 'invalid_grant') {
        console.error('INVALID_GRANT - authorization code may be expired or already used');
        throw new Error('Authorization code expired or invalid. Please try connecting again quickly after granting permissions.');
      }
      
      throw new Error(`Token exchange failed: ${errorJson.error_description || errorJson.error || errorText}`);
    } catch (parseError) {
      console.error('Failed to parse error response:', parseError);
      throw new Error(`Failed to exchange authorization code (${tokenResponse.status}): ${errorText}`);
    }
  }

  const tokenData = await tokenResponse.json();
  console.log('=== TOKEN EXCHANGE SUCCESS ===');
  console.log('🎉 Token exchange successful with hardcoded secret workaround!');
  console.log('✅ Hardcoded client secret working correctly');
  console.log('Token data keys:', Object.keys(tokenData));
  console.log('Access token length:', tokenData.access_token?.length || 'N/A');
  console.log('Refresh token provided:', !!tokenData.refresh_token);

  return tokenData;
}
