export function validateAuthCode(code: string): void {
  if (!code || typeof code !== 'string' || code.length > 1000) {
    throw new Error('Invalid authorization code format');
  }
}

export function validateRedirectUri(redirectUri: string): void {
  if (
    !redirectUri ||
    typeof redirectUri !== 'string' ||
    (
      !redirectUri.startsWith('https://') &&
      !redirectUri.startsWith('http://localhost')
    )
  ) {
    console.error('Rejected redirectUri:', redirectUri);
    throw new Error('Invalid redirect URI format');
  }
}
