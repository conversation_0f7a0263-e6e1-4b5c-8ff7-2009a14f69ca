
export async function fetchYouTubeChannelInfo(accessToken: string) {
  console.log('=== FETCHING YOUTUBE CHANNEL INFO ===');
  console.log('Access Token (first 20 chars):', accessToken?.substring(0, 20) + '...');
  
  // Use mine=true to get the authenticated user's channel data
  const channelResponse = await fetch(
    'https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics&mine=true',
    {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'User-Agent': 'MyContentHub/1.0'
      },
    }
  );

  console.log('YouTube API response status:', channelResponse.status);

  if (!channelResponse.ok) {
    const errorText = await channelResponse.text();
    console.error('YouTube API error:', errorText);
    throw new Error(`Failed to fetch YouTube channel (${channelResponse.status}): ${errorText}`);
  }

  const channelData = await channelResponse.json();
  console.log('YouTube API response received');
  console.log('Full API Response:', JSON.stringify(channelData, null, 2));
  console.log('Channels found:', channelData.items?.length || 0);
  
  if (!channelData.items || channelData.items.length === 0) {
    console.error('No YouTube channel found in response:', channelData);
    throw new Error('No YouTube channel found for this account');
  }

  const channel = channelData.items[0];
  console.log('YouTube channel fetched successfully:', {
    id: channel.id,
    title: channel.snippet.title,
    subscriberCount: channel.statistics?.subscriberCount,
    viewCount: channel.statistics?.viewCount,
    videoCount: channel.statistics?.videoCount
  });

  return channel;
}
