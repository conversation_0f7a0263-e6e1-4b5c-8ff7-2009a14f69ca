
export async function storeYouTubeConnectionData(
  supabase: any,
  userId: string,
  tokenData: any,
  channel: any
) {
  console.log('=== STORING CONNECTION DATA ===');
  
  // Sanitize channel data before storing
  const sanitizedChannelName = channel.snippet.title?.substring(0, 255) || 'Unknown Channel';
  const subscriberCount = Math.max(0, parseInt(channel.statistics.subscriberCount) || 0);

  const { error: updateError } = await supabase
    .from('users')
    .update({
      youtube_access_token: tokenData.access_token,
      youtube_refresh_token: tokenData.refresh_token,
      youtube_channel_id: channel.id,
      youtube_channel_name: sanitizedChannel<PERSON>ame,
      youtube_thumbnail_url: channel.snippet.thumbnails?.default?.url,
      youtube_subscriber_baseline: subscriberCount,
      last_youtube_sync: new Date().toISOString(),
    })
    .eq('id', userId);

  if (updateError) {
    console.error('Database update error:', updateError);
    throw new Error('Failed to store YouTube connection data');
  }

  console.log('=== YOUTUBE CONNECTION COMPLETED SUCCESSFULLY ===');
  console.log('🎉 Hardcoded secret workaround successful!');
  console.log('🔧 Temporary mode: bypassing Supabase environment variable bug');
  console.log('User ID:', userId);
  console.log('Channel ID:', channel.id);
  console.log('Channel Name:', sanitizedChannelName);
  console.log('Subscriber Count:', subscriberCount);

  // Return the complete data structure that the frontend expects
  return {
    youtube_channel_id: channel.id,
    youtube_channel_name: sanitizedChannelName,
    youtube_access_token: tokenData.access_token,
    youtube_refresh_token: tokenData.refresh_token,
    youtube_thumbnail_url: channel.snippet.thumbnails?.default?.url,
    youtube_subscriber_baseline: subscriberCount,
    last_youtube_sync: new Date().toISOString(),
    // Legacy format for backward compatibility
    channelId: channel.id,
    channelName: sanitizedChannelName,
    thumbnailUrl: channel.snippet.thumbnails?.default?.url,
    subscriberCount: subscriberCount,
  };
}
