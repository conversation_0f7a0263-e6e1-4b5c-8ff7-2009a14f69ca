/**
 * Centralized OpenAI Configuration for Cost Optimization and Consistency
 * 
 * This file contains optimized settings for all OpenAI API calls to ensure:
 * - Cost efficiency through appropriate model selection
 * - Consistent quality across features
 * - Proper token limits to prevent overuse
 */

export interface OpenAIConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
}

export interface FeatureConfig {
  creditsUsed: number;
  config: OpenAIConfig;
}

/**
 * Model selection based on user tier for cost optimization
 */
export function getModelForTier(userTier: string): string {
  switch (userTier) {
    case 'ai_pro':
      return 'gpt-4o'; // Premium model for Pro users
    case 'ai_lite':
      return 'gpt-4o-mini'; // Balanced model for Lite users
    case 'analytics_only':
    default:
      return 'gpt-3.5-turbo'; // Cost-effective model for basic users
  }
}

/**
 * Optimized configurations for each AI feature
 * Temperature settings optimized for consistency and quality:
 * - 0.3: Factual, consistent content (SEO, keywords)
 * - 0.7: Balanced creativity (descriptions, hashtags)
 * - 0.8: High creativity (titles, topics, scripts)
 */
export const AI_FEATURE_CONFIGS: Record<string, FeatureConfig> = {
  'keyword-strategy': {
    creditsUsed: 3,
    config: {
      model: 'gpt-4o-mini', // Will be overridden by tier
      temperature: 0.3, // Low for factual keyword analysis
      maxTokens: 2000,
      systemPrompt: 'You are a YouTube keyword strategy expert. Always return valid JSON only.'
    }
  },
  
  'keyword-research': {
    creditsUsed: 2,
    config: {
      model: 'gpt-4o-mini',
      temperature: 0.3, // Low for factual research
      maxTokens: 800,
      systemPrompt: 'You are an expert YouTube SEO researcher who provides data-driven keyword and hashtag recommendations. Always return valid JSON with the exact structure requested.'
    }
  },
  
  'hashtag-generation': {
    creditsUsed: 1,
    config: {
      model: 'gpt-4o-mini',
      temperature: 0.7, // Balanced for creative but relevant hashtags
      maxTokens: 400,
      systemPrompt: 'You are an expert YouTube SEO specialist who creates optimized hashtags for maximum discoverability. Always return valid JSON with the exact structure requested.'
    }
  },
  
  'title-optimization': {
    creditsUsed: 2,
    config: {
      model: 'gpt-4o-mini',
      temperature: 0.8, // High creativity for engaging titles
      maxTokens: 200,
      systemPrompt: 'You are an expert YouTube content strategist. Generate 3 optimized video titles that are catchy, SEO-friendly, and likely to get more clicks and views. Each title should be under 60 characters and include relevant keywords. Return only the 3 titles, each on a new line, numbered 1-3.'
    }
  },
  
  'topic-generation': {
    creditsUsed: 3,
    config: {
      model: 'gpt-4o-mini',
      temperature: 0.8, // High creativity for diverse topics
      maxTokens: 800,
      systemPrompt: 'You are an expert YouTube content strategist. Generate creative, engaging video topic ideas that will perform well on YouTube. Always return valid JSON array format.'
    }
  },
  
  'title-generation': {
    creditsUsed: 2,
    config: {
      model: 'gpt-4o-mini',
      temperature: 0.8, // High creativity for engaging titles
      maxTokens: 400,
      systemPrompt: 'You are an expert YouTube title writer. Create compelling, click-worthy titles that follow YouTube best practices. Keep titles under 60 characters for optimal display.'
    }
  },
  
  'hook-generation': {
    creditsUsed: 2,
    config: {
      model: 'gpt-4o-mini',
      temperature: 0.8, // High creativity for engaging hooks
      maxTokens: 600,
      systemPrompt: 'You are an expert YouTube script writer specializing in creating engaging video hooks that capture viewer attention in the first 15 seconds.'
    }
  },
  
  'seo-generation': {
    creditsUsed: 2,
    config: {
      model: 'gpt-4o-mini',
      temperature: 0.3, // Low for factual SEO content
      maxTokens: 500,
      systemPrompt: 'You are a YouTube SEO expert. Generate relevant tags and hashtags that will help videos rank better and reach the right audience. Always return valid JSON format.'
    }
  },
  
  'description-generation': {
    creditsUsed: 3,
    config: {
      model: 'gpt-4o-mini',
      temperature: 0.7, // Balanced for engaging but informative descriptions
      maxTokens: 800,
      systemPrompt: 'You are an expert YouTube description writer. Create engaging, SEO-optimized descriptions that encourage engagement and provide value to viewers.'
    }
  },
  
  'script-generation': {
    creditsUsed: 5,
    config: {
      model: 'gpt-4o-mini',
      temperature: 0.8, // High creativity for engaging scripts
      maxTokens: 1500,
      systemPrompt: 'You are an expert YouTube script writer. Create engaging, well-structured video scripts that maintain viewer attention and encourage engagement.'
    }
  }
};

/**
 * Get optimized configuration for a specific feature and user tier
 */
export function getFeatureConfig(featureType: string, userTier: string = 'analytics_only'): FeatureConfig {
  const baseConfig = AI_FEATURE_CONFIGS[featureType];
  
  if (!baseConfig) {
    // Fallback configuration for unknown features
    return {
      creditsUsed: 2,
      config: {
        model: getModelForTier(userTier),
        temperature: 0.7,
        maxTokens: 500,
        systemPrompt: 'You are a helpful AI assistant that generates high-quality YouTube content ideas and optimizations.'
      }
    };
  }
  
  // Override model based on user tier
  return {
    ...baseConfig,
    config: {
      ...baseConfig.config,
      model: getModelForTier(userTier)
    }
  };
}

/**
 * Cost estimation per 1K tokens (approximate, as of 2024)
 */
export const TOKEN_COSTS = {
  'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
  'gpt-4o-mini': { input: 0.00015, output: 0.0006 },
  'gpt-4o': { input: 0.005, output: 0.015 }
};

/**
 * Dynamic token allocation based on content type and user tier
 */
export function getDynamicTokenLimit(featureType: string, userTier: string, inputLength: number = 0): number {
  const baseConfig = AI_FEATURE_CONFIGS[featureType];
  if (!baseConfig) return 500; // Default fallback

  let baseTokens = baseConfig.config.maxTokens;

  // Adjust based on user tier
  const tierMultiplier = getTierTokenMultiplier(userTier);

  // Adjust based on input length (longer inputs may need longer outputs)
  const inputMultiplier = Math.min(1 + (inputLength / 1000), 2); // Max 2x for very long inputs

  // Calculate dynamic limit
  const dynamicLimit = Math.floor(baseTokens * tierMultiplier * inputMultiplier);

  // Apply reasonable bounds
  const minTokens = Math.floor(baseTokens * 0.5); // At least 50% of base
  const maxTokens = Math.floor(baseTokens * 2.5); // At most 250% of base

  return Math.max(minTokens, Math.min(maxTokens, dynamicLimit));
}

/**
 * Get token multiplier based on user tier
 */
function getTierTokenMultiplier(userTier: string): number {
  switch (userTier) {
    case 'ai_pro':
      return 1.5; // Pro users get 50% more tokens
    case 'ai_lite':
      return 1.2; // Lite users get 20% more tokens
    case 'analytics_only':
    default:
      return 1.0; // Basic users get standard allocation
  }
}

/**
 * Estimate input token count (rough approximation)
 */
export function estimateInputTokens(text: string): number {
  // Rough approximation: 1 token ≈ 4 characters for English text
  return Math.ceil(text.length / 4);
}

/**
 * Get optimized configuration with dynamic token allocation
 */
export function getOptimizedFeatureConfig(
  featureType: string,
  userTier: string = 'analytics_only',
  inputText: string = ''
): FeatureConfig {
  const baseConfig = getFeatureConfig(featureType, userTier);
  const inputTokens = estimateInputTokens(inputText);
  const dynamicTokenLimit = getDynamicTokenLimit(featureType, userTier, inputText.length);

  return {
    ...baseConfig,
    config: {
      ...baseConfig.config,
      maxTokens: dynamicTokenLimit
    }
  };
}

/**
 * Estimate cost for a request
 */
export function estimateRequestCost(model: string, inputTokens: number, outputTokens: number): number {
  const costs = TOKEN_COSTS[model as keyof typeof TOKEN_COSTS];
  if (!costs) return 0;

  return (inputTokens / 1000 * costs.input) + (outputTokens / 1000 * costs.output);
}

/**
 * Model fallback hierarchy for handling rate limits and errors
 */
export const MODEL_FALLBACK_HIERARCHY = {
  'gpt-4o': ['gpt-4o-mini', 'gpt-3.5-turbo'],
  'gpt-4o-mini': ['gpt-3.5-turbo'],
  'gpt-3.5-turbo': [] // No fallback for the cheapest model
};

/**
 * Error types that should trigger model fallback
 */
export const FALLBACK_ERROR_TYPES = [
  'rate_limit_exceeded',
  'insufficient_quota',
  'model_overloaded',
  'context_length_exceeded'
];

/**
 * Check if an error should trigger model fallback
 */
export function shouldFallback(error: any): boolean {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.code?.toLowerCase() || '';

  return FALLBACK_ERROR_TYPES.some(errorType =>
    errorMessage.includes(errorType.replace('_', ' ')) ||
    errorCode.includes(errorType)
  ) ||
  error.status === 429 || // Rate limit HTTP status
  error.status === 503;   // Service unavailable
}

/**
 * Get the next fallback model
 */
export function getFallbackModel(currentModel: string): string | null {
  const fallbacks = MODEL_FALLBACK_HIERARCHY[currentModel as keyof typeof MODEL_FALLBACK_HIERARCHY];
  return fallbacks && fallbacks.length > 0 ? fallbacks[0] : null;
}

/**
 * Execute OpenAI request with automatic fallback
 */
export async function executeWithFallback(
  requestConfig: any,
  maxRetries: number = 2
): Promise<any> {
  let currentModel = requestConfig.model;
  let attempt = 0;
  let lastError: any;

  while (attempt <= maxRetries) {
    try {
      console.log(`[FALLBACK] Attempt ${attempt + 1} with model: ${currentModel}`);

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...requestConfig,
          model: currentModel
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        const error = {
          status: response.status,
          message: errorData.error?.message || 'Unknown error',
          code: errorData.error?.code || 'unknown',
          type: errorData.error?.type || 'unknown'
        };

        if (shouldFallback(error)) {
          const fallbackModel = getFallbackModel(currentModel);

          if (fallbackModel && attempt < maxRetries) {
            console.log(`[FALLBACK] ${error.message}, falling back from ${currentModel} to ${fallbackModel}`);
            currentModel = fallbackModel;
            lastError = error;
            attempt++;
            continue;
          }
        }

        throw error;
      }

      const data = await response.json();

      // Log successful fallback if we used a different model
      if (currentModel !== requestConfig.model) {
        console.log(`[FALLBACK] Success with fallback model: ${currentModel} (original: ${requestConfig.model})`);
      }

      return { data, modelUsed: currentModel };

    } catch (error) {
      if (shouldFallback(error)) {
        const fallbackModel = getFallbackModel(currentModel);

        if (fallbackModel && attempt < maxRetries) {
          console.log(`[FALLBACK] Error: ${error.message}, falling back from ${currentModel} to ${fallbackModel}`);
          currentModel = fallbackModel;
          lastError = error;
          attempt++;
          continue;
        }
      }

      // If we can't fallback or have exhausted retries, throw the error
      throw lastError || error;
    }
  }

  throw lastError || new Error('Max retries exceeded');
}

/**
 * Get cost-optimized model selection based on complexity and user tier
 */
export function getCostOptimizedModel(featureType: string, userTier: string, inputComplexity: 'low' | 'medium' | 'high' = 'medium'): string {
  // For simple tasks, use cheaper models even for Pro users
  if (inputComplexity === 'low' && ['hashtag-generation', 'seo-generation'].includes(featureType)) {
    return 'gpt-3.5-turbo';
  }

  // For complex tasks, Pro users get premium models
  if (inputComplexity === 'high' && userTier === 'ai_pro') {
    return 'gpt-4o';
  }

  // Default model selection based on tier
  return getModelForTier(userTier);
}
