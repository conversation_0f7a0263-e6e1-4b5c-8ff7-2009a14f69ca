/**
 * Enhanced Analytics Utilities for AI Usage Tracking and Cost Optimization
 * 
 * This module provides:
 * - Detailed cost tracking per feature and user
 * - Usage pattern analysis
 * - Performance metrics
 * - Cost optimization recommendations
 */

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';
import { estimateRequestCost, TOKEN_COSTS } from './openai-config.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export interface DetailedUsageLog {
  id?: string;
  user_id: string;
  feature_type: string;
  model_used: string;
  model_requested: string;
  input_tokens: number;
  output_tokens: number;
  estimated_cost: number;
  response_time_ms: number;
  cache_hit: boolean;
  fallback_used: boolean;
  user_tier: string;
  prompt_complexity: 'low' | 'medium' | 'high';
  success: boolean;
  error_type?: string;
  created_at?: string;
}

export interface UsageAnalytics {
  totalRequests: number;
  totalCost: number;
  averageResponseTime: number;
  cacheHitRate: number;
  fallbackRate: number;
  successRate: number;
  costByFeature: Record<string, number>;
  costByModel: Record<string, number>;
  costByUser: Record<string, number>;
  topCostlyFeatures: Array<{ feature: string; cost: number; requests: number }>;
  topUsers: Array<{ userId: string; cost: number; requests: number }>;
  recommendations: string[];
}

/**
 * Log detailed AI usage with cost and performance metrics
 */
export async function logDetailedUsage(usage: DetailedUsageLog): Promise<void> {
  try {
    const { error } = await supabase
      .from('ai_usage_analytics')
      .insert({
        user_id: usage.user_id,
        feature_type: usage.feature_type,
        model_used: usage.model_used,
        model_requested: usage.model_requested,
        input_tokens: usage.input_tokens,
        output_tokens: usage.output_tokens,
        estimated_cost: usage.estimated_cost,
        response_time_ms: usage.response_time_ms,
        cache_hit: usage.cache_hit,
        fallback_used: usage.fallback_used,
        user_tier: usage.user_tier,
        prompt_complexity: usage.prompt_complexity,
        success: usage.success,
        error_type: usage.error_type,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.warn('[ANALYTICS] Error logging detailed usage:', error);
    } else {
      console.log(`[ANALYTICS] Logged usage: ${usage.feature_type} - ${usage.model_used} - $${usage.estimated_cost.toFixed(4)}`);
    }
  } catch (error) {
    console.warn('[ANALYTICS] Error in logDetailedUsage:', error);
  }
}

/**
 * Calculate estimated cost from token usage
 */
export function calculateCost(model: string, inputTokens: number, outputTokens: number): number {
  return estimateRequestCost(model, inputTokens, outputTokens);
}

/**
 * Estimate token counts from OpenAI response
 */
export function extractTokenCounts(response: any): { inputTokens: number; outputTokens: number } {
  const usage = response.usage;
  if (usage) {
    return {
      inputTokens: usage.prompt_tokens || 0,
      outputTokens: usage.completion_tokens || 0
    };
  }
  
  // Fallback estimation if usage data not available
  const content = response.choices?.[0]?.message?.content || '';
  return {
    inputTokens: 0, // Will be estimated separately
    outputTokens: Math.ceil(content.length / 4) // Rough approximation
  };
}

/**
 * Determine prompt complexity based on length and content
 */
export function analyzePromptComplexity(prompt: string): 'low' | 'medium' | 'high' {
  const length = prompt.length;
  const hasComplexInstructions = /\b(analyze|compare|generate|create|optimize|strategy)\b/i.test(prompt);
  const hasMultipleRequirements = (prompt.match(/\b(and|also|additionally|furthermore)\b/gi) || []).length > 2;
  
  if (length > 1000 || (hasComplexInstructions && hasMultipleRequirements)) {
    return 'high';
  } else if (length > 300 || hasComplexInstructions) {
    return 'medium';
  } else {
    return 'low';
  }
}

/**
 * Get comprehensive usage analytics for a time period
 */
export async function getUsageAnalytics(
  startDate: string,
  endDate: string,
  userId?: string
): Promise<UsageAnalytics> {
  try {
    let query = supabase
      .from('ai_usage_analytics')
      .select('*')
      .gte('created_at', startDate)
      .lte('created_at', endDate);

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data, error } = await query;

    if (error || !data) {
      throw new Error('Failed to fetch analytics data');
    }

    const analytics: UsageAnalytics = {
      totalRequests: data.length,
      totalCost: data.reduce((sum, log) => sum + (log.estimated_cost || 0), 0),
      averageResponseTime: data.reduce((sum, log) => sum + (log.response_time_ms || 0), 0) / data.length,
      cacheHitRate: data.filter(log => log.cache_hit).length / data.length,
      fallbackRate: data.filter(log => log.fallback_used).length / data.length,
      successRate: data.filter(log => log.success).length / data.length,
      costByFeature: {},
      costByModel: {},
      costByUser: {},
      topCostlyFeatures: [],
      topUsers: [],
      recommendations: []
    };

    // Calculate cost breakdowns
    data.forEach(log => {
      const cost = log.estimated_cost || 0;
      
      // By feature
      analytics.costByFeature[log.feature_type] = (analytics.costByFeature[log.feature_type] || 0) + cost;
      
      // By model
      analytics.costByModel[log.model_used] = (analytics.costByModel[log.model_used] || 0) + cost;
      
      // By user
      analytics.costByUser[log.user_id] = (analytics.costByUser[log.user_id] || 0) + cost;
    });

    // Generate top costly features
    analytics.topCostlyFeatures = Object.entries(analytics.costByFeature)
      .map(([feature, cost]) => ({
        feature,
        cost,
        requests: data.filter(log => log.feature_type === feature).length
      }))
      .sort((a, b) => b.cost - a.cost)
      .slice(0, 10);

    // Generate top users
    analytics.topUsers = Object.entries(analytics.costByUser)
      .map(([userId, cost]) => ({
        userId,
        cost,
        requests: data.filter(log => log.user_id === userId).length
      }))
      .sort((a, b) => b.cost - a.cost)
      .slice(0, 10);

    // Generate recommendations
    analytics.recommendations = generateRecommendations(analytics, data);

    return analytics;
  } catch (error) {
    console.error('[ANALYTICS] Error getting usage analytics:', error);
    throw error;
  }
}

/**
 * Generate cost optimization recommendations
 */
function generateRecommendations(analytics: UsageAnalytics, rawData: any[]): string[] {
  const recommendations: string[] = [];

  // Cache hit rate recommendations
  if (analytics.cacheHitRate < 0.3) {
    recommendations.push('Consider increasing cache TTL for frequently requested content to improve cache hit rate');
  }

  // High-cost feature recommendations
  const highCostFeatures = analytics.topCostlyFeatures.filter(f => f.cost > analytics.totalCost * 0.2);
  if (highCostFeatures.length > 0) {
    recommendations.push(`High-cost features detected: ${highCostFeatures.map(f => f.feature).join(', ')}. Consider optimizing token usage or model selection.`);
  }

  // Model usage recommendations
  const gpt4Usage = analytics.costByModel['gpt-4o'] || 0;
  if (gpt4Usage > analytics.totalCost * 0.5) {
    recommendations.push('High GPT-4o usage detected. Consider using GPT-4o-mini for simpler tasks to reduce costs.');
  }

  // Fallback rate recommendations
  if (analytics.fallbackRate > 0.1) {
    recommendations.push('High fallback rate detected. Consider reviewing rate limits and request patterns.');
  }

  // Response time recommendations
  if (analytics.averageResponseTime > 5000) {
    recommendations.push('High average response time. Consider optimizing prompts or using faster models for time-sensitive features.');
  }

  return recommendations;
}

/**
 * Get real-time cost tracking for a user
 */
export async function getUserCostTracking(userId: string, timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<any> {
  try {
    const now = new Date();
    let startDate: Date;

    switch (timeframe) {
      case 'hour':
        startDate = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case 'day':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
    }

    const { data, error } = await supabase
      .from('ai_usage_analytics')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString());

    if (error || !data) {
      return { totalCost: 0, requestCount: 0, features: {} };
    }

    const totalCost = data.reduce((sum, log) => sum + (log.estimated_cost || 0), 0);
    const requestCount = data.length;
    const features = data.reduce((acc, log) => {
      acc[log.feature_type] = (acc[log.feature_type] || 0) + (log.estimated_cost || 0);
      return acc;
    }, {} as Record<string, number>);

    return { totalCost, requestCount, features };
  } catch (error) {
    console.error('[ANALYTICS] Error getting user cost tracking:', error);
    return { totalCost: 0, requestCount: 0, features: {} };
  }
}
