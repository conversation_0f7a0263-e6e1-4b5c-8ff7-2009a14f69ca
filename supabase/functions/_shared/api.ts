import { corsHeaders } from './cors';

// Standard API response format
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    requestId: string;
    timestamp: string;
  };
}

// Helper to create standardized responses
export const createResponse = <T>(
  data: T | null = null,
  status = 200,
  errorInfo: { code?: string; message?: string; details?: any } | null = null
): Response => {
  const requestId = crypto.randomUUID();
  const timestamp = new Date().toISOString();
  
  const responseBody: ApiResponse<T> = {
    success: status >= 200 && status < 300,
    meta: {
      requestId,
      timestamp,
    },
  };
  
  if (data !== null) {
    responseBody.data = data;
  }
  
  if (errorInfo) {
    responseBody.error = {
      code: errorInfo.code || 'unknown_error',
      message: errorInfo.message || 'An unknown error occurred',
      details: errorInfo.details,
    };
  }
  
  return new Response(
    JSON.stringify(responseBody),
    {
      status,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json',
      },
    }
  );
};

// Input validation helper
export const validateInput = (
  input: any,
  schema: Record<string, (value: any) => boolean>,
  requiredFields: string[] = []
): { valid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};
  
  // Check required fields
  for (const field of requiredFields) {
    if (input[field] === undefined || input[field] === null || input[field] === '') {
      errors[field] = `${field} is required`;
    }
  }
  
  // Validate against schema
  for (const [field, validator] of Object.entries(schema)) {
    if (input[field] !== undefined && !validator(input[field])) {
      errors[field] = `Invalid ${field}`;
    }
  }
  
  return {
    valid: Object.keys(errors).length === 0,
    errors,
  };
};