/**
 * Caching utilities for OpenAI responses to reduce API calls and costs
 * 
 * This module provides:
 * - Response caching with configurable TTL
 * - Cache key generation based on prompts and parameters
 * - Automatic cache cleanup
 * - Cost tracking and savings metrics
 */

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export interface CacheEntry {
  id?: string;
  cache_key: string;
  feature_type: string;
  prompt_hash: string;
  response_data: any;
  user_tier: string;
  created_at?: string;
  expires_at: string;
  hit_count?: number;
}

export interface CacheConfig {
  ttlHours: number;
  enableCaching: boolean;
  maxCacheSize: number;
}

/**
 * Default cache configurations for different feature types
 */
export const CACHE_CONFIGS: Record<string, CacheConfig> = {
  'keyword-strategy': { ttlHours: 48, enableCaching: true, maxCacheSize: 1000 },
  'keyword-research': { ttlHours: 24, enableCaching: true, maxCacheSize: 500 },
  'hashtag-generation': { ttlHours: 12, enableCaching: true, maxCacheSize: 300 },
  'title-optimization': { ttlHours: 6, enableCaching: false, maxCacheSize: 0 }, // Disabled for personalized content
  'topic-generation': { ttlHours: 24, enableCaching: true, maxCacheSize: 200 },
  'seo-generation': { ttlHours: 48, enableCaching: true, maxCacheSize: 500 },
  'description-generation': { ttlHours: 6, enableCaching: false, maxCacheSize: 0 }, // Disabled for personalized content
  'script-generation': { ttlHours: 1, enableCaching: false, maxCacheSize: 0 } // Disabled for highly personalized content
};

/**
 * Generate a cache key based on feature type, prompt, and parameters
 */
export function generateCacheKey(featureType: string, prompt: string, userTier: string, additionalParams: Record<string, any> = {}): string {
  const normalizedPrompt = prompt.toLowerCase().trim();
  const paramsString = JSON.stringify(additionalParams, Object.keys(additionalParams).sort());
  const combinedString = `${featureType}:${normalizedPrompt}:${userTier}:${paramsString}`;
  
  // Simple hash function for cache key
  let hash = 0;
  for (let i = 0; i < combinedString.length; i++) {
    const char = combinedString.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return `${featureType}_${Math.abs(hash).toString(36)}`;
}

/**
 * Generate a hash for the prompt content
 */
export function generatePromptHash(prompt: string): string {
  let hash = 0;
  for (let i = 0; i < prompt.length; i++) {
    const char = prompt.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return Math.abs(hash).toString(36);
}

/**
 * Check if caching is enabled for a feature type
 */
export function isCachingEnabled(featureType: string): boolean {
  const config = CACHE_CONFIGS[featureType];
  return config ? config.enableCaching : false;
}

/**
 * Get cached response if available and not expired
 */
export async function getCachedResponse(cacheKey: string): Promise<any | null> {
  try {
    const { data, error } = await supabase
      .from('ai_response_cache')
      .select('*')
      .eq('cache_key', cacheKey)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (error || !data) {
      return null;
    }

    // Increment hit count
    await supabase
      .from('ai_response_cache')
      .update({ hit_count: (data.hit_count || 0) + 1 })
      .eq('id', data.id);

    console.log(`[CACHE] Cache hit for key: ${cacheKey}`);
    return data.response_data;
  } catch (error) {
    console.warn('[CACHE] Error retrieving cached response:', error);
    return null;
  }
}

/**
 * Store response in cache
 */
export async function setCachedResponse(
  cacheKey: string,
  featureType: string,
  prompt: string,
  responseData: any,
  userTier: string
): Promise<void> {
  try {
    const config = CACHE_CONFIGS[featureType];
    if (!config || !config.enableCaching) {
      return;
    }

    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + config.ttlHours);

    const cacheEntry: CacheEntry = {
      cache_key: cacheKey,
      feature_type: featureType,
      prompt_hash: generatePromptHash(prompt),
      response_data: responseData,
      user_tier: userTier,
      expires_at: expiresAt.toISOString(),
      hit_count: 0
    };

    // Check cache size and clean up if necessary
    await cleanupExpiredCache(featureType);
    await enforceMaxCacheSize(featureType, config.maxCacheSize);

    const { error } = await supabase
      .from('ai_response_cache')
      .insert(cacheEntry);

    if (error) {
      console.warn('[CACHE] Error storing cached response:', error);
    } else {
      console.log(`[CACHE] Cached response for key: ${cacheKey}, expires: ${expiresAt.toISOString()}`);
    }
  } catch (error) {
    console.warn('[CACHE] Error in setCachedResponse:', error);
  }
}

/**
 * Clean up expired cache entries
 */
export async function cleanupExpiredCache(featureType?: string): Promise<void> {
  try {
    let query = supabase
      .from('ai_response_cache')
      .delete()
      .lt('expires_at', new Date().toISOString());

    if (featureType) {
      query = query.eq('feature_type', featureType);
    }

    const { error } = await query;
    
    if (error) {
      console.warn('[CACHE] Error cleaning up expired cache:', error);
    }
  } catch (error) {
    console.warn('[CACHE] Error in cleanupExpiredCache:', error);
  }
}

/**
 * Enforce maximum cache size by removing oldest entries
 */
export async function enforceMaxCacheSize(featureType: string, maxSize: number): Promise<void> {
  try {
    if (maxSize <= 0) return;

    const { data: countData, error: countError } = await supabase
      .from('ai_response_cache')
      .select('id', { count: 'exact' })
      .eq('feature_type', featureType);

    if (countError || !countData) return;

    const currentCount = countData.length;
    if (currentCount <= maxSize) return;

    // Remove oldest entries to get under the limit
    const entriesToRemove = currentCount - maxSize + 10; // Remove extra to avoid frequent cleanup

    const { data: oldestEntries, error: selectError } = await supabase
      .from('ai_response_cache')
      .select('id')
      .eq('feature_type', featureType)
      .order('created_at', { ascending: true })
      .limit(entriesToRemove);

    if (selectError || !oldestEntries) return;

    const idsToRemove = oldestEntries.map(entry => entry.id);
    
    const { error: deleteError } = await supabase
      .from('ai_response_cache')
      .delete()
      .in('id', idsToRemove);

    if (deleteError) {
      console.warn('[CACHE] Error enforcing max cache size:', deleteError);
    } else {
      console.log(`[CACHE] Removed ${entriesToRemove} old entries for ${featureType}`);
    }
  } catch (error) {
    console.warn('[CACHE] Error in enforceMaxCacheSize:', error);
  }
}

/**
 * Request deduplication to prevent duplicate API calls within short time windows
 */
export interface PendingRequest {
  id: string;
  cache_key: string;
  feature_type: string;
  user_id: string;
  created_at: string;
  promise?: Promise<any>;
}

// In-memory store for pending requests (will be cleared on function restart)
const pendingRequests = new Map<string, PendingRequest>();

/**
 * Check if a similar request is already in progress
 */
export async function checkPendingRequest(
  cacheKey: string,
  featureType: string,
  userId: string
): Promise<PendingRequest | null> {
  // Check in-memory pending requests first
  const existingRequest = pendingRequests.get(cacheKey);
  if (existingRequest) {
    const age = Date.now() - new Date(existingRequest.created_at).getTime();
    if (age < 5 * 60 * 1000) { // 5 minutes
      console.log(`[DEDUP] Found pending request for key: ${cacheKey}`);
      return existingRequest;
    } else {
      // Remove expired pending request
      pendingRequests.delete(cacheKey);
    }
  }

  // Check database for recent requests from same user
  try {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

    const { data, error } = await supabase
      .from('ai_usage_logs')
      .select('id, created_at')
      .eq('user_id', userId)
      .eq('feature_type', featureType)
      .gte('created_at', fiveMinutesAgo.toISOString())
      .order('created_at', { ascending: false })
      .limit(1);

    if (error || !data || data.length === 0) {
      return null;
    }

    // If there's a very recent request (within 30 seconds), consider it duplicate
    const lastRequest = data[0];
    const timeSinceLastRequest = Date.now() - new Date(lastRequest.created_at).getTime();

    if (timeSinceLastRequest < 30 * 1000) { // 30 seconds
      console.log(`[DEDUP] Recent duplicate request detected for user ${userId}, feature ${featureType}`);
      return {
        id: lastRequest.id,
        cache_key: cacheKey,
        feature_type: featureType,
        user_id: userId,
        created_at: lastRequest.created_at
      };
    }

    return null;
  } catch (error) {
    console.warn('[DEDUP] Error checking pending requests:', error);
    return null;
  }
}

/**
 * Register a pending request
 */
export function registerPendingRequest(
  cacheKey: string,
  featureType: string,
  userId: string,
  promise: Promise<any>
): void {
  const request: PendingRequest = {
    id: crypto.randomUUID(),
    cache_key: cacheKey,
    feature_type: featureType,
    user_id: userId,
    created_at: new Date().toISOString(),
    promise
  };

  pendingRequests.set(cacheKey, request);

  // Clean up when promise resolves
  promise.finally(() => {
    pendingRequests.delete(cacheKey);
  });
}

/**
 * Clean up expired pending requests
 */
export function cleanupPendingRequests(): void {
  const now = Date.now();
  const expiredKeys: string[] = [];

  for (const [key, request] of pendingRequests.entries()) {
    const age = now - new Date(request.created_at).getTime();
    if (age > 10 * 60 * 1000) { // 10 minutes
      expiredKeys.push(key);
    }
  }

  expiredKeys.forEach(key => pendingRequests.delete(key));

  if (expiredKeys.length > 0) {
    console.log(`[DEDUP] Cleaned up ${expiredKeys.length} expired pending requests`);
  }
}

/**
 * Get cache statistics for monitoring
 */
export async function getCacheStats(featureType?: string): Promise<any> {
  try {
    let query = supabase
      .from('ai_response_cache')
      .select('feature_type, hit_count, created_at, expires_at');

    if (featureType) {
      query = query.eq('feature_type', featureType);
    }

    const { data, error } = await query;
    
    if (error || !data) {
      return { error: 'Failed to fetch cache stats' };
    }

    const stats = {
      totalEntries: data.length,
      totalHits: data.reduce((sum, entry) => sum + (entry.hit_count || 0), 0),
      byFeatureType: {} as Record<string, any>
    };

    // Group by feature type
    data.forEach(entry => {
      if (!stats.byFeatureType[entry.feature_type]) {
        stats.byFeatureType[entry.feature_type] = {
          entries: 0,
          hits: 0,
          avgHitsPerEntry: 0
        };
      }
      
      stats.byFeatureType[entry.feature_type].entries++;
      stats.byFeatureType[entry.feature_type].hits += entry.hit_count || 0;
    });

    // Calculate averages
    Object.keys(stats.byFeatureType).forEach(type => {
      const typeStats = stats.byFeatureType[type];
      typeStats.avgHitsPerEntry = typeStats.entries > 0 ? typeStats.hits / typeStats.entries : 0;
    });

    return stats;
  } catch (error) {
    return { error: 'Error calculating cache stats' };
  }
}
