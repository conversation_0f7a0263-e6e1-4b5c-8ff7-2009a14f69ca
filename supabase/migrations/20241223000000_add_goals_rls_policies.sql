-- Enable RLS on goals table if not already enabled
ALTER TABLE goals ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own goals" ON goals;
DROP POLICY IF EXISTS "Users can insert their own goals" ON goals;
DROP POLICY IF EXISTS "Users can update their own goals" ON goals;
DROP POLICY IF EXISTS "Users can delete their own goals" ON goals;

-- Create RLS policies for goals table
CREATE POLICY "Users can view their own goals"
    ON goals FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own goals"
    ON goals FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own goals"
    ON goals FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own goals"
    ON goals FOR DELETE
    USING (auth.uid() = user_id);

-- Enable RLS on goal_progress table if not already enabled
ALTER TABLE goal_progress ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own goal progress" ON goal_progress;
DROP POLICY IF EXISTS "Users can insert their own goal progress" ON goal_progress;
DROP POLICY IF EXISTS "Users can update their own goal progress" ON goal_progress;
DROP POLICY IF EXISTS "Users can delete their own goal progress" ON goal_progress;

-- Create RLS policies for goal_progress table
CREATE POLICY "Users can view their own goal progress"
    ON goal_progress FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own goal progress"
    ON goal_progress FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own goal progress"
    ON goal_progress FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own goal progress"
    ON goal_progress FOR DELETE
    USING (auth.uid() = user_id);
