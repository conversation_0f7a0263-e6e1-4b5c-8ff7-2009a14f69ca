-- Create enhanced AI usage analytics table for detailed cost tracking
CREATE TABLE IF NOT EXISTS ai_usage_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  feature_type TEXT NOT NULL,
  model_used TEXT NOT NULL,
  model_requested TEXT NOT NULL,
  input_tokens INTEGER NOT NULL DEFAULT 0,
  output_tokens INTEGER NOT NULL DEFAULT 0,
  estimated_cost DECIMAL(10,6) NOT NULL DEFAULT 0,
  response_time_ms INTEGER NOT NULL DEFAULT 0,
  cache_hit BOOLEAN NOT NULL DEFAULT false,
  fallback_used BOOLEAN NOT NULL DEFAULT false,
  user_tier TEXT NOT NULL,
  prompt_complexity TEXT NOT NULL CHECK (prompt_complexity IN ('low', 'medium', 'high')),
  success BOOLEAN NOT NULL DEFAULT true,
  error_type TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>reate indexes for performance
CREATE INDEX IF NOT EXISTS idx_ai_usage_analytics_user_id ON ai_usage_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_usage_analytics_feature_type ON ai_usage_analytics(feature_type);
CREATE INDEX IF NOT EXISTS idx_ai_usage_analytics_model_used ON ai_usage_analytics(model_used);
CREATE INDEX IF NOT EXISTS idx_ai_usage_analytics_created_at ON ai_usage_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_usage_analytics_user_tier ON ai_usage_analytics(user_tier);
CREATE INDEX IF NOT EXISTS idx_ai_usage_analytics_success ON ai_usage_analytics(success);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_ai_usage_analytics_user_date ON ai_usage_analytics(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_ai_usage_analytics_feature_date ON ai_usage_analytics(feature_type, created_at);
CREATE INDEX IF NOT EXISTS idx_ai_usage_analytics_cost_analysis ON ai_usage_analytics(user_tier, feature_type, estimated_cost);

-- Add RLS policies
ALTER TABLE ai_usage_analytics ENABLE ROW LEVEL SECURITY;

-- Policy for service role (used by edge functions)
CREATE POLICY "Service role can manage analytics" ON ai_usage_analytics
  FOR ALL USING (auth.role() = 'service_role');

-- Policy for authenticated users to read their own analytics
CREATE POLICY "Users can read their own analytics" ON ai_usage_analytics
  FOR SELECT USING (auth.uid() = user_id);

-- Add comments for documentation
COMMENT ON TABLE ai_usage_analytics IS 'Detailed analytics for AI usage tracking and cost optimization';
COMMENT ON COLUMN ai_usage_analytics.user_id IS 'User who made the AI request';
COMMENT ON COLUMN ai_usage_analytics.feature_type IS 'Type of AI feature used (keyword-strategy, hashtag-generation, etc.)';
COMMENT ON COLUMN ai_usage_analytics.model_used IS 'Actual OpenAI model used (may differ from requested due to fallback)';
COMMENT ON COLUMN ai_usage_analytics.model_requested IS 'Originally requested OpenAI model';
COMMENT ON COLUMN ai_usage_analytics.input_tokens IS 'Number of input tokens consumed';
COMMENT ON COLUMN ai_usage_analytics.output_tokens IS 'Number of output tokens generated';
COMMENT ON COLUMN ai_usage_analytics.estimated_cost IS 'Estimated cost in USD for this request';
COMMENT ON COLUMN ai_usage_analytics.response_time_ms IS 'Response time in milliseconds';
COMMENT ON COLUMN ai_usage_analytics.cache_hit IS 'Whether this request was served from cache';
COMMENT ON COLUMN ai_usage_analytics.fallback_used IS 'Whether model fallback was used';
COMMENT ON COLUMN ai_usage_analytics.user_tier IS 'User subscription tier at time of request';
COMMENT ON COLUMN ai_usage_analytics.prompt_complexity IS 'Analyzed complexity of the prompt';
COMMENT ON COLUMN ai_usage_analytics.success IS 'Whether the request was successful';
COMMENT ON COLUMN ai_usage_analytics.error_type IS 'Type of error if request failed';

-- Create function to get cost summary for a user
CREATE OR REPLACE FUNCTION get_user_cost_summary(
  target_user_id UUID,
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
  end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
  total_cost DECIMAL,
  total_requests BIGINT,
  cache_hit_rate DECIMAL,
  fallback_rate DECIMAL,
  success_rate DECIMAL,
  avg_response_time DECIMAL,
  cost_by_feature JSONB,
  cost_by_model JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  WITH analytics_data AS (
    SELECT 
      estimated_cost,
      cache_hit,
      fallback_used,
      success,
      response_time_ms,
      feature_type,
      model_used
    FROM ai_usage_analytics
    WHERE user_id = target_user_id
      AND created_at >= start_date
      AND created_at <= end_date
  ),
  summary_stats AS (
    SELECT 
      COALESCE(SUM(estimated_cost), 0) as total_cost,
      COUNT(*) as total_requests,
      CASE 
        WHEN COUNT(*) > 0 THEN ROUND(COUNT(*) FILTER (WHERE cache_hit = true) * 100.0 / COUNT(*), 2)
        ELSE 0
      END as cache_hit_rate,
      CASE 
        WHEN COUNT(*) > 0 THEN ROUND(COUNT(*) FILTER (WHERE fallback_used = true) * 100.0 / COUNT(*), 2)
        ELSE 0
      END as fallback_rate,
      CASE 
        WHEN COUNT(*) > 0 THEN ROUND(COUNT(*) FILTER (WHERE success = true) * 100.0 / COUNT(*), 2)
        ELSE 0
      END as success_rate,
      CASE 
        WHEN COUNT(*) > 0 THEN ROUND(AVG(response_time_ms), 0)
        ELSE 0
      END as avg_response_time
    FROM analytics_data
  ),
  feature_costs AS (
    SELECT jsonb_object_agg(feature_type, ROUND(cost_sum, 4)) as cost_by_feature
    FROM (
      SELECT feature_type, SUM(estimated_cost) as cost_sum
      FROM analytics_data
      GROUP BY feature_type
    ) t
  ),
  model_costs AS (
    SELECT jsonb_object_agg(model_used, ROUND(cost_sum, 4)) as cost_by_model
    FROM (
      SELECT model_used, SUM(estimated_cost) as cost_sum
      FROM analytics_data
      GROUP BY model_used
    ) t
  )
  SELECT 
    s.total_cost,
    s.total_requests,
    s.cache_hit_rate,
    s.fallback_rate,
    s.success_rate,
    s.avg_response_time,
    COALESCE(f.cost_by_feature, '{}'::jsonb),
    COALESCE(m.cost_by_model, '{}'::jsonb)
  FROM summary_stats s
  CROSS JOIN feature_costs f
  CROSS JOIN model_costs m;
END;
$$;

-- Create function to clean up old analytics data (keep last 90 days)
CREATE OR REPLACE FUNCTION cleanup_old_analytics()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  DELETE FROM ai_usage_analytics 
  WHERE created_at < NOW() - INTERVAL '90 days';
  
  -- Log cleanup activity
  INSERT INTO public.system_logs (event_type, message, created_at)
  VALUES ('analytics_cleanup', 'Cleaned up old AI usage analytics data', NOW())
  ON CONFLICT DO NOTHING;
END;
$$;

-- Create a scheduled job to run cleanup weekly (requires pg_cron extension)
DO $$
BEGIN
  BEGIN
    PERFORM cron.schedule('cleanup-analytics', '0 3 * * 0', 'SELECT cleanup_old_analytics();');
  EXCEPTION WHEN OTHERS THEN
    NULL;
  END;
END;
$$;
