-- Create whiteboard_projects table
CREATE TABLE whiteboard_projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    data JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- <PERSON>reate whiteboard_nodes table
CREATE TABLE whiteboard_nodes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES whiteboard_projects(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    position_x INTEGER NOT NULL,
    position_y INTEGER NOT NULL,
    data JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create whiteboard_connections table
CREATE TABLE whiteboard_connections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES whiteboard_projects(id) ON DELETE CASCADE,
    from_node UUID NOT NULL REFERENCES whiteboard_nodes(id) ON DELETE CASCADE,
    to_node UUID NOT NULL REFERENCES whiteboard_nodes(id) ON DELETE CASCADE,
    style JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create user_credits table
CREATE TABLE user_credits (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    credits_used INTEGER NOT NULL DEFAULT 0,
    credits_total INTEGER NOT NULL DEFAULT 500,
    reset_date TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create indexes
CREATE INDEX idx_whiteboard_projects_user_id ON whiteboard_projects(user_id);
CREATE INDEX idx_whiteboard_nodes_project_id ON whiteboard_nodes(project_id);
CREATE INDEX idx_whiteboard_connections_project_id ON whiteboard_connections(project_id);
CREATE INDEX idx_whiteboard_connections_from_node ON whiteboard_connections(from_node);
CREATE INDEX idx_whiteboard_connections_to_node ON whiteboard_connections(to_node);

-- Create RLS policies
ALTER TABLE whiteboard_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE whiteboard_nodes ENABLE ROW LEVEL SECURITY;
ALTER TABLE whiteboard_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_credits ENABLE ROW LEVEL SECURITY;

-- Whiteboard projects policies
CREATE POLICY "Users can view their own projects"
    ON whiteboard_projects FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own projects"
    ON whiteboard_projects FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own projects"
    ON whiteboard_projects FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own projects"
    ON whiteboard_projects FOR DELETE
    USING (auth.uid() = user_id);

-- Whiteboard nodes policies
CREATE POLICY "Users can view nodes in their projects"
    ON whiteboard_nodes FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM whiteboard_projects
            WHERE whiteboard_projects.id = whiteboard_nodes.project_id
            AND whiteboard_projects.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert nodes in their projects"
    ON whiteboard_nodes FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM whiteboard_projects
            WHERE whiteboard_projects.id = project_id
            AND whiteboard_projects.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update nodes in their projects"
    ON whiteboard_nodes FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM whiteboard_projects
            WHERE whiteboard_projects.id = whiteboard_nodes.project_id
            AND whiteboard_projects.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete nodes in their projects"
    ON whiteboard_nodes FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM whiteboard_projects
            WHERE whiteboard_projects.id = whiteboard_nodes.project_id
            AND whiteboard_projects.user_id = auth.uid()
        )
    );

-- Whiteboard connections policies
CREATE POLICY "Users can view connections in their projects"
    ON whiteboard_connections FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM whiteboard_projects
            WHERE whiteboard_projects.id = whiteboard_connections.project_id
            AND whiteboard_projects.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert connections in their projects"
    ON whiteboard_connections FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM whiteboard_projects
            WHERE whiteboard_projects.id = project_id
            AND whiteboard_projects.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update connections in their projects"
    ON whiteboard_connections FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM whiteboard_projects
            WHERE whiteboard_projects.id = whiteboard_connections.project_id
            AND whiteboard_projects.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete connections in their projects"
    ON whiteboard_connections FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM whiteboard_projects
            WHERE whiteboard_projects.id = whiteboard_connections.project_id
            AND whiteboard_projects.user_id = auth.uid()
        )
    );

-- User credits policies
CREATE POLICY "Users can view their own credits"
    ON user_credits FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own credits"
    ON user_credits FOR UPDATE
    USING (auth.uid() = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for whiteboard_projects
CREATE TRIGGER update_whiteboard_projects_updated_at
    BEFORE UPDATE ON whiteboard_projects
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column(); 