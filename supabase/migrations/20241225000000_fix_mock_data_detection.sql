-- Fix mock data detection to not flag user-created pillars
-- This migration updates the cleanup function to only target obvious mock data

-- Update the cleanup_mock_data function to use better detection logic
CREATE OR REPLACE FUNCTION cleanup_mock_data(user_id UUID)
RETURNS JSONB AS $$
DECLARE
  deleted_videos INT;
  deleted_pillars INT;
  result JSONB;
BEGIN
  -- Delete mock videos
  WITH deleted AS (
    DELETE FROM videos
    WHERE user_id = $1
    AND (
      title LIKE '%React Server Components%' OR
      title LIKE '%React Hooks Complete Guide%' OR
      title LIKE '%Building a Full Stack React App%' OR
      title LIKE '%React State Management%' OR
      title LIKE '%Home Office Setup%' OR
      title LIKE '%AI Tools Revolutionizing%' OR
      title LIKE '%Boost Your Work From Home%' OR
      title LIKE '%How to Grow Your Channel%' OR
      title LIKE '%Beginner''s Guide to YouTube%' OR
      title LIKE '%Top 10 Camera Tips%' OR
      title LIKE '%YouTube Algorithm Explained%' OR
      title LIKE '%Camera Settings for Begin<PERSON>%' OR
      title LIKE '%mock%' OR
      title LIKE '%demo%' OR
      title LIKE '%test%'
    )
    RETURNING id
  )
  SELECT COUNT(*) INTO deleted_videos FROM deleted;

  -- Delete mock pillars - only obvious mock indicators, NOT user-created content
  WITH deleted AS (
    DELETE FROM content_pillars
    WHERE user_id = $1
    AND (
      name LIKE '%mock%' OR
      name LIKE '%demo%' OR
      name LIKE '%test%' OR
      name LIKE '%sample%'
    )
    RETURNING id
  )
  SELECT COUNT(*) INTO deleted_pillars FROM deleted;

  -- Return results
  result := jsonb_build_object(
    'deleted_videos', deleted_videos,
    'deleted_pillars', deleted_pillars
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION cleanup_mock_data TO authenticated;
