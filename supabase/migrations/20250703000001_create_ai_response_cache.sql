-- Create AI response cache table for cost optimization
CREATE TABLE IF NOT EXISTS ai_response_cache (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  cache_key TEXT NOT NULL UNIQUE,
  feature_type TEXT NOT NULL,
  prompt_hash TEXT NOT NULL,
  response_data JSONB NOT NULL,
  user_tier TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  hit_count INTEGER DEFAULT 0
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_ai_response_cache_key ON ai_response_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_ai_response_cache_feature_type ON ai_response_cache(feature_type);
CREATE INDEX IF NOT EXISTS idx_ai_response_cache_expires_at ON ai_response_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_ai_response_cache_prompt_hash ON ai_response_cache(prompt_hash);
CREATE INDEX IF NOT EXISTS idx_ai_response_cache_user_tier ON ai_response_cache(user_tier);

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_ai_response_cache_lookup ON ai_response_cache(cache_key, expires_at);

-- Add RLS policies
ALTER TABLE ai_response_cache ENABLE ROW LEVEL SECURITY;

-- Policy for service role (used by edge functions)
CREATE POLICY "Service role can manage cache" ON ai_response_cache
  FOR ALL USING (auth.role() = 'service_role');

-- Policy for authenticated users to read their own cached responses
CREATE POLICY "Users can read cache for their tier" ON ai_response_cache
  FOR SELECT USING (auth.role() = 'authenticated');

-- Add comments for documentation
COMMENT ON TABLE ai_response_cache IS 'Caches AI responses to reduce API calls and costs';
COMMENT ON COLUMN ai_response_cache.cache_key IS 'Unique identifier for cached response based on prompt and parameters';
COMMENT ON COLUMN ai_response_cache.feature_type IS 'Type of AI feature (keyword-strategy, hashtag-generation, etc.)';
COMMENT ON COLUMN ai_response_cache.prompt_hash IS 'Hash of the original prompt for deduplication';
COMMENT ON COLUMN ai_response_cache.response_data IS 'Cached AI response data in JSON format';
COMMENT ON COLUMN ai_response_cache.user_tier IS 'User subscription tier when response was generated';
COMMENT ON COLUMN ai_response_cache.expires_at IS 'When this cache entry expires';
COMMENT ON COLUMN ai_response_cache.hit_count IS 'Number of times this cached response has been used';

-- Create function to automatically clean up expired cache entries
CREATE OR REPLACE FUNCTION cleanup_expired_ai_cache()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  DELETE FROM ai_response_cache 
  WHERE expires_at < NOW();
  
  -- Log cleanup activity
  INSERT INTO public.system_logs (event_type, message, created_at)
  VALUES ('cache_cleanup', 'Cleaned up expired AI response cache entries', NOW())
  ON CONFLICT DO NOTHING;
END;
$$;

-- Create a scheduled job to run cleanup daily (requires pg_cron extension)
-- This will be enabled if pg_cron is available
DO $$
BEGIN
  -- Try to create the cron job, ignore if pg_cron is not available
  BEGIN
    PERFORM cron.schedule('cleanup-ai-cache', '0 2 * * *', 'SELECT cleanup_expired_ai_cache();');
  EXCEPTION WHEN OTHERS THEN
    -- pg_cron not available, cleanup will be handled manually
    NULL;
  END;
END;
$$;
