-- Create a function to detect mock data
CREATE OR REPLACE FUNCTION prevent_mock_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Check for mock video titles
  IF NEW.title LIKE '%React Server Components%' OR
     NEW.title LIKE '%React Hooks Complete Guide%' OR
     NEW.title LIKE '%Building a Full Stack React App%' OR
     NEW.title LIKE '%React State Management%' OR
     NEW.title LIKE '%Home Office Setup%' OR
     NEW.title LIKE '%AI Tools Revolutionizing%' OR
     NEW.title LIKE '%Boost Your Work From Home%' OR
     NEW.title LIKE '%How to Grow Your Channel%' OR
     NEW.title LIKE '%Beginner''s Guide to YouTube%' OR
     NEW.title LIKE '%Top 10 Camera Tips%' OR
     NEW.title LIKE '%YouTube Algorithm Explained%' OR
     NEW.title LIKE '%Camera Settings for Beginners%' OR
     NEW.title LIKE '%mock%' OR
     NEW.title LIKE '%demo%' OR
     NEW.title LIKE '%test%' THEN
    RAISE EXCEPTION 'Mock data is not allowed in production';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to videos table
CREATE TRIGGER prevent_mock_videos
BEFORE INSERT OR UPDATE ON videos
FOR EACH ROW
EXECUTE FUNCTION prevent_mock_data();

-- Create a function to detect mock pillar names
CREATE OR REPLACE FUNCTION prevent_mock_pillars()
RETURNS TRIGGER AS $$
BEGIN
  -- Check for mock pillar names
  IF NEW.name LIKE '%mock%' OR
     NEW.name LIKE '%demo%' OR
     NEW.name LIKE '%test%' THEN
    RAISE EXCEPTION 'Mock data is not allowed in production';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to content_pillars table
CREATE TRIGGER prevent_mock_pillars
BEFORE INSERT OR UPDATE ON content_pillars
FOR EACH ROW
EXECUTE FUNCTION prevent_mock_pillars();