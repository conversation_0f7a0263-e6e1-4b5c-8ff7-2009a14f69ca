
project_id = "xpemuejeohbpsxoqvdry"

[api]
enabled = true
port = 54321
schemas = ["public", "storage", "graphql_public"]

[db]
port = 54322

[studio]
enabled = true
port = 54323
api_url = "http://localhost"

[storage]
enabled = true
file_size_limit = 50

[functions]
[functions.check-subscription]
verify_jwt = true
[functions.customer-portal]
verify_jwt = true
[functions.create-checkout]
verify_jwt = true
[functions.create-seats-checkout]
verify_jwt = true
[functions.stripe-webhook]
verify_jwt = false
[functions.youtube-oauth]
verify_jwt = true
[functions.keyword-strategy]
verify_jwt = true
[functions.optimize-title]
verify_jwt = true
[functions.analyze-title]
verify_jwt = true
[functions.generate-hashtags]
verify_jwt = true

[inbucket]
enabled = true
port = 54324
