// Save this to a file and run with: node test-backend-connection.js
const http = require('http');

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/health',
  method: 'GET'
};

console.log('Testing connection to backend server...');
const req = http.request(options, (res) => {
  console.log(`STATUS: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('RESPONSE:', data);
    console.log('\nConnection successful! Backend server is running.');
  });
});

req.on('error', (e) => {
  console.error('\nConnection failed! Error details:');
  console.error(e.message);
  console.error('\nPossible solutions:');
  console.error('1. Make sure the backend server is running on port 3000');
  console.error('2. Check for any firewall or network issues');
  console.error('3. Verify the backend server is configured to listen on localhost:3000');
});

req.end();