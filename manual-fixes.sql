-- Create indexes for each table with foreign keys
-- Run these one by one and check if the warnings disappear

-- For competitor_analyses
DROP INDEX IF EXISTS idx_competitor_analyses_user_id;
CREATE INDEX idx_competitor_analyses_user_id ON public.competitor_analyses(user_id);
ANALYZE public.competitor_analyses;

-- For content_pillars
DROP INDEX IF EXISTS idx_content_pillars_user_id;
CREATE INDEX idx_content_pillars_user_id ON public.content_pillars(user_id);
ANALYZE public.content_pillars;

-- Continue with other tables as needed