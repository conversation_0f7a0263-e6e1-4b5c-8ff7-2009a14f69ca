# AI Dev Tasks Integration

This project now includes the AI Dev Tasks system, which provides a structured approach to feature development using AI assistance. The system helps break down complex features into manageable tasks and guides AI through systematic implementation.

## What is AI Dev Tasks?

AI Dev Tasks is a workflow system that brings structure to AI-assisted development by:

1. **Defining Clear Scope** - Creating Product Requirement Documents (PRDs)
2. **Detailed Planning** - Breaking down PRDs into actionable task lists
3. **Iterative Implementation** - Guiding AI to tackle one task at a time with checkpoints

## Files in This Integration

- `create-prd.mdc` - Guides AI in generating Product Requirement Documents
- `generate-tasks.mdc` - Converts PRDs into detailed, step-by-step task lists
- `process-task-list.mdc` - Manages task execution one step at a time
- `tasks/` - Directory where all PRDs and task lists are stored

## How to Use the AI Dev Tasks System

### Step 1: Create a Product Requirement Document (PRD)

In Cursor's Agent chat, use:
```
Use @create-prd.mdc
Here's the feature I want to build: [Describe your feature in detail]
Reference these files to help you: [Optional: @file1.py @file2.ts]
```

The AI will ask clarifying questions before generating a comprehensive PRD saved as `tasks/prd-[feature-name].md`.

### Step 2: Generate Task List from PRD

Once you have your PRD, generate tasks:
```
Now take @tasks/prd-[your-feature].md and create tasks using @generate-tasks.mdc
```

This creates `tasks/tasks-prd-[your-feature].md` with parent tasks and detailed sub-tasks.

### Step 3: Process Tasks Step-by-Step

Start working through tasks systematically:
```
Please start on task 1.1 and use @process-task-list.mdc
```

The AI will:
- Work on one sub-task at a time
- Wait for your approval before moving to the next task
- Mark completed tasks as `[x]`
- Update the relevant files list

### Step 4: Review and Approve

As each task completes:
- Review the changes
- Reply "yes" or "y" to approve and continue
- Provide feedback if changes are needed

## Project Structure Integration

Your React + TypeScript + Vite project with shadcn/ui is perfect for this system. The task lists will reference:

- Components in `src/components/`
- Pages/routes in `src/pages/` or similar
- Utilities in `src/lib/`
- Types in `src/types/`
- API integration with Supabase
- Styling with Tailwind CSS

## Benefits for Your Project

- **Structured Development**: Clear process from idea to implemented feature
- **Quality Control**: Review AI changes at each small step
- **Progress Tracking**: Visual representation of completed vs remaining work
- **Reduced Complexity**: Breaks large features into digestible pieces
- **Better AI Results**: More reliable than single, large prompts

## Example Workflow

1. "I want to add a user dashboard with analytics widgets"
2. AI asks clarifying questions about data sources, chart types, permissions
3. PRD generated with detailed requirements
4. Task list created with specific implementation steps
5. AI implements each sub-task one by one with your approval
6. Feature completed with proper testing and documentation

## Tips for Success

- **Be Specific**: Provide clear context and requirements
- **Review Regularly**: Check each completed sub-task before proceeding
- **Iterate**: Provide feedback to refine the approach
- **Tag Files**: Reference relevant existing files when creating PRDs

## Getting Started

To test the system, try creating a simple feature like "Add a theme toggle button to the navigation" and follow the workflow above.

## OpenAI Security Integration (June 2024)

### How OpenAI is now integrated
- **OpenAI API calls are made from a backend Express server only.**
- The backend exposes a POST `/api/openai` endpoint that proxies requests to OpenAI securely.
- The OpenAI API key is stored in `backend/.env` and **never exposed to the frontend or committed to git**.
- The frontend (React/Vite) calls `/api/openai` for all OpenAI-powered features.

### Setup Instructions

1. **Add your OpenAI API key**
   - Create a file at `backend/.env` with the following content:
     ```
     OPENAI_API_KEY=sk-REPLACE_WITH_YOUR_OPENAI_KEY
     ```
   - **Never commit this file to git.**

2. **Install dependencies**
   - Run:
     ```bash
     npm install
     ```

3. **Run the backend server**
   - From the project root:
     ```bash
     npm run backend
     ```
   - The backend will start on port 5000 by default.

4. **Run the frontend**
   - Usual Vite/React start:
     ```bash
     npm run dev
     ```

5. **How to use OpenAI in the frontend**
   - Use `fetch('/api/openai', { ... })` to call the backend for completions.
   - Do **not** import or use the OpenAI SDK in any frontend code.

### Security Notes
- `.env` and `backend/.env` are gitignored.
- No API keys are present in any committed files.
- All OpenAI requests are server-side only.

### For Production
- Ensure your backend server is deployed securely and the `.env` file is set on the server.
- Never expose your OpenAI API key to the browser or client-side code. 