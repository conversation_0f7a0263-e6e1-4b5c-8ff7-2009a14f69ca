-- <PERSON><PERSON>t to remove unused indexes safely
-- Using CONCURRENTLY to avoid locking tables during removal

-- Public schema indexes
DROP INDEX CONCURRENTLY IF EXISTS public.content_pillars_user_id_idx;
DROP INDEX CONCURRENTLY IF EXISTS public.idx_subscribers_user_id;
DROP INDEX CONCURRENTLY IF EXISTS public.goals_user_id_index;
DROP INDEX CONCURRENTLY IF EXISTS public.idx_videos_user_id;
DROP INDEX CONCURRENTLY IF EXISTS public.idx_videos_pillar_id;

-- Verify removal was successful
SELECT schemaname, indexname 
FROM pg_indexes 
WHERE schemaname = 'public' 
AND indexname IN (
  'content_pillars_user_id_idx',
  'idx_subscribers_user_id',
  'goals_user_id_index',
  'idx_videos_user_id',
  'idx_videos_pillar_id'
);