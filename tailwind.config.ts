import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		fontSize: {
			'xs': ['1rem', { lineHeight: '1.5rem' }],      // Minimum 16px
			'sm': ['1.125rem', { lineHeight: '1.75rem' }], // 18px
			'base': ['1.125rem', { lineHeight: '1.75rem' }], // 18px
			'lg': ['1.25rem', { lineHeight: '1.875rem' }],  // 20px
			'xl': ['1.375rem', { lineHeight: '2rem' }],     // 22px
			'2xl': ['1.5rem', { lineHeight: '2.25rem' }],     // 24px (was 24px)
			'3xl': ['1.875rem', { lineHeight: '2.5rem' }],    // 30px (was 30px)
			'4xl': ['2.25rem', { lineHeight: '3rem' }],       // 36px (was 36px)
			'5xl': ['3rem', { lineHeight: '3.5rem' }],        // 48px (was 48px)
			'6xl': ['3.75rem', { lineHeight: '4rem' }],       // 60px (was 60px)
			'7xl': ['4.5rem', { lineHeight: '4.5rem' }],      // 72px (was 72px)
			'8xl': ['6rem', { lineHeight: '6rem' }],          // 96px (was 96px)
			'9xl': ['8rem', { lineHeight: '8rem' }],          // 128px (was 128px)
		},
		extend: {
			fontFamily: {
				sans: ['Montserrat', 'Arial', 'sans-serif'],
				montserrat: ['Montserrat', 'Arial', 'sans-serif'],
			},
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				// YouTube-Inspired Color Palette
				'deep-red': {
					DEFAULT: '#DC2626',
					light: '#FCA5A5',
					dark: '#991B1B'
				},
				'blue': {
					DEFAULT: '#3B82F6',
					light: '#DBEAFE',
					dark: '#1D4ED8'
				},
				'orange': {
					DEFAULT: '#F97316',
					light: '#FED7AA',
					dark: '#C2410C'
				},
				'green': {
					DEFAULT: '#16A34A',
					light: '#BBF7D0',
					dark: '#166534'
				},
				// Legacy brand colors (keeping for compatibility)
				teal: {
					DEFAULT: '#06B6D4', // Updated to bright-cyan
					light: '#CFFAFE'
				},
				cyan: {
					DEFAULT: '#06B6D4',
					light: '#CFFAFE'
				},
				terracotta: {
					DEFAULT: '#F472B6', // Updated to coral-pink
					dark: '#DB2777',
					light: '#FCE7F3'
				},
				yellow: {
					DEFAULT: '#ffda63'
				},
				white: '#FFFFFF',
				black: '#000000',
				// Dark theme colors
				'dark-gray': {
					DEFAULT: '#000000',
					dark: '#000000'
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
