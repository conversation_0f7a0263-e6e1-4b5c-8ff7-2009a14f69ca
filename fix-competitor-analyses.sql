-- Create index for competitor_analyses.user_id
CREATE INDEX IF NOT EXISTS idx_competitor_analyses_user_id 
ON public.competitor_analyses(user_id);

-- Create index for content_pillars.user_id (visible in your screenshot)
CREATE INDEX IF NOT EXISTS idx_content_pillars_user_id
ON public.content_pillars(user_id);

-- Create indexes for other tables visible in your screenshot
CREATE INDEX IF NOT EXISTS idx_goals_user_id
ON public.goals(user_id);

CREATE INDEX IF NOT EXISTS idx_team_members_user_id
ON public.team_members(user_id);

CREATE INDEX IF NOT EXISTS idx_videos_user_id
ON public.videos(user_id);

-- Force a database statistics update
ANALYZE public.competitor_analyses;
ANALYZE public.content_pillars;
ANALYZE public.goals;
ANALYZE public.team_members;
ANALYZE public.videos;