-- COMPREHENSIVE GOALS TABLE DIAGNOSTIC AND FIX SCRIPT
-- Run this in Supabase SQL Editor

-- STEP 1: Diagnostic query to check current goals table structure
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length,
    numeric_precision,
    numeric_scale
FROM information_schema.columns
WHERE table_name = 'goals'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- STEP 2: Check if table exists and has data
SELECT
    COUNT(*) as total_goals,
    COUNT(DISTINCT type) as unique_goal_types,
    array_agg(DISTINCT type) as goal_types
FROM goals;

-- STEP 3: Check for any existing data that might cause issues
SELECT
    type,
    COUNT(*) as count,
    MIN(current_value) as min_current,
    MAX(current_value) as max_current,
    MIN(target_value) as min_target,
    MAX(target_value) as max_target
FROM goals
GROUP BY type;

-- STEP 4: Fix data types if they are incorrect (INTEGER instead of NUMERIC)
DO $$
BEGIN
    -- Check if current_value is INTEGER and convert to NUMERIC
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'goals'
        AND column_name = 'current_value'
        AND data_type = 'integer'
    ) THEN
        ALTER TABLE goals ALTER COLUMN current_value TYPE NUMERIC;
        RAISE NOTICE 'Converted current_value from INTEGER to NUMERIC';
    END IF;

    -- Check if target_value is INTEGER and convert to NUMERIC
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'goals'
        AND column_name = 'target_value'
        AND data_type = 'integer'
    ) THEN
        ALTER TABLE goals ALTER COLUMN target_value TYPE NUMERIC;
        RAISE NOTICE 'Converted target_value from INTEGER to NUMERIC';
    END IF;

    -- Add missing columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'goals' AND column_name = 'current_value') THEN
        ALTER TABLE goals ADD COLUMN current_value NUMERIC DEFAULT 0;
        RAISE NOTICE 'Added current_value column';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'goals' AND column_name = 'target_value') THEN
        ALTER TABLE goals ADD COLUMN target_value NUMERIC NOT NULL DEFAULT 0;
        RAISE NOTICE 'Added target_value column';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'goals' AND column_name = 'end_date') THEN
        ALTER TABLE goals ADD COLUMN end_date DATE;
        RAISE NOTICE 'Added end_date column';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'goals' AND column_name = 'type') THEN
        ALTER TABLE goals ADD COLUMN type TEXT NOT NULL DEFAULT 'subscribers';
        RAISE NOTICE 'Added type column';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'goals' AND column_name = 'user_id') THEN
        ALTER TABLE goals ADD COLUMN user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added user_id column';
    END IF;
END $$;

-- STEP 5: Ensure RLS is enabled and policies exist
ALTER TABLE goals ENABLE ROW LEVEL SECURITY;

-- Create RLS policies if they don't exist
DO $$
BEGIN
    -- Check and create SELECT policy
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'goals'
        AND policyname = 'Users can view their own goals'
    ) THEN
        EXECUTE 'CREATE POLICY "Users can view their own goals" ON goals FOR SELECT USING (auth.uid() = user_id)';
        RAISE NOTICE 'Created SELECT policy for goals';
    END IF;

    -- Check and create INSERT policy
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'goals'
        AND policyname = 'Users can insert their own goals'
    ) THEN
        EXECUTE 'CREATE POLICY "Users can insert their own goals" ON goals FOR INSERT WITH CHECK (auth.uid() = user_id)';
        RAISE NOTICE 'Created INSERT policy for goals';
    END IF;

    -- Check and create UPDATE policy
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'goals'
        AND policyname = 'Users can update their own goals'
    ) THEN
        EXECUTE 'CREATE POLICY "Users can update their own goals" ON goals FOR UPDATE USING (auth.uid() = user_id)';
        RAISE NOTICE 'Created UPDATE policy for goals';
    END IF;

    -- Check and create DELETE policy
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'goals'
        AND policyname = 'Users can delete their own goals'
    ) THEN
        EXECUTE 'CREATE POLICY "Users can delete their own goals" ON goals FOR DELETE USING (auth.uid() = user_id)';
        RAISE NOTICE 'Created DELETE policy for goals';
    END IF;
END $$;

-- STEP 6: Show final table structure
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'goals'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- STEP 7: Show RLS policies
SELECT
    policyname,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE tablename = 'goals';

-- STEP 8: Quick diagnostic to check if changes were applied
SELECT 'Current table structure:' as info;
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'goals'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- STEP 9: Test data insertion (optional - comment out if not needed)
/*
INSERT INTO goals (user_id, type, current_value, target_value, end_date)
VALUES (
    auth.uid(),
    'revenue',
    100.50,
    1000.00,
    CURRENT_DATE + INTERVAL '30 days'
);
*/
