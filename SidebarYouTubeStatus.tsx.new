import React from 'react';
import { useYouTubeConnection } from '@/hooks/useYouTubeConnection';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Youtube, CheckCircle, Unlink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

const SidebarYouTubeStatus = () => {
  const { youtubeData, isConnected, tokenStatus, refreshConnection } = useYouTubeConnection();
  const hasChannel = !!youtubeData?.youtube_channel_id;
  const hasValidToken = tokenStatus.isValid;
  const hasRecentSync = youtubeData?.last_youtube_sync && 
    new Date(youtubeData.last_youtube_sync) > new Date(Date.now() - 24 * 60 * 60 * 1000); // Within 24 hours
  const navigate = useNavigate();

  // Use the same logic as the main connection hook for consistency
  const showConnected = isConnected; // This already includes proper logic
  // Show warning if channel exists but not connected (which means token issues)
  const showTokenWarning = hasChannel && !isConnected;

  const handleDisconnect = async () => {
    if (!youtubeData?.id) {
      toast.error('User not found');
      return;
    }
    
    try {
      const { error } = await supabase
        .from('users')
        .update({
          youtube_channel_id: null,
          youtube_access_token: null,
          youtube_refresh_token: null,
          youtube_channel_name: null,
          youtube_subscriber_baseline: null,
          last_youtube_sync: null,
          youtube_thumbnail_url: null
        })
        .eq('id', youtubeData.id);

      if (error) throw error;

      toast.success('YouTube channel disconnected successfully');
      
      // Force a refresh of the connection data
      refreshConnection();
      
      // Add a small delay and then navigate to refresh the UI
      setTimeout(() => {
        navigate('/');
        // Force a page reload after navigation
        setTimeout(() => window.location.reload(), 100);
      }, 500);
    } catch (error) {
      console.error('Error disconnecting YouTube:', error);
      toast.error('Failed to disconnect YouTube channel');
    }
  };

  return (
    <div className="px-4 py-3 border-b border-gray-700">
      {/* YouTube Integration Header */}
      <div className="flex items-center text-white mb-2">
        <Youtube className="w-5 h-5 mr-2 text-red-500" />
        YouTube Integration
      </div>

      {/* Channel Name with Connected Badge */}
      {youtubeData?.youtube_channel_name && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-400 truncate">
            {youtubeData.youtube_channel_name}
          </p>
          {showConnected && (
            <Badge className="bg-green-500 text-white ml-2 flex-shrink-0">
              <CheckCircle className="w-3 h-3 mr-1" />
              Connected
            </Badge>
          )}
        </div>
      )}

      {showTokenWarning && (
        <div className="mt-2 p-2 bg-orange/20 border border-orange/40 rounded flex flex-col items-start">
          <div className="flex items-center text-orange text-xs mb-2">
            <AlertTriangle className="w-4 h-4 mr-1" />
            Token expired – reconnect needed
          </div>
          <div className="flex flex-col w-full gap-2">
            <Button
              size="sm"
              className="bg-teal hover:bg-teal/90 text-white w-full"
              onClick={() => navigate('/settings?tab=youtube')}
            >
              Reconnect YouTube
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="border-orange hover:bg-orange/10 text-orange w-full"
              onClick={() => navigate('/settings?tab=youtube')}
            >
              Manage Connection
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-gray-400 hover:text-white hover:bg-gray-800 w-full"
              onClick={handleDisconnect}
            >
              <Unlink className="w-3 h-3 mr-1" />
              Disconnect YouTube
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SidebarYouTubeStatus;