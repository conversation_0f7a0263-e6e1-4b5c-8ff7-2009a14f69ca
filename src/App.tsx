import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from 'next-themes';
import { Toaster } from 'react-hot-toast';
import ConnectionErrorBoundary from '@/components/ConnectionErrorBoundary';
import './App.css';

// Create a client with enhanced security settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
      retry: (failureCount, error: any) => {
        // Don't retry on authentication errors
        if (error?.status === 401 || error?.status === 403) {
          return false;
        }
        return failureCount < 3;
      },
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="dark">
        <Toaster position="top-center" />
        <ConnectionErrorBoundary>
          {/* Your app content here */}
          <div className="app-container">
            {/* Your routes and components */}
          </div>
        </ConnectionErrorBoundary>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
