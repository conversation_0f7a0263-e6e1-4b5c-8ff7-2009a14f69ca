import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from 'next-themes';
import { Toaster } from 'react-hot-toast';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AuthProvider } from '@/components/AuthProvider';
import AppContent from '@/components/AppContent';
import DashboardPage from '@/pages/DashboardPage';
import PillarsPage from '@/pages/PillarsPage';
import IdeasPage from '@/pages/IdeasPage';
import CreatorStudioPage from '@/pages/CreatorStudioPage';
import CalendarPage from '@/pages/CalendarPage';
import SettingsPage from '@/pages/SettingsPage';
import BillingPage from '@/pages/BillingPage';
import BillingSuccessPage from '@/pages/BillingSuccessPage';
import BillingCancelPage from '@/pages/BillingCancelPage';
import PricingPage from '@/pages/PricingPage';
import TestPage from '@/pages/TestPage';
import YouTubeCallbackPage from '@/pages/YouTubeCallbackPage';
import NotFound from '@/pages/NotFound';
import ConnectionDiagnostic from '@/components/ConnectionDiagnostic';
import './App.css';

// Create a client with enhanced security settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
      retry: (failureCount, error: any) => {
        // Don't retry on authentication errors
        if (error?.status === 401 || error?.status === 403) {
          return false;
        }
        return failureCount < 3;
      },
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="dark">
        <Toaster position="top-center" />
        <Router>
          <AuthProvider>
            <SidebarProvider>
              <Routes>
                <Route path="/" element={<AppContent />}>
                  <Route index element={<Navigate to="/dashboard" replace />} />
                  <Route path="dashboard" element={<DashboardPage />} />
                  <Route path="pillars" element={<PillarsPage />} />
                  <Route path="ideas" element={<IdeasPage />} />
                  <Route path="creator-studio" element={<CreatorStudioPage />} />
                  <Route path="calendar" element={<CalendarPage />} />
                  <Route path="settings" element={<SettingsPage />} />
                  <Route path="billing" element={<BillingPage />} />
                  <Route path="billing/success" element={<BillingSuccessPage />} />
                  <Route path="billing/cancel" element={<BillingCancelPage />} />
                  <Route path="pricing" element={<PricingPage />} />
                  <Route path="test" element={<TestPage />} />
                  <Route path="diagnostics" element={<ConnectionDiagnostic />} />
                  <Route path="youtube/callback" element={<YouTubeCallbackPage />} />
                  <Route path="*" element={<NotFound />} />
                </Route>
              </Routes>
            </SidebarProvider>
          </AuthProvider>
        </Router>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
