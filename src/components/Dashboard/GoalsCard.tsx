import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, Edit2, Trash2 } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

interface Goal {
  id: string;
  name: string;
  current: number;
  target: number;
  unit?: string;
}

interface GoalsCardProps {
  goals?: Goal[];
  onCreateGoal?: () => void;
  onEditGoal?: (goalId: string) => void;
  onDeleteGoal?: (goalId: string) => void;
}

const GoalsCard: React.FC<GoalsCardProps> = ({
  goals = [
    { id: '1', name: 'monthly_views', current: 1000, target: 6000 },
    { id: '2', name: 'revenue', current: 100, target: 10000 },
    { id: '3', name: 'subscribers', current: 250, target: 10000 }
  ],
  onCreateGoal,
  onEditGoal,
  onDeleteGoal
}) => {
  return (
    <Card className="rounded-xl bg-purple-900/40 border-purple-800/50">
      <CardHeader className="bg-purple-900/60 rounded-t-xl border-b border-purple-800/50 flex flex-row items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-white">Goals</h2>
          <p className="text-base text-gray-300">Track your progress and set new targets</p>
        </div>
        <Button
          size="sm"
          className="bg-cyan-500 hover:bg-cyan-600 text-white"
          onClick={onCreateGoal}
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Goal
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        <div className="p-4">
          <h3 className="text-lg font-medium text-white mb-4 flex items-center">
            <span className="w-5 h-5 rounded-full bg-purple-500 flex items-center justify-center text-white mr-2">
              <span className="text-xs">✓</span>
            </span>
            Goals Progress (Live Data)
          </h3>
          
          <div className="space-y-6">
            {goals.map((goal) => (
              <div key={goal.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="w-5 h-5 rounded-full bg-purple-500 flex items-center justify-center text-white mr-2">
                      <span className="text-xs">✓</span>
                    </span>
                    <span className="text-gray-300">{goal.name}</span>
                  </div>
                  <div className="flex space-x-2">
                    <button 
                      className="text-gray-400 hover:text-white"
                      onClick={() => onEditGoal && onEditGoal(goal.id)}
                    >
                      <Edit2 className="w-4 h-4" />
                    </button>
                    <button 
                      className="text-gray-400 hover:text-white"
                      onClick={() => onDeleteGoal && onDeleteGoal(goal.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <Progress 
                  value={(goal.current / goal.target) * 100} 
                  className="h-2 bg-gray-700"
                  indicatorClassName={
                    goal.name === 'monthly_views' ? "bg-gradient-to-r from-orange-500 to-red-500" :
                    goal.name === 'revenue' ? "bg-gradient-to-r from-orange-500 to-red-500" :
                    "bg-gradient-to-r from-orange-500 to-red-500"
                  }
                />
                
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">{goal.current}</span>
                  <span className="text-gray-400">{goal.target}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default GoalsCard;