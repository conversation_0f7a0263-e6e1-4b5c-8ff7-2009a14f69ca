import React from 'react';
import { ContentPillar } from '@/types/pillar';

interface ContentBalanceWidgetProps {
  pillars: ContentPillar[];
}

const ContentBalanceWidget: React.FC<ContentBalanceWidgetProps> = ({ pillars }) => {
  // Add this function to format percentages
  const formatPercentage = (value: number | null | undefined) => {
    if (value === null || value === undefined) return '0%';
    return `${value}%`;
  };

  // Add this function to determine performance text
  const getPerformanceText = (pillar: ContentPillar) => {
    const actual = pillar.actual_percentage || 0;
    const target = pillar.target_percentage || 0;
    
    const diff = actual - target;
    if (Math.abs(diff) < 5) return 'On Target';
    return diff > 0 ? `${diff}% Over` : `${Math.abs(diff)}% Under`;
  };

  // Add this function to determine performance color
  const getPerformanceColor = (pillar: ContentPillar) => {
    const actual = pillar.actual_percentage || 0;
    const target = pillar.target_percentage || 0;
    
    const diff = actual - target;
    if (Math.abs(diff) < 5) return 'text-green-400';
    return diff > 0 ? 'text-yellow-400' : 'text-red-400';
  };

  // If no pillars, show empty state
  if (!pillars || pillars.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-4 h-full">
        <h3 className="text-lg font-medium text-white mb-4">Content Balance</h3>
        <p className="text-sm text-gray-400">No content pillars found. Create pillars to see your content balance.</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-4 h-full">
      <h3 className="text-lg font-medium text-white mb-4">Content Balance</h3>
      <div className="space-y-1">
        {pillars.map((pillar) => (
          <div key={pillar.id} className="flex items-center justify-between py-2">
            <div className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: pillar.color || '#37BEB0' }}
              />
              <span className="text-sm">{pillar.name}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm">
                {formatPercentage(pillar.actual_percentage)} / {formatPercentage(pillar.target_percentage)}
              </span>
              <span className={`text-xs ${getPerformanceColor(pillar)}`}>
                {getPerformanceText(pillar)}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ContentBalanceWidget;
