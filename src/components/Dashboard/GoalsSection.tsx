import React, { useState, useRef } from 'react';
import { Target, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import GoalsProgressCard from './GoalsProgressCard';
import GoalModal from './GoalModal';

interface GoalsSectionProps {
  user: any;
  navigate?: (path: string) => void;
}

const GoalsSection: React.FC<GoalsSectionProps> = ({ user, navigate }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleGoalCreated = () => {
    setIsModalOpen(false);
    // Trigger refresh of the goals progress card
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="space-y-6">
      {/* Goals Header with Create Button */}
      <div className="flex justify-between items-center bg-purple-900/80 p-4 rounded-lg border border-purple-800">
        <div>
          <h2 className="text-xl font-semibold text-white">Goals</h2>
          <p className="text-base text-gray-300">Track your progress and set new targets</p>
        </div>
        <Button
          onClick={() => setIsModalOpen(true)}
          size="sm"
          className="bg-cyan-500 hover:bg-cyan-600 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Goal
        </Button>
      </div>

      {/* Goals Progress Card Container */}
      <div className="bg-purple-900/80 rounded-lg border border-purple-800 min-h-[300px]">
        <GoalsProgressCard
          user={user}
          navigate={navigate}
          onCreateGoal={() => setIsModalOpen(true)}
          key={refreshTrigger} // Force re-render when goals are created
        />
      </div>

      {/* Goal Modal */}
      <GoalModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onGoalCreated={handleGoalCreated}
        user={user}
      />
    </div>
  );
};

export default GoalsSection;
