import React from 'react';
import { Target, TrendingUp, Trophy } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface GoalsEmptyStateProps {
  onCreateGoal: () => void;
}

const GoalsEmptyState = ({ onCreateGoal }: GoalsEmptyStateProps) => {
  const exampleGoals = [
    { icon: Target, title: "Reach 1,000 Subscribers", progress: 0, target: 1000 },
    { icon: TrendingUp, title: "10,000 Monthly Views", progress: 0, target: 10000 },
    { icon: Trophy, title: "Upload 50 Videos", progress: 0, target: 50 }
  ];

  return (
    <Card className="dashboard-card bg-gradient-to-br from-orange/10 to-golden/10 border-gray-600/30">
      <CardContent className="p-8">
        <div className="text-center space-y-6">
          <div className="w-20 h-20 mx-auto bg-gray-700/50 rounded-full flex items-center justify-center">
            <Target className="w-12 h-12 text-orange" />
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-white mb-2">Set Your First Goal</h3>
            <p className="text-gray-300 max-w-md mx-auto leading-relaxed mb-6">
              Track your YouTube growth with specific, measurable goals. Here's what other creators are working towards:
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {exampleGoals.map((goal, index) => {
              const Icon = goal.icon;
              return (
                <div key={index} className="bg-gray-800/40 rounded-lg p-4 border border-gray-600/30">
                  <Icon className="w-6 h-6 text-orange mb-2 mx-auto" />
                  <h4 className="text-sm font-medium text-white mb-1">{goal.title}</h4>
                  <div className="w-full bg-gray-600 rounded-full h-2">
                    <div className="bg-orange h-2 rounded-full w-0"></div>
                  </div>
                  <p className="text-xs text-gray-400 mt-1">0 / {goal.target.toLocaleString()}</p>
                </div>
              );
            })}
          </div>

          <Button 
            onClick={onCreateGoal}
            className="bg-teal hover:bg-teal/90 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Create Your First Goal
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default GoalsEmptyState;
