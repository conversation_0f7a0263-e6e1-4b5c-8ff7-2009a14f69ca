import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Calendar, DollarSign } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

// Define the PerformanceAlertsSection component inline
const PerformanceAlertsSection = () => {
  const navigate = useNavigate();

  const performanceAlerts = [
    {
      id: 1,
      type: 'underperforming',
      icon: AlertTriangle,
      title: '3 videos underperforming',
      description: 'Recent uploads 40% below average',
      action: 'Analyze',
      severity: 'warning'
    },
    {
      id: 2,
      type: 'trend',
      icon: Calendar,
      title: 'Tuesday uploads +40% views',
      description: 'Consistent outperformance pattern',
      action: 'Schedule more',
      severity: 'success'
    },
    {
      id: 3,
      type: 'revenue',
      icon: DollarSign,
      title: 'Equipment Reviews up 60%',
      description: 'Higher engagement and revenue',
      action: 'Create more',
      severity: 'success'
    }
  ];

  const handleAlertAction = (alert) => {
    switch (alert.type) {
      case 'underperforming':
        navigate('/analytics');
        break;
      case 'trend':
        navigate('/calendar');
        break;
      case 'revenue':
        navigate('/ideas');
        break;
      default:
        break;
    }
  };

  const getSeverityStyle = (severity) => {
    switch (severity) {
      case 'warning': 
        return {
          border: 'border-orange/20',
          bg: 'bg-orange/5',
          iconBg: 'bg-orange/20',
          iconColor: 'text-orange',
          buttonStyle: 'border-orange/50 text-orange hover:bg-orange/10'
        };
      case 'success': 
        return {
          border: 'border-green/20',
          bg: 'bg-green/5',
          iconBg: 'bg-green/20',
          iconColor: 'text-green',
          buttonStyle: 'border-green/50 text-green hover:bg-green/10'
        };
      default: 
        return {
          border: 'border-gray-600',
          bg: '',
          iconBg: 'bg-gray-600',
          iconColor: 'text-gray-300',
          buttonStyle: 'border-gray-500 text-gray-300 hover:bg-gray-500/10'
        };
    }
  };

  return (
    <div className="space-y-3">
      {performanceAlerts.map((alert) => {
        const Icon = alert.icon;
        const styles = getSeverityStyle(alert.severity);
        
        return (
          <Card key={alert.id} className={`dashboard-card border ${styles.border} ${styles.bg}`}>
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded ${styles.iconBg}`}>
                  <Icon className={`w-4 h-4 ${styles.iconColor}`} />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-white text-sm mb-1">{alert.title}</h4>
                  <p className="text-xs text-gray-300 mb-2">{alert.description}</p>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleAlertAction(alert)}
                    className={`text-xs h-7 ${styles.buttonStyle}`}
                  >
                    {alert.action}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

// Main InsightsTab component
const InsightsTab = () => {
  return (
    <div className="grid grid-cols-1 gap-6">
      <div className="bg-[#6b46c1] rounded-lg border border-[#5b35b5] p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Insights</h2>
        <PerformanceAlertsSection />
      </div>
    </div>
  );
};

export default InsightsTab;