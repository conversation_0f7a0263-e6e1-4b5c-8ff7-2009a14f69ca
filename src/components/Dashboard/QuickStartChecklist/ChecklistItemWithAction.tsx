
import React from 'react';
import { CheckCircle, ArrowRight } from 'lucide-react';
import { ChecklistItem } from './types';

interface ChecklistItemWithActionProps {
  item: ChecklistItem;
  index: number;
  onAction: (action: () => void) => void;
}

const ChecklistItemWithAction = ({ item, index, onAction }: ChecklistItemWithActionProps) => {
  const Icon = item.icon;

  return (
    <div className="flex items-center space-x-4 p-4 rounded-lg bg-gray-800/30 hover:bg-gray-700/40 transition-all duration-200 border border-gray-600/30">
      <div className="flex items-center space-x-4 flex-1">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gray-700 text-gray-300 text-xs font-medium">
            {index + 1}
          </div>
          {item.completed ? (
            <CheckCircle className="w-6 h-6 text-green-400" />
          ) : (
            <Icon className="w-6 h-6 text-gray-400" />
          )}
        </div>
        
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-1">
            <h3 className={`font-medium ${item.completed ? 'text-green-400' : 'text-white'}`}>
              {item.label}
            </h3>
            {item.id === 'youtubeConnected' && (
              <div className="bg-blue/20 border border-blue/30 rounded px-2 py-1 w-fit">
                <span className="text-blue text-xs font-medium">💡 Tip: This unlocks automatic video import and performance insights</span>
              </div>
            )}
          </div>
          <p className="text-sm text-gray-400">{item.description}</p>
        </div>
      </div>
      
      {item.action && !item.completed && (
        <button
          onClick={() => onAction(item.action!)}
          className="flex items-center px-4 py-2 bg-blue hover:bg-blue-dark text-white rounded-lg font-medium transition-colors"
        >
          Start
          <ArrowRight className="w-4 h-4 ml-2" />
        </button>
      )}
    </div>
  );
};

export default ChecklistItemWithAction;
