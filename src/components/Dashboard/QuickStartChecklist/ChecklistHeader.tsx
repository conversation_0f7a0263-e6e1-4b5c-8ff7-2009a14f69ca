import React from 'react';
import { CardHeader, CardTitle } from '@/components/ui/card';
import { Rocket } from 'lucide-react';

interface ChecklistHeaderProps {
  completedCount: number;
  totalCount: number;
  progressPercentage: number;
}

const ChecklistHeader = ({ completedCount, totalCount, progressPercentage }: ChecklistHeaderProps) => {
  return (
    <CardHeader className="pb-6">
      <div className="flex items-center justify-between">
        <div>
          <CardTitle className="text-white flex items-center text-2xl font-bold mb-3">
            <Rocket className="w-7 h-7 mr-3 text-orange" />
            <span className="text-blue">Start Here</span>
          </CardTitle>
          <p className="text-gray-300 text-lg leading-relaxed">
            Welcome to MyContentHub! Complete these steps to unlock powerful insights for your content strategy.
          </p>
        </div>
        <div className="text-center bg-gray-800/50 rounded-lg p-4 border border-gray-600">
          <div className="text-3xl font-bold text-blue mb-1">
            {completedCount}/{totalCount}
          </div>
          <div className="text-sm text-gray-400">Steps Complete</div>
        </div>
      </div>
      
      <div className="mt-6">
        <div className="flex justify-between items-center text-sm mb-3">
          <span className="text-gray-400 font-medium">Your Progress</span>
          <span className="text-blue font-bold text-base">
            {Math.round(progressPercentage)}% complete
          </span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-4 shadow-inner">
          <div
            className="bg-gradient-to-r from-blue to-orange h-4 rounded-full transition-all duration-700 ease-out shadow-lg"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>
    </CardHeader>
  );
};

export default ChecklistHeader;
