
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { ChecklistState } from './types';

export const useQuickStartChecklist = (user: any, onComplete?: () => void) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showCelebration, setShowCelebration] = useState(false);
  const [checklistItems, setChecklistItems] = useState<ChecklistState>({
    accountCreated: true,
    youtubeConnected: false,
    videosImported: false,
    pillarsCreated: false,
    goalSet: false
  });

  useEffect(() => {
    // Hide immediately if onboarding is already completed
    if (user?.onboarding_completed) {
      setIsVisible(false);
      return;
    }

    // Only show if onboarding is not completed
    setIsVisible(true);
    checkProgress();
  }, [user]);

  const checkProgress = async () => {
    if (!user) return;

    try {
      const youtubeConnected = !!user.youtube_channel_id;

      const { data: videos } = await supabase
        .from('videos')
        .select('id')
        .eq('user_id', user.id)
        .eq('status', 'published');
      const videosImported = (videos?.length || 0) > 0;

      const { data: pillars } = await supabase
        .from('content_pillars')
        .select('id')
        .eq('user_id', user.id);
      const pillarsCreated = (pillars?.length || 0) > 0;

      const { data: goals } = await supabase
        .from('goals')
        .select('id')
        .eq('user_id', user.id);
      const goalSet = (goals?.length || 0) > 0;

      const newChecklistItems = {
        accountCreated: true,
        youtubeConnected,
        videosImported,
        pillarsCreated,
        goalSet
      };

      setChecklistItems(newChecklistItems);

      const allComplete = Object.values(newChecklistItems).every(Boolean);
      if (allComplete && !user.onboarding_completed) {
        console.log('All checklist items completed, marking onboarding as complete');
        handleOnboardingComplete();
      }
    } catch (error) {
      console.error('Error checking progress:', error);
    }
  };

  const handleOnboardingComplete = async () => {
    console.log('Starting onboarding completion process');
    setShowCelebration(true);
    
    try {
      const { error } = await supabase
        .from('users')
        .update({ onboarding_completed: true })
        .eq('id', user.id);
      
      if (error) {
        console.error('Error updating onboarding status:', error);
      } else {
        console.log('Successfully marked onboarding as completed');
      }
    } catch (error) {
      console.error('Error updating onboarding status:', error);
    }

    // Hide the checklist after celebration
    setTimeout(() => {
      console.log('Hiding checklist after celebration');
      setIsVisible(false);
      onComplete?.();
    }, 3000);
  };

  const completedCount = Object.values(checklistItems).filter(Boolean).length;
  const totalCount = 5;
  const progressPercentage = (completedCount / totalCount) * 100;

  return {
    isVisible,
    showCelebration,
    checklistItems,
    completedCount,
    totalCount,
    progressPercentage
  };
};
