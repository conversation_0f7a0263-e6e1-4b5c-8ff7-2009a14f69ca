
export interface ChecklistItem {
  id: string;
  label: string;
  description: string;
  icon: any;
  completed: boolean;
  action: (() => void) | null;
}

export interface ChecklistState {
  accountCreated: boolean;
  youtubeConnected: boolean;
  videosImported: boolean;
  pillarsCreated: boolean;
  goalSet: boolean;
}

export interface QuickStartChecklistProps {
  user: any;
  onComplete: () => void;
  navigate: (path: string) => void;
}
