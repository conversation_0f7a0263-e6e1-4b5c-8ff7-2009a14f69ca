
import React, { useState } from 'react';
import { CardContent } from '@/components/ui/card';
import { UserCheck, Youtube, Target, Goal, Lightbulb } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { ChecklistState, ChecklistItem } from './types';
import ChecklistItemWithAction from './ChecklistItemWithAction';
import YouTubeConnectionWizard from '@/components/YouTube/YouTubeConnectionWizard';

interface ChecklistItemsProps {
  checklistItems: ChecklistState;
}

const ChecklistItems = ({ checklistItems }: ChecklistItemsProps) => {
  const navigate = useNavigate();
  const [showYouTubeWizard, setShowYouTubeWizard] = useState(false);

  const items: ChecklistItem[] = [
    {
      id: 'accountCreated',
      label: 'Account created',
      description: 'Welcome to MyContentHub! Your journey starts here.',
      icon: UserCheck,
      completed: checklistItems.accountCreated,
      action: null
    },
    {
      id: 'youtubeConnected',
      label: 'Connect YouTube channel',
      description: 'Link your channel to import videos and track performance',
      icon: Youtube,
      completed: checklistItems.youtubeConnected,
      action: () => setShowYouTubeWizard(true)
    },
    {
      id: 'videosImported',
      label: 'Import your videos',
      description: 'Bring in your existing content to analyze performance',
      icon: Lightbulb,
      completed: checklistItems.videosImported,
      action: () => navigate('/settings?tab=youtube')
    },
    {
      id: 'pillarsCreated',
      label: 'Set up content pillars',
      description: 'Define your content strategy framework and categories',
      icon: Target,
      completed: checklistItems.pillarsCreated,
      action: () => navigate('/pillars')
    },
    {
      id: 'goalSet',
      label: 'Create your first goal',
      description: 'Set subscriber or view targets to track your growth',
      icon: Goal,
      completed: checklistItems.goalSet,
      action: () => {
        // Scroll to goals section on dashboard instead of navigating to separate page
        const goalsSection = document.querySelector('[data-goals-section]');
        if (goalsSection) {
          goalsSection.scrollIntoView({ behavior: 'smooth' });
          // Trigger goal creation modal
          const createGoalButton = goalsSection.querySelector('[data-create-goal-button]');
          if (createGoalButton) {
            (createGoalButton as HTMLElement).click();
          }
        }
      }
    }
  ];

  const handleYouTubeConnect = () => {
    setShowYouTubeWizard(false);
    navigate('/settings?tab=youtube');
  };

  const handleAction = (action: () => void) => {
    action();
  };

  return (
    <>
      <CardContent className="px-8 pb-8">
        <div className="space-y-5">
          {items.map((item, index) => (
            <ChecklistItemWithAction
              key={item.id}
              item={item}
              index={index}
              onAction={handleAction}
            />
          ))}
        </div>
      </CardContent>

      <YouTubeConnectionWizard
        isOpen={showYouTubeWizard}
        onClose={() => setShowYouTubeWizard(false)}
        onConnect={handleYouTubeConnect}
      />
    </>
  );
};

export default ChecklistItems;
