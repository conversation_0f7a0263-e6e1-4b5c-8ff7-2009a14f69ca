
import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import PillarFitSection from './VideoAnalysisModal/PillarFitSection';
import TimingAnalysisSection from './VideoAnalysisModal/TimingAnalysisSection';
import QuickFactorsSection from './VideoAnalysisModal/QuickFactorsSection';
import StrategyRecommendations from './VideoAnalysisModal/StrategyRecommendations';
import { 
  Video, 
  calculatePerformanceMetrics, 
  getPublishingData, 
  analyzeVideoFactors, 
  generateKeyInsight 
} from './VideoAnalysisModal/utils';

interface VideoAnalysisModalProps {
  video: Video | null;
  isOpen: boolean;
  onClose: () => void;
  channelAverage: number;
}

const VideoAnalysisModal = ({ video, isOpen, onClose, channelAverage }: VideoAnalysisModalProps) => {
  if (!video) return null;

  const { performancePercent, performanceBadge } = calculatePerformanceMetrics(video, channelAverage);
  const { publishedDay, publishedTime } = getPublishingData(video.published_at);
  const videoFactors = analyzeVideoFactors(video, publishedDay);
  const keyInsight = generateKeyInsight(performancePercent, videoFactors, publishedDay);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[500px] p-0">
        <DialogHeader className="p-6 pb-4">
          <div className="flex-1">
            <DialogTitle className="text-lg font-semibold text-white mb-2 line-clamp-2">
              {video.title}
            </DialogTitle>
            <div className="flex items-center gap-3">
              <Badge className={performanceBadge.color}>
                {performanceBadge.label}
              </Badge>
              <span className="text-sm text-gray-300">
                {video.views.toLocaleString()} views ({performancePercent}% of your average)
              </span>
            </div>
          </div>
        </DialogHeader>

        <div className="px-6 pb-6 space-y-4">
          <PillarFitSection video={video} pillarFit={videoFactors.pillarFit} />
          
          <TimingAnalysisSection
            publishedDay={publishedDay}
            publishedTime={publishedTime}
            timingGood={videoFactors.timingGood}
          />

          <QuickFactorsSection
            titleLength={videoFactors.titleLength}
            isTitleGood={videoFactors.isTitleGood}
            schedulingGood={videoFactors.schedulingGood}
            daysFromPrevious={videoFactors.daysFromPrevious}
            performancePercent={performancePercent}
          />

          <div className="border-t border-gray-600 pt-4">
            <h3 className="font-medium text-white mb-2">Key Insight</h3>
            <p className="text-sm text-gray-300">{keyInsight}</p>
          </div>

          <StrategyRecommendations
            video={video}
            performancePercent={performancePercent}
            timingGood={videoFactors.timingGood}
            isTitleGood={videoFactors.isTitleGood}
            titleLength={videoFactors.titleLength}
            schedulingGood={videoFactors.schedulingGood}
            daysFromPrevious={videoFactors.daysFromPrevious}
            publishedDay={publishedDay}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default VideoAnalysisModal;
