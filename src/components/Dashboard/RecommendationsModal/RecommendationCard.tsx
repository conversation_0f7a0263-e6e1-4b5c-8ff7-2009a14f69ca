
import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, AlertTriangle, X, ArrowR<PERSON>, Trash } from 'lucide-react';
import { Recommendation } from './types';

interface RecommendationCardProps {
  recommendation: Recommendation;
  onDismiss: (id: string, title: string) => void;
}

const RecommendationCard = ({ recommendation: rec, onDismiss }: RecommendationCardProps) => {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return Check;
      case 'warning': return AlertTriangle;
      case 'error': return X;
      default: return Check;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-400';
      case 'warning': return 'text-yellow-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const StatusIcon = getStatusIcon(rec.status);

  return (
    <div className="bg-gray-700 rounded-lg p-4 border border-gray-600">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-full ${rec.status === 'success' ? 'bg-green-500/20' : rec.status === 'warning' ? 'bg-yellow-500/20' : 'bg-red-500/20'}`}>
            <StatusIcon className={`w-4 h-4 ${getStatusColor(rec.status)}`} />
          </div>
          <div>
            <div className="flex items-center space-x-2 mb-1">
              <h3 className="text-white font-medium">{rec.title}</h3>
              <Badge 
                variant="outline" 
                className={`text-xs ${getPriorityColor(rec.priority)} text-white border-none`}
              >
                {rec.priority} priority
              </Badge>
            </div>
            <p className="text-gray-400 text-sm">{rec.description}</p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onDismiss(rec.id, rec.title)}
          className="text-gray-400 hover:text-red-400 hover:bg-red-500/10"
        >
          <Trash className="w-4 h-4" />
        </Button>
      </div>

      <div className="ml-11 space-y-3">
        <div>
          <h4 className="text-sm font-medium text-gray-300 mb-2">Recommended Actions:</h4>
          <ul className="space-y-1">
            {rec.actions.map((action, actionIndex) => (
              <li key={actionIndex} className="text-sm text-gray-400 flex items-start">
                <ArrowRight className="w-3 h-3 mt-0.5 mr-2 text-teal flex-shrink-0" />
                {action}
              </li>
            ))}
          </ul>
        </div>

        <Button 
          size="sm" 
          variant="outline"
          className="text-teal border-teal hover:bg-teal hover:text-white"
          onClick={rec.ctaAction}
        >
          {rec.cta}
          <ArrowRight className="w-3 h-3 ml-1" />
        </Button>
      </div>
    </div>
  );
};

export default RecommendationCard;
