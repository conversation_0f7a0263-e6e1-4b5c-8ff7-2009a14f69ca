
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Lightbulb } from 'lucide-react';
import { RecommendationsModalProps } from './types';
import { useRecommendations } from './useRecommendations';
import RecommendationCard from './RecommendationCard';
import EmptyState from './EmptyState';

const RecommendationsModal = ({ open, onOpenChange, healthScore }: RecommendationsModalProps) => {
  const { visibleRecommendations, dismissRecommendation } = useRecommendations();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-3">
            <span>Content Strategy Recommendations</span>
            <Badge variant="outline" className="text-sm bg-teal/20 text-teal border-teal">
              Health Score: {Math.round(healthScore)}%
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <p className="text-gray-300 text-sm">
            Based on your current content health score, here are personalized recommendations to improve your channel's performance:
          </p>

          {visibleRecommendations.length === 0 ? (
            <EmptyState />
          ) : (
            <div className="grid gap-4">
              {visibleRecommendations.map((rec) => (
                <RecommendationCard
                  key={rec.id}
                  recommendation={rec}
                  onDismiss={dismissRecommendation}
                />
              ))}
            </div>
          )}

          <div className="bg-teal/10 border border-teal/30 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Lightbulb className="w-5 h-5 text-teal mt-0.5" />
              <div>
                <h3 className="text-white font-medium mb-2">Pro Tip</h3>
                <p className="text-gray-300 text-sm">
                  Focus on the high-priority recommendations first. Small improvements in engagement and content balance 
                  can significantly boost your overall health score within 2-3 weeks.
                </p>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RecommendationsModal;
