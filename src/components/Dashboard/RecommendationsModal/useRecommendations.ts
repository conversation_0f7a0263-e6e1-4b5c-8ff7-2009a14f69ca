
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { Recommendation } from './types';
import { AlertTriangle, Check, Calendar, TrendingUp } from 'lucide-react';

export const useRecommendations = () => {
  const navigate = useNavigate();
  const [dismissedRecommendations, setDismissedRecommendations] = useState<Set<string>>(new Set());

  const handleNavigateToIdeas = () => {
    navigate('/ideas');
    toast.success('Navigate to Ideas Bank to plan equipment review videos');
  };

  const handleNavigateToAnalytics = () => {
    navigate('/analytics');
    toast.success('Check your analytics to improve engagement');
  };

  const handleNavigateToCalendar = () => {
    navigate('/calendar');
    toast.success('View your content calendar');
  };

  const handleNavigateToPillars = () => {
    navigate('/pillars');
    toast.success('Review your content pillars strategy');
  };

  const recommendations: Recommendation[] = [
    {
      id: 'missing-equipment-reviews',
      category: 'Content Balance',
      priority: 'high',
      icon: AlertTriangle,
      status: 'warning',
      title: 'Missing Equipment Reviews Content',
      description: 'No Equipment Reviews videos in 3 weeks',
      actions: [
        'Plan 2-3 equipment review videos for next month',
        'Research trending gear in your niche',
        'Create comparison videos between popular products'
      ],
      cta: 'Add to Ideas Bank',
      ctaAction: handleNavigateToIdeas
    },
    {
      id: 'engagement-declining',
      category: 'Performance',
      priority: 'high',
      icon: TrendingUp,
      status: 'error',
      title: 'Engagement Rate Declining',
      description: 'Engagement dropped 12% this week',
      actions: [
        'Review recent video intros - keep them under 10 seconds',
        'Add more interactive elements (polls, questions)',
        'Respond to comments within first 2 hours of upload'
      ],
      cta: 'View Analytics',
      ctaAction: handleNavigateToAnalytics
    },
    {
      id: 'upload-consistency',
      category: 'Schedule',
      priority: 'medium',
      icon: Calendar,
      status: 'success',
      title: 'Maintain Upload Consistency',
      description: 'Great 3-week upload streak!',
      actions: [
        'Continue current upload schedule',
        'Batch film content to stay ahead',
        'Set up backup content for busy periods'
      ],
      cta: 'View Calendar',
      ctaAction: handleNavigateToCalendar
    },
    {
      id: 'pillars-balanced',
      category: 'Strategy',
      priority: 'low',
      icon: Check,
      status: 'success',
      title: 'Content Pillars Well Balanced',
      description: 'Good distribution across content types',
      actions: [
        'Monitor pillar performance monthly',
        'Consider slight adjustments based on seasonal trends',
        'Experiment with new sub-topics within existing pillars'
      ],
      cta: 'View Pillars',
      ctaAction: handleNavigateToPillars
    }
  ];

  const visibleRecommendations = recommendations.filter(rec => !dismissedRecommendations.has(rec.id));

  const dismissRecommendation = (recommendationId: string, title: string) => {
    setDismissedRecommendations(prev => new Set([...prev, recommendationId]));
    toast.success(`"${title}" recommendation dismissed`);
  };

  return {
    visibleRecommendations,
    dismissRecommendation
  };
};
