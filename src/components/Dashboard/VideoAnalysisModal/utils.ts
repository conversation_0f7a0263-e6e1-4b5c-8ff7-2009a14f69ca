
export interface Video {
  id: string;
  title: string;
  views: number;
  published_at: string;
  pillar?: {
    name: string;
    color: string;
  };
}

export const getPerformanceCategory = (views: number, channelAverage: number) => {
  if (channelAverage === 0) return 'No data';
  
  const ratio = views / channelAverage;
  if (ratio >= 1.5) return 'Excellent';
  if (ratio >= 1.2) return 'Good';
  if (ratio >= 0.8) return 'Average';
  return 'Below Average';
};

export const getPerformanceColor = (category: string) => {
  switch (category) {
    case 'Excellent': return 'text-green-400';
    case 'Good': return 'text-teal-400';
    case 'Average': return 'text-yellow-400';
    case 'Below Average': return 'text-red-400';
    default: return 'text-gray-400';
  }
};

export const calculatePerformanceMetrics = (video: Video, channelAverage: number) => {
  const performancePercent = channelAverage > 0 ? Math.round((video.views / channelAverage) * 100) : 0;
  const category = getPerformanceCategory(video.views, channelAverage);
  
  return {
    performancePercent,
    performanceBadge: {
      label: category,
      color: getPerformanceColor(category)
    }
  };
};

export const getPublishingData = (publishedAt: string) => {
  const date = new Date(publishedAt);
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  
  return {
    publishedDay: days[date.getDay()],
    publishedTime: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  };
};

export const analyzeVideoFactors = (video: Video, publishedDay: string) => {
  const titleLength = video.title.length;
  const isTitleGood = titleLength >= 40 && titleLength <= 70;
  const timingGood = ['Tuesday', 'Wednesday', 'Thursday'].includes(publishedDay);
  const pillarFit = video.pillar ? 'Strong Match' : 'Needs Assignment';
  
  return {
    titleLength,
    isTitleGood,
    timingGood,
    pillarFit,
    schedulingGood: true, // Simplified for now
    daysFromPrevious: 0 // Would need calculation from previous videos
  };
};

export const generateKeyInsight = (performancePercent: number, videoFactors: any, publishedDay: string) => {
  if (performancePercent >= 120) {
    return `This video is performing ${performancePercent}% above your average! Consider creating similar content on ${videoFactors.timingGood ? publishedDay : 'Tuesday-Thursday'} for consistent results.`;
  }
  
  if (performancePercent < 50) {
    const factors = [];
    if (!videoFactors.isTitleGood) factors.push('title optimization');
    if (!videoFactors.timingGood) factors.push('publishing schedule');
    if (!videoFactors.pillarFit) factors.push('pillar assignment');
    
    return factors.length > 0 
      ? `This video underperformed. Consider improving: ${factors.join(', ')}.`
      : 'This video underperformed. Review your content strategy and audience engagement patterns.';
  }
  
  return 'This video performed within your normal range. Continue with your current strategy while testing small improvements.';
};

export const getTimingInsights = (publishedAt: string) => {
  if (!publishedAt) return null;
  
  const date = new Date(publishedAt);
  const dayOfWeek = date.getDay();
  const hour = date.getHours();
  
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const dayName = days[dayOfWeek];
  
  let timeOfDay = 'Morning';
  if (hour >= 12 && hour < 17) timeOfDay = 'Afternoon';
  else if (hour >= 17 && hour < 21) timeOfDay = 'Evening';
  else if (hour >= 21 || hour < 6) timeOfDay = 'Night';
  
  return {
    dayName,
    timeOfDay,
    hour: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  };
};

export const getEngagementRate = (likes: number, comments: number, views: number) => {
  if (views === 0) return 0;
  return ((likes + comments) / views) * 100;
};

export const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};
