
import React from 'react';
import { Clock, Target, BarChart3, TrendingUp, Lightbulb } from 'lucide-react';
import { Video } from './utils';

interface StrategyRecommendationsProps {
  video: Video;
  performancePercent: number;
  timingGood: boolean;
  isTitleGood: boolean;
  titleLength: number;
  schedulingGood: boolean;
  daysFromPrevious: number;
  publishedDay: string;
}

const StrategyRecommendations = ({
  video,
  performancePercent,
  timingGood,
  isTitleGood,
  titleLength,
  schedulingGood,
  daysFromPrevious,
  publishedDay
}: StrategyRecommendationsProps) => {
  const getStrategyRecommendations = () => {
    if (performancePercent >= 70) return null;

    const recommendations = [];

    if (!timingGood) {
      recommendations.push({
        icon: Clock,
        title: 'Optimize Publishing Schedule',
        description: `Try publishing on Tuesday-Thursday between 2-4 PM when your audience is most active. Avoid ${publishedDay}s which show 30% lower engagement.`
      });
    }

    if (!isTitleGood) {
      recommendations.push({
        icon: Target,
        title: 'Improve Title Strategy',
        description: titleLength < 40 
          ? 'Expand your title to 40-70 characters with more descriptive keywords and emotional hooks to improve click-through rates.'
          : 'Shorten your title to 40-70 characters for better visibility in search results and suggested videos.'
      });
    }

    if (!video.pillar) {
      recommendations.push({
        icon: BarChart3,
        title: 'Assign Content Pillar',
        description: 'Videos with clear pillar assignment perform 25% better. Assign this video to your best-performing pillar to improve future content strategy.'
      });
    }

    if (!schedulingGood) {
      recommendations.push({
        icon: TrendingUp,
        title: 'Maintain Consistent Schedule',
        description: 'Post every 3-7 days to maintain audience engagement. Your current gap may be causing algorithm penalties and audience drop-off.'
      });
    }

    // Always include at least one recommendation for underperforming videos
    if (recommendations.length === 0) {
      recommendations.push({
        icon: Lightbulb,
        title: 'Content Strategy Review',
        description: 'Consider analyzing your top-performing videos for patterns in topics, thumbnail style, and posting strategy to replicate success.'
      });
    }

    return recommendations;
  };

  const recommendations = getStrategyRecommendations();

  if (!recommendations) return null;

  return (
    <div className="border-t border-gray-600 pt-4">
      <h3 className="font-medium text-white mb-3">Strategy Recommendations</h3>
      <div className="space-y-3">
        {recommendations.map((rec, index) => {
          const IconComponent = rec.icon;
          return (
            <div key={index} className="p-3 bg-yellow/10 border border-yellow/20 rounded-lg">
              <div className="flex items-start gap-3">
                <IconComponent className="w-4 h-4 text-yellow mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-white mb-1">{rec.title}</h4>
                  <p className="text-xs text-gray-300 leading-relaxed">{rec.description}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default StrategyRecommendations;
