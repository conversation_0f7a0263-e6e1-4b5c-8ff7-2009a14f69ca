
import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>cle, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, Trending<PERSON>p, TrendingDown } from 'lucide-react';

interface QuickFactorsSectionProps {
  titleLength: number;
  isTitleGood: boolean;
  schedulingGood: boolean;
  daysFromPrevious: number;
  performancePercent: number;
}

const QuickFactorsSection = ({ 
  titleLength, 
  isTitleGood, 
  schedulingGood, 
  daysFromPrevious, 
  performancePercent 
}: QuickFactorsSectionProps) => {
  return (
    <div className="border-t border-gray-600 pt-4">
      <div className="flex items-center gap-2 mb-3">
        <BarChart3 className="w-4 h-4 text-teal" />
        <h3 className="font-medium text-white">Quick Factors</h3>
      </div>
      <div className="pl-6 space-y-2">
        <div className="flex items-center gap-2">
          {isTitleGood ? (
            <CheckCircle className="w-4 h-4 text-green-400" />
          ) : (
            <XCircle className="w-4 h-4 text-red-400" />
          )}
          <span className={`text-sm ${isTitleGood ? 'text-green-400' : 'text-red-400'}`}>
            Title length: {isTitleGood ? `Good (${titleLength} chars)` : `${titleLength < 40 ? 'Too short' : 'Too long'} (${titleLength} chars)`}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          {schedulingGood ? (
            <CheckCircle className="w-4 h-4 text-green-400" />
          ) : (
            <AlertTriangle className="w-4 h-4 text-orange-400" />
          )}
          <span className={`text-sm ${schedulingGood ? 'text-green-400' : 'text-orange-400'}`}>
            Posting frequency: {schedulingGood ? 'On schedule' : `${daysFromPrevious} days (audience expects 3-7)`}
          </span>
        </div>

        <div className="flex items-center gap-2">
          {performancePercent >= 100 ? (
            <TrendingUp className="w-4 h-4 text-green-400" />
          ) : (
            <TrendingDown className="w-4 h-4 text-red-400" />
          )}
          <span className={`text-sm ${performancePercent >= 100 ? 'text-green-400' : 'text-red-400'}`}>
            Trend: {performancePercent >= 100 ? 'Above average' : 'Below recent performance'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default QuickFactorsSection;
