
import React from 'react';
import { Clock, CheckCircle, XCircle } from 'lucide-react';

interface TimingAnalysisSectionProps {
  publishedDay: string;
  publishedTime: string;
  timingGood: boolean;
}

const TimingAnalysisSection = ({ publishedDay, publishedTime, timingGood }: TimingAnalysisSectionProps) => {
  return (
    <div className="border-t border-gray-600 pt-4">
      <div className="flex items-center gap-2 mb-3">
        <Clock className="w-4 h-4 text-teal" />
        <h3 className="font-medium text-white">Timing Analysis</h3>
      </div>
      <div className="pl-6 space-y-2">
        <p className="text-sm text-gray-300">
          Published: {publishedDay} at {publishedTime}
        </p>
        <div className="flex items-center gap-2">
          {timingGood ? (
            <CheckCircle className="w-4 h-4 text-green-400" />
          ) : (
            <XCircle className="w-4 h-4 text-red-400" />
          )}
          <span className={`text-sm ${timingGood ? 'text-green-400' : 'text-red-400'}`}>
            {timingGood ? 'Good timing' : `${publishedDay} is typically your lowest traffic day`}
          </span>
        </div>
      </div>
    </div>
  );
};

export default TimingAnalysisSection;
