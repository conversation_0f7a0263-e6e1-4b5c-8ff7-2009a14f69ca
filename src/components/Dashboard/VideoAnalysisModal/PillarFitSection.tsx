
import React from 'react';
import { Target } from 'lucide-react';
import { Video } from './utils';

interface PillarFitSectionProps {
  video: Video;
  pillarFit: string;
}

const PillarFitSection = ({ video, pillarFit }: PillarFitSectionProps) => {
  return (
    <div className="border-t border-gray-600 pt-4">
      <div className="flex items-center gap-2 mb-3">
        <Target className="w-4 h-4 text-teal" />
        <h3 className="font-medium text-white">Pillar Fit</h3>
      </div>
      <div className="pl-6 space-y-1">
        <p className="text-sm text-gray-300">
          Assigned: <span className="text-white">{video.pillar?.name || 'No Pillar'}</span>
        </p>
        <p className="text-sm text-gray-300">
          Confidence: <span className={pillarFit === 'Strong Match' ? 'text-green-400' : 'text-orange-400'}>
            {pillarFit}
          </span>
        </p>
      </div>
    </div>
  );
};

export default PillarFitSection;
