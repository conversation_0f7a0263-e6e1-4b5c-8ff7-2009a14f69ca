import React, { useState, useEffect } from 'react';
import { Target, Plus, Trash2, Edit2, Trophy } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';
import { useDashboardStats } from '@/hooks/useDashboardStats';
import GoalModal from './GoalModal';
import DeleteGoalDialog from './DeleteGoalDialog';

interface GoalsProgressProps {
  user: any;
}

const GoalsProgress = ({ user }: GoalsProgressProps) => {
  const [goals, setGoals] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingGoal, setEditingGoal] = useState<any>(null);
  const [deleteGoal, setDeleteGoal] = useState<any>(null);
  const { stats } = useDashboardStats({ user });

  // Set up real-time subscription only for goals table
  useEffect(() => {
    if (!user?.id) return;

    // Subscribe only to goals table changes
    const goalsChannel = supabase
      .channel('goals-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'goals',
          filter: `user_id=eq.${user.id}`
        },
        (payload) => {
          console.log('🎯 Goals data changed:', payload.eventType);
          // Only fetch all goals if it's a new goal or deletion
          if (payload.eventType === 'INSERT' || payload.eventType === 'DELETE') {
            fetchGoals();
          } else if (payload.eventType === 'UPDATE') {
            // For updates, just update the specific goal
            setGoals(currentGoals => 
              currentGoals.map(g => 
                g.id === payload.new.id ? { ...g, ...payload.new } : g
              )
            );
          }
        }
      )
      .subscribe();

    // Initial fetch
    fetchGoals();

    // Cleanup subscription
    return () => {
      supabase.removeChannel(goalsChannel);
    };
  }, [user?.id]);

  // Refresh goals less frequently (every 15 minutes)
  useEffect(() => {
    if (!user?.id) return;

    // Refresh goals every 15 minutes
    const intervalId = setInterval(() => {
      console.log('⏰ Periodic goals refresh...');
      fetchGoals();
    }, 15 * 60 * 1000); // 15 minutes

    return () => clearInterval(intervalId);
  }, [user?.id]);

  const fetchGoals = async () => {
    try {
      setIsLoading(true);
      
      // Get fresh user data first to ensure we have the latest subscriber count
      const { data: freshUserData, error: userError } = await supabase
        .from('users')
        .select('*, preferences')
        .eq('id', user.id)
        .single();

      if (userError) throw userError;

      // First fetch goals
      const { data: goalsData, error: goalsError } = await supabase
        .from('goals')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (goalsError) throw goalsError;

      // Then fetch videos once for all monthly views goals
      let videosData = [];
      const hasMonthlyViewsGoals = goalsData?.some(goal => goal.type === 'monthly_views');
      
      if (hasMonthlyViewsGoals) {
        const { data: videos, error: videosError } = await supabase
          .from('videos')
          .select('views, published_at')
          .eq('user_id', user.id)
          .eq('status', 'published');
        
        if (videosError) throw videosError;
        videosData = videos || [];
      }

      // Calculate real metrics for each goal efficiently
      const enhancedGoals = (goalsData || []).map((goal) => {
        let currentValue = goal.current_value || 0;

        // Get real current value based on goal type
        if (goal.type === 'subscribers') {
          // Use the fresh subscriber count
          currentValue = freshUserData.youtube_subscriber_baseline || 0;
        } else if (goal.type === 'monthly_views') {
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          
          const recentViews = videosData
            .filter(video => video.published_at && new Date(video.published_at) > thirtyDaysAgo)
            .reduce((sum, video) => sum + (video.views || 0), 0);
          
          currentValue = recentViews;
        }

        // Calculate progress and days remaining
        const progress = Math.min(100, (currentValue / goal.target_value) * 100);
        const isAchieved = currentValue >= goal.target_value;
        
        let daysRemaining = null;
        let pace = 'No deadline';
        
        if (goal.end_date) {
          const endDate = new Date(goal.end_date);
          const today = new Date();
          daysRemaining = Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
          
          if (daysRemaining < 0) {
            pace = 'Overdue';
          } else if (daysRemaining === 0) {
            pace = 'Due today';
          } else {
            const createdDate = new Date(goal.created_at);
            const totalDays = Math.ceil((endDate.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24));
            const daysPassed = totalDays - daysRemaining;
            
            if (daysPassed > 0) {
              const expectedProgress = (daysPassed / totalDays) * 100;
              pace = progress >= expectedProgress ? 'On Track' : 'Behind Pace';
            } else {
              pace = 'Just started';
            }
          }
        }

        return {
          ...goal,
          current_value: currentValue,
          progress,
          pace,
          daysRemaining,
          isAchieved
        };
      });

      setGoals(enhancedGoals);
    } catch (error) {
      console.error('Error fetching goals:', error);
      // Don't show error toast, just log the error and continue
      setGoals([]);
    } finally {
      setIsLoading(false);
    }
  };

  const getGoalTypeLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      'subscribers': 'Subscribers',
      'monthly_views': 'Monthly Views',
      'revenue': 'Revenue',
      'watch_time': 'Watch Time (hrs)',
      'videos': 'Videos Published'
    };
    return labels[type] || type.charAt(0).toUpperCase() + type.slice(1);
  };

  const handleEditGoal = (goal: any) => {
    setEditingGoal(goal);
    setIsModalOpen(true);
  };

  const handleDeleteGoal = async () => {
    if (!deleteGoal) return;

    try {
      const { error } = await supabase
        .from('goals')
        .delete()
        .eq('id', deleteGoal.id);

      if (error) throw error;

      toast({ title: "Goal deleted successfully!" });
      setDeleteGoal(null);
      fetchGoals();
    } catch (error) {
      console.error('Error deleting goal:', error);
      toast({ 
        title: "Error deleting goal", 
        description: "Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingGoal(null);
  };

  if (isLoading) {
    return (
      <Card className="dashboard-card">
        <CardHeader>
          <CardTitle className="flex items-center justify-between text-white">
            <div className="flex items-center">
              <Target className="w-5 h-5 mr-2 text-teal" />
              Goal Progress
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex justify-center items-center py-8">
            <div className="w-8 h-8 border-4 border-teal border-t-transparent rounded-full animate-spin"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="dashboard-card">
        <CardHeader>
          <CardTitle className="flex items-center justify-between text-white">
            <div className="flex items-center">
              <Target className="w-5 h-5 mr-2 text-teal" />
              Goal Progress
              {user?.youtube_channel_id && (
                <span className="ml-2 text-sm font-normal text-green-400">(Live Data)</span>
              )}
            </div>
            <Button
              onClick={() => setIsModalOpen(true)}
              size="sm"
              className="bg-teal hover:bg-teal/90 text-white"
              data-create-goal-button
            >
              <Plus className="w-4 h-4 mr-1" />
              Create Goal
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {goals.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 bg-teal/10 rounded-full flex items-center justify-center">
                <Target className="w-8 h-8 text-teal" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">Set Your First Goal</h3>
              <p className="text-gray-300 mb-6 max-w-sm mx-auto">
                Track your progress towards subscribers, views, or revenue targets with smart goal tracking.
              </p>
              <Button
                onClick={() => setIsModalOpen(true)}
                className="bg-teal hover:bg-teal/90 text-white"
                data-create-goal-button
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Goal
              </Button>
            </div>
          ) : (
            goals.map((goal, index) => {
              const isOnTrack = goal.pace === 'On Track' || goal.pace === 'Just started';
              const isOverdue = goal.pace === 'Overdue';
              
              return (
                <div key={goal.id || index} className="space-y-3 group">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-semibold text-white">
                          {getGoalTypeLabel(goal.type)}
                        </h3>
                        {goal.isAchieved && (
                          <div className="flex items-center text-yellow">
                            <Trophy className="w-4 h-4 mr-1" />
                            <span className="text-sm font-medium">Achieved! 🎉</span>
                          </div>
                        )}
                      </div>
                      <p className="text-sm text-gray-300">
                        {goal.current_value?.toLocaleString()} / {goal.target_value?.toLocaleString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="text-right">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          goal.isAchieved ? 'bg-yellow-900 text-yellow-400' :
                          isOnTrack ? 'bg-teal-dark text-teal' : 
                          isOverdue ? 'bg-red-900 text-red-400' : 
                          'bg-yellow-900 text-yellow-400'
                        }`}>
                          {goal.isAchieved ? 'Achieved' : goal.pace}
                        </span>
                        {goal.daysRemaining !== null && !goal.isAchieved && (
                          <p className="text-sm text-gray-300 mt-1">
                            {goal.daysRemaining > 0 ? `${goal.daysRemaining} days left` : 
                             goal.daysRemaining === 0 ? 'Due today' : 
                             `${Math.abs(goal.daysRemaining)} days overdue`}
                          </p>
                        )}
                      </div>
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleEditGoal(goal)}
                          className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-gray-700"
                        >
                          <Edit2 className="w-3 h-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => setDeleteGoal(goal)}
                          className="h-8 w-8 p-0 text-gray-400 hover:text-red-400 hover:bg-gray-700"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  <Progress 
                    value={goal.progress} 
                    className={`h-3 ${goal.isAchieved ? 'bg-yellow-900' : ''}`}
                  />
                  <p className="text-sm text-gray-300">
                    Progress: <span className="font-medium text-white">
                      {goal.progress.toFixed(1)}% complete
                    </span>
                  </p>
                </div>
              );
            })
          )}
        </CardContent>
      </Card>

      <GoalModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onGoalCreated={fetchGoals}
        user={user}
        editGoal={editingGoal}
      />

      <DeleteGoalDialog
        isOpen={!!deleteGoal}
        onClose={() => setDeleteGoal(null)}
        onConfirm={handleDeleteGoal}
        goalTitle={deleteGoal ? getGoalTypeLabel(deleteGoal.type) : ''}
      />
    </>
  );
};

export default GoalsProgress;
