import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Youtube, Upload, BarChart3, Calendar, Target, Zap, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface QuickActionsCardProps {
  user: any;
  videoCount: number;
  pillarsCount: number;
}

const QuickActionsCard = ({ user, videoCount, pillarsCount }: QuickActionsCardProps) => {
  const navigate = useNavigate();
  const isYouTubeConnected = !!user?.youtube_channel_id;

  const getSmartActions = () => {
    // Priority 1: YouTube not connected
    if (!isYouTubeConnected) {
      return {
        primary: {
          title: 'Connect YouTube Channel',
          description: 'Import your videos and start tracking performance',
          icon: Youtube,
          action: () => navigate('/settings?tab=youtube'),
          color: 'bg-coral-pink hover:bg-coral-pink-dark'
        },
        secondary: [
          {
            title: 'Set Up Content Pillars',
            description: 'Define your content strategy framework',
            icon: Target,
            action: () => navigate('/pillars')
          }
        ]
      };
    }

    // Priority 2: Connected but no videos imported
    if (isYouTubeConnected && videoCount === 0) {
      return {
        primary: {
          title: 'Import Your Videos',
          description: 'Analyze your existing content performance',
          icon: Upload,
          action: () => navigate('/dashboard'), // This would trigger the import flow
          color: 'bg-bright-cyan hover:bg-bright-cyan-dark'
        },
        secondary: [
          {
            title: 'Set Up Content Pillars',
            description: 'Define your content categories',
            icon: Target,
            action: () => navigate('/pillars')
          }
        ]
      };
    }

    // Priority 3: Videos imported but no pillars
    if (videoCount > 0 && pillarsCount === 0) {
      return {
        primary: {
          title: 'Set Up Content Pillars',
          description: 'Categorize your content for better insights',
          icon: Target,
          action: () => navigate('/pillars'),
          color: 'bg-deep-purple hover:bg-deep-purple-dark'
        },
        secondary: [
          {
            title: 'Plan New Content',
            description: 'Schedule your upcoming videos',
            icon: Calendar,
            action: () => navigate('/calendar')
          }
        ]
      };
    }

    // All setup complete - growth actions
    return {
      primary: {
        title: 'View Analytics',
        description: 'See your latest performance insights',
        icon: BarChart3,
        action: () => navigate('/analytics'),
        color: 'bg-emerald-green hover:bg-emerald-green-dark'
      },
      secondary: [
        {
          title: 'Plan New Content',
          description: 'Schedule your next videos',
          icon: Calendar,
          action: () => navigate('/calendar')
        }
      ]
    };
  };

  const { primary, secondary } = getSmartActions();

  return (
    <Card className="bg-gray-800/40 border-gray-600/30 shadow-lg">
      <CardHeader className="pb-4">
        <CardTitle className="text-white flex items-center text-lg">
          <Zap className="w-5 h-5 mr-2 text-deep-purple" />
          Next Steps
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Primary Action */}
        <div className="relative overflow-hidden rounded-lg bg-gradient-to-br from-gray-700/50 to-gray-800/50 p-6 border border-gray-600/30">
          <div className="relative z-10">
            <div className="flex items-start space-x-4 mb-4">
              <div className={`p-3 rounded-lg ${primary.color || 'bg-teal hover:bg-teal/90'}`}>
                <primary.icon className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-white text-lg mb-1">
                  {primary.title}
                </h3>
                <p className="text-gray-300 text-sm">
                  {primary.description}
                </p>
              </div>
            </div>
            <Button 
              onClick={primary.action}
              className={`w-full ${primary.color || 'bg-teal hover:bg-teal/90'} text-white font-medium`}
            >
              {primary.title}
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
          
          {/* Subtle background decoration */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-white/5 to-transparent rounded-full blur-xl" />
        </div>

        {/* Secondary Actions */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-400 uppercase tracking-wide">
            Other Actions
          </h4>
          {secondary.map((action, index) => {
            const Icon = action.icon;
            return (
              <button
                key={index}
                onClick={action.action}
                className="w-full flex items-center p-4 rounded-lg bg-gray-700/30 hover:bg-gray-600/40 transition-all duration-200 border border-gray-600/20 hover:border-gray-500/30 group"
              >
                <Icon className="w-5 h-5 mr-3 text-gray-400 group-hover:text-white transition-colors" />
                <div className="text-left flex-1">
                  <p className="font-medium text-white text-sm group-hover:text-white">
                    {action.title}
                  </p>
                  <p className="text-xs text-gray-400 group-hover:text-gray-300">
                    {action.description}
                  </p>
                </div>
                <ArrowRight className="w-4 h-4 text-gray-500 group-hover:text-gray-300 transition-colors" />
              </button>
            );
          })}
        </div>

        {/* Progress Indicator */}
        <div className="pt-4 border-t border-gray-600/30">
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-400">Setup Progress</span>
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                <div className={`w-2 h-2 rounded-full ${isYouTubeConnected ? 'bg-green' : 'bg-gray-600'}`} />
                <div className={`w-2 h-2 rounded-full ${videoCount > 0 ? 'bg-green' : 'bg-gray-600'}`} />
                <div className={`w-2 h-2 rounded-full ${pillarsCount > 0 ? 'bg-green' : 'bg-gray-600'}`} />
              </div>
              <span className="text-gray-400">
                {[isYouTubeConnected, videoCount > 0, pillarsCount > 0].filter(Boolean).length}/3
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActionsCard;
