
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { FileText, Edit, Calendar, CheckCircle, AlertTriangle } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { useQuery } from '@tanstack/react-query';

const ContentPipelineWidget = () => {
  const { user } = useAuth();

  const { data: pipelineData, isLoading } = useQuery({
    queryKey: ['content-pipeline', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;

      const { data: videos, error } = await supabase
        .from('videos')
        .select('status')
        .eq('user_id', user.id);

      if (error) throw error;

      const statusCounts = {
        idea: 0,
        script: 0,
        scheduled: 0,
        published: 0
      };

      videos?.forEach(video => {
        if (video.status in statusCounts) {
          statusCounts[video.status as keyof typeof statusCounts]++;
        }
      });

      return statusCounts;
    },
    enabled: !!user?.id
  });

  const getHealthStatus = () => {
    if (!pipelineData) return 'unknown';
    
    const { idea, script, scheduled } = pipelineData;
    const totalInProgress = idea + script + scheduled;
    
    if (totalInProgress === 0) return 'low';
    if (totalInProgress >= 5) return 'healthy';
    return 'moderate';
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-400';
      case 'moderate': return 'text-yellow-400';
      case 'low': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-4 h-4" />;
      case 'moderate': 
      case 'low': return <AlertTriangle className="w-4 h-4" />;
      default: return <AlertTriangle className="w-4 h-4" />;
    }
  };

  if (isLoading) {
    return (
      <Card className="dashboard-card">
        <CardHeader>
          <CardTitle className="text-white">Content Pipeline Health</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-700 rounded w-3/4"></div>
            <div className="h-20 bg-gray-700 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const healthStatus = getHealthStatus();
  const stages = [
    { 
      name: 'Ideas', 
      count: pipelineData?.idea || 0, 
      icon: FileText, 
              color: 'text-teal-400',
        bgColor: 'bg-teal-400/10'
    },
    { 
      name: 'Scripts', 
      count: pipelineData?.script || 0, 
      icon: Edit, 
      color: 'text-orange-400',
      bgColor: 'bg-orange-400/10'
    },
    { 
      name: 'Scheduled', 
      count: pipelineData?.scheduled || 0, 
      icon: Calendar, 
      color: 'text-purple-400',
      bgColor: 'bg-purple-400/10'
    },
    { 
      name: 'Published', 
      count: pipelineData?.published || 0, 
      icon: CheckCircle, 
      color: 'text-green-400',
      bgColor: 'bg-green-400/10'
    }
  ];

  return (
    <Card className="dashboard-card">
      <CardHeader>
        <CardTitle className="text-white flex items-center justify-between">
          <span>Content Pipeline Health</span>
          <div className={`flex items-center space-x-2 ${getHealthColor(healthStatus)}`}>
            {getHealthIcon(healthStatus)}
            <span className="text-sm capitalize">{healthStatus}</span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-4 gap-4">
          {stages.map((stage, index) => {
            const Icon = stage.icon;
            return (
              <div key={stage.name} className="text-center">
                <div className={`${stage.bgColor} rounded-lg p-3 mb-2`}>
                  <Icon className={`w-6 h-6 ${stage.color} mx-auto`} />
                </div>
                <div className="text-2xl font-bold text-white">{stage.count}</div>
                <div className="text-xs text-gray-400">{stage.name}</div>
                {index < stages.length - 1 && (
                  <div className="hidden sm:block absolute top-1/2 right-0 transform translate-x-2 -translate-y-1/2">
                    <div className="w-4 h-0.5 bg-gray-600"></div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
        
        {healthStatus === 'low' && (
          <div className="mt-4 p-3 bg-red-400/10 border border-red-400/20 rounded-lg">
            <p className="text-red-400 text-sm">
              💡 Your content pipeline is running low. Consider creating more ideas or scheduling existing content.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ContentPipelineWidget;
