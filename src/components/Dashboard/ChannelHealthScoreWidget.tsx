
import React, { useState, useEffect } from 'react';
import { TrendingUp } from 'lucide-react';

interface ChannelHealthScoreWidgetProps {
  user: any;
  stats: any;
}

const ChannelHealthScoreWidget = ({ user, stats }: ChannelHealthScoreWidgetProps) => {
  const [healthScore, setHealthScore] = useState(0);
  
  useEffect(() => {
    // Calculate health score based on stats
    const calculateScore = () => {
      // Default score if no data
      if (!stats || !user) return 65;
      
      let score = 65; // Base score
      
      // Adjust based on video count
      if (stats.videosPublished > 10) score += 10;
      else if (stats.videosPublished > 5) score += 5;
      
      // Adjust based on views
      if (stats.monthlyViews > 10000) score += 15;
      else if (stats.monthlyViews > 5000) score += 10;
      else if (stats.monthlyViews > 1000) score += 5;
      
      return Math.min(score, 100);
    };
    
    // Animate score
    const targetScore = calculateScore();
    const timer = setTimeout(() => {
      const increment = targetScore / 50;
      const interval = setInterval(() => {
        setHealthScore(prev => {
          if (prev >= targetScore) {
            clearInterval(interval);
            return targetScore;
          }
          return Math.min(prev + increment, targetScore);
        });
      }, 20);
      
      return () => clearInterval(interval);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [stats, user]);

  const getProgressColor = (score: number) => {
    if (score >= 80) return 'border-green-400';
    if (score >= 60) return 'border-yellow-400';
    return 'border-red-400';
  };

  const getHealthLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Needs Improvement';
    return 'Poor';
  };

  const getHealthDescription = (score: number) => {
    if (score >= 80) return 'Your channel is thriving!';
    if (score >= 60) return 'Good progress, keep it up!';
    if (score >= 40) return 'Room for improvement';
    return 'Needs attention';
  };

  const healthFactors = [
    { 
      label: 'Upload Consistency', 
      value: Math.min(stats?.videosPublished ? stats.videosPublished * 10 : 30, 100),
      color: 'bg-teal'
    },
    { 
      label: 'Audience Growth', 
      value: 75,
      color: 'bg-green-400'
    },
    { 
      label: 'Content Engagement', 
      value: 60,
      color: 'bg-yellow-400'
    }
  ];

  return (
    <div className="bg-purple-900/80 rounded-lg p-4 border border-purple-800 h-full">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-base font-medium text-gray-300">Channel Health</h3>
        <div className="p-2 bg-purple-800/80 rounded-lg">
          <TrendingUp className="w-6 h-6 text-blue-400" />
        </div>
      </div>

      {/* Circular Progress */}
      <div className="flex items-center justify-between mb-4">
        <div className="relative w-20 h-20">
          <div className="absolute inset-0 rounded-full border-4 border-purple-800/80"></div>
          <div 
            className={`absolute inset-0 rounded-full border-4 border-t-transparent transition-all duration-1000 ${getProgressColor(healthScore)}`}
            style={{
              transform: `rotate(${(healthScore / 100) * 360}deg)`,
              borderTopColor: 'transparent'
            }}
          ></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-2xl font-bold text-white">{Math.round(healthScore)}</span>
          </div>
        </div>
        
        <div className="ml-4">
          <div className="text-base font-medium text-white">{getHealthLabel(healthScore)}</div>
          <div className="text-base text-gray-300 mt-1">{getHealthDescription(healthScore)}</div>
        </div>
      </div>
      
      {/* Health Factors */}
      <div className="space-y-3">
        {healthFactors.map((factor, index) => (
          <div key={index} className="flex items-center justify-between">
            <span className="text-base text-gray-300">{factor.label}</span>
            <div className="flex items-center">
              <div className="w-20 h-3 bg-purple-800/80 rounded-full overflow-hidden">
                <div 
                  className={`h-full ${factor.color}`} 
                  style={{ width: `${factor.value}%` }}
                ></div>
              </div>
              <span className="ml-2 text-base text-white">{factor.value}%</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ChannelHealthScoreWidget;
