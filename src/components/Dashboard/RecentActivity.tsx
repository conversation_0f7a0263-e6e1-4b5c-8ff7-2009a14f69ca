
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { TooltipProvider } from '@/components/ui/tooltip';
import { useNavigate } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import VideoAnalysisModal from './VideoAnalysisModal';
import { useRecentActivity } from './RecentActivity/hooks/useRecentActivity';
import { Activity, RecentActivityProps } from './RecentActivity/types';
import { getDisplayLimit } from './RecentActivity/utils';
import ActivityList from './RecentActivity/ActivityList';
import LoadingState from './RecentActivity/LoadingState';

const RecentActivity = ({ user }: RecentActivityProps) => {
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const navigate = useNavigate();

  const { activities, channelAverage, loading } = useRecentActivity(user);

  const handleAnalyzeVideo = (activity: Activity) => {
    if (activity.video) {
      console.log('Opening analysis for video:', activity.video.title);
      setSelectedVideo({
        id: activity.video.id,
        title: activity.video.title,
        views: activity.video.views || 0,
        published_at: activity.video.published_at || activity.video.created_at,
        pillar: activity.video.pillar
      });
      setIsModalOpen(true);
    }
  };

  const handleActivityClick = (activity: Activity) => {
    console.log('Activity clicked:', activity);
    
    if (activity.video) {
      const status = activity.video.status;
      
      if (status === 'published') {
        const pillarId = activity.video.pillar_id;
        
        if (pillarId) {
          navigate(`/pillars?tab=published&pillar=${pillarId}`);
        } else {
          navigate('/pillars?tab=published');
        }
      } else if (status === 'scheduled') {
        navigate('/calendar');
      } else {
        navigate('/ideas');
      }
    }
  };

  const handleViewAllActivity = () => {
    navigate('/pillars?tab=published');
  };

  if (loading) {
    return <LoadingState />;
  }

  const displayLimit = getDisplayLimit();
  const displayActivities = activities.slice(0, displayLimit);

  if (!user?.youtube_channel_id) {
    return (
      <div>
        <div className="text-center py-8">
          <p className="text-gray-400">Connect YouTube to see activity</p>
          <p className="text-sm text-gray-500 mt-2">Your recent video activity will appear here</p>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="space-y-0">
        {displayActivities.length > 0 ? (
          <>
            <ActivityList
              activities={displayActivities}
              onActivityClick={handleActivityClick}
              onAnalyzeVideo={handleAnalyzeVideo}
            />
            <Button
              variant="ghost"
              className="w-full py-3 text-gray-300 hover:text-white hover:bg-purple-800/50 flex items-center justify-center"
              onClick={handleViewAllActivity}
            >
              View All Activity
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-400">No recent activity</p>
            <p className="text-sm text-gray-500 mt-2">Your published videos will appear here</p>
          </div>
        )}
      </div>

      <VideoAnalysisModal
        video={selectedVideo}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        channelAverage={channelAverage}
      />
    </TooltipProvider>
  );
};

export default RecentActivity;
