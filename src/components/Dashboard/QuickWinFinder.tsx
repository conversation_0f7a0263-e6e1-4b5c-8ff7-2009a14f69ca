
import React, { useState, useEffect } from 'react';
import { Target, Info, TrendingUp } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { supabase } from '@/lib/supabase';
import { useNavigate } from 'react-router-dom';

interface QuickWinFinderProps {
  user: any;
}

interface PillarAnalysis {
  id: string;
  name: string;
  avgViews: number;
  videoCount: number;
  percentage: number;
  color: string;
}

const QuickWinFinder = ({ user }: QuickWinFinderProps) => {
  const [analysis, setAnalysis] = useState<{
    bestPillar: PillarAnalysis | null;
    recommendation: string;
    hasOpportunity: boolean;
  }>({ bestPillar: null, recommendation: '', hasOpportunity: false });
  const navigate = useNavigate();

  useEffect(() => {
    if (user?.id && user?.youtube_channel_id) {
      analyzeQuickWins();
    } else {
      setAnalysis({
        bestPillar: null,
        recommendation: 'Connect your YouTube channel to see optimization opportunities.',
        hasOpportunity: false
      });
    }
  }, [user]);

  const analyzeQuickWins = async () => {
    try {
      // Get pillars and their video performance
      const { data: pillars, error: pillarsError } = await supabase
        .from('content_pillars')
        .select('*')
        .eq('user_id', user.id);

      if (pillarsError) throw pillarsError;

      // Get videos from last 30 days - only real YouTube videos
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { data: videos, error: videosError } = await supabase
        .from('videos')
        .select('pillar_id, views, published_at')
        .eq('user_id', user.id)
        .eq('status', 'published')
        .not('youtube_video_id', 'is', null)
        .not('youtube_thumbnail_url', 'is', null)
        .not('published_at', 'is', null)
        .gte('published_at', thirtyDaysAgo.toISOString());

      if (videosError) throw videosError;

      if (!pillars || !videos || videos.length === 0) {
        setAnalysis({
          bestPillar: null,
          recommendation: 'Import your YouTube videos to see optimization opportunities.',
          hasOpportunity: false
        });
        return;
      }

      // Calculate performance for each pillar
      const pillarAnalysis: PillarAnalysis[] = pillars.map(pillar => {
        const pillarVideos = videos.filter(v => v.pillar_id === pillar.id);
        const avgViews = pillarVideos.length > 0 
          ? pillarVideos.reduce((sum, v) => sum + (v.views || 0), 0) / pillarVideos.length 
          : 0;
        const percentage = (pillarVideos.length / videos.length) * 100;

        return {
          id: pillar.id,
          name: pillar.name,
          avgViews,
          videoCount: pillarVideos.length,
          percentage,
          color: pillar.color
        };
      });

      // Find overall average views
      const overallAvgViews = videos.reduce((sum, v) => sum + (v.views || 0), 0) / videos.length;

      // Find best performing pillar that's underutilized
      const bestOpportunity = pillarAnalysis
        .filter(p => p.videoCount > 0) // Only pillars with videos
        .find(p => 
          p.avgViews > overallAvgViews * 1.5 && // 50% better than average
          p.percentage < 30 // Less than 30% of content
        );

      if (bestOpportunity) {
        const suggestedPercentage = Math.min(bestOpportunity.percentage + 15, 40);
        const extraViews = Math.round(
          (bestOpportunity.avgViews - overallAvgViews) * 
          ((suggestedPercentage - bestOpportunity.percentage) / 100) * 
          videos.length * 4 // Estimate monthly impact
        );

        setAnalysis({
          bestPillar: bestOpportunity,
          recommendation: `Your ${bestOpportunity.name} content gets ${Math.round(((bestOpportunity.avgViews / overallAvgViews - 1) * 100))}% more views but only makes up ${Math.round(bestOpportunity.percentage)}% of your videos. Increase to ${Math.round(suggestedPercentage)}% for an estimated ${extraViews.toLocaleString()} extra monthly views.`,
          hasOpportunity: true
        });
      } else {
        setAnalysis({
          bestPillar: null,
          recommendation: 'Your content mix is well-balanced. Keep up the great work!',
          hasOpportunity: false
        });
      }
    } catch (error) {
      console.error('Error analyzing quick wins:', error);
      setAnalysis({
        bestPillar: null,
        recommendation: 'Unable to analyze performance data.',
        hasOpportunity: false
      });
    }
  };

  const handleViewPerformance = () => {
    navigate('/pillars');
  };

  return (
    <Card className="dashboard-card" style={{ backgroundColor: analysis.hasOpportunity ? '#37BEB0' : undefined }}>
      <CardHeader>
        <CardTitle className={`flex items-center justify-between ${analysis.hasOpportunity ? 'text-white' : 'text-white'}`}>
          <div className="flex items-center">
            <Target className="w-5 h-5 mr-2" />
            🎯 Quick Win Opportunity
          </div>
          <div className="relative group">
            <Info className="w-4 h-4 cursor-help" />
            <div className="absolute right-0 top-6 w-48 p-2 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
              Based on your last 30 published videos
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className={`mb-4 ${analysis.hasOpportunity ? 'text-white' : 'text-gray-300'}`}>
          {analysis.recommendation}
        </p>
        
        {analysis.hasOpportunity && analysis.bestPillar && (
          <Button 
            onClick={handleViewPerformance}
            variant="outline"
            className="border-white text-white hover:bg-white hover:text-teal"
          >
            <TrendingUp className="w-4 h-4 mr-2" />
            View {analysis.bestPillar.name} Performance
          </Button>
        )}

        {!analysis.hasOpportunity && (
          <Button 
            onClick={handleViewPerformance}
            variant="outline"
            className="border-teal text-teal hover:bg-teal hover:text-white"
          >
            <TrendingUp className="w-4 h-4 mr-2" />
            View All Pillars
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default QuickWinFinder;
