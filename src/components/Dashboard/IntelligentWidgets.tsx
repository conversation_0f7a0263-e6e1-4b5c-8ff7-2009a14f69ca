
import React from 'react';
import ContentHealthWidget from './ContentHealthWidget';
import InsightsNotificationCard from './InsightsNotificationCard';

interface IntelligentWidgetsProps {
  pillars: any[];
  videoCount: number;
  onTabChange: (tab: string) => void;
}

const IntelligentWidgets = ({ pillars, videoCount, onTabChange }: IntelligentWidgetsProps) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <ContentHealthWidget />
      <InsightsNotificationCard 
        pillars={pillars}
        videoCount={videoCount}
        onViewInsights={() => onTabChange('insights')}
      />
    </div>
  );
};

export default IntelligentWidgets;
