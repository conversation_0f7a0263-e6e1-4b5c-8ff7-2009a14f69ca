import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { BarChart3, Target, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface InsightsCardProps {
  insights: any[];
  title?: string;
}

const InsightsCard: React.FC<InsightsCardProps> = ({ 
  insights = [], 
  title = "Insights" 
}) => {
  const navigate = useNavigate();
  
  const handleViewAnalytics = () => {
    navigate('/analytics');
  };

  return (
    <Card className="rounded-xl bg-purple-900/40 border-purple-800/50">
      <CardHeader className="flex flex-row items-center justify-between bg-purple-900/60 rounded-t-xl border-b border-purple-800/50">
        <div>
          <h2 className="text-xl font-semibold text-white">{title}</h2>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            className="border-cyan-500 text-cyan-500 hover:bg-cyan-500 hover:text-white"
            onClick={handleViewAnalytics}
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            View Analytics
          </Button>
          <Button
            size="sm"
            className="bg-cyan-500 hover:bg-cyan-600 text-white"
            onClick={() => navigate('/pillars')}
          >
            <Target className="w-4 h-4 mr-2" />
            Adjust Strategy
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {insights.length > 0 ? (
          <div className="p-4 space-y-4">
            {insights.map((insight, index) => (
              <div key={index} className="p-4 bg-gray-700/20 rounded-lg border border-gray-600/30">
                {/* Render your insight content here */}
                {insight.content || 
                  <div className="flex items-center">
                    {insight.icon && <span className="mr-2">{insight.icon}</span>}
                    <div>
                      <h4 className="font-medium text-white">{insight.title}</h4>
                      <p className="text-sm text-gray-400">{insight.description}</p>
                    </div>
                  </div>
                }
              </div>
            ))}
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleViewAnalytics}
              className="mt-4 border-teal text-teal hover:bg-teal hover:text-white"
            >
              View All Insights
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        ) : (
          <div className="text-center py-8">
            <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-300 mb-2">
              No Insights Available
            </h3>
            <p className="text-gray-400 text-sm mb-6">
              Add more content and connect your channels to get personalized insights.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default InsightsCard;
