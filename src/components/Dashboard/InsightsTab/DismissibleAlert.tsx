
import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { AlertType } from '@/hooks/useDismissedAlerts';

interface DismissibleAlertProps {
  alertType: AlertType;
  icon: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  onDismiss: (alertType: AlertType) => void;
}

const DismissibleAlert = ({ 
  alertType, 
  icon, 
  children, 
  className,
  onDismiss 
}: DismissibleAlertProps) => {
  const handleDismiss = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDismiss(alertType);
  };

  return (
    <Alert className={`relative ${className}`}>
      <div className="flex items-start justify-between gap-4">
        <div className="flex items-start gap-3 flex-1 min-w-0">
          {icon}
          <AlertDescription className="flex-1">
            {children}
          </AlertDescription>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 hover:bg-white/10 flex-shrink-0"
          onClick={handleDismiss}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </Alert>
  );
};

export default DismissibleAlert;
