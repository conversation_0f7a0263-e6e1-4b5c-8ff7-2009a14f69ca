
import React from 'react';
import { useDismissedAlerts } from '@/hooks/useDismissedAlerts';
import TrialAlert from './alerts/TrialAlert';
import YouTubeConnectionAlert from './alerts/YouTubeConnectionAlert';
import YouTubeTokenAlert from './alerts/YouTubeTokenAlert';
import DataStalenessAlert from './alerts/DataStalenessAlert';
import SyncErrorAlert from './alerts/SyncErrorAlert';
import PillarBalanceAlert from './alerts/PillarBalanceAlert';
import StrategyDriftAlert from './alerts/StrategyDriftAlert';

interface PillarData {
  id: string;
  name: string;
  target_percentage: number;
  actual_percentage: number;
  color: string;
  video_count?: number;
  avg_views?: number;
}

interface ConsolidatedInsightsProps {
  pillars: PillarData[];
  videoCount: number;
  user?: any;
}

const ConsolidatedInsights = ({ pillars, videoCount, user }: ConsolidatedInsightsProps) => {
  const { loading: dismissedLoading } = useDismissedAlerts();

  if (dismissedLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="w-6 h-6 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <TrialAlert user={user} />
      <YouTubeConnectionAlert user={user} />
      <YouTubeTokenAlert user={user} />
      <DataStalenessAlert user={user} />
      <SyncErrorAlert user={user} />
      <PillarBalanceAlert pillars={pillars} />
      <StrategyDriftAlert pillars={pillars} />
    </div>
  );
};

export default ConsolidatedInsights;
