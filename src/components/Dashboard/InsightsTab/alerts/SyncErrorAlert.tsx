import React from 'react';
import { AlertTriangle } from 'lucide-react';
import DismissibleAlert from '../DismissibleAlert';
import { useDismissedAlerts } from '@/hooks/useDismissedAlerts';
import { useDashboardStats } from '@/hooks/useDashboardStats';

interface SyncErrorAlertProps {
  user: any;
}

const SyncErrorAlert = ({ user }: SyncErrorAlertProps) => {
  const { dismissAlert, isAlertDismissed } = useDismissedAlerts();
  const { lastSyncError } = useDashboardStats({ user });

  // Don't show if no error or dismissed
  if (!lastSyncError || isAlertDismissed('sync_error')) {
    return null;
  }

  return (
    <DismissibleAlert
      alertType="sync_error"
      icon={<AlertTriangle className="h-4 w-4 text-red-400 mt-0.5 flex-shrink-0" />}
      className="bg-red-500/20 border-red-500 text-white"
      onDismiss={dismissAlert}
    >
      <div className="flex-1 min-w-0">
        <strong>Sync Error:</strong>{' '}
        <span className="text-gray-300">{lastSyncError}</span>
      </div>
    </DismissibleAlert>
  );
};

export default SyncErrorAlert;
