import React from 'react';
import { AlertTriangle } from 'lucide-react';
import DismissibleAlert from '../DismissibleAlert';
import { useDismissedAlerts } from '@/hooks/useDismissedAlerts';
import { useYouTubeConnection } from '@/hooks/useYouTubeConnection';

interface DataStalenessAlertProps {
  user: any;
}

const DataStalenessAlert = ({ user }: DataStalenessAlertProps) => {
  const { dismissAlert, isAlertDismissed } = useDismissedAlerts();
  const { youtubeData } = useYouTubeConnection();

  const hasChannel = !!youtubeData?.youtube_channel_id;
  const preferences = user?.preferences || {};
  const lastSyncTime = user?.last_youtube_sync || preferences.last_full_sync;

  const isDataStale = () => {
    if (!lastSyncTime) return true;
    const lastSync = new Date(lastSyncTime);
    const now = new Date();
    const hoursSinceSync = (now.getTime() - lastSync.getTime()) / (1000 * 60 * 60);
    return hoursSinceSync > 24; // Consider stale if older than 24 hours
  };

  // Don't show if no channel, data is fresh, or dismissed
  if (!hasChannel || !isDataStale() || isAlertDismissed('data_staleness')) {
    return null;
  }

  return (
    <DismissibleAlert
      alertType="data_staleness"
      icon={<AlertTriangle className="h-4 w-4 text-yellow-400 mt-0.5 flex-shrink-0" />}
      className="bg-yellow-500/20 border-yellow-500 text-white"
      onDismiss={dismissAlert}
    >
      <div className="flex-1 min-w-0">
        <strong>Data May Be Outdated:</strong>{' '}
        <span className="text-gray-300">
          Your data hasn't been synced recently. Click "Sync YouTube" in the sidebar for the latest metrics.
        </span>
      </div>
    </DismissibleAlert>
  );
};

export default DataStalenessAlert;
