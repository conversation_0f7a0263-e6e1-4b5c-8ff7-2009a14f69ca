
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Youtube } from 'lucide-react';
import { Button } from '@/components/ui/button';
import DismissibleAlert from '../DismissibleAlert';
import { useDismissedAlerts } from '@/hooks/useDismissedAlerts';

interface YouTubeConnectionAlertProps {
  user: any;
}

const YouTubeConnectionAlert = ({ user }: YouTubeConnectionAlertProps) => {
  const navigate = useNavigate();
  const { dismissAlert, isAlertDismissed } = useDismissedAlerts();

  const isYouTubeConnected = !!user?.youtube_channel_id;

  if (isYouTubeConnected || isAlertDismissed('youtube_connection')) {
    return null;
  }

  return (
    <DismissibleAlert
      alertType="youtube_connection"
      icon={<Youtube className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0 animate-pulse" />}
      className="bg-red-500/20 border-red-500 text-white animate-fade-in"
      onDismiss={dismissAlert}
    >
      <div className="flex items-center justify-between w-full">
        <span className="flex-1 min-w-0">
          <strong>⚡ Connect YouTube:</strong> Unlock powerful insights by connecting your channel now!
        </span>
        <Button 
          size="sm" 
          variant="outline" 
          onClick={() => navigate('/settings?tab=youtube')}
          className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white ml-4 flex-shrink-0 font-semibold animate-pulse hover:animate-none transition-all"
        >
          🚀 Connect Now!
        </Button>
      </div>
    </DismissibleAlert>
  );
};

export default YouTubeConnectionAlert;
