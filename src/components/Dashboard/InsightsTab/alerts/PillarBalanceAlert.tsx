import React from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import DismissibleAlert from '../DismissibleAlert';
import { useDismissedAlerts } from '@/hooks/useDismissedAlerts';

interface PillarData {
  id: string;
  name: string;
  target_percentage: number;
  actual_percentage: number;
  color: string;
  video_count?: number;
  avg_views?: number;
}

interface PillarBalanceAlertProps {
  pillars: PillarData[];
}

const PillarBalanceAlert = ({ pillars }: PillarBalanceAlertProps) => {
  const navigate = useNavigate();
  const { dismissAlert, isAlertDismissed } = useDismissedAlerts();

  const totalPercentage = pillars.reduce((sum, pillar) => sum + pillar.target_percentage, 0);
  const hasBalanceIssue = Math.abs(totalPercentage - 100) > 5;

  const handleAdjustPillars = () => navigate('/pillars');

  if (!hasBalanceIssue || pillars.length === 0 || isAlertDismissed('pillar_balance')) {
    return null;
  }

  return (
    <DismissibleAlert
      alertType="pillar_balance"
      icon={<AlertTriangle className="h-4 w-4 text-orange mt-0.5 flex-shrink-0" />}
      className="bg-orange/20 border-orange text-white"
      onDismiss={dismissAlert}
    >
      <div className="flex items-center justify-between w-full">
        <span className="flex-1 min-w-0">
          <strong>Pillar Balance Issue:</strong> Your pillar target percentages total {totalPercentage}%. Consider adjusting them to total 100% for optimal balance.
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={handleAdjustPillars}
          className="border-teal text-teal hover:bg-teal hover:text-white ml-4 flex-shrink-0"
        >
          Fix Balance
        </Button>
      </div>
    </DismissibleAlert>
  );
};

export default PillarBalanceAlert;
