import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Crown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import DismissibleAlert from '../DismissibleAlert';
import { useDismissedAlerts } from '@/hooks/useDismissedAlerts';

interface TrialAlertProps {
  user: any;
}

const TrialAlert = ({ user }: TrialAlertProps) => {
  const navigate = useNavigate();
  const { dismissAlert, isAlertDismissed } = useDismissedAlerts();

  const getTrialDaysLeft = () => {
    if (!user?.current_period_end) {
      return 7; // fallback
    }
    
    const trialEnd = new Date(user.current_period_end);
    const now = new Date();
    const diffTime = trialEnd.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const shouldShowTrialAlert = user && 
    user.subscription_status === 'trialing' && 
    (!user.subscription_tier || user.subscription_tier === 'starter') && 
    getTrialDaysLeft() > 0;

  const trialDaysLeft = shouldShowTrialAlert ? getTrialDaysLeft() : 0;

  if (!shouldShowTrialAlert || isAlertDismissed('trial_ending')) {
    return null;
  }

  return (
    <DismissibleAlert
      alertType="trial_ending"
      icon={<Crown className="h-4 w-4 text-orange mt-0.5 flex-shrink-0" />}
      className="bg-orange/20 border-orange text-white"
      onDismiss={dismissAlert}
    >
      <div className="flex items-center justify-between w-full">
        <span className="flex-1 min-w-0">
          <strong>Trial Ending Soon:</strong> {trialDaysLeft} {trialDaysLeft === 1 ? 'day' : 'days'} left in your trial - Upgrade now to continue enjoying all features!
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate('/pricing')}
          className="border-teal text-teal hover:bg-teal hover:text-white ml-4 flex-shrink-0"
        >
          View Plans
        </Button>
      </div>
    </DismissibleAlert>
  );
};

export default TrialAlert;
