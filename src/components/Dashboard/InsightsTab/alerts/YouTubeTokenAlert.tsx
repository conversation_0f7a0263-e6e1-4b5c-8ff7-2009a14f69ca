import React, { useState } from 'react';
import { Alert<PERSON>riangle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import DismissibleAlert from '../DismissibleAlert';
import { useDismissedAlerts } from '@/hooks/useDismissedAlerts';
import { useYouTubeConnection } from '@/hooks/useYouTubeConnection';

interface YouTubeTokenAlertProps {
  user: any;
}

const YouTubeTokenAlert = ({ user }: YouTubeTokenAlertProps) => {
  const [isCheckingToken, setIsCheckingToken] = useState(false);
  const { dismissAlert, isAlertDismissed } = useDismissedAlerts();
  const { youtubeData, isConnected, tokenStatus, checkTokenStatus } = useYouTubeConnection();

  const hasChannel = !!youtubeData?.youtube_channel_id;
  const hasValidToken = tokenStatus.isValid;

  const handleTokenRefresh = async () => {
    setIsCheckingToken(true);
    toast.info('Checking YouTube token status...');
    try {
      await checkTokenStatus();
      if (tokenStatus.isValid) {
        toast.success('YouTube connection is valid!');
      } else {
        toast.error(tokenStatus.error || 'YouTube token is invalid or expired.');
      }
    } catch (error) {
      toast.error('Failed to check YouTube token status.');
    } finally {
      setIsCheckingToken(false);
    }
  };

  // Don't show if no channel, valid token, or dismissed
  if (!hasChannel || hasValidToken || isAlertDismissed('youtube_token_issue')) {
    return null;
  }

  return (
    <DismissibleAlert
      alertType="youtube_token_issue"
      icon={<AlertTriangle className="h-4 w-4 text-orange mt-0.5 flex-shrink-0" />}
      className="bg-orange/20 border-orange text-white"
      onDismiss={dismissAlert}
    >
      <div className="flex items-center justify-between w-full">
        <div className="flex-1 min-w-0">
          <strong>YouTube Connection Issue:</strong>{' '}
          <span className="text-gray-300">
            {isConnected 
              ? 'Connection working but token may need refresh for full features.' 
              : 'Your YouTube token has expired. Please reconnect for full functionality.'
            }
          </span>
        </div>
        <Button
          onClick={handleTokenRefresh}
          size="sm"
          className="bg-teal hover:bg-teal/90 text-white ml-4 flex-shrink-0"
          disabled={isCheckingToken}
        >
          <RefreshCw className={`w-4 h-4 mr-1 ${isCheckingToken ? 'animate-spin' : ''}`} />
          {isCheckingToken ? 'Checking...' : 'Check Status'}
        </Button>
      </div>
    </DismissibleAlert>
  );
};

export default YouTubeTokenAlert;
