import React from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import DismissibleAlert from '../DismissibleAlert';
import { useDismissedAlerts } from '@/hooks/useDismissedAlerts';

interface PillarData {
  id: string;
  name: string;
  target_percentage: number;
  actual_percentage: number;
  color: string;
  video_count?: number;
  avg_views?: number;
}

interface StrategyDriftAlertProps {
  pillars: PillarData[];
}

const StrategyDriftAlert = ({ pillars }: StrategyDriftAlertProps) => {
  const navigate = useNavigate();
  const { dismissAlert, isAlertDismissed } = useDismissedAlerts();

  const pillarGaps = pillars.map(pillar => ({
    ...pillar,
    gap: pillar.target_percentage - pillar.actual_percentage,
    percentageGap: Math.abs(pillar.target_percentage - pillar.actual_percentage)
  }));

  const driftPillars = pillarGaps.filter(p => p.percentageGap > 10);

  const handleAdjustPillars = () => navigate('/pillars');

  if (driftPillars.length === 0 || isAlertDismissed('strategy_drift')) {
    return null;
  }

  return (
    <DismissibleAlert
      alertType="strategy_drift"
      icon={<AlertTriangle className="h-4 w-4 text-orange mt-0.5 flex-shrink-0" />}
      className="bg-orange/20 border-orange text-white"
      onDismiss={dismissAlert}
    >
      <div className="flex items-center justify-between w-full">
        <span className="flex-1 min-w-0">
          <strong>Strategy Drift Detected:</strong> {driftPillars.length} content type{driftPillars.length > 1 ? 's' : ''} need rebalancing
        </span>
        <Button 
          size="sm" 
          variant="outline" 
          onClick={handleAdjustPillars}
          className="border-teal text-teal hover:bg-teal hover:text-white ml-4 flex-shrink-0"
        >
          Adjust Strategy
        </Button>
      </div>
    </DismissibleAlert>
  );
};

export default StrategyDriftAlert;
