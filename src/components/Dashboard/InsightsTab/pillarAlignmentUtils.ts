
import { PillarAlignment } from './types';

export const calculatePillarAlignment = (
  pillars: Array<{
    id: string;
    name: string;
    target_percentage: number;
    actual_percentage: number;
    color: string;
    avg_views?: number;
    video_count?: number;
  }>
): PillarAlignment[] => {
  // Calculate average views across all pillars
  const totalAvgViews = pillars.reduce((sum, p) => sum + (p.avg_views || 0), 0) / pillars.length;
  
  // Find the best performing pillar
  const bestPerformingPillar = pillars.reduce((best, current) => 
    (current.avg_views || 0) > (best.avg_views || 0) ? current : best, 
    pillars[0]
  );

  return pillars.map(pillar => {
    const diff = pillar.actual_percentage - pillar.target_percentage;
    const absDiff = Math.abs(diff);
    const isTopPerformer = pillar.id === bestPerformingPillar?.id;
    const performsAboveAverage = (pillar.avg_views || 0) > totalAvgViews * 1.2; // 20% above average
    
    let status: 'over' | 'under' | 'on-target' = 'on-target';
    let severity: 'urgent' | 'moderate' | 'good' = 'good';
    let recommendation = 'Content balance is on target.';

    if (absDiff <= 5) {
      status = 'on-target';
      severity = 'good';
    } else if (diff > 0) {
      // Over target
      status = 'over';
      
      if (isTopPerformer || performsAboveAverage) {
        // If it's the best performer, don't penalize as heavily
        if (absDiff > 25) {
          severity = 'moderate';
          recommendation = `${pillar.name} is your top performer! Consider slight rebalancing only if other pillars need attention.`;
        } else {
          severity = 'good';
          recommendation = `${pillar.name} performs well - this imbalance might be strategic. Monitor other pillars.`;
        }
      } else {
        // Normal over-target logic for poor performers
        severity = absDiff > 15 ? 'urgent' : 'moderate';
        recommendation = `Reduce by ${Math.round(absDiff)}% to optimize balance and try other content types.`;
      }
    } else {
      // Under target
      status = 'under';
      severity = absDiff > 15 ? 'urgent' : 'moderate';
      
      if (performsAboveAverage) {
        recommendation = `${pillar.name} performs well but needs more content. Increase by ${Math.round(absDiff)}% for better balance.`;
      } else {
        recommendation = `Increase by ${Math.round(absDiff)}% to reach target. Consider improving content quality.`;
      }
    }

    return {
      id: pillar.id,
      name: pillar.name,
      target: pillar.target_percentage,
      actual: pillar.actual_percentage,
      color: pillar.color,
      status,
      severity,
      recommendation,
      isTopPerformer,
      performsAboveAverage
    };
  });
};
