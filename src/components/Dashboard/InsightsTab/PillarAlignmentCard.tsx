
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { CheckCircle, AlertTriangle, TrendingUp, Star } from 'lucide-react';
import { PillarAlignment } from './types';

interface PillarAlignmentCardProps {
  pillar: PillarAlignment;
  onAdjustStrategy: (pillar: PillarAlignment) => void;
  onMonitorBalance: (pillar: PillarAlignment) => void;
}

const PillarAlignmentCard = ({ pillar, onAdjustStrategy, onMonitorBalance }: PillarAlignmentCardProps) => {
  const getStatusIcon = () => {
    if (pillar.isTopPerformer && pillar.status === 'over') {
      return <Star className="w-4 h-4 text-yellow-400" />;
    }
    if (pillar.status === 'on-target') return <CheckCircle className="w-4 h-4 text-green-400" />;
    if (pillar.severity === 'urgent') return <AlertTriangle className="w-4 h-4 text-red-400" />;
    return <TrendingUp className="w-4 h-4 text-orange" />;
  };

  const getStatusText = () => {
    if (pillar.status === 'on-target') return 'On Target';
    const diff = pillar.actual - pillar.target;
    const prefix = diff > 0 ? '+' : '';
    
    if (pillar.isTopPerformer && pillar.status === 'over') {
      return `${prefix}${diff.toFixed(1)}% (Top Performer)`;
    }
    
    return `${prefix}${diff.toFixed(1)}%`;
  };

  const getCardBorder = () => {
    if (pillar.isTopPerformer && pillar.status === 'over') {
      return 'border-yellow-400/50 border-2';
    }
    if (pillar.severity === 'urgent') return 'border-red-400/50 border-2';
    if (pillar.severity === 'moderate') return 'border-orange/50 border-2';
    return 'border-green-400/50 border-2';
  };

  const getButtonText = () => {
    if (pillar.isTopPerformer && pillar.status === 'over') {
      return 'Monitor Balance';
    }
    return 'Adjust Strategy';
  };

  return (
    <Card className={`dashboard-card ${getCardBorder()}`}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center justify-between text-white text-base">
          <div className="flex items-center space-x-2">
            <div
              className="w-5 h-5 rounded-full border-2 border-gray-600"
              style={{ backgroundColor: pillar.color }}
            />
            <span>{pillar.name}</span>
            {pillar.isTopPerformer && (
              <Star className="w-4 h-4 text-yellow-400 fill-current" />
            )}
          </div>
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <span className={`text-sm ${
              pillar.isTopPerformer && pillar.status === 'over' ? 'text-yellow-400' :
              pillar.severity === 'urgent' ? 'text-red-400' :
              pillar.severity === 'moderate' ? 'text-orange' : 'text-green-400'
            }`}>
              {getStatusText()}
            </span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="space-y-1">
          <div className="flex justify-between text-xs text-gray-400">
            <span>Target: {pillar.target}%</span>
            <span>Actual: {pillar.actual}%</span>
          </div>
          <Progress 
            value={pillar.actual} 
            className="h-2"
          />
          {/* Target indicator line */}
          <div className="relative h-1">
            <div 
              className="absolute w-1 h-4 bg-white/80 rounded-sm -top-1"
              style={{ left: `${pillar.target}%` }}
            />
          </div>
        </div>
        
        <p className="text-xs text-gray-300">{pillar.recommendation}</p>
        
        {pillar.severity !== 'good' && (
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => {
              if (pillar.isTopPerformer && pillar.status === 'over') {
                onMonitorBalance(pillar);
              } else {
                onAdjustStrategy(pillar);
              }
            }}
            className={`text-xs h-8 ${
              pillar.isTopPerformer && pillar.status === 'over'
                ? 'border-yellow-400/50 text-yellow-400 hover:bg-yellow-400/10'
                : pillar.severity === 'urgent' 
                  ? 'border-red-400/50 text-red-400 hover:bg-red-400/10' 
                  : 'border-orange/50 text-orange hover:bg-orange/10'
            }`}
          >
            {getButtonText()}
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default PillarAlignmentCard;
