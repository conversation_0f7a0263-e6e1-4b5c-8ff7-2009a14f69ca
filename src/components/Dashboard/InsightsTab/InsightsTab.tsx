
import React from 'react';
import ConsolidatedInsights from './ConsolidatedInsights';
import { InsightsTabProps } from './types';

const InsightsTab = ({ user, pillars, videoCount }: InsightsTabProps) => {
  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-white">🔔 Notifications & Insights</h2>
        <p className="text-gray-300">All your alerts and strategic recommendations in one place</p>
      </div>
      
      <ConsolidatedInsights pillars={pillars} videoCount={videoCount} user={user} />
    </div>
  );
};

export default InsightsTab;
