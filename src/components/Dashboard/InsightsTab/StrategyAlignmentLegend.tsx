
import React from 'react';

const StrategyAlignmentLegend = () => {
  return (
    <div className="flex items-center space-x-4 text-xs text-gray-400">
      <div className="flex items-center space-x-1">
        <div className="w-4 h-4 bg-red-400 rounded-full border border-red-300"></div>
        <span>Urgent</span>
      </div>
      <div className="flex items-center space-x-1">
        <div className="w-4 h-4 bg-orange rounded-full border border-orange-300"></div>
        <span>Moderate</span>
      </div>
      <div className="flex items-center space-x-1">
        <div className="w-4 h-4 bg-yellow-400 rounded-full border border-yellow-300"></div>
        <span>Top Performer</span>
      </div>
      <div className="flex items-center space-x-1">
        <div className="w-4 h-4 bg-green-400 rounded-full border border-green-300"></div>
        <span>On Target</span>
      </div>
    </div>
  );
};

export default StrategyAlignmentLegend;
