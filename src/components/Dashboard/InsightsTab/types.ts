
export interface InsightsTabProps {
  user: any;
  pillars: Array<{
    id: string;
    name: string;
    target_percentage: number;
    actual_percentage: number;
    color: string;
  }>;
  videoCount: number;
}

export interface PillarAlignment {
  id: string;
  name: string;
  target: number;
  actual: number;
  color: string;
  status: 'over' | 'under' | 'on-target';
  severity: 'urgent' | 'moderate' | 'good';
  recommendation: string;
  isTopPerformer?: boolean;
  performsAboveAverage?: boolean;
}

export interface PerformanceAlert {
  id: number;
  type: string;
  icon: any;
  title: string;
  description: string;
  action: string;
  severity: 'warning' | 'success' | 'info';
}
