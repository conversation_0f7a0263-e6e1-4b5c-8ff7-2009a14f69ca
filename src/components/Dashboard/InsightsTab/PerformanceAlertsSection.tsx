import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Calendar, DollarSign } from 'lucide-react';

// Define the PerformanceAlert type if it's not imported
interface PerformanceAlert {
  id: number;
  type: string;
  icon: any;
  title: string;
  description: string;
  action: string;
  severity: 'warning' | 'success' | 'info';
}

const PerformanceAlertsSection = () => {
  const navigate = useNavigate();

  const performanceAlerts: PerformanceAlert[] = [
    {
      id: 1,
      type: 'underperforming',
      icon: AlertTriangle,
      title: '3 videos underperforming',
      description: 'Recent uploads 40% below average',
      action: 'Analyze',
      severity: 'warning'
    },
    {
      id: 2,
      type: 'trend',
      icon: Calendar,
      title: 'Tuesday uploads +40% views',
      description: 'Consistent outperformance pattern',
      action: 'Schedule more',
      severity: 'success'
    },
    {
      id: 3,
      type: 'revenue',
      icon: DollarSign,
      title: 'Equipment Reviews up 60%',
      description: 'Higher engagement and revenue',
      action: 'Create more',
      severity: 'success'
    }
  ];

  const handleAlertAction = (alert: PerformanceAlert) => {
    switch (alert.type) {
      case 'underperforming':
        navigate('/analytics');
        break;
      case 'trend':
        navigate('/calendar');
        break;
      case 'revenue':
        navigate('/ideas');
        break;
      default:
        break;
    }
  };

  const getSeverityStyle = (severity: string) => {
    switch (severity) {
      case 'warning':
        return {
          iconBg: 'bg-orange/20',
          iconColor: 'text-orange',
          buttonStyle: 'border-orange/50 text-orange hover:bg-orange/10'
        };
      case 'success':
        return {
          iconBg: 'bg-green/20',
          iconColor: 'text-green',
          buttonStyle: 'border-green/50 text-green hover:bg-green/10'
        };
      default:
        return {
          iconBg: 'bg-gray-600',
          iconColor: 'text-gray-300',
          buttonStyle: 'border-gray-500 text-gray-300 hover:bg-gray-500/10'
        };
    }
  };

  return (
    <div className="space-y-3">
      {performanceAlerts.map((alert) => {
        const Icon = alert.icon;
        const styles = getSeverityStyle(alert.severity);

        return (
          <div key={alert.id} className="p-4 bg-purple-900/80 border border-purple-800 rounded-lg">
            <div className="flex items-start space-x-3">
              <div className={`p-2 rounded ${styles.iconBg}`}>
                <Icon className={`w-4 h-4 ${styles.iconColor}`} />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-white text-sm mb-1">{alert.title}</h4>
                <p className="text-xs text-gray-300 mb-2">{alert.description}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleAlertAction(alert)}
                  className={`text-xs h-7 ${styles.buttonStyle}`}
                >
                  {alert.action}
                </Button>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PerformanceAlertsSection;
