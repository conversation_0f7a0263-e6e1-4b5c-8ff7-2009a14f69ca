
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface PillarData {
  id: string;
  name: string;
  target_percentage: number;
  actual_percentage: number;
  color: string;
  video_count?: number;
  avg_views?: number;
}

interface ContentBalanceOverviewProps {
  pillars: PillarData[];
}

const ContentBalanceOverview = ({ pillars }: ContentBalanceOverviewProps) => {
  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white">Content Balance Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {pillars.map(pillar => {
            const gap = pillar.target_percentage - pillar.actual_percentage;
            const status = Math.abs(gap) <= 5 ? 'balanced' : gap > 0 ? 'under' : 'over';
            const statusColor = status === 'balanced' ? 'text-green-400' : 
                               status === 'under' ? 'text-red-400' : 'text-orange';
            const statusText = status === 'balanced' ? 'On Target' :
                              status === 'under' ? `${Math.abs(gap)}% Under` : `${Math.abs(gap)}% Over`;

            return (
              <div key={pillar.id} className="flex items-center justify-between p-3 bg-gray-700/50 rounded">
                <div className="flex items-center space-x-3">
                  <div 
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: pillar.color }}
                  />
                  <span className="text-white font-medium">{pillar.name}</span>
                  <span className="text-gray-400 text-sm">
                    {pillar.actual_percentage}% / {pillar.target_percentage}%
                  </span>
                </div>
                <span className={`text-sm font-medium ${statusColor}`}>
                  {statusText}
                </span>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default ContentBalanceOverview;
