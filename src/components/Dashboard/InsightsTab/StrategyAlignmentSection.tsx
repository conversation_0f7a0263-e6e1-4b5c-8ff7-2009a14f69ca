
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { PillarAlignment } from './types';
import { calculatePillarAlignment } from './pillarAlignmentUtils';
import PillarAlignmentCard from './PillarAlignmentCard';
import StrategyAlignmentLegend from './StrategyAlignmentLegend';

interface StrategyAlignmentSectionProps {
  pillars: Array<{
    id: string;
    name: string;
    target_percentage: number;
    actual_percentage: number;
    color: string;
    avg_views?: number;
    video_count?: number;
  }>;
}

const StrategyAlignmentSection = ({ pillars }: StrategyAlignmentSectionProps) => {
  const navigate = useNavigate();

  const pillarAlignment = calculatePillarAlignment(pillars);

  const handleAdjustStrategy = (pillar: PillarAlignment) => {
    if (pillar.status === 'under') {
      // Navigate to ideas page to create content for this pillar
      navigate(`/ideas?pillar=${pillar.id}&action=create`);
    } else if (pillar.status === 'over') {
      // Navigate to calendar to review and rebalance content
      navigate('/calendar');
    } else {
      // Navigate to pillars page to adjust targets
      navigate('/pillars');
    }
  };

  const handleMonitorBalance = (pillar: PillarAlignment) => {
    // Navigate to analytics for performance monitoring
    navigate('/analytics');
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-white">📊 Content Balance by Pillar</h2>
        <StrategyAlignmentLegend />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {pillarAlignment.map((pillar) => (
          <PillarAlignmentCard
            key={pillar.id}
            pillar={pillar}
            onAdjustStrategy={handleAdjustStrategy}
            onMonitorBalance={handleMonitorBalance}
          />
        ))}
      </div>
    </div>
  );
};

export default StrategyAlignmentSection;
