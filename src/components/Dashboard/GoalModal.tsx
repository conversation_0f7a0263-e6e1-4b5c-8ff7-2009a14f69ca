
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CalendarIcon, Target } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { supabase } from '@/lib/supabase';

interface GoalModalProps {
  isOpen: boolean;
  onClose: () => void;
  onGoalCreated: () => void;
  user: any;
  editGoal?: any;
}

const GoalModal = ({ isOpen, onClose, onGoalCreated, user, editGoal }: GoalModalProps) => {
  const [goalType, setGoalType] = useState('subscribers');
  const [currentValue, setCurrentValue] = useState('');
  const [targetValue, setTargetValue] = useState('');
  const [deadline, setDeadline] = useState<Date>();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (editGoal) {
      setGoalType(editGoal.type);
      setCurrentValue(editGoal.current_value?.toString() || '');
      setTargetValue(editGoal.target_value?.toString() || '');
      setDeadline(editGoal.end_date ? new Date(editGoal.end_date) : undefined);
    }
  }, [editGoal]);

  // Separate effect for auto-filling current value based on goal type
  useEffect(() => {
    if (!editGoal) {
      console.log('Auto-filling current value for goal type:', goalType);
      console.log('User data:', user);

      if (goalType === 'subscribers' && user?.youtube_subscriber_baseline) {
        console.log('Setting subscribers baseline:', user.youtube_subscriber_baseline);
        setCurrentValue(user.youtube_subscriber_baseline.toString());
      } else if (goalType === 'monthly_views') {
        // For monthly views, try to get current monthly views from preferences
        const preferences = user?.preferences || {};
        const monthlyViews = preferences.youtube_monthly_views || 0;
        console.log('Setting monthly views from preferences:', monthlyViews);
        setCurrentValue(monthlyViews.toString());
      } else if (goalType === 'revenue') {
        // For revenue, start with 0 if no baseline available
        console.log('Setting revenue to 0');
        setCurrentValue('0');
      }
    }
  }, [goalType, user, editGoal]);

  const goalTypes = [
    { value: 'subscribers', label: 'Subscribers', description: 'YouTube channel subscribers' },
    { value: 'monthly_views', label: 'Monthly Views', description: 'Views in the last 30 days' },
    { value: 'revenue', label: 'Revenue', description: 'Monthly revenue target' }
  ];

  const isValidGoal = () => {
    // Use parseFloat for all goal types to handle decimal values properly
    const current = parseFloat(currentValue);
    const target = parseFloat(targetValue);

    console.log('Validation check:', {
      goalType,
      currentValue,
      targetValue,
      deadline,
      parsedCurrent: current,
      parsedTarget: target
    });

    // Check if values are valid numbers and not empty
    if (isNaN(current) || isNaN(target) || currentValue.trim() === '' || targetValue.trim() === '') {
      console.log('❌ Invalid numbers or empty values');
      return false;
    }

    // Values must be non-negative
    if (current < 0 || target < 0) {
      console.log('❌ Negative values');
      return false;
    }

    // Target must be greater than current
    if (target <= current) {
      console.log('❌ Target not greater than current');
      return false;
    }

    // Must have a deadline
    if (!deadline) {
      console.log('❌ No deadline set');
      return false;
    }

    console.log('✅ Validation passed');
    return true;
  };

  const handleSubmit = async () => {
    console.log('Goal submission:', { goalType, currentValue, targetValue, deadline });
    console.log('User object:', user);

    // Check current auth state
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    console.log('🔐 Auth Debug:', {
      session: !!session,
      sessionError,
      sessionUserId: session?.user?.id,
      userPropId: user?.id,
      userObject: user,
      idsMatch: session?.user?.id === user?.id
    });

    if (!user?.id) {
      console.error('❌ No user ID available');
      toast.error("Authentication error. Please refresh and try again.");
      return;
    }

    if (!isValidGoal()) {
      console.log('❌ Validation failed');
      toast.error("Please fill in all fields with valid values");
      return;
    }

    setIsLoading(true);
    try {
      const goalData = {
        user_id: user.id,
        type: goalType,
        current_value: parseFloat(currentValue) || 0,
        target_value: parseFloat(targetValue) || 0,
        end_date: deadline ? deadline.toISOString().split('T')[0] : null
      };

      // Validate data before sending
      const validationResults = {
        user_id_valid: typeof goalData.user_id === 'string' && goalData.user_id.length > 0,
        type_valid: typeof goalData.type === 'string' && goalData.type.length > 0 && ['subscribers', 'monthly_views', 'revenue'].includes(goalData.type),
        current_value_valid: typeof goalData.current_value === 'number' && !isNaN(goalData.current_value) && goalData.current_value >= 0,
        target_value_valid: typeof goalData.target_value === 'number' && !isNaN(goalData.target_value) && goalData.target_value > 0,
        end_date_valid: goalData.end_date === null || (typeof goalData.end_date === 'string' && goalData.end_date.match(/^\d{4}-\d{2}-\d{2}$/))
      };

      console.log('🔍 Pre-submission validation:', validationResults);

      // Check if any validation failed
      const failedValidations = Object.entries(validationResults).filter(([, valid]) => !valid);
      if (failedValidations.length > 0) {
        console.error('❌ Data validation failed:', failedValidations);
        toast.error(`Invalid data: ${failedValidations.map(([key]) => key).join(', ')}`);
        return;
      }

      console.log('Submitting to database:', goalData);
      console.log('Data types check:', {
        user_id: typeof goalData.user_id,
        type: typeof goalData.type,
        current_value: typeof goalData.current_value,
        target_value: typeof goalData.target_value,
        end_date: typeof goalData.end_date,
        current_value_isNaN: isNaN(goalData.current_value),
        target_value_isNaN: isNaN(goalData.target_value)
      });

      // Additional debugging for monthly_views specifically
      if (goalType === 'monthly_views') {
        console.log('🔍 Monthly Views Debug:', {
          originalCurrentValue: currentValue,
          originalTargetValue: targetValue,
          parsedCurrentValue: goalData.current_value,
          parsedTargetValue: goalData.target_value,
          userPreferences: user?.preferences,
          monthlyViewsFromPrefs: user?.preferences?.youtube_monthly_views
        });
      }

      if (editGoal) {
        const { error } = await supabase
          .from('goals')
          .update(goalData)
          .eq('id', editGoal.id);

        if (error) {
          console.error('Update error:', error);
          throw error;
        }
        toast.success("Goal updated successfully!");
      } else {
        const { data, error } = await supabase
          .from('goals')
          .insert([goalData])
          .select();

        if (error) {
          console.error('❌ Database insert error:', error);
          console.error('Error details:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code
          });
          console.error('Goal data that failed:', goalData);
          console.error('Full error object:', JSON.stringify(error, null, 2));

          // Special handling for monthly_views errors
          if (goalType === 'monthly_views') {
            console.error('🔍 Monthly Views Error Analysis:', {
              errorMessage: error.message,
              errorCode: error.code,
              goalType: goalData.type,
              currentValue: goalData.current_value,
              targetValue: goalData.target_value,
              userID: goalData.user_id,
              endDate: goalData.end_date
            });
          }

          throw error;
        }
        console.log('✅ Goal created successfully:', data);

        // Verify the goal was actually created by fetching it
        const { data: verifyData, error: verifyError } = await supabase
          .from('goals')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(1);

        if (verifyError) {
          console.error('❌ Error verifying goal creation:', verifyError);
        } else {
          console.log('🔍 Verification - Latest goal in DB:', verifyData);
        }

        toast.success("Goal created successfully!");
      }

      console.log('✅ Goal operation completed, calling onGoalCreated');
      onGoalCreated();
      onClose();
      resetForm();
    } catch (error) {
      console.error('❌ Error saving goal:', error);

      // Show more specific error message
      let errorMessage = "Error saving goal. Please try again.";
      if (error?.message) {
        console.log('🔍 Analyzing error message:', error.message);
        if (error.message.includes('permission') || error.message.includes('policy')) {
          errorMessage = "Permission denied. Please check your account access.";
        } else if (error.message.includes('constraint') || error.message.includes('violates')) {
          errorMessage = `Database constraint error: ${error.message}`;
        } else if (error.message.includes('network')) {
          errorMessage = "Network error. Please check your connection.";
        } else {
          // Show the actual error message for debugging
          errorMessage = `Database error: ${error.message}`;
        }
      }

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setGoalType('subscribers');
    setCurrentValue('');
    setTargetValue('');
    setDeadline(undefined);
  };

  const getMinDate = () => {
    const minDate = new Date();
    minDate.setDate(minDate.getDate() + 1); // Allow goals starting tomorrow
    return minDate;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] bg-gray-800 border-gray-600">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <Target className="w-5 h-5 mr-2 text-teal" />
            {editGoal ? 'Edit Goal' : 'Create New Goal'}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Goal Type */}
          <div className="space-y-2">
            <Label htmlFor="goalType" className="text-white">Goal Type</Label>
            <Select value={goalType} onValueChange={setGoalType}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-700 border-gray-600">
                {goalTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value} className="text-white hover:bg-gray-600">
                    <div>
                      <div className="font-medium">{type.label}</div>
                      <div className="text-sm text-gray-400">{type.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Current Value */}
          <div className="space-y-2">
            <Label htmlFor="currentValue" className="text-white">
              Current Value {goalType === 'revenue' && <span className="text-gray-400">($)</span>}
            </Label>
            <Input
              id="currentValue"
              type="number"
              step={goalType === 'revenue' ? '0.01' : '1'}
              min="0"
              value={currentValue}
              onChange={(e) => setCurrentValue(e.target.value)}
              placeholder={goalType === 'revenue' ? '0.00' : '0'}
              className="bg-gray-700 border-gray-600 text-white"
            />
          </div>

          {/* Target Value */}
          <div className="space-y-2">
            <Label htmlFor="targetValue" className="text-white">
              Target Value {goalType === 'revenue' && <span className="text-gray-400">($)</span>}
            </Label>
            <Input
              id="targetValue"
              type="number"
              step={goalType === 'revenue' ? '0.01' : '1'}
              min="0"
              value={targetValue}
              onChange={(e) => setTargetValue(e.target.value)}
              placeholder={goalType === 'revenue' ? '1000.00' : '1000'}
              className="bg-gray-700 border-gray-600 text-white"
            />
          </div>

          {/* Deadline */}
          <div className="space-y-2">
            <Label className="text-white">Deadline</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal bg-gray-700 border-gray-600 text-white hover:bg-gray-600",
                    !deadline && "text-gray-400"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {deadline ? format(deadline, "PPP") : "Select deadline"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 bg-gray-700 border-gray-600" align="start">
                <Calendar
                  mode="single"
                  selected={deadline}
                  onSelect={setDeadline}
                  disabled={(date) => date < getMinDate()}
                  initialFocus
                  className="pointer-events-auto"
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Preview */}
          {currentValue && targetValue && deadline && (
            <div className="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
              <h4 className="text-white font-medium mb-2">Goal Preview</h4>
              <p className="text-gray-300 text-sm">
                From <span className="font-bold text-white">
                  {goalType === 'revenue'
                    ? `$${parseFloat(currentValue).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
                    : parseFloat(currentValue).toLocaleString()}
                </span> to{' '}
                <span className="font-bold text-teal">
                  {goalType === 'revenue'
                    ? `$${parseFloat(targetValue).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
                    : parseFloat(targetValue).toLocaleString()}
                </span> by{' '}
                <span className="font-bold text-white">{format(deadline, "MMMM d, yyyy")}</span>
              </p>
              <p className="text-gray-400 text-xs mt-1">
                Increase of {goalType === 'revenue'
                  ? `$${(parseFloat(targetValue) - parseFloat(currentValue)).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
                  : (parseFloat(targetValue) - parseFloat(currentValue)).toLocaleString()}
              </p>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="ghost"
            onClick={onClose}
            className="text-gray-300 hover:text-white hover:bg-gray-700"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="bg-teal hover:bg-teal/90 text-white"
          >
            {isLoading ? 'Saving...' : editGoal ? 'Update Goal' : 'Create Goal'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default GoalModal;
