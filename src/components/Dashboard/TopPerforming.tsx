import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Trophy } from 'lucide-react';

// Sample data for top performing videos
const videos = [
  {
    id: 1,
    title: 'Boost Your Work From Home Setup',
    views: 12765,
    thumbnail: '/images/thumbnails/work-from-home.jpg',
    category: null
  },
  {
    id: 2,
    title: 'DAWN OF A COLONY',
    views: 750,
    thumbnail: '/images/thumbnails/colony.jpg',
    category: 'Educational'
  },
  {
    id: 3,
    title: 'NED KELLY - The Story Behind The Legend',
    views: 440,
    thumbnail: '/images/thumbnails/ned-kelly.jpg',
    category: null
  }
];

const TopPerforming: React.FC = () => {
  return (
    <Card className="bg-sidebar-background/80 border-sidebar-border overflow-hidden">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-base font-medium">Top Performing</h3>
          <Trophy className="text-yellow-400 w-5 h-5" />
        </div>
        
        <div className="space-y-4">
          {videos.map((video) => (
            <div key={video.id} className="flex items-center">
              <div className="w-12 h-12 rounded overflow-hidden flex-shrink-0 mr-3">
                {video.thumbnail ? (
                  <img src={video.thumbnail} alt={video.title} className="w-full h-full object-cover" />
                ) : (
                  <div className="w-full h-full bg-gray-700 flex items-center justify-center">
                    <Video className="w-6 h-6 text-gray-400" />
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium truncate">{video.title}</h4>
                <div className="flex items-center mt-1">
                  <Eye className="w-3.5 h-3.5 mr-1 opacity-90" />
                  <span className="text-xs opacity-90">{video.views} views</span>
                </div>
              </div>
              {video.category && (
                <span className="ml-2 px-2 py-1 text-xs font-medium rounded bg-orange-500/20 text-orange-400">
                  {video.category}
                </span>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default TopPerforming;
