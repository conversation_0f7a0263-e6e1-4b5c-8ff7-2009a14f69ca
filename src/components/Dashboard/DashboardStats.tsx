import React, { useState } from 'react';
import { Users, Eye, Clock, TrendingUp, AlertTriangle, RefreshCw } from 'lucide-react';
import { useDashboardStats } from '@/hooks/useDashboardStats';
import { formatNumber, getChangeText } from '@/utils/statsUtils';
import StatCard from './StatCard';
import ChannelHealthScoreWidget from './ChannelHealthScoreWidget';
import TopPerformingContentWidget from './TopPerformingContentWidget';
import { Button } from '@/components/ui/button';
import { useYouTubeConnection } from '@/hooks/useYouTubeConnection';

interface DashboardStatsProps {
  user: any;
}

const DashboardStats = ({ user }: DashboardStatsProps) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isCheckingToken, setIsCheckingToken] = useState(false);
  const { stats, lastSyncError, refetch } = useDashboardStats({ user });
  const { youtubeData, isConnected, tokenStatus, checkTokenStatus } = useYouTubeConnection();

  const hasChannel = !!youtubeData?.youtube_channel_id;
  const hasValidToken = tokenStatus.isValid;
  const preferences = user?.preferences || {};
  const hasEnhancedSync = preferences.last_full_sync;
  const lastSyncTime = user.last_youtube_sync || preferences.last_full_sync;

  // Debug logging
  React.useEffect(() => {
    console.log('=== DASHBOARD STATS DEBUG ===');
    console.log('User YouTube Data:', {
      channelId: youtubeData?.youtube_channel_id,
      channelName: youtubeData?.youtube_channel_name,
      subscriberBaseline: youtubeData?.youtube_subscriber_baseline,
      lastSync: youtubeData?.last_youtube_sync,
      preferences: user?.preferences
    });
    console.log('Stats:', stats);
    console.log('Token Status:', tokenStatus);
    console.log('Connection Status:', {
      isConnected,
      hasChannel,
      hasValidToken,
      hasEnhancedSync,
      lastSyncTime
    });
  }, [youtubeData, stats, tokenStatus, isConnected, hasChannel, hasValidToken, hasEnhancedSync, lastSyncTime, user?.preferences]);

  const handleTokenRefresh = async () => {
    setIsCheckingToken(true);
    toast.info('Checking YouTube token status...');
    try {
      await checkTokenStatus();
      if (tokenStatus.isValid) {
        toast.success('YouTube connection is valid!');
      } else {
        toast.error(tokenStatus.error || 'YouTube token is invalid or expired.');
      }
    } catch (error) {
      toast.error('Failed to check YouTube token status.');
    } finally {
      setIsCheckingToken(false);
    }
  };

  const handleRefreshData = async () => {
    try {
      setIsRefreshing(true);
      console.log('Manually refreshing dashboard data...');
      await refetch();
      // If YouTube is connected, also refresh token status
      if (hasChannel) {
        await checkTokenStatus();
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const isDataStale = () => {
    if (!lastSyncTime) return true;
    const lastSync = new Date(lastSyncTime);
    const now = new Date();
    const hoursSinceSync = (now.getTime() - lastSync.getTime()) / (1000 * 60 * 60);
    return hoursSinceSync > 24; // Consider data stale after 24 hours
  };

  const statsData = [
    { 
      label: 'Total Subscribers', 
      value: isConnected && stats.subscribers > 0 
        ? stats.subscribers.toLocaleString() 
        : hasChannel ? (youtubeData?.youtube_subscriber_baseline?.toLocaleString() || '--') : '--', 
      change: getChangeText(user, isConnected, 'subscribers', stats.subscribers), 
      icon: Users 
    },
    { 
      label: 'Monthly Views', 
      value: isConnected && stats.monthlyViews > 0 
        ? formatNumber(stats.monthlyViews) 
        : '--', 
      change: getChangeText(user, isConnected, 'monthlyViews', stats.monthlyViews), 
      icon: Eye 
    },
    { 
      label: 'Avg. Watch Time', 
      value: isConnected ? stats.avgWatchTime : '--', 
      change: getChangeText(user, isConnected, 'avgWatchTime', 0), 
      icon: Clock 
    },
    { 
      label: 'Videos Published', 
      value: isConnected && stats.videosPublished > 0 
        ? stats.videosPublished.toString() 
        : '0', 
      change: getChangeText(user, isConnected, 'videosPublished', stats.videosPublished), 
      icon: TrendingUp 
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-white">Channel Performance</h2>
          <div className="flex items-center mt-1 space-x-4">
            {hasEnhancedSync && (
              <div className="flex items-center text-xs text-green-400">
                <TrendingUp className="w-3 h-3 mr-1" />
                Real-time sync enabled
              </div>
            )}
            {isDataStale() && (
              <div className="flex items-center text-xs text-orange">
                <AlertTriangle className="w-3 h-3 mr-1" />
                Data may be outdated
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {hasChannel && lastSyncTime && (
            <div className="text-right">
              <span className="text-sm text-gray-300 block">
                Last updated: {new Date(lastSyncTime).toLocaleDateString()}
              </span>
              {hasEnhancedSync && (
                <span className="text-xs text-green-400">
                  Real-time data synced
                </span>
              )}
            </div>
          )}
          <Button
            onClick={handleRefreshData}
            size="sm"
            variant="outline"
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
            disabled={isRefreshing}
          >
            <RefreshCw className={`w-4 h-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>
      </div>



      {/* Stats Grid - 2 rows of 3 columns */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* First row - Original stats */}
        {statsData.map((stat, index) => (
          <StatCard
            key={index}
            label={stat.label}
            value={stat.value}
            change={stat.change}
            icon={stat.icon}
            isYouTubeConnected={isConnected}
          />
        ))}
        
        {/* Second row - New widgets */}
        <ChannelHealthScoreWidget user={user} stats={stats} />
        <TopPerformingContentWidget user={user} />
      </div>
    </div>
  );
};

export default DashboardStats;
