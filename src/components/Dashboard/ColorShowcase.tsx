import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Palette, Sparkles, Target, TrendingUp, Zap } from 'lucide-react';

const ColorShowcase = () => {
  return (
    <Card className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-gray-600/30 shadow-xl">
      <CardHeader className="pb-4">
        <CardTitle className="text-white flex items-center text-lg">
          <Palette className="w-5 h-5 mr-2 text-deep-red" />
          YouTube-Inspired Theme
        </CardTitle>
        <p className="text-gray-300 text-sm">
          Bold, energetic palette inspired by YouTube's brand colors
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Color Swatches */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center space-y-2">
            <div className="w-16 h-16 bg-deep-red rounded-lg mx-auto shadow-lg border-2 border-deep-red/30"></div>
            <div>
              <p className="text-white text-sm font-medium">Deep Red</p>
              <p className="text-gray-400 text-xs">Ideas, creativity, taglines</p>
            </div>
          </div>
          
          <div className="text-center space-y-2">
            <div className="w-16 h-16 bg-blue rounded-lg mx-auto shadow-lg border-2 border-blue/30"></div>
            <div>
              <p className="text-white text-sm font-medium">Blue</p>
              <p className="text-gray-400 text-xs">Primary text, main branding</p>
            </div>
          </div>
          
          <div className="text-center space-y-2">
            <div className="w-16 h-16 bg-orange rounded-lg mx-auto shadow-lg border-2 border-orange/30"></div>
            <div>
              <p className="text-white text-sm font-medium">Orange</p>
              <p className="text-gray-400 text-xs">CTAs, interactive elements</p>
            </div>
          </div>
          
          <div className="text-center space-y-2">
            <div className="w-16 h-16 bg-green rounded-lg mx-auto shadow-lg border-2 border-green/30"></div>
            <div>
              <p className="text-white text-sm font-medium">Green</p>
              <p className="text-gray-400 text-xs">Success, growth metrics</p>
            </div>
          </div>
        </div>

        {/* Interactive Buttons Showcase */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-400 uppercase tracking-wide">
            Interactive Elements
          </h4>
          <div className="grid grid-cols-2 gap-3">
            <Button className="bg-deep-red hover:bg-deep-red-dark text-white">
              <Sparkles className="w-4 h-4 mr-2" />
              Ideas
            </Button>
            <Button className="bg-blue hover:bg-blue-dark text-white">
              <Target className="w-4 h-4 mr-2" />
              Branding
            </Button>
            <Button className="bg-orange hover:bg-orange-dark text-white">
              <Zap className="w-4 h-4 mr-2" />
              CTAs
            </Button>
            <Button className="bg-green hover:bg-green-dark text-white">
              <TrendingUp className="w-4 h-4 mr-2" />
              Success
            </Button>
          </div>
        </div>

        {/* Gradient Showcase */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-400 uppercase tracking-wide">
            Gradient Combinations
          </h4>
          <div className="space-y-2">
            <div className="h-12 bg-gradient-to-r from-deep-red to-blue rounded-lg flex items-center justify-center">
              <span className="text-white font-medium">Red → Blue</span>
            </div>
            <div className="h-12 bg-gradient-to-r from-blue to-green rounded-lg flex items-center justify-center">
              <span className="text-white font-medium">Blue → Green</span>
            </div>
            <div className="h-12 bg-gradient-to-r from-orange to-deep-red rounded-lg flex items-center justify-center">
              <span className="text-white font-medium">Orange → Red</span>
            </div>
          </div>
        </div>

        {/* Theme Benefits */}
        <div className="bg-gradient-to-r from-deep-red/10 to-blue/10 p-4 rounded-lg border border-deep-red/20">
          <h5 className="text-white font-medium mb-2">🔥 Best For:</h5>
          <ul className="text-gray-300 text-sm space-y-1">
            <li>• Energetic, bold brand presence</li>
            <li>• Familiar, trusted color associations</li>
            <li>• Content creators and digital brands</li>
            <li>• Modern, high-contrast UI</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default ColorShowcase; 