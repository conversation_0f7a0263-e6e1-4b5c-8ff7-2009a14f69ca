
import React, { useState, useEffect } from 'react';
import { Target, Plus, Trash2, Edit } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import GoalModal from './GoalModal';
import DeleteGoalDialog from './DeleteGoalDialog';
import GoalsEmptyState from './EmptyStates/GoalsEmptyState';
import { useNavigate } from 'react-router-dom';

interface Goal {
  id: string;
  type: string;
  target_value: number;
  current_value: number;
  end_date: string;
}

interface GoalsProgressCardProps {
  user: any;
  navigate?: (path: string) => void;
  onCreateGoal?: () => void;
}

const GoalsProgressCard: React.FC<GoalsProgressCardProps> = ({ user, navigate: propNavigate, onCreateGoal }) => {
  // Use provided navigate or get from hook
  const hookNavigate = useNavigate();
  const navigate = propNavigate || hookNavigate;

  // Goals are now integrated into Dashboard - no separate navigation needed

  const [goals, setGoals] = useState<Goal[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null);
  const [deletingGoal, setDeletingGoal] = useState<Goal | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchGoals();
    }
  }, [user]);

  const fetchGoals = async () => {
    try {
      console.log('🔄 Fetching goals for user:', user.id);
      setIsLoading(true);
      const { data, error } = await supabase
        .from('goals')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching goals:', error);
        throw error;
      }

      console.log('📊 Goals fetched:', data);
      setGoals(data || []);
    } catch (error) {
      console.error('Error fetching goals:', error);
      // Don't show error toast, just log the error and continue
      setGoals([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoalCreated = () => {
    console.log('🎯 Goal created callback triggered, refetching goals...');
    fetchGoals();
    setIsModalOpen(false);
    setEditingGoal(null);
  };

  const handleGoalDeleted = async (goalId: string) => {
    try {
      const { error } = await supabase
        .from('goals')
        .delete()
        .eq('id', goalId);

      if (error) throw error;

      setGoals(goals.filter(goal => goal.id !== goalId));
      toast.success('Goal deleted successfully');
    } catch (error) {
      console.error('Error deleting goal:', error);
      toast.error('Failed to delete goal');
    } finally {
      setDeletingGoal(null);
    }
  };

  const getGoalTypeIcon = (type: string) => {
    switch (type) {
      case 'subscribers':
        return <Target className="w-4 h-4 mr-2 text-white" />;
      default:
        return <Target className="w-4 h-4 mr-2 text-white" />;
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center">
          <div className="w-6 h-6 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (goals.length === 0) {
    return (
      <>
        <GoalsEmptyState onCreateGoal={onCreateGoal || (() => setIsModalOpen(true))} />

        <GoalModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setEditingGoal(null);
          }}
          onGoalCreated={handleGoalCreated}
          user={user}
          editGoal={editingGoal}
        />
      </>
    );
  }

  return (
    <>
      <div className="p-6">
        <div className="pb-2">
          <h3 className="text-white font-semibold">
            <Target className="w-5 h-5 mr-2 text-gray-400 inline-block align-middle" />
            Goals Progress (Live Data)
          </h3>
        </div>
        <div className="space-y-4">
          {goals.map((goal) => (
            <div key={goal.id} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  {getGoalTypeIcon(goal.type)}
                  <span className="text-sm font-medium text-white">{goal.type}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="icon" onClick={() => {
                    setEditingGoal(goal);
                    setIsModalOpen(true);
                  }}>
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="icon" onClick={() => setDeletingGoal(goal)}>
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <Progress value={(goal.current_value / goal.target_value) * 100} />
              <div className="flex items-center justify-between text-xs text-gray-400">
                <span>{goal.current_value}</span>
                <span>{goal.target_value}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <GoalModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingGoal(null);
        }}
        onGoalCreated={handleGoalCreated}
        user={user}
        editGoal={editingGoal}
      />

      <DeleteGoalDialog
        isOpen={!!deletingGoal}
        onClose={() => setDeletingGoal(null)}
        onConfirm={() => deletingGoal && handleGoalDeleted(deletingGoal.id)}
        goalTitle={deletingGoal ? deletingGoal.type : ''}
      />
      {/* "View All Goals" button removed - goals are now integrated into Dashboard */}
    </>
  );
};

export default GoalsProgressCard;
