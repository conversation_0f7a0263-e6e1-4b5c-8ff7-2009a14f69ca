import React from 'react';

interface GoalProgressBarProps {
  current: number;
  target: number;
  type: string;
}

const GoalProgressBar = ({ current, target, type }: GoalProgressBarProps) => {
  const percentage = Math.min(Math.round((current / target) * 100), 100);
  
  const getProgressColor = () => {
    if (type === 'subscribers') return 'bg-orange';
    if (type === 'revenue') return 'bg-green-400';
    if (type === 'monthly_views') return 'bg-orange';
    return 'bg-blue-400';
  };
  
  return (
    <div className="w-full h-3 bg-purple-800/80 rounded-full overflow-hidden">
      <div 
        className={`h-full ${getProgressColor()}`} 
        style={{ width: `${percentage}%` }}
      />
    </div>
  );
};

export default GoalProgressBar;