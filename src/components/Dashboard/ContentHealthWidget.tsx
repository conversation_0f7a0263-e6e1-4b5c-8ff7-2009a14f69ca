import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check, AlertTriangle, X } from 'lucide-react';
import RecommendationsModal from './RecommendationsModal/RecommendationsModal';

const ContentHealthWidget = () => {
  const [healthScore, setHealthScore] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const targetScore = 85;

  useEffect(() => {
    const timer = setTimeout(() => {
      const increment = targetScore / 50;
      const interval = setInterval(() => {
        setHealthScore(prev => {
          if (prev >= targetScore) {
            clearInterval(interval);
            return targetScore;
          }
          return prev + increment;
        });
      }, 20);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getProgressColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const healthMetrics = [
    { 
      text: 'Consistent uploads (3 weeks streak!)', 
      status: 'success', 
      icon: Check 
    },
    { 
      text: 'Balanced content pillars', 
      status: 'success', 
      icon: Check 
    },
    { 
      text: 'Engagement dropping (-12% this week)', 
      status: 'warning', 
      icon: AlertTriangle 
    },
    { 
      text: 'No Equipment Reviews in 3 weeks', 
      status: 'error', 
      icon: X 
    }
  ];

  return (
    <>
      <Card className="bg-gray-800 border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-lg">Content Strategy Health</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Circular Progress */}
          <div className="flex flex-col items-center space-y-3">
            <div className="relative w-24 h-24">
              <div className="absolute inset-0 rounded-full border-4 border-gray-600"></div>
              <div 
                className={`absolute inset-0 rounded-full border-4 border-t-transparent transition-all duration-1000 ${getProgressColor(healthScore)}`}
                style={{
                  transform: `rotate(${(healthScore / 100) * 360}deg)`,
                  borderTopColor: 'transparent'
                }}
              ></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className={`text-2xl font-bold ${getScoreColor(healthScore)}`}>
                  {Math.round(healthScore)}%
                </span>
              </div>
            </div>
          </div>

          {/* Health Metrics */}
          <div className="space-y-3">
            {healthMetrics.map((metric, index) => {
              const IconComponent = metric.icon;
              const iconColor = 
                metric.status === 'success' ? 'text-green-400' :
                metric.status === 'warning' ? 'text-yellow-400' : 'text-red-400';
              
              return (
                <div key={index} className="flex items-start space-x-3">
                  <IconComponent className={`w-4 h-4 mt-0.5 ${iconColor}`} />
                  <span className="text-gray-300 text-sm">{metric.text}</span>
                </div>
              );
            })}
          </div>

          <Button 
            className="w-full bg-teal hover:bg-teal/90 text-white"
            onClick={() => setIsModalOpen(true)}
          >
            View Recommendations
          </Button>
        </CardContent>
      </Card>

      <RecommendationsModal 
        open={isModalOpen} 
        onOpenChange={setIsModalOpen}
        healthScore={healthScore}
      />
    </>
  );
};

export default ContentHealthWidget;
