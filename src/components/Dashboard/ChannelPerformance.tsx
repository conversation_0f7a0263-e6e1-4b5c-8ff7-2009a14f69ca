return (
  <Card className="bg-sidebar-background border-sidebar-border overflow-hidden">
    <CardHeader className="pb-2">
      <div className="flex justify-between items-center">
        <div>
          <CardTitle className="text-xl font-semibold">Channel Performance</CardTitle>
          <CardDescription className="text-sm">
            <span className="flex items-center">
              <CircleDot className="w-3 h-3 text-green-400 mr-1.5" />
              Real-time sync enabled
            </span>
          </CardDescription>
        </div>
        <div className="text-right">
          <p className="text-sm mb-1">Last updated: {lastUpdated}</p>
          <Button variant="outline" size="sm" className="border-blue-500 bg-blue-500/20 hover:bg-blue-500/30">
            <RefreshCw className="w-4 h-4 mr-1.5" />
            Refresh
          </Button>
        </div>
      </div>
    </CardHeader>
    
    <CardContent>
      {/* Main stats grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Total Subscribers */}
        <div className="stat-container">
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-base font-medium">Total Subscribers</h3>
            <Users className="w-5 h-5 text-purple-400" />
          </div>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold">{subscriberCount || 0}</span>
            <span className="text-sm ml-2">From YouTube API</span>
          </div>
        </div>
        
        {/* Monthly Views */}
        <div className="stat-container">
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-base font-medium">Monthly Views</h3>
            <Eye className="w-5 h-5 text-blue-400" />
          </div>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold">{monthlyViews || '--'}</span>
            <span className="text-sm ml-2">{!monthlyViews && 'No recent views data'}</span>
          </div>
        </div>
        
        {/* Avg. Watch Time */}
        <div className="stat-container">
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-base font-medium">Avg. Watch Time</h3>
            <Clock className="w-5 h-5 text-teal-400" />
          </div>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold">{avgWatchTime || '0:00'}</span>
            <span className="text-sm ml-2">{!avgWatchTime && 'No watch time data'}</span>
          </div>
        </div>
      </div>
      
      {/* Videos Published */}
      <div className="mb-6">
        <div className="stat-container">
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-base font-medium">Videos Published</h3>
            <TrendingUp className="w-5 h-5 text-green-400" />
          </div>
          <div className="flex items-baseline">
            <span className="text-3xl font-bold">{videoCount || 0}</span>
            <span className="text-sm ml-2">From YouTube API</span>
          </div>
        </div>
      </div>
      
      {/* Channel Health and Top Performing sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <ChannelHealth />
        <TopPerforming />
      </div>
    </CardContent>
  </Card>
);
