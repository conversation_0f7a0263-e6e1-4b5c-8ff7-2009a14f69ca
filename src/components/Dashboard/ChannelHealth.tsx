import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { TrendingUp } from 'lucide-react';
import { CircularProgressIndicator } from '@/components/ui/circular-progress';

// Sample data for channel health metrics
const score = 70;
const healthStatus = 'Good';
const metrics = [
  { name: 'Upload Consistency', value: '90%', color: 'bg-blue-400' },
  { name: 'Audience Growth', value: '75%', color: 'bg-green-400' },
  { name: 'Content Engagement', value: '60%', color: 'bg-yellow-400' }
];

const ChannelHealth: React.FC = () => {
  return (
    <Card className="bg-sidebar-background/80 border-sidebar-border overflow-hidden">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-base font-medium">Channel Health</h3>
          <TrendingUp className="text-purple-400 w-5 h-5" />
        </div>
        
        <div className="flex items-center mb-6">
          <div className="relative w-16 h-16 mr-4">
            <CircularProgressIndicator value={score} size={64} strokeWidth={8} />
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-xl font-bold">{score}</span>
            </div>
          </div>
          <div>
            <h4 className="text-lg font-semibold">{healthStatus}</h4>
            <p className="text-base">Good progress, keep it up!</p>
          </div>
        </div>
        
        {/* Metrics section */}
        <div className="space-y-3">
          {metrics.map((metric) => (
            <div key={metric.name} className="space-y-1">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">{metric.name}</span>
                <span className="text-sm font-bold">{metric.value}</span>
              </div>
              <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                <div 
                  className={`h-full rounded-full ${metric.color}`}
                  style={{ width: `${metric.value}` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ChannelHealth;
