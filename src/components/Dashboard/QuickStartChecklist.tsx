
import React from 'react';
import { Card } from '@/components/ui/card';
import { useQuickStartChecklist } from './QuickStartChecklist/useQuickStartChecklist';
import CelebrationView from './QuickStartChecklist/CelebrationView';
import ChecklistHeader from './QuickStartChecklist/ChecklistHeader';
import ChecklistItems from './QuickStartChecklist/ChecklistItems';
import { QuickStartChecklistProps } from './QuickStartChecklist/types';

const QuickStartChecklist = ({ user, onComplete, navigate }: QuickStartChecklistProps) => {
  const {
    isVisible,
    showCelebration,
    checklistItems,
    completedCount,
    totalCount,
    progressPercentage
  } = useQuickStartChecklist(user, onComplete);

  if (!isVisible) return null;

  if (showCelebration) {
    return <CelebrationView />;
  }

  return (
    <div>
      <ChecklistHeader
        completedCount={completedCount}
        totalCount={totalCount}
        progressPercentage={progressPercentage}
      />
      <ChecklistItems checklistItems={checklistItems} />
    </div>
  );
};

export default QuickStartChecklist;
