
import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';

interface DashboardWelcomeHeaderProps {
  userProfile: any;
}

const DashboardWelcomeHeader = ({ userProfile }: DashboardWelcomeHeaderProps) => {
  const { user } = useAuth();
  const [fullUserProfile, setFullUserProfile] = useState<any>(null);

  useEffect(() => {
    if (user?.id) {
      const fetchFullProfile = async () => {
        try {
          const { data, error } = await supabase
            .from('users')
            .select('name, youtube_channel_id')
            .eq('id', user.id)
            .single();

          if (error) throw error;
          setFullUserProfile(data);
        } catch (error) {
          console.error('Error fetching full user profile:', error);
        }
      };

      fetchFullProfile();
    }
  }, [user?.id]);

  const userName = fullUserProfile?.name || 'Creator';
  const isYouTubeConnected = !!fullUserProfile?.youtube_channel_id;

  return (
    <div className="text-center space-y-6">
      <div className="flex items-center justify-center">
        <img
          src="/MCH_White_Logotype.svg"
          alt="MyContentHub"
          className="h-60 object-contain"
          loading="lazy"
        />
      </div>
      <div>
        <h1 className="text-4xl font-bold bg-gradient-to-r from-bright-cyan to-deep-purple bg-clip-text text-transparent mb-2">
          Welcome back, {userName}! 👋
        </h1>
        <p className="text-xl text-gray-300">
          Your content strategy hub is ready
        </p>
      </div>
    </div>
  );
};

export default DashboardWelcomeHeader;
