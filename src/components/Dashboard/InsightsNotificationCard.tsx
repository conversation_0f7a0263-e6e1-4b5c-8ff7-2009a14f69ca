
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Zap, TrendingDown, AlertTriangle } from 'lucide-react';

interface InsightsNotificationCardProps {
  pillars: any[];
  videoCount: number;
  onViewInsights: () => void;
}

const InsightsNotificationCard = ({ pillars, videoCount, onViewInsights }: InsightsNotificationCardProps) => {
  // Calculate insights summary
  const driftPillars = pillars.filter(pillar => 
    Math.abs(pillar.actual_percentage - pillar.target_percentage) > 10
  );
  
  const hasContentDrift = driftPillars.length > 0;
  const hasPerformanceInsights = videoCount > 10;
  
  console.log('InsightsNotificationCard debug:', {
    pillarsCount: pillars.length,
    driftPillars: driftPillars.length,
    hasContentDrift,
    hasPerformanceInsights,
    videoCount
  });
  
  if (!hasContentDrift && !hasPerformanceInsights) {
    return null;
  }

  const getMainMessage = () => {
    if (hasContentDrift && hasPerformanceInsights) {
      return "We found ways to grow your channel faster!";
    } else if (hasContentDrift) {
      return "Your content balance needs adjusting!";
    } else {
      return "We spotted opportunities to boost your views!";
    }
  };

  const getSubMessage = () => {
    const messages = [];
    
    if (hasContentDrift) {
      if (driftPillars.length === 1) {
        messages.push("One content type needs better balance");
      } else {
        messages.push(`${driftPillars.length} content types need rebalancing`);
      }
    }
    
    if (hasPerformanceInsights) {
      messages.push('Video performance patterns we can improve');
    }
    
    return messages.join(' • ');
  };

  const mainMessage = getMainMessage();
  const subMessage = getSubMessage();
  
  console.log('Messages generated:', { mainMessage, subMessage });

  const handleClick = () => {
    console.log('Insights card clicked - switching to insights tab');
    onViewInsights();
  };

  return (
    <Card 
      className="bg-gradient-to-r from-orange/20 to-teal/20 border-orange/50 cursor-pointer hover:from-orange/30 hover:to-teal/30 transition-all duration-200 hover:shadow-lg"
      onClick={handleClick}
    >
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          <div className="bg-orange/20 p-3 rounded-lg">
            <Zap className="w-6 h-6 text-orange" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-white mb-2">
              💡 Growth Tips Ready!
            </h3>
            <p className="text-white mb-3 font-medium">
              {mainMessage}
            </p>
            <p className="text-gray-300 text-sm mb-4">
              {subMessage}
            </p>
            <div className="flex items-center space-x-4 text-sm text-gray-400">
              {hasContentDrift && (
                <div className="flex items-center space-x-1">
                  <TrendingDown className="w-4 h-4" />
                  <span>Content balance tips</span>
                </div>
              )}
              {hasPerformanceInsights && (
                <div className="flex items-center space-x-1">
                  <AlertTriangle className="w-4 h-4" />
                  <span>Performance insights</span>
                </div>
              )}
            </div>
            <div className="mt-4 text-xs text-gray-400 italic">
              Click anywhere to see your personalized recommendations →
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default InsightsNotificationCard;
