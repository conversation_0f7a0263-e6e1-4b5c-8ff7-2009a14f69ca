
import React from 'react';
import { Activity } from './types';
import ActivityItem from './ActivityItem';

interface ActivityListProps {
  activities: Activity[];
  onActivityClick: (activity: Activity) => void;
  onAnalyzeVideo: (activity: Activity) => void;
}

const ActivityList = ({ activities, onActivityClick, onAnalyzeVideo }: ActivityListProps) => {
  if (activities.length === 0) {
    return (
      <p className="text-gray-300 text-center py-4">
        No recent activity. Start by creating your first video idea!
      </p>
    );
  }

  return (
    <>
      {activities.map((activity, index) => (
        <ActivityItem
          key={index}
          activity={activity}
          onActivityClick={onActivityClick}
          onAnalyzeVideo={onAnalyzeVideo}
        />
      ))}
    </>
  );
};

export default ActivityList;
