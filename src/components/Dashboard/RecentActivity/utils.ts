
export const getTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

  if (diffDays > 0) {
    return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
  } else {
    return 'Less than an hour ago';
  }
};

export const isVideoOldEnough = (dateString: string): boolean => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  console.log(`Video date: ${dateString}, Days ago: ${diffDays}, Old enough: ${diffDays >= 1}`);
  
  // Changed from 7 days to 1 day for testing - you can change this back to 7 if needed
  return diffDays >= 1;
};

export const getDisplayLimit = (): number => {
  return window.innerWidth < 768 ? 2 : 3;
};
