
export interface Video {
  id: string;
  title: string;
  views: number | null;
  published_at: string | null;
  created_at: string;
  status: string | null;
  pillar_id: string | null;
  pillar?: {
    name: string;
    color: string;
  };
}

export interface Activity {
  id: string;
  action: string;
  title: string;
  time: string;
  pillar?: {
    name: string;
    color: string;
  };
  video?: Video;
  canAnalyze: boolean;
}

export interface RecentActivityProps {
  user: any;
}
