
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Activity } from '../types';

export const useRecentActivity = (user: any) => {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [channelAverage, setChannelAverage] = useState(0);

  useEffect(() => {
    if (user) {
      fetchActivities();
    } else {
      setActivities([]);
      setLoading(false);
    }
  }, [user]);

  const fetchActivities = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const activities: Activity[] = [];

      // Fetch recent videos with pillar information - only real YouTube videos
      const { data: videos } = await supabase
        .from('videos')
        .select(`
          id, 
          title, 
          status, 
          created_at, 
          published_at, 
          views,
          pillar_id,
          youtube_video_id,
          youtube_thumbnail_url,
          content_pillars(name, color)
        `)
        .eq('user_id', user.id)
        .not('youtube_video_id', 'is', null)
        .not('youtube_thumbnail_url', 'is', null)
        .order('created_at', { ascending: false })
        .limit(10);

      // Calculate channel average from published videos
      const publishedVideos = videos?.filter(v => v.status === 'published' && v.views) || [];
      const avgViews = publishedVideos.length > 0 
        ? publishedVideos.reduce((sum, v) => sum + (v.views || 0), 0) / publishedVideos.length 
        : 0;
      setChannelAverage(avgViews);

      // Process videos into activities
      videos?.forEach(video => {
        const timeAgo = getTimeAgo(video.published_at || video.created_at);
        
        if (video.status === 'published' && video.published_at) {
          activities.push({
            id: `video-${video.id}`,
            action: 'Published video',
            title: video.title,
            time: timeAgo,
            pillar: video.content_pillars ? {
              name: video.content_pillars.name,
              color: video.content_pillars.color
            } : undefined,
            video: {
              id: video.id,
              title: video.title,
              views: video.views || 0,
              published_at: video.published_at,
              created_at: video.created_at,
              status: video.status,
              pillar_id: video.pillar_id,
              pillar: video.content_pillars ? {
                name: video.content_pillars.name,
                color: video.content_pillars.color
              } : undefined
            },
            canAnalyze: true
          });
        } else if (video.status === 'idea') {
          activities.push({
            id: `idea-${video.id}`,
            action: 'Created idea',
            title: video.title,
            time: timeAgo,
            pillar: video.content_pillars ? {
              name: video.content_pillars.name,
              color: video.content_pillars.color
            } : undefined,
            video: {
              id: video.id,
              title: video.title,
              views: video.views || 0,
              published_at: video.published_at,
              created_at: video.created_at,
              status: video.status,
              pillar_id: video.pillar_id,
              pillar: video.content_pillars ? {
                name: video.content_pillars.name,
                color: video.content_pillars.color
              } : undefined
            },
            canAnalyze: false
          });
        }
      });

      setActivities(activities);
    } catch (error) {
      console.error('Error fetching activities:', error);
      setActivities([]);
    } finally {
      setLoading(false);
    }
  };

  const getTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    const diffInWeeks = Math.floor(diffInDays / 7);
    return `${diffInWeeks}w ago`;
  };

  return { activities, channelAverage, loading };
};
