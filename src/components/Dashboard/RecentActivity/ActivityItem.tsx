
import React from 'react';
import { BarChart3 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Activity } from './types';

interface ActivityItemProps {
  activity: Activity;
  onActivityClick: (activity: Activity) => void;
  onAnalyzeVideo: (activity: Activity) => void;
}

const ActivityItem = ({ activity, onActivityClick, onAnalyzeVideo }: ActivityItemProps) => {
  const handleItemClick = (e: React.MouseEvent) => {
    // Don't trigger if clicking on the analyze button
    if ((e.target as HTMLElement).closest('button')) {
      return;
    }
    onActivityClick(activity);
  };

  const handleAnalyzeClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAnalyzeVideo(activity);
  };

  return (
    <div 
      className="flex items-start space-x-3 p-3 bg-purple-900/80 border border-purple-800 rounded-lg hover:bg-purple-900/90 transition-colors cursor-pointer group"
      onClick={handleItemClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onActivityClick(activity);
        }
      }}
    >
      <div className="w-2 h-2 bg-teal rounded-full mt-2 flex-shrink-0"></div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-white group-hover:text-teal transition-colors">
          {activity.action}
        </p>
        <p className="text-sm text-gray-300 truncate">{activity.title}</p>
        {activity.pillar && (
          <span 
            className="inline-block text-white text-xs px-2 py-1 rounded-full mt-1"
            style={{ backgroundColor: activity.pillar.color + '40', color: activity.pillar.color }}
          >
            {activity.pillar.name}
          </span>
        )}
      </div>
      <div className="flex items-center gap-2 flex-shrink-0">
        <span className="text-xs text-gray-400">{activity.time}</span>
        {activity.canAnalyze && activity.video && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAnalyzeClick}
                className="p-1 h-auto text-gray-400 hover:text-teal hover:bg-purple-800/50"
              >
                <BarChart3 className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>View performance analysis</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default ActivityItem;
