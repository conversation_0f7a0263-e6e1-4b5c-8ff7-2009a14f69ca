
import React, { useState, useEffect } from 'react';
import { Trophy, Eye, Video } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';

interface TopPerformingContentWidgetProps {
  user: any;
}

const TopPerformingContentWidget = ({ user }: TopPerformingContentWidgetProps) => {
  const [topVideos, setTopVideos] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!user?.id) {
      setIsLoading(false);
      return;
    }

    const fetchTopVideos = async () => {
      try {
        console.log('Fetching top performing videos for user:', user.id);
        
        const { data, error } = await supabase
          .from('videos')
          .select(`
            id,
            title,
            views,
            like_count,
            published_at,
            youtube_video_id,
            youtube_thumbnail_url,
            content_pillars(name, color)
          `)
          .eq('user_id', user.id)
          .eq('status', 'published')
          .not('youtube_video_id', 'is', null)
          .order('views', { ascending: false })
          .limit(3);

        if (error) throw error;

        const processedVideos = (data || []).map(video => ({
          id: video.id,
          title: video.title,
          views: video.views,
          thumbnail: video.youtube_thumbnail_url,
          content_pillars: video.content_pillars
        }));

        setTopVideos(processedVideos);
      } catch (err) {
        console.error('Error fetching top videos:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTopVideos();
  }, [user?.id]);

  return (
    <div className="bg-purple-900/80 rounded-lg p-4 border border-purple-800 h-full">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-base font-medium text-gray-300">Top Performing</h3>
        <div className="p-2 bg-purple-800/80 rounded-lg">
          <Trophy className="w-6 h-6 text-blue-400" />
        </div>
      </div>
      
      <div className="space-y-2">
        {isLoading ? (
          <div className="flex justify-center items-center h-32">
            <div className="w-6 h-6 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : topVideos && topVideos.length > 0 ? (
          topVideos.map((video, index) => (
            <div key={video.id} className="py-2">
              <div className="flex items-center space-x-2">
                <div className="flex-shrink-0">
                  {video.thumbnail ? (
                    <img 
                      src={video.thumbnail} 
                      alt={video.title} 
                      className="w-12 h-12 rounded object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-purple-800/80 rounded flex items-center justify-center">
                      <Video className="w-6 h-6 text-gray-400" />
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center">
                    <h4 className="text-base font-medium text-white truncate">
                      {video.title}
                    </h4>
                    {video.content_pillars && (
                      <Badge 
                        className="text-white text-sm ml-2"
                        style={{ backgroundColor: video.content_pillars.color }}
                      >
                        {video.content_pillars.name}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center text-sm text-gray-400 mt-1">
                    <Eye className="w-4 h-4 mr-1" />
                    {video.views?.toLocaleString() || 0} views
                  </div>
                </div>
              </div>
              {index < topVideos.length - 1 && (
                <div className="border-b border-purple-800 mt-2"></div>
              )}
            </div>
          ))
        ) : (
          <div className="text-center py-4">
            <Trophy className="w-10 h-10 text-gray-600 mx-auto mb-2" />
            <p className="text-base text-gray-400">
              No video data available yet
            </p>
            <p className="text-sm text-gray-500 mt-1">
              Import your YouTube videos to see top performing content
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TopPerformingContentWidget;
