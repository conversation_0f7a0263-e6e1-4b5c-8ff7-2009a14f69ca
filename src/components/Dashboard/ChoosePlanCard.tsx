
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Crown, ArrowRight, Star } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface ChoosePlanCardProps {
  userTier?: string;
}

const ChoosePlanCard = ({ userTier }: ChoosePlanCardProps) => {
  const navigate = useNavigate();

  // Don't show if user already has a paid plan
  if (userTier && userTier !== 'starter') {
    return null;
  }

  return (
    <Card className="bg-gradient-to-br from-orange/20 to-yellow/20 border-orange/30 shadow-lg">
      <CardHeader className="pb-4">
        <CardTitle className="text-white flex items-center text-lg">
          <Crown className="w-5 h-5 mr-2 text-orange" />
          Unlock Your Channel's Potential
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <p className="text-gray-300 text-sm">
            You're currently on the free trial. Upgrade to unlock powerful features:
          </p>
          
          <div className="space-y-2">
            <div className="flex items-center text-sm text-gray-300">
              <Star className="w-4 h-4 mr-2 text-yellow" />
              More content pillars
            </div>
            <div className="flex items-center text-sm text-gray-300">
              <Star className="w-4 h-4 mr-2 text-yellow" />
              Unlimited AI credits
            </div>
            <div className="flex items-center text-sm text-gray-300">
              <Star className="w-4 h-4 mr-2 text-yellow" />
              Advanced analytics
            </div>
            <div className="flex items-center text-sm text-gray-300">
              <Star className="w-4 h-4 mr-2 text-yellow" />
              YouTube integration
            </div>
          </div>
        </div>

        <div className="pt-2">
          <Button 
            onClick={() => navigate('/pricing')}
            className="w-full bg-gradient-to-r from-orange to-yellow hover:from-orange/90 hover:to-yellow/90 text-white font-medium"
          >
            Choose Your Plan
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>

        <div className="text-center">
          <p className="text-xs text-gray-400">
            7-day free trial • Cancel anytime
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default ChoosePlanCard;
