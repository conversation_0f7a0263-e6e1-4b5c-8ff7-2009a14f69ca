
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Sparkles } from 'lucide-react';

const AIInsightsWidget = () => {
  const [currentInsightIndex, setCurrentInsightIndex] = useState(0);

  const insights = [
    "📈 Your tutorials get 3x more views than reviews",
    "🎯 'How to' titles outperform 'Top 10' by 40%",
    "⏰ Your audience is most active at 7 PM EST",
    "💡 Next video suggestion: 'How to Film in Low Light'",
    "🔥 Thumbnail A/B testing shows warm colors get 25% more clicks",
    "📱 65% of your views come from mobile devices",
    "⭐ Videos with captions get 15% higher engagement"
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentInsightIndex((prev) => (prev + 1) % insights.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [insights.length]);

  return (
    <Card className="bg-gradient-to-br from-teal to-purple-600 border-none text-white overflow-hidden relative">
      <div className="absolute inset-0 bg-black/20"></div>
      <CardHeader className="relative z-10 pb-3">
        <CardTitle className="text-white flex items-center text-lg">
          <div className="flex items-center space-x-2 mr-3">
            <Brain className="w-5 h-5" />
            <Sparkles className="w-4 h-4" />
          </div>
          AI Insights
        </CardTitle>
      </CardHeader>
      <CardContent className="relative z-10 space-y-4">
        <div className="space-y-3">
          <p className="text-sm font-medium text-white/90">This Week's AI Insights:</p>
          <div className="min-h-[60px] flex items-center">
            <p className="text-white/95 font-medium animate-fade-in">
              {insights[currentInsightIndex]}
            </p>
          </div>
        </div>
        
        <div className="flex items-center justify-between pt-2">
          <div className="flex space-x-1">
            {insights.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentInsightIndex ? 'bg-white' : 'bg-white/30'
                }`}
              />
            ))}
          </div>
          <Button 
            variant="ghost" 
            size="sm"
            className="text-white hover:bg-white/20 hover:text-white border border-white/30"
          >
            View More Insights
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default AIInsightsWidget;
