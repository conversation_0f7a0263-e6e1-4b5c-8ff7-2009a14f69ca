import React from 'react';
import { LucideIcon } from 'lucide-react';

interface StatCardProps {
  label: string;
  value: string | number;
  change: string;
  icon: LucideIcon;
  isYouTubeConnected: boolean;
}

const StatCard = ({ label, value, change, icon: Icon, isYouTubeConnected }: StatCardProps) => {
  const isPositive = change.includes('+');
  const isNegative = change.includes('-');
  const isNeutral = !isPositive && !isNegative;

  return (
    <div className="bg-purple-900/80 rounded-lg p-4 border border-purple-800 h-full">
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <h3 className="text-base font-medium text-gray-300 mb-1">{label}</h3>
          <div className="flex items-baseline">
            <p className="text-3xl font-semibold text-white truncate">{value}</p>
            {isYouTubeConnected && change && (
              <span
                className={`ml-2 text-base font-medium ${
                  isPositive ? 'text-green-400' :
                  isNegative ? 'text-red-400' :
                  'text-gray-400'
                }`}
              >
                {change}
              </span>
            )}
          </div>
        </div>
        <div className="p-2 bg-purple-800/80 rounded-lg">
          <Icon className="w-6 h-6 text-blue-400" />
        </div>
      </div>
    </div>
  );
};

export default StatCard;
