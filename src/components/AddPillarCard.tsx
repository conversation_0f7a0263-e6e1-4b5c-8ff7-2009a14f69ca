import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface AddPillarCardProps {
  pillarCount: number;
  maxPillars: number;
  onAddPillar: () => void;
  planName: string;
}

const AddPillarCard: React.FC<AddPillarCardProps> = ({ 
  pillarCount, 
  maxPillars, 
  onAddPillar,
  planName 
}) => {
  return (
    <Card className="glass-effect border-dashed border-gray-700 hover:border-white/30 transition-all">
      <CardContent className="flex flex-col items-center justify-center h-full py-8">
        <div className="w-12 h-12 rounded-full bg-gray-700/50 flex items-center justify-center mb-4">
          <Plus className="w-6 h-6 text-gray-400" />
        </div>
        <h3 className="text-white text-lg font-medium mb-1">Add New Pillar</h3>
        <p className="text-gray-400 text-sm mb-4">{pillarCount}/{maxPillars} pillars ({planName})</p>
        
        <Button 
          onClick={onAddPillar}
          className="glass-button bg-teal/20 hover:bg-teal/30 text-white border-teal/30"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Pillar
        </Button>
        
        {pillarCount >= maxPillars && (
          <p className="text-xs text-gray-500 mt-4">Upgrade your plan to add more pillars</p>
        )}
      </CardContent>
    </Card>
  );
};

export default AddPillarCard;