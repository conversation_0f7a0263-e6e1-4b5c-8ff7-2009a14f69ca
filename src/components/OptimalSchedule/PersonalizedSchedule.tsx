
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Clock, TrendingUp, Calendar } from 'lucide-react';

const PersonalizedSchedule = () => {
  return (
    <Card className="content-card border-gray-600/50">
      <CardHeader>
        <CardTitle className="text-white flex items-center space-x-2">
          <Calendar className="w-5 h-5 text-teal" />
          <span>Your Optimal Schedule</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="text-center py-8">
          <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            No Schedule Data Available
          </h3>
          <p className="text-gray-400 text-sm mb-4">
            We need more video data to analyze your optimal posting schedule.
          </p>
          <div className="space-y-2 text-sm text-gray-500">
            <p>• Publish more videos to build data</p>
            <p>• Connect your YouTube channel</p>
            <p>• Wait for performance analytics</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-gray-700/30 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="w-4 h-4 text-teal" />
              <span className="text-sm font-medium text-gray-300">Best Time</span>
            </div>
            <p className="text-gray-400 text-sm">Analyzing your data...</p>
          </div>
          
          <div className="p-4 bg-gray-700/30 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Calendar className="w-4 h-4 text-teal" />
              <span className="text-sm font-medium text-gray-300">Best Day</span>
            </div>
            <p className="text-gray-400 text-sm">Need more videos to analyze</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PersonalizedSchedule;
