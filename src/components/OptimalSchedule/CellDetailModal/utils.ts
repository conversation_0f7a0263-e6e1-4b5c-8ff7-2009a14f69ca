
export const generateDetailedStats = (pillar: string) => {
  const baseViews = 2000 + Math.random() * 8000;
  const boost = Math.random() * 80 - 20; // -20% to +60%
  const engagementRate = 3.5 + Math.random() * 4; // 3.5% to 7.5%
  const clickThroughRate = 8 + Math.random() * 6; // 8% to 14%
  const avgWatchTime = 3.2 + Math.random() * 2.8; // 3.2 to 6 minutes
  
  const videosCount = Math.floor(Math.random() * 5) + 1;
  const videos = Array.from({ length: videosCount }, (_, i) => ({
    title: `${pillar} Video ${i + 1}`,
    views: Math.round(baseViews * (0.7 + Math.random() * 0.6)),
    engagement: engagementRate + (Math.random() * 2 - 1),
    publishTime: `${Math.floor(Math.random() * 12) + 8}:00 ${Math.random() > 0.5 ? 'AM' : 'PM'}`
  }));

  return {
    avgViews: Math.round(baseViews),
    boost,
    engagementRate: Math.round(engagementRate * 100) / 100,
    clickThroughRate: Math.round(clickThroughRate * 100) / 100,
    avgWatchTime: Math.round(avgWatchTime * 100) / 100,
    videos: videos.sort((a, b) => b.views - a.views),
    bestTime: videos.reduce((best, current) => 
      current.views > best.views ? current : best
    ).publishTime,
    totalEngagement: Math.round(baseViews * engagementRate / 100)
  };
};
