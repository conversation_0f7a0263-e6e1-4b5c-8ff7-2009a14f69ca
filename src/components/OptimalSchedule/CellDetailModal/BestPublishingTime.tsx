
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Clock } from 'lucide-react';

interface BestPublishingTimeProps {
  bestTime: string;
  day: string;
  boost: number;
}

const BestPublishingTime = ({ bestTime, day, boost }: BestPublishingTimeProps) => {
  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="flex items-center text-white">
          <Clock className="w-5 h-5 mr-2 text-teal" />
          Optimal Publishing Time
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <div className="text-3xl font-bold text-white">{bestTime}</div>
            <div className="text-gray-300">Peak engagement window on {day}s</div>
          </div>
          <div className="text-right">
            <div className="text-lg font-semibold text-teal">+{Math.round(boost * 0.3)}%</div>
            <div className="text-sm text-gray-400">vs other times</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BestPublishingTime;
