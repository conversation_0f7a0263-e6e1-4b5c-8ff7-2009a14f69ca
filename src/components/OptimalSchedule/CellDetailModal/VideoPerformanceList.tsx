
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface Video {
  title: string;
  views: number;
  engagement: number;
  publishTime: string;
}

interface VideoPerformanceListProps {
  videos: Video[];
  pillar: string;
  day: string;
}

const VideoPerformanceList = ({ videos, pillar, day }: VideoPerformanceListProps) => {
  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white">Recent {pillar} Videos on {day}s</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {videos.map((video, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
              <div className="flex-1">
                <div className="font-medium text-white">{video.title}</div>
                <div className="text-sm text-gray-400">Published at {video.publishTime}</div>
              </div>
              <div className="text-right">
                <div className="font-semibold text-white">{video.views.toLocaleString()} views</div>
                <div className="text-sm text-gray-400">{video.engagement}% engagement</div>
              </div>
              {index === 0 && (
                <div className="ml-3 px-2 py-1 bg-teal/20 text-teal text-xs font-medium rounded">
                  Top Performer
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default VideoPerformanceList;
