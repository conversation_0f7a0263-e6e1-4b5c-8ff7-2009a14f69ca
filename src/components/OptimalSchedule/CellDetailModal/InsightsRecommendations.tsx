
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface InsightsRecommendationsProps {
  pillar: string;
  day: string;
  boost: number;
  stats: {
    bestTime: string;
    engagementRate: number;
    avgWatchTime: number;
  };
}

const InsightsRecommendations = ({ pillar, day, boost, stats }: InsightsRecommendationsProps) => {
  const getRecommendation = () => {
    if (boost > 30) {
      return `${pillar} content performs exceptionally well on ${day}s. Consider scheduling your most important ${pillar.toLowerCase()} videos on this day.`;
    } else if (boost > 10) {
      return `${day} is a strong day for ${pillar} content. Good choice for regular publishing.`;
    } else if (boost > -10) {
      return `${pillar} content performs averagely on ${day}s. Consider optimizing posting times or content format.`;
    } else {
      return `${day} shows lower performance for ${pillar} content. Consider focusing on other days or different content types.`;
    }
  };

  const isPositiveBoost = boost > 0;

  return (
    <Card className="bg-gradient-to-r from-teal/10 to-orange/10 border border-teal/30">
      <CardHeader>
        <CardTitle className="text-white">📊 Performance Insights</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-300 leading-relaxed">
          {getRecommendation()}
        </p>
        
        <div className="mt-4 p-4 bg-gray-800/50 rounded-lg">
          <h4 className="font-semibold text-white mb-2">Quick Tips:</h4>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• Best publishing time: {stats.bestTime}</li>
            <li>• Average performance boost: {isPositiveBoost ? '+' : ''}{Math.round(boost)}%</li>
            <li>• Optimal content length: {stats.avgWatchTime} minutes</li>
            <li>• Expected engagement: ~{stats.engagementRate}%</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default InsightsRecommendations;
