
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { TrendingUp, TrendingDown, Eye, Heart, Users, Play } from 'lucide-react';

interface MetricsOverviewProps {
  stats: {
    avgViews: number;
    boost: number;
    engagementRate: number;
    clickThroughRate: number;
    avgWatchTime: number;
    totalEngagement: number;
  };
}

const MetricsOverview = ({ stats }: MetricsOverviewProps) => {
  const isPositiveBoost = stats.boost > 0;

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <Card className="bg-gray-800 border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-sm text-white">
            <Eye className="w-4 h-4 mr-2 text-teal" />
            Avg Views
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-white">{stats.avgViews.toLocaleString()}</div>
          <div className={`text-sm flex items-center ${isPositiveBoost ? 'text-green-400' : 'text-red-400'}`}>
            {isPositiveBoost ? <TrendingUp className="w-3 h-3 mr-1" /> : <TrendingDown className="w-3 h-3 mr-1" />}
            {isPositiveBoost ? '+' : ''}{Math.round(stats.boost)}% vs avg
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gray-800 border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-sm text-white">
            <Heart className="w-4 h-4 mr-2 text-teal" />
            Engagement
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-white">{stats.engagementRate}%</div>
          <div className="text-sm text-gray-400">{stats.totalEngagement.toLocaleString()} interactions</div>
        </CardContent>
      </Card>

      <Card className="bg-gray-800 border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-sm text-white">
            <Users className="w-4 h-4 mr-2 text-teal" />
            Click Rate
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-white">{stats.clickThroughRate}%</div>
          <div className="text-sm text-gray-400">Thumbnail CTR</div>
        </CardContent>
      </Card>

      <Card className="bg-gray-800 border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-sm text-white">
            <Play className="w-4 h-4 mr-2 text-teal" />
            Watch Time
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-white">{stats.avgWatchTime}m</div>
          <div className="text-sm text-gray-400">Average duration</div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MetricsOverview;
