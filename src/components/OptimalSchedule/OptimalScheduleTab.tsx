
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, <PERSON>Title } from '@/components/ui/card';
import { Calendar } from 'lucide-react';
import ScheduleHeatmap from './ScheduleHeatmap';
import SimplifiedScheduleFlow from './SimplifiedScheduleFlow';
import { useState } from 'react';

interface ContentPillar {
  id: string;
  name: string;
  color: string;
  actual_percentage: number;
  video_count: number;
  avg_views: number;
  best_day: string;
  best_day_boost: number;
}

interface OptimalScheduleTabProps {
  pillars: ContentPillar[];
}

const OptimalScheduleTab = ({ pillars }: OptimalScheduleTabProps) => {
  const [heatmapData, setHeatmapData] = useState(null);

  // Add a callback function to receive heatmap data
  const onHeatmapDataReady = (data) => {
    setHeatmapData(data);
  };

  return (
    <div className="space-y-8">
      {/* Simplified Header */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-white">Your Optimal Publishing Schedule</h2>
        <p className="text-gray-300">Based on your historical performance data from the last 90 days</p>
      </div>

      {/* Performance Heatmap - Simplified */}
      <Card className="bg-gray-800 border-gray-600">
        <CardHeader>
          <CardTitle className="text-white flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-teal" />
            <span>When Your Content Performs Best</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScheduleHeatmap onDataReady={onHeatmapDataReady} />
        </CardContent>
      </Card>

      {/* Simplified Schedule Flow - Now with heatmap data */}
      <SimplifiedScheduleFlow pillars={pillars} heatmapData={heatmapData} />
    </div>
  );
};

export default OptimalScheduleTab;
