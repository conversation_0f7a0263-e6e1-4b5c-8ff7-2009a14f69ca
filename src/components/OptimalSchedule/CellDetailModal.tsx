
import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import MetricsOverview from './CellDetailModal/MetricsOverview';
import BestPublishingTime from './CellDetailModal/BestPublishingTime';
import VideoPerformanceList from './CellDetailModal/VideoPerformanceList';
import InsightsRecommendations from './CellDetailModal/InsightsRecommendations';
import { generateDetailedStats } from './CellDetailModal/utils';

interface CellDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  pillar: string;
  day: string;
  pillarColor: string;
}

const CellDetailModal = ({ isOpen, onClose, pillar, day, pillarColor }: CellDetailModalProps) => {
  const stats = generateDetailedStats(pillar);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-3">
            <div 
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: pillarColor }}
            />
            <span>{pillar} Performance on {day}s</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <MetricsOverview stats={stats} />
          
          <BestPublishingTime 
            bestTime={stats.bestTime}
            day={day}
            boost={stats.boost}
          />

          <VideoPerformanceList 
            videos={stats.videos}
            pillar={pillar}
            day={day}
          />

          <InsightsRecommendations 
            pillar={pillar}
            day={day}
            boost={stats.boost}
            stats={stats}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CellDetailModal;
