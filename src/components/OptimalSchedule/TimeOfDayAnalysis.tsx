
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from '@/components/ui/card';
import { Clock, BarChart3 } from 'lucide-react';

const TimeOfDayAnalysis = () => {
  const timeSlots = [
    { label: 'Morning (6AM-12PM)', performance: 0, color: 'bg-gray-600/20' },
    { label: 'Afternoon (12PM-6PM)', performance: 0, color: 'bg-gray-600/20' },
    { label: 'Evening (6PM-10PM)', performance: 0, color: 'bg-gray-600/20' },
    { label: 'Night (10PM-6AM)', performance: 0, color: 'bg-gray-600/20' }
  ];

  return (
    <Card className="content-card border-gray-600/50">
      <CardHeader>
        <CardTitle className="text-white flex items-center space-x-2">
          <Clock className="w-5 h-5 text-teal" />
          <span>Time of Day Analysis</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-6 mb-6">
          <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            No Performance Data
          </h3>
          <p className="text-gray-400 text-sm">
            Publish more videos at different times to see performance patterns.
          </p>
        </div>

        <div className="space-y-4">
          {timeSlots.map((slot, index) => (
            <div key={index} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-300">{slot.label}</span>
                <span className="text-sm text-gray-400">No data</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${slot.color}`}
                  style={{ width: '0%' }}
                />
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 p-4 bg-gray-700/20 rounded-lg">
          <p className="text-sm text-gray-400">
            💡 <strong>Tip:</strong> Try publishing videos at different times and days to discover when your audience is most active.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default TimeOfDayAnalysis;
