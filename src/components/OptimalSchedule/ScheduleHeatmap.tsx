
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, Clock, TrendingUp } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { useSettingsPreferences } from '@/hooks/settings/useSettingsPreferences';

interface HeatmapData {
  day: number; // 0-6 (Sunday-Saturday)
  hour: number; // 0-23
  count: number; // Number of videos published
  avgViews: number; // Average views for videos published at this time
  performance: number; // Performance score (0-100)
}

interface ScheduleHeatmapProps {
  onDataReady?: (data: any) => void;
}

const ScheduleHeatmap = ({ onDataReady }: ScheduleHeatmapProps) => {
  const { user } = useAuth();
  const { preferences } = useSettingsPreferences();
  const [heatmapData, setHeatmapData] = useState<HeatmapData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasData, setHasData] = useState(false);

  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const timeSlots = [0, 6, 12, 18]; // 12am, 6am, 12pm, 6pm

  useEffect(() => {
    if (user) {
      fetchHeatmapData();
    }
  }, [user]);

  const fetchHeatmapData = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Fetch published videos with views data
      const { data: videos, error } = await supabase
        .from('videos')
        .select('published_at, views, youtube_video_id')
        .eq('user_id', user.id)
        .eq('status', 'published')
        .not('published_at', 'is', null)
        .not('youtube_video_id', 'is', null)
        .gte('published_at', new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()); // Last 90 days

      if (error) throw error;

      if (!videos || videos.length < 3) {
        setHasData(false);
        setIsLoading(false);
        return;
      }

      // Process videos into heatmap data
      const heatmapMap = new Map<string, { count: number; totalViews: number; videos: any[] }>();

      // Debug: Log timezone conversion for first few videos
      const currentUserTimezone = getUserTimezone();
      console.log('🕐 Heatmap Timezone Debug:', {
        userTimezone: currentUserTimezone,
        userTimezoneFromPreferences: preferences?.timezone,
        autoDetectedTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        userTimezoneOffset: new Date().getTimezoneOffset(),
        sampleVideos: videos.slice(0, 3).map(v => {
          const date = new Date(v.published_at);
          // Convert to user's preferred timezone
          const userTimezoneDate = new Date(date.toLocaleString("en-US", {timeZone: currentUserTimezone}));
          return {
            published_at_raw: v.published_at,
            parsed_date: date.toISOString(),
            local_string: date.toLocaleString(),
            user_timezone_string: userTimezoneDate.toLocaleString(),
            utc_string: date.toUTCString(),
            local_day: date.getDay(),
            local_hour: date.getHours(),
            user_timezone_day: userTimezoneDate.getDay(),
            user_timezone_hour: userTimezoneDate.getHours(),
            utc_day: date.getUTCDay(),
            utc_hour: date.getUTCHours(),
            timezone_offset_minutes: date.getTimezoneOffset()
          };
        })
      });

      videos.forEach(video => {
        if (!video.published_at) return;

        // Convert to user's preferred timezone for analysis
        const publishDate = new Date(video.published_at);

        // Convert to user's timezone using their preference
        const userTimezoneDate = new Date(publishDate.toLocaleString("en-US", {timeZone: currentUserTimezone}));

        // Use user timezone methods to get day and hour
        const day = userTimezoneDate.getDay(); // 0-6 (Sunday-Saturday) in user's timezone
        const userHour = userTimezoneDate.getHours(); // 0-23 in user's timezone
        const hour = Math.floor(userHour / 6) * 6; // Group into 6-hour slots (0, 6, 12, 18)
        const key = `${day}-${hour}`;

        if (!heatmapMap.has(key)) {
          heatmapMap.set(key, { count: 0, totalViews: 0, videos: [] });
        }

        const slot = heatmapMap.get(key)!;
        slot.count++;
        slot.totalViews += video.views || 0;
        slot.videos.push(video);
      });

      // Convert to heatmap data with performance scores
      const processedData: HeatmapData[] = [];
      const allAvgViews = Array.from(heatmapMap.values()).map(slot =>
        slot.count > 0 ? slot.totalViews / slot.count : 0
      );
      const maxAvgViews = Math.max(...allAvgViews);

      for (let day = 0; day < 7; day++) {
        for (const hour of timeSlots) {
          const key = `${day}-${hour}`;
          const slot = heatmapMap.get(key);

          if (slot && slot.count > 0) {
            const avgViews = slot.totalViews / slot.count;
            const performance = maxAvgViews > 0 ? Math.round((avgViews / maxAvgViews) * 100) : 0;

            processedData.push({
              day,
              hour,
              count: slot.count,
              avgViews,
              performance
            });
          } else {
            processedData.push({
              day,
              hour,
              count: 0,
              avgViews: 0,
              performance: 0
            });
          }
        }
      }

      setHeatmapData(processedData);
      setHasData(true);
    } catch (error) {
      console.error('Error fetching heatmap data:', error);
      setHasData(false);
    } finally {
      setIsLoading(false);
    }
  };

  const getIntensityColor = (performance: number) => {
    if (performance === 0) return 'bg-gray-700/20';
    if (performance < 20) return 'bg-gray-600/30';
    if (performance < 40) return 'bg-gray-500/50';
    if (performance < 60) return 'bg-teal/30';
    if (performance < 80) return 'bg-teal/60';
    return 'bg-teal';
  };

  const formatTime = (hour: number) => {
    if (hour === 0) return '12am';
    if (hour === 12) return '12pm';
    return hour > 12 ? `${hour - 12}pm` : `${hour}am`;
  };

  const formatTimeRange = (hour: number) => {
    const endHour = hour + 6;
    return `${formatTime(hour)} - ${formatTime(endHour === 24 ? 0 : endHour)}`;
  };

  const getDataForSlot = (day: number, hour: number) => {
    return heatmapData.find(d => d.day === day && d.hour === hour);
  };

  const getRecommendations = () => {
    if (!heatmapData.length) return null;

    // Get top performing time slots (with at least 1 video)
    const performingSlots = heatmapData
      .filter(d => d.count > 0 && d.performance > 0)
      .sort((a, b) => b.performance - a.performance)
      .slice(0, 5);

    // Get best days overall
    const dayPerformance = days.map((dayName, dayIndex) => {
      const dayData = heatmapData.filter(d => d.day === dayIndex && d.count > 0);
      if (dayData.length === 0) return { day: dayName, avgPerformance: 0, totalVideos: 0 };

      const avgPerformance = dayData.reduce((sum, d) => sum + d.performance, 0) / dayData.length;
      const totalVideos = dayData.reduce((sum, d) => sum + d.count, 0);

      return { day: dayName, avgPerformance, totalVideos };
    }).sort((a, b) => b.avgPerformance - a.avgPerformance);

    // Get best times overall
    const timePerformance = timeSlots.map(hour => {
      const timeData = heatmapData.filter(d => d.hour === hour && d.count > 0);
      if (timeData.length === 0) return { time: formatTime(hour), avgPerformance: 0, totalVideos: 0 };

      const avgPerformance = timeData.reduce((sum, d) => sum + d.performance, 0) / timeData.length;
      const totalVideos = timeData.reduce((sum, d) => sum + d.count, 0);

      return { time: formatTime(hour), avgPerformance, totalVideos };
    }).sort((a, b) => b.avgPerformance - a.avgPerformance);

    return {
      topSlots: performingSlots,
      bestDays: dayPerformance.filter(d => d.totalVideos > 0).slice(0, 3),
      bestTimes: timePerformance.filter(t => t.totalVideos > 0).slice(0, 3),
      totalVideos: heatmapData.reduce((sum, d) => sum + d.count, 0)
    };
  };

  const recommendations = getRecommendations();

  // Get user's timezone for display - use preference if available, fallback to auto-detection
  const getUserTimezone = () => {
    if (preferences?.timezone) {
      return preferences.timezone;
    }
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch (error) {
      return 'Local Time';
    }
  };

  const userTimezone = getUserTimezone();

  // Test timezone behavior with a known UTC timestamp
  const testTimezoneConversion = () => {
    const utcTimestamp = "2024-01-15T14:30:00Z"; // 2:30 PM UTC
    const date = new Date(utcTimestamp);

    console.log('🧪 Timezone Conversion Test:', {
      input_utc: utcTimestamp,
      parsed_iso: date.toISOString(),
      local_string: date.toLocaleString(),
      local_day: date.getDay(),
      local_hour: date.getHours(),
      utc_day: date.getUTCDay(),
      utc_hour: date.getUTCHours(),
      timezone_offset: date.getTimezoneOffset(),
      user_timezone: userTimezone
    });
  };

  // Run test on component mount
  React.useEffect(() => {
    testTimezoneConversion();
  }, []);

  // Call the callback with the recommendations data
  useEffect(() => {
    if (recommendations && onDataReady) {
      onDataReady(recommendations);
    }
  }, [recommendations, onDataReady]);

  if (isLoading) {
    return (
      <Card className="content-card border-gray-600/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-teal" />
            <span>Publishing Heatmap</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Clock className="w-16 h-16 text-gray-400 mx-auto mb-4 animate-pulse" />
            <p className="text-gray-400">Loading your publishing patterns...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="content-card border-gray-600/50">
      <CardContent>
        {!hasData ? (
          <div className="text-center py-8 mb-6">
            <Clock className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-300 mb-2">
              Heatmap Not Available
            </h3>
            <p className="text-gray-400 text-sm">
              We need more published videos to generate your optimal posting schedule heatmap.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex items-center justify-between mb-4">
              <div className="space-y-1">
                <p className="text-sm text-gray-400">
                  Based on {recommendations?.totalVideos || 0} published videos (last 90 days)
                </p>
                <p className="text-xs text-gray-500">
                  All times shown in your local timezone: {userTimezone}
                </p>
              </div>
            </div>

            {/* Simplified Heatmap */}
            <div className="overflow-x-auto">
              <div className="min-w-[600px]">
                <div className="grid grid-cols-8 gap-1 mb-1">
                  <div className="text-xs text-gray-400"></div>
                  {days.map((day, index) => (
                    <div key={index} className="text-xs text-gray-400 text-center font-medium">
                      {day.substring(0, 3)}
                    </div>
                  ))}
                </div>

                {timeSlots.map(hour => (
                  <div key={hour} className="grid grid-cols-8 gap-1 mb-1">
                    <div className="text-xs text-gray-400 text-right pr-2">
                      {formatTime(hour)}
                    </div>
                    {days.map((_, dayIndex) => {
                      const data = getDataForSlot(dayIndex, hour);
                      const isTopPerformer = recommendations?.topSlots.slice(0, 3).some(slot =>
                        slot.day === dayIndex && slot.hour === hour
                      );

                      return (
                        <div
                          key={dayIndex}
                          className={`h-8 rounded flex items-center justify-center cursor-pointer transition-all duration-200 relative ${
                            isTopPerformer
                              ? 'border-2 border-teal shadow-lg'
                              : 'border border-gray-600/30'
                          } ${getIntensityColor(data?.performance || 0)}`}
                          title={`${days[dayIndex]} ${formatTime(hour)}: ${data?.count || 0} videos, ${Math.round(data?.avgViews || 0)} avg views`}
                        >
                          {isTopPerformer && (
                            <div className="absolute -top-1 -right-1 w-2 h-2 bg-teal rounded-full"></div>
                          )}
                          <span className="text-xs text-white font-medium">
                            {data && data.count > 0 ? data.count : ''}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                ))}
              </div>
            </div>

            {/* Simplified Recommendation */}
            <div className="bg-teal/10 border border-teal/20 rounded-lg p-3">
              <h4 className="text-sm font-medium text-teal mb-1">💡 Quick Recommendation</h4>
              <p className="text-sm text-gray-300">
                {recommendations.topSlots.length > 0 ? (
                  <>
                    Your best performing time is <strong className="text-white">
                    {days[recommendations.topSlots[0].day]} between {formatTimeRange(recommendations.topSlots[0].hour)}
                    </strong>
                  </>
                ) : (
                  "Publish more videos to get personalized recommendations."
                )}
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ScheduleHeatmap;
