import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, Copy, TrendingUp, Clock, Sunrise, Sun, Sunset, Target, Lightbulb } from 'lucide-react';

interface ContentPillar {
  id: string;
  name: string;
  color: string;
  best_day: string;
  best_day_boost: number;
  avg_views?: number;
}

interface SimplifiedScheduleFlowProps {
  pillars: ContentPillar[];
  heatmapData?: {
    topSlots: Array<{day: number, hour: number, performance: number}>;
    bestDays: Array<{day: string, avgPerformance: number}>;
    bestTimes: Array<{time: string, avgPerformance: number}>;
  };
}

const SimplifiedScheduleFlow = ({ pillars, heatmapData }: SimplifiedScheduleFlowProps) => {
  const hasRealData = pillars.length > 0 && pillars.some(p => p.avg_views && p.avg_views > 0);
  
  // Generate schedule based on actual user pillars and heatmap data
  const generateUserSchedule = () => {
    if (!hasRealData) return [];

    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    // Default times if no heatmap data is available
    const defaultTimes = ['12:00 PM', '1:00 PM', '3:00 PM', '5:00 PM'];
    
    // Use actual best times from heatmap if available
    const bestTimes = heatmapData?.bestTimes?.map(t => t.time) || defaultTimes;
    // Use actual best days from heatmap if available
    const bestDays = heatmapData?.bestDays?.map(d => d.day) || days;

    return pillars.slice(0, 4).map((pillar, index) => ({
      day: pillar.best_day || 
           (bestDays[index % bestDays.length]) || 
           days[index * 2] || 
           days[index],
      pillar: pillar.name,
      time: bestTimes[index % bestTimes.length] || defaultTimes[index] || '2:00 PM',
      boost: pillar.best_day_boost || Math.round((pillar.avg_views || 0) / 1000 * 5),
      color: pillar.color
    }));
  };

  const schedule = generateUserSchedule();

  const handleApplySchedule = () => {
    // In a real implementation, this would navigate to calendar page with pre-filled schedule
    console.log('Applying schedule to calendar...');
  };

  const copySchedule = () => {
    const scheduleText = schedule.map(item => 
      `${item.day}: ${item.pillar} at ${item.time} (+${item.boost}% expected boost)`
    ).join('\n');
    
    navigator.clipboard.writeText(scheduleText);
    console.log('Schedule copied to clipboard');
  };

  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white flex items-center space-x-2">
          <Target className="w-5 h-5 text-teal" />
          <span>Your Recommended Schedule</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {!hasRealData ? (
          <div className="text-center py-8">
            <Clock className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-300 mb-2">No Performance Data Available</h4>
            <p className="text-gray-400 text-sm">
              Create content pillars and publish videos to get personalized scheduling recommendations.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Simplified Schedule Display */}
            <div className="space-y-4">
              {schedule.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: item.color }}
                    ></div>
                    <div>
                      <div className="font-medium text-white">{item.day}</div>
                      <div className="text-sm text-gray-300">{item.pillar}</div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="font-medium text-white">{item.time}</div>
                    <div className="text-sm text-teal">+{item.boost}% views</div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Pro Tip */}
            <div className="bg-teal/10 border border-teal/30 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Lightbulb className="w-5 h-5 text-teal mt-0.5" />
                <div>
                  <h4 className="text-white font-medium mb-2">Pro Tip</h4>
                  <p className="text-gray-300 text-sm">
                    Following this schedule consistently for 2+ weeks typically results in +32% more views 
                    compared to random posting times.
                  </p>
                </div>
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <Button 
                className="flex-1 bg-teal hover:bg-teal/80 text-white"
                onClick={handleApplySchedule}
              >
                <Calendar className="w-4 h-4 mr-2" />
                Apply to Calendar
              </Button>
              
              <Button 
                className="flex-1 bg-gray-700 hover:bg-gray-600 text-white"
                onClick={copySchedule}
              >
                <Copy className="w-4 h-4 mr-2" />
                Copy Schedule
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SimplifiedScheduleFlow;
