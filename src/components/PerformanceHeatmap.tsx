
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface HeatmapData {
  pillar: string;
  days: number[]; // Performance scores for each day (0-100)
  color: string;
}

interface PerformanceHeatmapProps {
  pillars: Array<{
    id: string;
    name: string;
    color: string;
  }>;
}

const PerformanceHeatmap = ({ pillars }: PerformanceHeatmapProps) => {
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  
  // Mock data - in real app this would come from analytics
  const heatmapData: HeatmapData[] = pillars.map(pillar => ({
    pillar: pillar.name,
    days: [65, 45, 80, 90, 75, 40, 35], // Mock performance scores
    color: pillar.color
  }));

  // Find best day/pillar combination
  const bestPerformance = heatmapData.reduce((best, pillarData) => {
    const maxDayIndex = pillarData.days.indexOf(Math.max(...pillarData.days));
    const maxScore = pillarData.days[maxDayIndex];
    
    if (maxScore > best.score) {
      return {
        pillar: pillarData.pillar,
        day: days[maxDayIndex],
        score: maxScore
      };
    }
    return best;
  }, { pillar: '', day: '', score: 0 });

  const getIntensity = (score: number) => {
    // Convert score to opacity for teal color
    const opacity = (score / 100) * 0.8 + 0.1; // Min 0.1, max 0.9
    return `rgba(55, 190, 176, ${opacity})`;
  };

  return (
    <Card className="content-card mt-8">
      <CardHeader>
        <CardTitle className="text-white">When Each Pillar Performs Best</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Heatmap Grid */}
          <div className="overflow-x-auto">
            <div className="min-w-[600px]">
              {/* Header */}
              <div className="grid grid-cols-8 gap-2 mb-2">
                <div className="text-sm font-medium text-gray-300"></div>
                {days.map(day => (
                  <div key={day} className="text-sm font-medium text-gray-300 text-center">
                    {day}
                  </div>
                ))}
              </div>
              
              {/* Heatmap Rows */}
              {heatmapData.map((pillarData, index) => (
                <div key={index} className="grid grid-cols-8 gap-2 mb-2">
                  <div className="text-sm text-gray-300 truncate pr-2">
                    {pillarData.pillar}
                  </div>
                  {pillarData.days.map((score, dayIndex) => (
                    <div
                      key={dayIndex}
                      className="h-8 rounded border border-gray-600 flex items-center justify-center text-xs text-white cursor-pointer hover:border-teal transition-colors"
                      style={{ backgroundColor: getIntensity(score) }}
                      title={`${pillarData.pillar} on ${days[dayIndex]}: ${score}% performance`}
                    >
                      {score}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>

          {/* Legend */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-600">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-300">Performance:</span>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'rgba(55, 190, 176, 0.2)' }}></div>
                <span className="text-xs text-gray-400">Low</span>
                <div className="w-4 h-4 rounded" style={{ backgroundColor: 'rgba(55, 190, 176, 0.9)' }}></div>
                <span className="text-xs text-gray-400">High</span>
              </div>
            </div>
            
            {/* Recommendation */}
            <div className="text-sm text-teal font-medium">
              Next recommendation: Post {bestPerformance.pillar} on {bestPerformance.day}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PerformanceHeatmap;
