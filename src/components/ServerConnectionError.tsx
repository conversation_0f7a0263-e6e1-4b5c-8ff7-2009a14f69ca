import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Server, Settings } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { checkApiConnection } from '@/lib/connection-utils';

interface ServerConnectionErrorProps {
  onRetry?: () => void;
  errorMessage?: string;
  showDiagnostics?: boolean;
}

const ServerConnectionError = ({ 
  onRetry, 
  errorMessage,
  showDiagnostics = true
}: ServerConnectionErrorProps) => {
  const navigate = useNavigate();
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const handleRetry = async () => {
    if (onRetry) {
      onRetry();
      return;
    }

    setIsRetrying(true);
    setRetryCount(prev => prev + 1);
    
    try {
      const result = await checkApiConnection('/health');
      if (result.ok) {
        window.location.reload();
      } else {
        // Still not connected
        setTimeout(() => setIsRetrying(false), 1000);
      }
    } catch (error) {
      console.error('Retry connection failed:', error);
      setTimeout(() => setIsRetrying(false), 1000);
    }
  };

  const handleRunDiagnostics = () => {
    navigate('/diagnostics');
  };

  return (
    <Card className="max-w-md mx-auto bg-gray-800 border-orange-500/30">
      <CardHeader className="pb-3">
        <CardTitle className="text-xl font-bold text-white flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-orange-500" />
          Server Connection Error
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col items-center justify-center py-4">
          <Server className="w-12 h-12 text-orange-500" />
          <p className="text-gray-300 text-center mt-4">
            {errorMessage || "We're having trouble connecting to our servers. This could be due to your internet connection or our servers might be temporarily unavailable."}
          </p>
        </div>

        <div className="space-y-3">
          <Button 
            onClick={handleRetry} 
            disabled={isRetrying}
            className="w-full bg-teal hover:bg-teal/90 text-white"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
            {isRetrying ? 'Retrying...' : 'Retry Connection'}
          </Button>
          
          {showDiagnostics && (
            <Button 
              onClick={handleRunDiagnostics}
              variant="outline" 
              className="w-full border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              <Settings className="w-4 h-4 mr-2" />
              Run Connection Diagnostics
            </Button>
          )}
        </div>

        <div className="bg-gray-700/50 rounded-lg p-3 text-sm text-gray-400">
          <p className="font-medium text-gray-300 mb-1">Troubleshooting Tips:</p>
          <ul className="space-y-1 list-disc pl-5">
            <li>Check your internet connection</li>
            <li>Try refreshing the page</li>
            <li>Clear your browser cache</li>
            <li>Try again in a few minutes</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default ServerConnectionError;