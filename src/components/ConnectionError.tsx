import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Wifi, WifiOff, Server, Settings } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface ConnectionErrorProps {
  onRetry?: () => void;
  errorType?: 'server' | 'database' | 'api';
  errorMessage?: string;
  showDiagnostics?: boolean;
}

const ConnectionError = ({ 
  onRetry, 
  errorType = 'server',
  errorMessage,
  showDiagnostics = true
}: ConnectionErrorProps) => {
  const navigate = useNavigate();

  const getErrorTitle = () => {
    switch (errorType) {
      case 'database': return 'Database Connection Error';
      case 'api': return 'API Connection Error';
      default: return 'Server Connection Error';
    }
  };

  const getErrorIcon = () => {
    switch (errorType) {
      case 'database': return <Server className="w-12 h-12 text-orange-500" />;
      case 'api': return <Wifi className="w-12 h-12 text-orange-500" />;
      default: return <WifiOff className="w-12 h-12 text-orange-500" />;
    }
  };

  const getDefaultErrorMessage = () => {
    switch (errorType) {
      case 'database': return "We're having trouble connecting to our database. This might be due to maintenance or a temporary outage.";
      case 'api': return "We can't reach our API servers right now. This might be a temporary network issue.";
      default: return "We're having trouble connecting to our servers. This could be due to your internet connection or our servers might be temporarily unavailable.";
    }
  };

  const handleRunDiagnostics = () => {
    navigate('/diagnostics');
  };

  return (
    <Card className="max-w-md mx-auto bg-gray-800 border-orange-500/30">
      <CardHeader className="pb-3">
        <CardTitle className="text-xl font-bold text-white flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-orange-500" />
          {getErrorTitle()}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col items-center justify-center py-4">
          {getErrorIcon()}
          <p className="text-gray-300 text-center mt-4">
            {errorMessage || getDefaultErrorMessage()}
          </p>
        </div>

        <div className="space-y-3">
          <Button 
            onClick={onRetry} 
            className="w-full bg-teal hover:bg-teal/90 text-white"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry Connection
          </Button>
          
          {showDiagnostics && (
            <Button 
              onClick={handleRunDiagnostics}
              variant="outline" 
              className="w-full border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              <Settings className="w-4 h-4 mr-2" />
              Run Connection Diagnostics
            </Button>
          )}
        </div>

        <div className="bg-gray-700/50 rounded-lg p-3 text-sm text-gray-400">
          <p className="font-medium text-gray-300 mb-1">Troubleshooting Tips:</p>
          <ul className="space-y-1 list-disc pl-5">
            <li>Check your internet connection</li>
            <li>Try refreshing the page</li>
            <li>Clear your browser cache</li>
            <li>Try again in a few minutes</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default ConnectionError;