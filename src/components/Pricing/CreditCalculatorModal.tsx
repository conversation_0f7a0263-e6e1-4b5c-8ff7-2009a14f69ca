
import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';

interface CreditCalculatorModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CreditCalculatorModal = ({ isOpen, onClose }: CreditCalculatorModalProps) => {
  const aiTools = [
    { name: 'Title Optimizer', cost: 1, description: '1 optimized title set' },
    { name: 'Comment Assistant', cost: 1, description: '1 helpful reply' },
    { name: 'Keyword Research', cost: 2, description: '1 keyword list' },
    { name: 'Description Generator', cost: 2, description: '1 SEO description' },
    { name: 'Hook Generator', cost: 3, description: '1 compelling hook' },
    { name: 'Shorts Converter', cost: 4, description: '1 short script' },
    { name: 'Script Writer', cost: 5, description: '1 full video script' }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto bg-gray-800 border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-white text-xl font-montserrat">
            AI Credit Calculator
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <p className="text-gray-300 text-sm">
            Here's what you can create with your AI credits:
          </p>
          
          <div className="grid gap-3">
            {aiTools.map((tool, index) => (
              <Card key={index} className="bg-gray-700 border-gray-600">
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-white font-semibold">{tool.name}</h3>
                      <p className="text-gray-400 text-sm">{tool.description}</p>
                    </div>
                    <div className="text-right">
                      <span className="text-teal font-bold text-lg">{tool.cost}</span>
                      <p className="text-gray-400 text-xs">credit{tool.cost > 1 ? 's' : ''}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="mt-6 p-4 bg-teal/10 rounded-lg border border-teal/20">
            <h4 className="text-teal font-semibold mb-2">Credit Examples by Plan:</h4>
            <ul className="space-y-1 text-sm text-gray-300">
              <li>• <strong>MCH Starter (50 credits):</strong> 10 full scripts OR 50 titles OR 16 hooks</li>
              <li>• <strong>MCH Lite (200 credits):</strong> 40 scripts OR 200 titles OR 66 hooks</li>
              <li>• <strong>MCH Pro (500 credits):</strong> 100 premium scripts OR 500 titles OR 166 hooks</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreditCalculatorModal;
