
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, CheckCircle } from 'lucide-react';

interface PricingTier {
  name: string;
  id: string;
  monthlyPrice: number;
  annualPrice: number;
  description: string;
  features: string[];
  popular?: boolean;
  badge?: string;
  badgeColor?: string;
}

interface PricingTierCardProps {
  tier: PricingTier;
  isAnnual: boolean;
  isCurrentPlan: boolean;
  isLoading: boolean;
  onSubscribe: (tierId: string) => void;
}

const PricingTierCard = ({ 
  tier, 
  isAnnual, 
  isCurrentPlan, 
  isLoading, 
  onSubscribe 
}: PricingTierCardProps) => {
  const currentPrice = isAnnual ? tier.annualPrice : tier.monthlyPrice;
  const annualSavings = (tier.monthlyPrice * 12) - tier.annualPrice;

  return (
    <Card
      className={`relative dashboard-card h-full flex flex-col transition-all duration-200 hover:scale-105 ${
        tier.popular && !isCurrentPlan ? 'ring-2 ring-teal shadow-lg shadow-teal/20' : ''
      } ${isCurrentPlan ? 'ring-2 ring-terracotta shadow-lg shadow-terracotta/20 bg-terracotta/5' : ''}`}
    >
      {/* Remove the problematic badges */}
      
      <CardHeader className="text-center pb-4">
        <div className="text-center mb-2">
          <CardTitle className="text-white text-2xl font-bold font-montserrat inline-block">
            {tier.name}
          </CardTitle>
          {tier.badge && (
            <span className={`text-xs ${tier.badgeColor} px-2 py-1 rounded ml-2 inline-block`}>
              {tier.badge}
            </span>
          )}
        </div>
        <p className="text-gray-300 text-base mb-6">{tier.description}</p>
        
        <div className="mb-4">
          <div className="flex items-baseline justify-center">
            <span className="text-4xl font-bold text-white font-montserrat">
              ${currentPrice}
            </span>
            <span className="text-gray-400 ml-2 text-base">
              /{isAnnual ? 'year' : 'month'}
            </span>
          </div>
          {isAnnual && (
            <div className="mt-2">
              <p className="text-base text-teal font-semibold">
                Save ${annualSavings.toFixed(2)} per year
              </p>
              <p className="text-sm text-gray-400">
                (2 months free!)
              </p>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col">        
        <div className="flex-1 mb-6">
          <ul className="space-y-4">
            {tier.features.map((feature, index) => (
              <li key={index} className="flex items-start text-gray-300">
                <Check className="w-5 h-5 text-teal mr-3 flex-shrink-0 mt-0.5" />
                <span className="text-base leading-relaxed">{feature}</span>
              </li>
            ))}
          </ul>
        </div>
        
        <Button
          onClick={() => onSubscribe(tier.id)}
          disabled={isLoading || isCurrentPlan}
          className={`w-full py-3 font-semibold text-base transition-all duration-200 mt-auto ${
            isCurrentPlan
              ? 'bg-terracotta border-2 border-terracotta text-white hover:bg-terracotta cursor-not-allowed'
              : tier.popular
              ? 'bg-teal hover:bg-teal/90 shadow-lg hover:shadow-xl text-white'
              : tier.id === 'analytics_only' // MCH Starter
              ? 'bg-orange hover:bg-orange/90 shadow-md hover:shadow-lg text-white'
              : 'bg-terracotta hover:bg-terracotta/90 shadow-md hover:shadow-lg text-white'
          }`}
        >
          {isCurrentPlan ? (
            <div className="flex items-center justify-center">
              <CheckCircle className="w-4 h-4 mr-2" />
              Current Plan
            </div>
          ) : isLoading ? (
            'Processing...'
          ) : (
            'Start Free Trial'
          )}
        </Button>
      </CardContent>
    </Card>
  );
};

export default PricingTierCard;
