
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { <PERSON>rkles, Lightbulb } from 'lucide-react';
import { ContentPillar } from './types';

interface TopicObject {
  title?: string;
  text?: string;
  description?: string;
  reason?: string;
}

interface TopicSelectionStepProps {
  topicOption: string;
  setTopicOption: (option: string) => void;
  selectedPillarForTopics: string;
  setSelectedPillarForTopics: (pillar: string) => void;
  manualTopic: string;
  setManualTopic: (topic: string) => void;
  generatedTopics: TopicObject[];
  formData: any;
  setFormData: (data: any) => void;
  pillars: ContentPillar[];
  loading: boolean;
  onGenerateTopics: () => void;
  onTopicSelect: (topic: string, pillarId: string) => void;
}

const TopicSelectionStep: React.FC<TopicSelectionStepProps> = ({
  topicOption,
  setTopicOption,
  selectedPillarForTopics,
  setSelectedPillarForTopics,
  manualTopic,
  setManualTopic,
  generatedTopics,
  formData,
  setFormData,
  pillars,
  loading,
  onGenerateTopics,
  onTopicSelect
}) => {
  console.log('TopicSelectionStep rendered with:', { 
    topicOption, 
    selectedPillarForTopics, 
    generatedTopics: generatedTopics?.length || 0,
    loading,
    generatedTopicsData: generatedTopics
  });

  const safeGeneratedTopics = Array.isArray(generatedTopics) ? generatedTopics : [];

  const getTopicText = (topic: TopicObject): string => {
    if (typeof topic === 'string') {
      return topic;
    }
    if (topic && typeof topic === 'object') {
      return topic.title || topic.text || topic.description || String(topic);
    }
    return 'Untitled Topic';
  };

  const getTopicDescription = (topic: TopicObject): string => {
    if (typeof topic === 'object' && topic) {
      return topic.description || topic.reason || '';
    }
    return '';
  };

  const handleTopicSelection = (topic: TopicObject) => {
    const topicText = getTopicText(topic);
    console.log('Topic selected:', topicText, 'for pillar:', selectedPillarForTopics);
    onTopicSelect(topicText, selectedPillarForTopics);
  };

  const handleManualTopicContinue = () => {
    if (!manualTopic || !selectedPillarForTopics) {
      return;
    }
    console.log('Manual topic selected:', manualTopic, 'for pillar:', selectedPillarForTopics);
    onTopicSelect(manualTopic, selectedPillarForTopics);
  };

  const handleGenerateTopics = () => {
    if (!selectedPillarForTopics) {
      console.log('No pillar selected for topic generation');
      return;
    }
    console.log('Generating topics for pillar:', selectedPillarForTopics);
    onGenerateTopics();
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-white flex items-center justify-center">
          <Lightbulb className="w-5 h-5 mr-2 text-yellow" />
          Choose Your Video Topic
        </h3>
        <p className="text-gray-300">Start with a compelling topic for your video</p>
      </div>

      <RadioGroup value={topicOption} onValueChange={setTopicOption}>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="generate" id="generate" />
            <Label htmlFor="generate" className="text-white">Generate AI topics</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="manual" id="manual" />
            <Label htmlFor="manual" className="text-white">Enter my own topic</Label>
          </div>
        </div>
      </RadioGroup>

      {topicOption === 'generate' && (
        <div className="space-y-4">
          <Select value={selectedPillarForTopics} onValueChange={setSelectedPillarForTopics}>
            <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
              <SelectValue placeholder="Choose content pillar" />
            </SelectTrigger>
            <SelectContent className="bg-gray-700 border-gray-600">
              {pillars.map(pillar => (
                <SelectItem key={pillar.id} value={pillar.id}>
                  {pillar.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button 
            onClick={handleGenerateTopics} 
            disabled={loading || !selectedPillarForTopics}
            className="bg-teal hover:bg-teal/90 text-white w-full"
          >
            {loading ? (
              <>
                <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="w-4 h-4 mr-2" />
                Generate Topics (3 credits)
              </>
            )}
          </Button>

          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="w-6 h-6 border-4 border-teal border-t-transparent rounded-full animate-spin"></div>
              <span className="ml-3 text-gray-300">Generating topics...</span>
            </div>
          )}

          {!loading && safeGeneratedTopics.length > 0 && (
            <div className="space-y-3">
              <h4 className="text-lg font-medium text-white">Generated Topics:</h4>
              {safeGeneratedTopics.map((topic, index) => {
                const topicText = getTopicText(topic);
                const topicDescription = getTopicDescription(topic);
                
                return (
                  <Card key={index} className="bg-gray-700 border-gray-600 cursor-pointer hover:bg-gray-600 transition-colors">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className="text-white font-medium mb-1">
                            {topicText}
                          </p>
                          {topicDescription && (
                            <p className="text-gray-300 text-sm">{topicDescription}</p>
                          )}
                        </div>
                        <Button
                          size="sm"
                          onClick={() => handleTopicSelection(topic)}
                          className="bg-teal hover:bg-teal/90 text-white ml-4"
                        >
                          Select
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}

          {!loading && safeGeneratedTopics.length === 0 && selectedPillarForTopics && (
            <div className="text-center py-8">
              <p className="text-gray-400">Click "Generate Topics" to get AI-powered topic suggestions</p>
            </div>
          )}
        </div>
      )}

      {topicOption === 'manual' && (
        <div className="space-y-4">
          <Select value={selectedPillarForTopics} onValueChange={setSelectedPillarForTopics}>
            <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
              <SelectValue placeholder="Choose content pillar" />
            </SelectTrigger>
            <SelectContent className="bg-gray-700 border-gray-600">
              {pillars.map(pillar => (
                <SelectItem key={pillar.id} value={pillar.id}>
                  {pillar.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Input
            placeholder="Enter your video topic..."
            value={manualTopic}
            onChange={(e) => setManualTopic(e.target.value)}
            className="bg-gray-700 border-gray-600 text-white"
          />

          <Button
            onClick={handleManualTopicContinue}
            disabled={!manualTopic || !selectedPillarForTopics}
            className="bg-teal hover:bg-teal/90 text-white w-full"
          >
            Continue with This Topic
          </Button>
        </div>
      )}
    </div>
  );
};

export default TopicSelectionStep;
