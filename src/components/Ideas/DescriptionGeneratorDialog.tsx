
import React, { useState } from 'react';
import { <PERSON>, Copy, Loader2 } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';

interface DescriptionGeneratorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  ideaTitle?: string;
  ideaDescription?: string;
  pillarName?: string;
  onDescriptionGenerated?: (description: string) => void;
}

const DescriptionGeneratorDialog: React.FC<DescriptionGeneratorDialogProps> = ({
  isOpen,
  onClose,
  ideaTitle = '',
  ideaDescription = '',
  pillarName,
  onDescriptionGenerated
}) => {
  const { user } = useAuth();
  const [title, setTitle] = useState(ideaTitle);
  const [description, setDescription] = useState(ideaDescription);
  const [generatedDescription, setGeneratedDescription] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);

  const generateDescription = async () => {
    if (!user) {
      toast.error('Please sign in to use this feature');
      return;
    }

    if (!title.trim()) {
      toast.error('Please enter a video title');
      return;
    }

    setIsGenerating(true);

    try {
      const { data, error } = await supabase.functions.invoke('generate-description', {
        body: {
          title: title.trim(),
          description: description.trim() || '',
          pillar: pillarName || ''
        }
      });

      if (error) throw error;

      setGeneratedDescription(data.description);
      
      if (onDescriptionGenerated) {
        onDescriptionGenerated(data.description);
      }

      toast.success('Description generated successfully!');
    } catch (error) {
      console.error('Error generating description:', error);
      toast.error('Failed to generate description');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedDescription);
      toast.success('Description copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy description');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <List className="w-5 h-5 mr-2 text-terracotta" />
            AI Description Generator
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Video Title *
            </label>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter your video title..."
              className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Brief Description (Optional)
            </label>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Add any additional context about your video..."
              className="min-h-[80px] bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
            />
          </div>

          {pillarName && (
            <div className="bg-gray-700 p-3 rounded-lg">
              <p className="text-terracotta text-sm">Content Pillar: {pillarName}</p>
            </div>
          )}

          <Button 
            onClick={generateDescription}
            disabled={isGenerating || !title.trim()}
            className="bg-terracotta hover:bg-terracotta/90 text-white"
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <List className="w-4 h-4 mr-2" />
                Generate Description (2 credits)
              </>
            )}
          </Button>

          {generatedDescription && (
            <Card className="bg-gray-700 border-gray-600">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white">Generated Description</CardTitle>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={copyToClipboard}
                    className="border-terracotta text-terracotta hover:bg-terracotta hover:text-white"
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    Copy for YouTube
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={generatedDescription}
                  onChange={(e) => setGeneratedDescription(e.target.value)}
                  className="min-h-[300px] text-white bg-gray-800 border-gray-600"
                  placeholder="Your generated description will appear here..."
                />
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DescriptionGeneratorDialog;
