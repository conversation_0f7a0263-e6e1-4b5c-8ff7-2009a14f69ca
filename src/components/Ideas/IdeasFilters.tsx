
import React from 'react';
import { Search, Filter } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ContentPillar {
  id: string;
  name: string;
  color: string | null;
}

interface IdeasFiltersProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterPillar: string;
  setFilterPillar: (pillar: string) => void;
  filterPriority: string;
  setFilterPriority: (priority: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  pillars: ContentPillar[];
}

const IdeasFilters: React.FC<IdeasFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  filterPillar,
  setFilterPillar,
  filterPriority,
  setFilterPriority,
  filterStatus,
  setFilterStatus,
  pillars
}) => {
  return (
    <div className="bg-gray-700 rounded-lg border border-gray-600 p-6">
      <div className="flex flex-col md:flex-row gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search ideas by title or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-gray-800 border-gray-600 text-white"
          />
        </div>
        <Select value={filterPillar} onValueChange={setFilterPillar}>
          <SelectTrigger className="w-full md:w-48 bg-gray-800 border-gray-600 text-white">
            <Filter className="w-4 h-4 mr-2" />
            <SelectValue placeholder="Filter by pillar" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Pillars</SelectItem>
            {pillars.map(pillar => (
              <SelectItem key={pillar.id} value={pillar.id}>{pillar.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={filterPriority} onValueChange={setFilterPriority}>
          <SelectTrigger className="w-full md:w-32 bg-gray-800 border-gray-600 text-white">
            <SelectValue placeholder="Priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="High">High</SelectItem>
            <SelectItem value="Medium">Medium</SelectItem>
            <SelectItem value="Low">Low</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-full md:w-32 bg-gray-800 border-gray-600 text-white">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="idea">Idea</SelectItem>
            <SelectItem value="planned">Planned</SelectItem>
            <SelectItem value="filming">Filming</SelectItem>
            <SelectItem value="editing">Editing</SelectItem>
            <SelectItem value="published">Published</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default IdeasFilters;
