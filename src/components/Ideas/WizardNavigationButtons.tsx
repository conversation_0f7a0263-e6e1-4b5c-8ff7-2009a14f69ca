import React from 'react';
import { Button } from '@/components/ui/button';
import { Sparkles } from 'lucide-react';

interface WizardNavigationButtonsProps {
  currentStep: number;
  totalSteps: number;
  loading: boolean;
  formData: any;
  onBack: () => void;
  onNext: () => void;
  onFinish: () => void;
  onClose: () => void;
}

const WizardNavigationButtons: React.FC<WizardNavigationButtonsProps> = ({
  currentStep,
  totalSteps,
  loading,
  formData,
  onBack,
  onNext,
  onFinish,
  onClose
}) => {
  const isLastStep = currentStep === totalSteps - 1;
  
  const canProceed = () => {
    console.log('Checking if can proceed:', { currentStep, formData });
    
    if (currentStep === 0 && !formData.topic) {
      console.log('Cannot proceed: missing topic');
      return false;
    }
    if (currentStep === 1 && (!formData.title || !formData.hook)) {
      console.log('Cannot proceed: missing title or hook', { 
        title: formData.title, 
        hook: formData.hook 
      });
      return false;
    }
    console.log('Can proceed');
    return true;
  };

  const handleNextClick = () => {
    console.log('Next button clicked');
    onNext();
  };

  const handleFinishClick = () => {
    console.log('Finish button clicked');
    onFinish();
  };

  return (
    <div className="border-t border-gray-600 p-4 flex justify-between">
      <Button
        variant="outline"
        onClick={onBack}
        disabled={currentStep === 0}
        className="border-gray-600 text-gray-300 hover:bg-gray-700"
      >
        Back
      </Button>

      <div className="flex gap-2">
        <Button
          variant="outline"
          onClick={onClose}
          className="border-gray-600 text-gray-300 hover:bg-gray-700"
        >
          Cancel
        </Button>

        {isLastStep ? (
          <Button
            onClick={handleFinishClick}
            disabled={loading || !formData.title}
            className="bg-teal hover:bg-teal/90 text-white"
          >
            <Sparkles className="w-4 h-4 mr-2" />
            Create Idea
          </Button>
        ) : (
          <Button
            onClick={handleNextClick}
            disabled={loading || !canProceed()}
            className="bg-teal hover:bg-teal/90 text-white"
          >
            Next Step
          </Button>
        )}
      </div>
    </div>
  );
};

export default WizardNavigationButtons;
