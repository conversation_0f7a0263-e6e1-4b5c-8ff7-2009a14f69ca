
import React from 'react';
import { Calendar, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { CardContent } from '@/components/ui/card';
import { VideoIdea } from './types';

interface IdeaCardContentProps {
  idea: VideoIdea;
  onMoveToCalendar: (id: string, title: string) => void;
}

const IdeaCardContent: React.FC<IdeaCardContentProps> = ({
  idea,
  onMoveToCalendar
}) => {
  return (
    <CardContent className="flex-1 flex flex-col justify-between space-y-4">
      <div className="space-y-3">
        {idea.description && (
          <p className="text-gray-300 text-sm leading-relaxed line-clamp-2">
            {idea.description}
          </p>
        )}

        {idea.scheduled_date && (
          <div className="flex items-center text-sm text-gray-400">
            <Calendar className="w-4 h-4 mr-2" />
            Target: {new Date(idea.scheduled_date).toLocaleDateString()}
          </div>
        )}
      </div>

      {idea.status === 'idea' && (
        <Button
          onClick={() => onMoveToCalendar(idea.id, idea.title)}
          variant="outline"
          size="sm"
          className="w-full border-teal text-teal hover:bg-teal hover:text-white mt-4"
        >
          <ArrowRight className="w-4 h-4 mr-2" />
          Move to Calendar
        </Button>
      )}
    </CardContent>
  );
};

export default IdeaCardContent;
