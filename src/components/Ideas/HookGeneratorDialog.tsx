import React, { useState } from 'react';
import { <PERSON>, Loader2 } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';

interface HookGeneratorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  ideaTitle?: string;
  pillarName?: string;
}

const HookGeneratorDialog: React.FC<HookGeneratorDialogProps> = ({
  isOpen,
  onClose,
  ideaTitle = '',
  pillarName = ''
}) => {
  const { user } = useAuth();
  const [videoTitle, setVideoTitle] = useState(ideaTitle);
  const [hookStyle, setHookStyle] = useState<string>('');
  const [targetAudience, setTargetAudience] = useState<string>('');
  const [generatedHooks, setGeneratedHooks] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const generateHooks = async () => {
    if (!user || !videoTitle.trim() || !hookStyle || !targetAudience) {
      toast.error('Please fill in all fields');
      return;
    }

    setIsGenerating(true);

    try {
      const { data, error } = await supabase.functions.invoke('generate-hooks', {
        body: {
          title: videoTitle.trim(),
          hookStyle,
          targetAudience,
          pillar: pillarName
        }
      });

      if (error) throw error;

      setGeneratedHooks(data.hooks || []);
      toast.success('Hooks generated successfully!');
    } catch (error) {
      console.error('Error generating hooks:', error);
      toast.error('Failed to generate hooks');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyHook = async (hook: string) => {
    try {
      await navigator.clipboard.writeText(hook);
      toast.success('Hook copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy hook');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <Fish className="w-5 h-5 mr-2 text-teal" />
            Hook Generator
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div>
            <label className="text-white text-sm font-medium mb-2 block">Video Title *</label>
            <Input
              value={videoTitle}
              onChange={(e) => setVideoTitle(e.target.value)}
              placeholder="Enter your video title..."
              className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
            />
          </div>

          {pillarName && (
            <div className="bg-gray-700 p-3 rounded-lg">
              <p className="text-teal text-sm">Content Pillar: {pillarName}</p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-white text-sm font-medium mb-2 block">Hook Style *</label>
              <Select value={hookStyle} onValueChange={setHookStyle}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select hook style" />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  <SelectItem value="question">Question Hook</SelectItem>
                  <SelectItem value="shocking">Shocking Statement</SelectItem>
                  <SelectItem value="story">Story Hook</SelectItem>
                  <SelectItem value="statistic">Statistic Hook</SelectItem>
                  <SelectItem value="mystery">Mystery Hook</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-white text-sm font-medium mb-2 block">Target Audience *</label>
              <Select value={targetAudience} onValueChange={setTargetAudience}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select target audience" />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  <SelectItem value="general">General Audience</SelectItem>
                  <SelectItem value="young">Young Adults (18-30)</SelectItem>
                  <SelectItem value="professionals">Professionals</SelectItem>
                  <SelectItem value="entrepreneurs">Entrepreneurs</SelectItem>
                  <SelectItem value="students">Students</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button 
            onClick={generateHooks}
            disabled={isGenerating || !videoTitle.trim() || !hookStyle || !targetAudience}
            className="bg-blue hover:bg-blue-dark text-white"
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Fish className="w-4 h-4 mr-2" />
                Generate Hooks (3 credits)
              </>
            )}
          </Button>

          {generatedHooks.length > 0 && (
            <Card className="bg-gray-700 border-gray-600">
              <CardHeader>
                <CardTitle className="text-white">Generated Hooks</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {generatedHooks.map((hook, index) => (
                  <div key={index} className="flex items-start justify-between p-3 bg-gray-800 rounded border border-gray-600">
                    <span className="text-white flex-1 pr-3">{hook}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyHook(hook)}
                      className="border-teal text-teal hover:bg-teal hover:text-white"
                    >
                      Copy
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default HookGeneratorDialog;
