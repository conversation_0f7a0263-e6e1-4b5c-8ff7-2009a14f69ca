
import React from 'react';
import { ContentPillar } from './types';
import WizardStepRenderer from './WizardStepRenderer';

interface WizardContentAreaProps {
  currentStep: number;
  wizardState: any;
  contentGeneration: any;
  pillars: ContentPillar[];
  steps: any[];
  onTopicSelect: (topic: string, pillarId: string) => void;
  onTitleSelect: (title: string) => void;
  onHookSelect: (hook: string) => void;
  onNext: () => void;
}

const WizardContentArea: React.FC<WizardContentAreaProps> = ({
  currentStep,
  wizardState,
  contentGeneration,
  pillars,
  steps,
  onTopicSelect,
  onTitleSelect,
  onHookSelect,
  onNext
}) => {
  const handleGenerateScript = async (length: string) => {
    console.log('Generating script with length:', length);
    try {
      const scripts = await contentGeneration.generateScript(wizardState.formData, length);
      wizardState.setGeneratedScripts(scripts);
    } catch (error) {
      console.error('Failed to generate script:', error);
    }
  };

  return (
    <WizardStepRenderer
      currentStep={currentStep}
      completedSteps={wizardState.completedSteps}
      skippedSteps={wizardState.skippedSteps}
      
      // Starting Point Props
      startingPointOption={wizardState.startingPointOption}
      setStartingPointOption={wizardState.setStartingPointOption}
      selectedPillarForTopics={wizardState.selectedPillarForTopics}
      setSelectedPillarForTopics={wizardState.setSelectedPillarForTopics}
      manualTopic={wizardState.manualTopic}
      setManualTopic={wizardState.setManualTopic}
      videoTitle={wizardState.videoTitle}
      setVideoTitle={wizardState.setVideoTitle}
      roughIdea={wizardState.roughIdea}
      setRoughIdea={wizardState.setRoughIdea}
      generatedTopics={wizardState.generatedTopics}
      
      // Generated Content
      generatedTitles={wizardState.generatedTitles}
      generatedHooks={wizardState.generatedHooks}
      generatedScripts={wizardState.generatedScripts}
      generatedDescriptions={wizardState.generatedDescriptions}
      
      // Form Data
      formData={wizardState.formData}
      setFormData={wizardState.setFormData}
      pillars={pillars}
      loading={wizardState.loading}
      
      // Actions
      onGenerateTopics={contentGeneration.generateTopics}
      onGenerateTitles={() => contentGeneration.generateContent('title-generation', `Generate 5 engaging YouTube video titles for: "${wizardState.formData.topic}"`, 2).then(wizardState.setGeneratedTitles)}
      onGenerateHooks={() => contentGeneration.generateContent('hook-generation', `Generate 3 engaging video hooks for title: "${wizardState.formData.title}" about topic: "${wizardState.formData.topic}"`, 2).then(wizardState.setGeneratedHooks)}
      onGenerateScript={handleGenerateScript}
      onGenerateDescription={() => contentGeneration.generateContent('description-generation', `Generate YouTube description for: "${wizardState.formData.title}"`, 2).then(wizardState.setGeneratedDescriptions)}
      onGenerateSEOTags={() => contentGeneration.generateSEOTags(wizardState.formData, pillars)}
      onSkipStep={() => {
        wizardState.setSkippedSteps([...wizardState.skippedSteps, currentStep]);
        onNext();
      }}
      onContinue={onNext}
      onSaveToIdeasBank={() => {}}
      onGenerateMore={(section: string) => {}}
      onCopyAll={() => {}}
      onCopySection={(content: string) => {}}
    />
  );
};

export default WizardContentArea;
