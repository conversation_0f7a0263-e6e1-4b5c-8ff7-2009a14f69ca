
import React from 'react';
import { DragDropContext, DropResult } from 'react-beautiful-dnd';
import { VideoIdea, ContentPillar } from './types';
import StatusColumn from './KanbanBoard/StatusColumn';
import { statusColumns } from './KanbanBoard/statusColumns';

interface KanbanBoardProps {
  ideas: VideoIdea[];
  pillars: ContentPillar[];
  onEditIdea: (idea: VideoIdea) => void;
  onDeleteIdea: (id: string, title: string) => void;
  onMoveToCalendar: (id: string, title: string) => void;
  onTitleUpdate?: (id: string, newTitle: string) => void;
  onAddNewIdea: () => void;
  onStatusChange?: (ideaId: string, newStatus: string) => void;
}

const KanbanBoard: React.FC<KanbanBoardProps> = ({
  ideas,
  pillars,
  onEditIdea,
  onDeleteIdea,
  onMoveToCalendar,
  onTitleUpdate,
  onAddNewIdea,
  onStatusChange
}) => {
  const getIdeasByStatus = (status: string) => {
    return ideas.filter(idea => idea.status === status && idea.id);
  };

  const handleDragEnd = async (result: DropResult) => {
    const { destination, source, draggableId } = result;

    // If dropped outside a droppable area
    if (!destination) {
      return;
    }

    // If dropped in the same position
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    // Find the idea being moved
    const movedIdea = ideas.find(idea => String(idea.id) === draggableId);
    if (!movedIdea) {
      console.error('Could not find idea with ID:', draggableId);
      return;
    }

    // Check if onStatusChange function is available
    if (!onStatusChange) {
      console.error('onStatusChange function not provided');
      return;
    }

    // Update the status if moving to a different column
    if (destination.droppableId !== source.droppableId) {
      try {
        await onStatusChange(movedIdea.id, destination.droppableId);
      } catch (error) {
        console.error('Error in onStatusChange:', error);
      }
    }
  };

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <div className="flex gap-6 overflow-x-auto pb-6">
        {statusColumns.map(column => (
          <StatusColumn
            key={column.id}
            column={column}
            ideas={getIdeasByStatus(column.id)}
            pillars={pillars}
            onEditIdea={onEditIdea}
            onDeleteIdea={onDeleteIdea}
            onMoveToCalendar={onMoveToCalendar}
            onTitleUpdate={onTitleUpdate}
            onAddNewIdea={onAddNewIdea}
          />
        ))}
      </div>
    </DragDropContext>
  );
};

export default KanbanBoard;
