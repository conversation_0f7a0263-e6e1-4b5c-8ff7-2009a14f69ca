
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { ContentPillar } from './types';
import { FormData } from './WizardFormData';
import WizardHeader from './WizardHeader';
import AIWizardSteps from './AIWizardSteps';
import StepNavigationDots from './StepNavigationDots';
import WizardStepRenderer from './WizardStepRenderer';

const steps = [
  { id: 0, title: 'Starting Point', icon: 'Lightbulb', description: 'Choose your starting point' },
  { id: 1, title: 'Title & Hook', icon: 'Target', description: 'Create compelling titles and hooks' },
  { id: 2, title: 'Script Creation', icon: 'Edit', description: 'Generate script content' },
  { id: 3, title: 'Publishing Essentials', icon: 'Hash', description: 'Description and SEO optimization' }
];

interface AIWizardContainerProps {
  currentStep: number;
  completedSteps: number[];
  skippedSteps: number[];
  loading: boolean;
  
  // Starting Point Props
  startingPointOption: string;
  setStartingPointOption: (option: string) => void;
  selectedPillarForTopics: string;
  setSelectedPillarForTopics: (pillar: string) => void;
  manualTopic: string;
  setManualTopic: (topic: string) => void;
  videoTitle: string;
  setVideoTitle: (title: string) => void;
  roughIdea: string;
  setRoughIdea: (idea: string) => void;
  generatedTopics: any[];
  
  // Generated Content
  generatedTitles: string[];
  generatedHooks: string[];
  generatedScripts: string[];
  generatedDescriptions: string[];
  
  // Form and Pillars
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  pillars: ContentPillar[];
  
  // Actions
  onGenerateTopics: () => void;
  onGenerateTitles: () => void;
  onGenerateHooks: () => void;
  onGenerateScript: (length: string) => void;
  onGenerateDescription: () => void;
  onGenerateSEOTags: () => Promise<{ tags: string[]; hashtags: string }>;
  onSkipStep: () => void;
  onContinue: () => void;
  onGoToStep: (step: number) => void;
  onSaveToIdeasBank: () => void;
  onGenerateMore: (section: string) => void;
  onCopyAll: () => void;
  onCopySection: (content: string) => void;
}

const AIWizardContainer: React.FC<AIWizardContainerProps> = ({
  currentStep,
  completedSteps,
  skippedSteps,
  loading,
  startingPointOption,
  setStartingPointOption,
  selectedPillarForTopics,
  setSelectedPillarForTopics,
  manualTopic,
  setManualTopic,
  videoTitle,
  setVideoTitle,
  roughIdea,
  setRoughIdea,
  generatedTopics,
  generatedTitles,
  generatedHooks,
  generatedScripts,
  generatedDescriptions,
  formData,
  setFormData,
  pillars,
  onGenerateTopics,
  onGenerateTitles,
  onGenerateHooks,
  onGenerateScript,
  onGenerateDescription,
  onGenerateSEOTags,
  onSkipStep,
  onContinue,
  onGoToStep,
  onSaveToIdeasBank,
  onGenerateMore,
  onCopyAll,
  onCopySection
}) => {
  return (
    <div className="space-y-8">
      <WizardHeader 
        currentStep={currentStep}
        steps={steps}
        progress={((currentStep + 1) / steps.length) * 100}
      />

      <StepNavigationDots
        steps={steps}
        currentStep={currentStep}
        completedSteps={completedSteps}
        skippedSteps={skippedSteps}
        onStepClick={onGoToStep}
      />

      <AIWizardSteps 
        currentStep={currentStep} 
        completedSteps={completedSteps}
        skippedSteps={skippedSteps}
      />

      <Card className="bg-gray-800 border-gray-600">
        <CardHeader>
          <CardTitle className="text-white">
            Step {currentStep + 1}: {steps[currentStep]?.title}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <WizardStepRenderer
            currentStep={currentStep}
            completedSteps={completedSteps}
            skippedSteps={skippedSteps}
            startingPointOption={startingPointOption}
            setStartingPointOption={setStartingPointOption}
            selectedPillarForTopics={selectedPillarForTopics}
            setSelectedPillarForTopics={setSelectedPillarForTopics}
            manualTopic={manualTopic}
            setManualTopic={setManualTopic}
            videoTitle={videoTitle}
            setVideoTitle={setVideoTitle}
            roughIdea={roughIdea}
            setRoughIdea={setRoughIdea}
            generatedTopics={generatedTopics}
            generatedTitles={generatedTitles}
            generatedHooks={generatedHooks}
            generatedScripts={generatedScripts}
            generatedDescriptions={generatedDescriptions}
            formData={formData}
            setFormData={setFormData}
            pillars={pillars}
            loading={loading}
            onGenerateTopics={onGenerateTopics}
            onGenerateTitles={onGenerateTitles}
            onGenerateHooks={onGenerateHooks}
            onGenerateScript={onGenerateScript}
            onGenerateDescription={onGenerateDescription}
            onGenerateSEOTags={onGenerateSEOTags}
            onSkipStep={onSkipStep}
            onContinue={onContinue}
            onSaveToIdeasBank={onSaveToIdeasBank}
            onGenerateMore={onGenerateMore}
            onCopyAll={onCopyAll}
            onCopySection={onCopySection}
          />

          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="w-8 h-8 border-4 border-teal border-t-transparent rounded-full animate-spin"></div>
              <span className="ml-3 text-gray-300">Generating content...</span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export { steps };
export default AIWizardContainer;
