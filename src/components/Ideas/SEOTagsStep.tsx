
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>rk<PERSON>, Target } from 'lucide-react';
import { ContentPillar } from './types';
import SEOStrengthIndicator from './SEO/SEOStrengthIndicator';
import TagsManager from './SEO/TagsManager';
import HashtagsEditor from './SEO/HashtagsEditor';
import SEOCompletionView from './SEO/SEOCompletionView';

interface FormData {
  topic: string;
  pillar_id: string;
  title: string;
  hook: string;
  thumbnail: string;
  script: string;
  description: string;
  tags: string[];
  hashtags: string;
}

interface SEOTagsStepProps {
  formData: FormData;
  completedSteps: number[];
  loading: boolean;
  onGenerateSEOTags: () => Promise<{ tags: string[]; hashtags: string }>;
  onSEOComplete: (tags: string[], hashtags: string) => void;
  onSaveToIdeasBank: () => void;
  pillars: ContentPillar[];
}

const SEOTagsStep: React.FC<SEOTagsStepProps> = ({
  formData,
  completedSteps,
  loading,
  onGenerateSEOTags,
  onSEOComplete,
  onSaveToIdeasBank,
  pillars
}) => {
  const [tags, setTags] = useState<string[]>(formData.tags);
  const [hashtags, setHashtags] = useState(formData.hashtags);
  const [hasGenerated, setHasGenerated] = useState(false);

  // Auto-generate tags when step loads (only once)
  useEffect(() => {
    if (!hasGenerated && formData.title && !loading) {
      handleGenerateTags();
      setHasGenerated(true);
    }
  }, [formData.title, hasGenerated, loading]);

  const handleGenerateTags = async () => {
    const result = await onGenerateSEOTags();
    setTags(result.tags);
    setHashtags(result.hashtags);
  };

  const getSEOStrength = () => {
    const tagCount = tags.length;
    if (tagCount >= 20) return { strength: 'Strong', color: 'text-green-500', bg: 'bg-green-900/20' };
    if (tagCount >= 10) return { strength: 'Medium', color: 'text-yellow-500', bg: 'bg-yellow-900/20' };
    return { strength: 'Weak', color: 'text-red-500', bg: 'bg-red-900/20' };
  };

  const seoData = getSEOStrength();
  const hashtagCount = hashtags.split(' ').filter(h => h.startsWith('#')).length;

  const handleContinue = () => {
    onSEOComplete(tags, hashtags);
  };

  // Show completion state if this step is completed
  if (completedSteps.includes(6)) {
    return (
      <SEOCompletionView
        formData={formData}
        tagCount={tags.length}
        seoStrength={seoData.strength}
        seoColor={seoData.color}
        onSaveToIdeasBank={onSaveToIdeasBank}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Previous step completion indicator */}
      {completedSteps.includes(5) && (
        <div className="p-4 bg-green-900/20 border border-green-500/20 rounded-lg">
          <p className="text-green-400 font-medium">✓ Description completed</p>
          <p className="text-gray-300 text-sm mt-1">Now optimizing for SEO & discovery...</p>
        </div>
      )}

      {/* SEO Header */}
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-white flex items-center justify-center">
          <Target className="w-5 h-5 mr-2 text-teal" />
          SEO & Discovery
        </h3>
        <p className="text-gray-300">Help people find your video</p>
      </div>

      {/* SEO Strength Indicator */}
      <SEOStrengthIndicator 
        tagCount={tags.length} 
        hashtagCount={hashtagCount} 
      />

      {/* Generate Tags Button */}
      {!hasGenerated && (
        <Button 
          onClick={handleGenerateTags} 
          disabled={loading} 
          className="bg-teal hover:bg-teal/90 text-white w-full"
        >
          <Sparkles className="w-4 h-4 mr-2" />
          Generate SEO Tags (1 credit)
        </Button>
      )}

      {/* Tags Section */}
      <TagsManager
        tags={tags}
        onTagsChange={setTags}
        onRegenerateTags={handleGenerateTags}
        loading={loading}
      />

      {/* Hashtags Section */}
      <HashtagsEditor
        hashtags={hashtags}
        onHashtagsChange={setHashtags}
      />

      {/* Continue Button */}
      <Button 
        onClick={handleContinue} 
        className="bg-teal hover:bg-teal/90 text-white w-full"
        disabled={tags.length === 0}
      >
        Continue to Final Review
      </Button>
    </div>
  );
};

export default SEOTagsStep;
