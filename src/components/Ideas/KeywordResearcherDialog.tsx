import React, { useState } from 'react';
import { Search, Loader2, TrendingUp } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';

interface KeywordData {
  keyword: string;
  searchVolume: string;
  difficulty: string;
  trend: string;
}

interface KeywordResearcherDialogProps {
  isOpen: boolean;
  onClose: () => void;
  ideaTitle?: string;
  pillarName?: string;
}

const KeywordResearcherDialog: React.FC<KeywordResearcherDialogProps> = ({
  isOpen,
  onClose,
  ideaTitle = '',
  pillarName = ''
}) => {
  const { user } = useAuth();
  const [topic, setTopic] = useState(ideaTitle);
  const [keywords, setKeywords] = useState<KeywordData[]>([]);
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const researchKeywords = async () => {
    if (!user || !topic.trim()) {
      toast.error('Please enter a topic to research');
      return;
    }

    setIsGenerating(true);

    try {
      const { data, error } = await supabase.functions.invoke('research-keywords', {
        body: {
          topic: topic.trim(),
          pillar: pillarName
        }
      });

      if (error) throw error;

      setKeywords(data.keywords || []);
      setHashtags(data.hashtags || []);
      toast.success('Keyword research completed!');
    } catch (error) {
      console.error('Error researching keywords:', error);
      toast.error('Failed to research keywords');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyKeywords = async () => {
    const keywordText = keywords.map(k => k.keyword).join(', ');
    try {
      await navigator.clipboard.writeText(keywordText);
      toast.success('Keywords copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy keywords');
    }
  };

  const copyHashtags = async () => {
    const hashtagText = hashtags.join(' ');
    try {
      await navigator.clipboard.writeText(hashtagText);
      toast.success('Hashtags copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy hashtags');
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'hard': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <Search className="w-5 h-5 mr-2 text-purple-500" />
            Keyword Researcher
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div>
            <label className="text-white text-sm font-medium mb-2 block">Topic or Video Idea *</label>
            <Input
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              placeholder="Enter your video topic or idea..."
              className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
            />
          </div>

          {pillarName && (
            <div className="bg-gray-700 p-3 rounded-lg">
              <p className="text-purple-500 text-sm">Content Pillar: {pillarName}</p>
            </div>
          )}

          <Button 
            onClick={researchKeywords}
            disabled={isGenerating || !topic.trim()}
            className="bg-teal hover:bg-teal/90 text-white"
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Researching...
              </>
            ) : (
              <>
                <Search className="w-4 h-4 mr-2" />
                Research Keywords (2 credits)
              </>
            )}
          </Button>

          {keywords.length > 0 && (
            <Card className="bg-gray-700 border-gray-600">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white flex items-center">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Keyword Research Results
                  </CardTitle>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={copyKeywords}
                    className="border-teal text-teal hover:bg-teal hover:text-white"
                  >
                    Copy All Keywords
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {keywords.map((keyword, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-800 rounded border border-gray-600">
                    <div className="flex-1">
                      <span className="text-white font-medium">{keyword.keyword}</span>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="secondary" className="bg-teal-500/20 text-teal-400">
                          {keyword.searchVolume}
                        </Badge>
                        <Badge className={`${getDifficultyColor(keyword.difficulty)} text-white`}>
                          {keyword.difficulty}
                        </Badge>
                        <Badge variant="outline" className="border-gray-500 text-gray-300">
                          {keyword.trend}
                        </Badge>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => window.open(`https://trends.google.com/trends/explore?q=${keyword.keyword}`, '_blank')}
                      className="border-teal text-teal hover:bg-teal hover:text-white"
                    >
                      View Trend
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {hashtags.length > 0 && (
            <Card className="bg-gray-700 border-gray-600">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white">Trending Hashtags</CardTitle>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={copyHashtags}
                    className="border-teal text-teal hover:bg-teal hover:text-white"
                  >
                    Copy Hashtags
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {hashtags.map((hashtag, index) => (
                    <Badge key={index} variant="secondary" className="bg-purple-500/20 text-purple-400">
                      {hashtag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default KeywordResearcherDialog;
