import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Co<PERSON>, Check, Loader2 } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';

interface Reply {
  text: string;
  tone: string;
}

interface CommentAssistantDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const CommentAssistantDialog: React.FC<CommentAssistantDialogProps> = ({
  isOpen,
  onClose
}) => {
  const { user } = useAuth();
  const [comment, setComment] = useState('');
  const [tone, setTone] = useState('friendly');
  const [replies, setReplies] = useState<Reply[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const generateReplies = async () => {
    if (!user) {
      toast.error('Please sign in to use this feature');
      return;
    }

    if (!comment.trim()) {
      toast.error('Please enter a comment to reply to');
      return;
    }

    setIsGenerating(true);
    try {
      const { data, error } = await supabase.functions.invoke('generate-comment-replies', {
        body: { 
          comment: comment.trim(),
          tone
        }
      });

      if (error) throw error;

      setReplies(data.replies || []);
      toast.success('Replies generated successfully!');
    } catch (error) {
      console.error('Error generating replies:', error);
      toast.error('Failed to generate replies');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyReply = async (reply: string, index: number) => {
    try {
      await navigator.clipboard.writeText(reply);
      setCopiedIndex(index);
      toast.success('Reply copied to clipboard!');
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      toast.error('Failed to copy reply');
    }
  };

  const resetAssistant = () => {
    setComment('');
    setReplies([]);
    setCopiedIndex(null);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <MessageCircle className="w-5 h-5 mr-2 text-orange" />
            Comment Reply Assistant
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Viewer Comment *
            </label>
            <Textarea
              placeholder="Paste or type the viewer comment here..."
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 min-h-[100px]"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Reply Tone
            </label>
            <Select value={tone} onValueChange={setTone}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-700 border-gray-600">
                <SelectItem value="friendly">Friendly</SelectItem>
                <SelectItem value="professional">Professional</SelectItem>
                <SelectItem value="casual">Casual</SelectItem>
                <SelectItem value="enthusiastic">Enthusiastic</SelectItem>
                <SelectItem value="helpful">Helpful</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            onClick={generateReplies}
            disabled={!comment.trim() || isGenerating}
            className="w-full bg-teal hover:bg-teal/90 text-white"
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating Replies...
              </>
            ) : (
              <>
                <MessageCircle className="w-4 h-4 mr-2" />
                Generate Replies (2 credits)
              </>
            )}
          </Button>

          {replies.length > 0 && (
            <Card className="bg-gray-700 border-gray-600">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white">Generated Replies</CardTitle>
                  <Button
                    onClick={resetAssistant}
                    variant="outline"
                    size="sm"
                    className="border-gray-600 text-gray-300 hover:bg-gray-600"
                  >
                    New Comment
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {replies.map((reply, index) => (
                  <div
                    key={index}
                    className="bg-gray-800 rounded-lg p-4 border border-gray-600"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <span className="text-xs text-gray-400 uppercase tracking-wide">
                        {reply.tone} Reply {index + 1}
                      </span>
                      <Button
                        onClick={() => copyReply(reply.text, index)}
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-gray-600"
                      >
                        {copiedIndex === index ? (
                          <Check className="w-4 h-4 text-green-500" />
                        ) : (
                          <Copy className="w-4 h-4 text-gray-400" />
                        )}
                      </Button>
                    </div>
                    <p className="text-gray-200 text-sm leading-relaxed">
                      {reply.text}
                    </p>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CommentAssistantDialog;
