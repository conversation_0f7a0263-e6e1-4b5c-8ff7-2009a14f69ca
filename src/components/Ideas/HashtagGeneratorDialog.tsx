import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Hash, Copy, AlertCircle } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { useAuthContext } from '@/components/AuthProvider';

interface HashtagGeneratorDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const HashtagGeneratorDialog: React.FC<HashtagGeneratorDialogProps> = ({
  isOpen,
  onClose
}) => {
  const { user } = useAuthContext();
  const [contentText, setContentText] = useState('');
  const [videoTitle, setVideoTitle] = useState('');
  const [generatedHashtags, setGeneratedHashtags] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [connectionError, setConnectionError] = useState(false);

  // Local hashtag generation function
  const generateLocalHashtags = (title: string, content: string): string[] => {
    // Extract potential keywords from title and content
    const combinedText = `${title} ${content}`.toLowerCase();
    
    // Common hashtag categories for content creators
    const commonHashtags = [
      '#contentcreator', '#creator', '#socialmedia', 
      '#trending', '#viral', '#fyp', '#foryoupage'
    ];
    
    // Platform-specific hashtags
    const platformHashtags = [
      '#youtube', '#instagram', '#tiktok', 
      '#reels', '#shorts', '#youtubeshorts'
    ];
    
    // Topic detection (very basic)
    const topics: {[key: string]: string[]} = {
      tech: ['tech', 'technology', 'coding', 'programming', 'developer', 'software', 'app', 'computer'],
      business: ['business', 'entrepreneur', 'startup', 'money', 'finance', 'investing', 'marketing'],
      lifestyle: ['lifestyle', 'life', 'daily', 'routine', 'vlog', 'day', 'living'],
      gaming: ['game', 'gaming', 'gamer', 'playthrough', 'stream', 'streaming', 'play'],
      education: ['learn', 'education', 'school', 'study', 'student', 'teacher', 'teaching', 'tutorial', 'howto'],
      beauty: ['beauty', 'makeup', 'skincare', 'cosmetics', 'fashion', 'style'],
      fitness: ['fitness', 'workout', 'gym', 'exercise', 'health', 'healthy', 'training'],
      food: ['food', 'recipe', 'cooking', 'cook', 'chef', 'meal', 'kitchen', 'baking', 'restaurant'],
      travel: ['travel', 'trip', 'journey', 'adventure', 'explore', 'destination', 'vacation']
    };
    
    // Detect topics from content
    const detectedTopics: string[] = [];
    Object.entries(topics).forEach(([topic, keywords]) => {
      if (keywords.some(keyword => combinedText.includes(keyword))) {
        detectedTopics.push(topic);
      }
    });
    
    // Generate topic-specific hashtags
    const topicHashtags = detectedTopics.flatMap(topic => {
      switch(topic) {
        case 'tech':
          return ['#tech', '#technology', '#coding', '#developer', '#programming'];
        case 'business':
          return ['#business', '#entrepreneur', '#marketing', '#success', '#startup'];
        case 'lifestyle':
          return ['#lifestyle', '#daily', '#life', '#vlog', '#dayinthelife'];
        case 'gaming':
          return ['#gaming', '#gamer', '#videogames', '#gameplay', '#streamer'];
        case 'education':
          return ['#learning', '#education', '#tutorial', '#howto', '#tips'];
        case 'beauty':
          return ['#beauty', '#makeup', '#skincare', '#fashion', '#style'];
        case 'fitness':
          return ['#fitness', '#workout', '#gym', '#health', '#training'];
        case 'food':
          return ['#food', '#recipe', '#cooking', '#homemade', '#foodie'];
        case 'travel':
          return ['#travel', '#adventure', '#explore', '#wanderlust', '#trip'];
        default:
          return [];
      }
    });
    
    // Extract potential keywords from title and content
    const words = combinedText
      .replace(/[^\w\s]/gi, '')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .filter(word => !['this', 'that', 'with', 'from', 'have', 'what', 'when', 'where', 'will', 'your'].includes(word));
    
    // Convert top words to hashtags
    const contentHashtags = [...new Set(words)]
      .slice(0, 5)
      .map(word => `#${word}`);
    
    // Combine all hashtag types and remove duplicates
    const allHashtags = [
      ...topicHashtags,
      ...contentHashtags,
      ...platformHashtags.slice(0, 2),
      ...commonHashtags.slice(0, 3)
    ];
    
    // Remove duplicates and limit to 10 hashtags
    return [...new Set(allHashtags)].slice(0, 10);
  };

  const generateHashtags = async () => {
    if (!contentText.trim() && !videoTitle.trim()) {
      toast.error('Please provide a title or content');
      return;
    }

    setIsGenerating(true);

    try {
      // Try AI-powered generation via Supabase Edge Function
      try {
        console.log('🚀 Attempting AI hashtag generation...');
        console.log('User authenticated:', !!user);
        console.log('Video title:', videoTitle);
        console.log('Content text:', contentText);

        // Check session
        const { data: session } = await supabase.auth.getSession();
        console.log('Session exists:', !!session.session);
        console.log('Access token exists:', !!session.session?.access_token);

        const { data, error } = await supabase.functions.invoke('generate-hashtags', {
          body: {
            title: videoTitle,
            content: contentText
          }
        });

        console.log('📡 Supabase function response:', { data, error });

        if (error) {
          console.error('❌ Supabase function error:', error);
          throw error;
        }

        if (data && data.hashtags && Array.isArray(data.hashtags)) {
          console.log('✅ AI hashtags received:', data.hashtags);
          setGeneratedHashtags(data.hashtags);
          setConnectionError(false);
          toast.success(`Generated ${data.hashtags.length} AI-powered hashtags (1 credit used)`);
        } else {
          console.error('❌ Invalid response format:', data);
          throw new Error('Invalid response format');
        }
      } catch (aiError) {
        console.error('❌ AI generation failed, falling back to local processing:', aiError);

        // Fall back to local processing
        const localHashtags = generateLocalHashtags(videoTitle, contentText);
        setGeneratedHashtags(localHashtags);
        setConnectionError(true);
        toast.success('Hashtags generated in offline mode');
      }
    } catch (error) {
      console.error('Error generating hashtags:', error);
      toast.error('Failed to generate hashtags');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedHashtags.join(' '));
    toast.success('Copied to clipboard!');
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-2xl bg-gray-900 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-white">Hashtag Generator</DialogTitle>
        </DialogHeader>
        
        {connectionError && (
          <div className="bg-orange-500/20 border border-orange-500/30 rounded-md p-3 mb-4">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-orange-400 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <p className="text-orange-300 text-sm font-medium">Working in offline mode</p>
                <p className="text-orange-200/70 text-xs mt-1">
                  Server connection unavailable. Basic hashtag generation is still available.
                </p>
              </div>
            </div>
          </div>
        )}
        
        <div className="space-y-4">
          <div>
            <Label className="text-white text-sm font-medium mb-2 block">Video Title</Label>
            <Input
              value={videoTitle}
              onChange={(e) => setVideoTitle(e.target.value)}
              placeholder="Enter your video title..."
              className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
            />
          </div>

          <div>
            <Label className="text-white text-sm font-medium mb-2 block">Video Content/Description</Label>
            <Textarea
              value={contentText}
              onChange={(e) => setContentText(e.target.value)}
              placeholder="Enter your video content or description..."
              className="min-h-[120px] bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
            />
          </div>

          <Button 
            onClick={generateHashtags}
            disabled={isGenerating || (!contentText.trim() && !videoTitle.trim())}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Hash className="w-4 h-4 mr-2" />
                {connectionError ? 'Generate Offline' : 'Generate Hashtags (1 credit)'}
              </>
            )}
          </Button>

          {generatedHashtags.length > 0 && (
            <div className="space-y-4">
              <div className="p-4 bg-gray-800 rounded-md border border-gray-700">
                <h3 className="text-white font-medium mb-2">Generated Hashtags</h3>
                <div className="flex flex-wrap gap-2">
                  {generatedHashtags.map((tag, index) => (
                    <span 
                      key={index} 
                      className="bg-purple-500/30 text-purple-200 px-2 py-1 rounded-md text-sm"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
              
              <Button 
                onClick={copyToClipboard}
                className="bg-gray-700 hover:bg-gray-600 text-white"
              >
                <Copy className="w-4 h-4 mr-2" />
                Copy All Hashtags
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default HashtagGeneratorDialog;