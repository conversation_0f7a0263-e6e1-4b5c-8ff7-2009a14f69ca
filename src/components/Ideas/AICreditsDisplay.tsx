
import React from 'react';
import { Spark<PERSON>, Crown, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { useAICredits } from '@/hooks/useAICredits';

const AICreditsDisplay: React.FC = () => {
  const navigate = useNavigate();
  const { 
    creditsUsed, 
    maxCredits, 
    userTier, 
    getRemainingCredits, 
    getResetInfo 
  } = useAICredits();

  const remainingCredits = getRemainingCredits();
  const isLowCredits = userTier !== 'studio' && (typeof remainingCredits === 'number' && remainingCredits <= 5);
  const isOutOfCredits = userTier !== 'studio' && (typeof remainingCredits === 'number' && remainingCredits === 0);

  return (
    <div className={`flex items-center justify-between p-4 rounded-lg border ${
      isOutOfCredits 
        ? 'bg-red-900/20 border-red-500/30' 
        : isLowCredits 
        ? 'bg-orange/20 border-orange/30' 
        : 'bg-gray-800/50 border-gray-600'
    }`}>
      <div className="flex items-center space-x-3">
        <div className={`p-2 rounded-lg ${
          isOutOfCredits 
            ? 'bg-red-500/20' 
            : isLowCredits 
            ? 'bg-orange/20' 
            : 'bg-gray-700/50'
        }`}>
          {userTier === 'studio' ? (
            <Zap className="w-5 h-5 text-yellow" />
          ) : (
            <Sparkles className={`w-5 h-5 ${isOutOfCredits ? 'text-red-400' : isLowCredits ? 'text-orange' : 'text-yellow'}`} />
          )}
        </div>
        <div>
          <div className={`font-medium ${isOutOfCredits ? 'text-red-400' : 'text-gray-300'}`}>
            {isOutOfCredits ? 'No AI Credits Remaining' : 
             userTier === 'studio' ? 'Unlimited AI Credits' : 
             `${remainingCredits}/${maxCredits} AI Credits`}
          </div>
          <div className="text-xs text-gray-400">
            {isOutOfCredits ? 'Upgrade to continue using AI features' : `Resets: ${getResetInfo()}`}
          </div>
        </div>
      </div>

      {(isLowCredits || isOutOfCredits) && userTier !== 'studio' && (
        <Button
          onClick={() => navigate('/pricing')}
          size="sm"
          className={`${
            isOutOfCredits 
              ? 'bg-red-600 hover:bg-red-700' 
              : 'bg-orange hover:bg-orange/90'
          } text-white font-medium`}
        >
          <Crown className="w-4 h-4 mr-2" />
          {isOutOfCredits ? 'Upgrade Now' : 'Upgrade'}
        </Button>
      )}
    </div>
  );
};

export default AICreditsDisplay;
