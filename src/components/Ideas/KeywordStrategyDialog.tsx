import React, { useState } from 'react';
import { Search, Loader2, Target, TrendingUp, Lightbulb, Zap } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { useAICredits } from '@/hooks/useAICredits';
import { toast } from 'sonner';

interface KeywordStrategyDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onTitleOptimizer?: () => void;
  onScriptWriter?: () => void;
}

interface KeywordAnalysis {
  keywordIntelligence: {
    contentType: string;
    competitionLevel: 'Low' | 'Medium' | 'High';
    opportunityScore: number;
    bestApproach: string;
  };
  smartKeywordVariations: {
    low: string[];
    medium: string[];
    high: string[];
  };
  contentStrategy: {
    titleAngles: string[];
    optimalLength: string;
    contentStructure: string[];
    differentiationAdvice: string;
  };
  longTailOpportunities: string[];
  proTips: string[];
  competitiveEdge: {
    missingContent: string;
    uniqueAngle: string;
  };
}

const KeywordStrategyDialog: React.FC<KeywordStrategyDialogProps> = ({
  isOpen,
  onClose,
  onTitleOptimizer,
  onScriptWriter
}) => {
  const { user } = useAuth();
  const { hasCreditsAvailable, useCredits, userTier } = useAICredits();
  const [keyword, setKeyword] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState<KeywordAnalysis | null>(null);

  const analyzeKeyword = async () => {
    if (!user || !keyword.trim()) {
      toast.error('Please enter a keyword to analyze');
      return;
    }

    if (!hasCreditsAvailable(3)) {
      toast.error('Not enough credits. You need 3 credits for keyword analysis.');
      return;
    }

    setIsAnalyzing(true);

    try {
      const { data, error } = await supabase.functions.invoke('keyword-strategy', {
        body: { 
          keyword: keyword.trim(),
          userTier 
        }
      });

      if (error) throw error;

      // Use credits after successful generation
      const success = await useCredits(3, 'keyword-strategy', keyword, JSON.stringify(data));
      if (!success) return;

      setAnalysis(data);
      toast.success('Keyword analysis complete!');
    } catch (error) {
      console.error('Error analyzing keyword:', error);
      toast.error('Failed to analyze keyword');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const tryAnotherKeyword = () => {
    setKeyword('');
    setAnalysis(null);
  };

  const getCompetitionColor = (level: string) => {
    switch (level) {
      case 'Low': return 'text-green-400 bg-green-500/10 border-green-500/20';
      case 'Medium': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20';
      case 'High': return 'text-red-400 bg-red-500/10 border-red-500/20';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/20';
    }
  };

  const getOpportunityColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto bg-gray-800 border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <Search className="w-5 h-5 mr-2 text-purple-500" />
            Keyword Strategy Advisor
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="space-y-4">
            <div>
              <label className="text-white text-sm font-medium mb-2 block">
                Enter Keyword or Topic
              </label>
              <Input
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                placeholder="e.g., video editing for beginners"
                className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
                onKeyPress={(e) => e.key === 'Enter' && !isAnalyzing && analyzeKeyword()}
              />
            </div>

            <Button
              onClick={analyzeKeyword}
              disabled={isAnalyzing || !keyword.trim()}
              className="bg-teal hover:bg-teal/90 text-white"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Search className="w-4 h-4 mr-2" />
                  Analyze Keyword (3 credits)
                </>
              )}
            </Button>
          </div>

          {analysis && (
            <div className="space-y-6">
              {/* Keyword Intelligence */}
              <Card className="bg-gray-700 border-gray-600">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Target className="w-5 h-5 mr-2 text-purple-500" />
                    Keyword Intelligence
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-gray-400 text-sm">Content Type</p>
                      <p className="text-white font-medium">{analysis.keywordIntelligence.contentType}</p>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Competition</p>
                      <Badge className={getCompetitionColor(analysis.keywordIntelligence.competitionLevel)}>
                        {analysis.keywordIntelligence.competitionLevel}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Opportunity Score</p>
                      <p className={`font-bold text-lg ${getOpportunityColor(analysis.keywordIntelligence.opportunityScore)}`}>
                        {analysis.keywordIntelligence.opportunityScore}/100
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Best Approach</p>
                      <p className="text-white font-medium">{analysis.keywordIntelligence.bestApproach}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Smart Keyword Variations */}
              <Card className="bg-gray-700 border-gray-600">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2 text-purple-500" />
                    Smart Keyword Variations
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="text-green-400 font-medium mb-2">Low Competition</h4>
                      <div className="space-y-1">
                        {analysis.smartKeywordVariations.low.map((kw, index) => (
                          <Badge key={index} className="text-green-400 bg-green-500/10 border-green-500/20 mr-1 mb-1">
                            {kw}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-yellow-400 font-medium mb-2">Medium Competition</h4>
                      <div className="space-y-1">
                        {analysis.smartKeywordVariations.medium.map((kw, index) => (
                          <Badge key={index} className="text-yellow-400 bg-yellow-500/10 border-yellow-500/20 mr-1 mb-1">
                            {kw}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-red-400 font-medium mb-2">High Competition</h4>
                      <div className="space-y-1">
                        {analysis.smartKeywordVariations.high.map((kw, index) => (
                          <Badge key={index} className="text-red-400 bg-red-500/10 border-red-500/20 mr-1 mb-1">
                            {kw}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Content Strategy Recommendations */}
              <Card className="bg-gray-700 border-gray-600">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Lightbulb className="w-5 h-5 mr-2 text-purple-500" />
                    Content Strategy Recommendations
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-white font-medium mb-2">Title Angles</h4>
                      <ul className="space-y-1">
                        {analysis.contentStrategy.titleAngles.map((angle, index) => (
                          <li key={index} className="text-gray-300 text-sm">• {angle}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className="text-white font-medium mb-2">Content Structure</h4>
                      <ul className="space-y-1">
                        {analysis.contentStrategy.contentStructure.map((tip, index) => (
                          <li key={index} className="text-gray-300 text-sm">• {tip}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-white font-medium mb-2">Optimal Video Length</h4>
                      <p className="text-purple-400">{analysis.contentStrategy.optimalLength}</p>
                    </div>
                    <div>
                      <h4 className="text-white font-medium mb-2">Differentiation Advice</h4>
                      <p className="text-gray-300 text-sm">{analysis.contentStrategy.differentiationAdvice}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Long-Tail Opportunities */}
              <Card className="bg-gray-700 border-gray-600">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Zap className="w-5 h-5 mr-2 text-purple-500" />
                    Long-Tail Opportunities
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {analysis.longTailOpportunities.map((keyword, index) => (
                      <Badge key={index} className="text-purple-400 bg-purple-500/10 border-purple-500/20">
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Pro Tips & Competitive Edge */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="bg-gray-700 border-gray-600">
                  <CardHeader>
                    <CardTitle className="text-white">Pro Tips</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {analysis.proTips.map((tip, index) => (
                        <li key={index} className="text-gray-300 text-sm">• {tip}</li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>

                <Card className="bg-gray-700 border-gray-600">
                  <CardHeader>
                    <CardTitle className="text-white">Competitive Edge</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <h4 className="text-white font-medium mb-1">Missing Content</h4>
                      <p className="text-gray-300 text-sm">{analysis.competitiveEdge.missingContent}</p>
                    </div>
                    <div>
                      <h4 className="text-white font-medium mb-1">Unique Angle</h4>
                      <p className="text-purple-400 text-sm">{analysis.competitiveEdge.uniqueAngle}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-600">
                {onTitleOptimizer && (
                  <Button 
                    onClick={onTitleOptimizer}
                    variant="outline"
                    className="border-teal text-teal hover:bg-teal hover:text-white"
                  >
                    Generate Title Options
                  </Button>
                )}
                {onScriptWriter && (
                  <Button 
                    onClick={onScriptWriter}
                    variant="outline"
                    className="border-teal text-teal hover:bg-teal hover:text-white"
                  >
                    Create Video Script
                  </Button>
                )}
                <Button 
                  onClick={tryAnotherKeyword}
                  variant="outline"
                  className="border-teal text-teal hover:bg-teal hover:text-white"
                >
                  Try Another Keyword
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default KeywordStrategyDialog;
