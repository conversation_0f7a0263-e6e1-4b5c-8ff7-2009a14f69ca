
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useEnhancedAIWizard } from '@/hooks/ideas/useEnhancedAIWizard';
import { ContentPillar } from './types';
import AIWizardContainer from './AIWizardContainer';

interface EnhancedAIWizardProps {
  isOpen: boolean;
  onClose: () => void;
  pillars: ContentPillar[];
  onIdeaGenerated: (ideaData: any) => void;
  title?: string;
}

const EnhancedAIWizard: React.FC<EnhancedAIWizardProps> = ({
  isOpen,
  onClose,
  pillars,
  onIdeaGenerated,
  title = "AI Video Wizard"
}) => {
  const {
    currentStep,
    completedSteps,
    skippedSteps,
    loading,
    startingPointOption,
    setStartingPointOption,
    selectedPillarForTopics,
    setSelectedPillarForTopics,
    manualTopic,
    setManualTopic,
    videoTitle,
    setVideoTitle,
    roughIdea,
    setRoughIdea,
    generatedTopics,
    generatedTitles,
    generatedHooks,
    generatedScripts,
    generatedDescriptions,
    formData,
    setFormData,
    handleContinue,
    handleSkipStep,
    handleGoToStep,
    handleGenerateTopics,
    handleGenerateTitles,
    handleGenerateHooks,
    handleGenerateScript,
    handleGenerateDescription,
    handleGenerateSEOTags,
    handleFinish,
    handleGenerateMore,
    handleCopyAll,
    handleCopySection
  } = useEnhancedAIWizard(pillars, onIdeaGenerated, onClose);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-gray-900 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-white">{title}</DialogTitle>
        </DialogHeader>

        <AIWizardContainer
          currentStep={currentStep}
          completedSteps={completedSteps}
          skippedSteps={skippedSteps}
          loading={loading}
          startingPointOption={startingPointOption}
          setStartingPointOption={setStartingPointOption}
          selectedPillarForTopics={selectedPillarForTopics}
          setSelectedPillarForTopics={setSelectedPillarForTopics}
          manualTopic={manualTopic}
          setManualTopic={setManualTopic}
          videoTitle={videoTitle}
          setVideoTitle={setVideoTitle}
          roughIdea={roughIdea}
          setRoughIdea={setRoughIdea}
          generatedTopics={generatedTopics}
          generatedTitles={generatedTitles}
          generatedHooks={generatedHooks}
          generatedScripts={generatedScripts}
          generatedDescriptions={generatedDescriptions}
          formData={formData}
          setFormData={setFormData}
          pillars={pillars}
          onGenerateTopics={handleGenerateTopics}
          onGenerateTitles={handleGenerateTitles}
          onGenerateHooks={handleGenerateHooks}
          onGenerateScript={handleGenerateScript}
          onGenerateDescription={handleGenerateDescription}
          onGenerateSEOTags={handleGenerateSEOTags}
          onSkipStep={handleSkipStep}
          onContinue={handleContinue}
          onGoToStep={handleGoToStep}
          onSaveToIdeasBank={handleFinish}
          onGenerateMore={handleGenerateMore}
          onCopyAll={handleCopyAll}
          onCopySection={handleCopySection}
        />
      </DialogContent>
    </Dialog>
  );
};

export default EnhancedAIWizard;
