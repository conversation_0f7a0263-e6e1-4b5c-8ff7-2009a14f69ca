
import React from 'react';
import { Button } from '@/components/ui/button';
import { Download, Plus, Copy, Check } from 'lucide-react';
import { toast } from 'sonner';

interface GeneratedThumbnail {
  id: string;
  imageUrl: string;
  text: string;
  template: string;
}

interface ThumbnailResultsProps {
  thumbnails: GeneratedThumbnail[];
  copiedThumbnail: string | null;
  onReset: () => void;
  onDownload: (thumbnail: GeneratedThumbnail) => void;
  onCopy: (thumbnail: GeneratedThumbnail) => void;
  onSaveToIdea: (thumbnail: GeneratedThumbnail) => void;
}

const ThumbnailResults: React.FC<ThumbnailResultsProps> = ({
  thumbnails,
  copiedThumbnail,
  onReset,
  onDownload,
  onCopy,
  onSaveToIdea
}) => {
  if (thumbnails.length === 0) return null;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-white font-medium">Generated Thumbnails:</h4>
        <Button
          onClick={onReset}
          variant="outline"
          size="sm"
          className="border-gray-600 text-gray-300 hover:bg-gray-700"
        >
          Generate New
        </Button>
      </div>

      <div className="space-y-4">
        {thumbnails.map((thumbnail) => (
          <div
            key={thumbnail.id}
            className="bg-gray-700 rounded-lg p-4 border border-gray-600"
          >
            <div className="flex items-start justify-between mb-3">
              <div>
                <h5 className="text-white font-medium">{thumbnail.text}</h5>
                <p className="text-xs text-gray-400 capitalize">{thumbnail.template} Template</p>
              </div>
            </div>
            
            <div className="aspect-video bg-gray-600 rounded-lg mb-3 flex items-center justify-center">
              <img 
                src={thumbnail.imageUrl} 
                alt={thumbnail.text}
                className="w-full h-full object-cover rounded-lg"
              />
            </div>

            <div className="flex gap-2">
              <Button
                onClick={() => onDownload(thumbnail)}
                variant="outline"
                size="sm"
                className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-600"
              >
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
              <Button
                onClick={() => onCopy(thumbnail)}
                variant="ghost"
                size="sm"
                className="hover:bg-gray-600"
              >
                {copiedThumbnail === thumbnail.id ? (
                  <Check className="w-4 h-4 text-green-500" />
                ) : (
                  <Copy className="w-4 h-4 text-gray-400" />
                )}
              </Button>
              <Button
                onClick={() => onSaveToIdea(thumbnail)}
                variant="outline"
                size="sm"
                className="border-teal text-teal hover:bg-teal hover:text-white"
              >
                <Plus className="w-4 h-4 mr-2" />
                Save to Idea
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ThumbnailResults;
