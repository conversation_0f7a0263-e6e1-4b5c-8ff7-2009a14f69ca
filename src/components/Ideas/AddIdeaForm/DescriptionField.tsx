
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { ContentPillar, NewIdea } from '../types';

interface DescriptionFieldProps {
  newIdea: NewIdea;
  setNewIdea: (idea: NewIdea) => void;
  pillars: ContentPillar[];
}

const DescriptionField: React.FC<DescriptionFieldProps> = ({
  newIdea,
  setNewIdea,
  pillars
}) => {
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);

  const generateDescription = async () => {
    if (!newIdea.title) {
      toast.error('Please enter a title first');
      return;
    }

    setIsGeneratingDescription(true);
    try {
      const selectedPillar = pillars.find(p => p.id === newIdea.pillar_id);
      const { data, error } = await supabase.functions.invoke('optimize-title', {
        body: { 
          title: newIdea.title, 
          description: '', 
          pillarName: selectedPillar?.name || 'General' 
        }
      });

      if (error) throw error;

      // Use the AI response to generate a description
      const description = `A comprehensive video about "${newIdea.title}" that provides valuable insights and actionable content for our audience.`;
      setNewIdea({ ...newIdea, description });
    } catch (error) {
      console.error('Error generating description:', error);
      toast.error('Failed to generate description');
    } finally {
      setIsGeneratingDescription(false);
    }
  };

  return (
    <div>
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-white">Description</label>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={generateDescription}
          disabled={isGeneratingDescription || !newIdea.title}
          className="text-xs text-gray-400 hover:text-white p-1"
        >
          {isGeneratingDescription ? (
            <Loader2 className="w-3 h-3 animate-spin mr-1" />
          ) : (
            <Sparkles className="w-3 h-3 mr-1" />
          )}
          Generate
        </Button>
      </div>
      <Textarea
        placeholder="Brief description of the video content"
        value={newIdea.description}
        onChange={(e) => setNewIdea({ ...newIdea, description: e.target.value })}
        className="bg-gray-700 border-gray-600 text-white"
      />
    </div>
  );
};

export default DescriptionField;
