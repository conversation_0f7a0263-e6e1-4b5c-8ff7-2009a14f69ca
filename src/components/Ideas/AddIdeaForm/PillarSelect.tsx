
import React, { useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ContentPillar, NewIdea } from '../types';

interface PillarSelectProps {
  newIdea: NewIdea;
  setNewIdea: (idea: NewIdea) => void;
  pillars: ContentPillar[];
}

const PillarSelect: React.FC<PillarSelectProps> = ({ newIdea, setNewIdea, pillars }) => {
  // Check if pillars array exists and has items
  const hasPillars = Array.isArray(pillars) && pillars.length > 0;
  
  // Set default pillar if available and none selected
  useEffect(() => {
    if (hasPillars && !newIdea.pillar_id && pillars[0]?.id) {
      setNewIdea({ ...newIdea, pillar_id: pillars[0].id });
    }
  }, [pillars, newIdea.pillar_id]);
  
  // Debug logging to check pillars data
  console.log('PillarSelect - pillars:', pillars);
  console.log('PillarSelect - selected pillar_id:', newIdea.pillar_id);
  
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-white">Content Pillar</label>
      <Select 
        value={newIdea.pillar_id} 
        onValueChange={(value) => setNewIdea({ ...newIdea, pillar_id: value })}
      >
        <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
          <SelectValue placeholder="Select a content pillar" />
        </SelectTrigger>
        <SelectContent>
          {hasPillars ? (
            pillars.map((pillar) => (
              <SelectItem key={pillar.id} value={pillar.id}>
                {pillar.name}
              </SelectItem>
            ))
          ) : (
            <SelectItem value="no-pillars" disabled>
              No pillars available - create one first
            </SelectItem>
          )}
        </SelectContent>
      </Select>
      {!hasPillars && (
        <p className="text-amber-400 text-xs mt-1">
          You need to create content pillars before adding ideas
        </p>
      )}
    </div>
  );
};

export default PillarSelect;
