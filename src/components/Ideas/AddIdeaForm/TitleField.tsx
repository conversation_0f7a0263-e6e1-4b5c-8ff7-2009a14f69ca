
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Spark<PERSON>, Loader2 } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { ContentPillar, NewIdea } from '../types';

interface TitleFieldProps {
  newIdea: NewIdea;
  setNewIdea: (idea: NewIdea) => void;
  pillars: ContentPillar[];
}

const TitleField: React.FC<TitleFieldProps> = ({
  newIdea,
  setNewIdea,
  pillars
}) => {
  const [isGeneratingTitle, setIsGeneratingTitle] = useState(false);
  const [titleSuggestions, setTitleSuggestions] = useState<string[]>([]);
  const [showTitleSuggestions, setShowTitleSuggestions] = useState(false);

  const generateTitleSuggestions = async () => {
    if (!newIdea.pillar_id) {
      toast.error('Please select a pillar first');
      return;
    }

    const selectedPillar = pillars.find(p => p.id === newIdea.pillar_id);
    if (!selectedPillar) return;

    setIsGeneratingTitle(true);
    try {
      const { data, error } = await supabase.functions.invoke('generate-ideas', {
        body: { pillarName: selectedPillar.name, pillarDescription: `Content for ${selectedPillar.name}` }
      });

      if (error) throw error;

      const suggestions = data.ideas?.map((idea: any) => idea.title) || [];
      setTitleSuggestions(suggestions.slice(0, 3));
      setShowTitleSuggestions(true);
    } catch (error) {
      console.error('Error generating title suggestions:', error);
      toast.error('Failed to generate title suggestions');
    } finally {
      setIsGeneratingTitle(false);
    }
  };

  return (
    <div>
      <label className="text-sm font-medium text-white">Video Title *</label>
      <div className="flex gap-2 mt-1">
        <div className="flex-1">
          <Input
            placeholder="Enter video title"
            value={newIdea.title}
            onChange={(e) => setNewIdea({ ...newIdea, title: e.target.value })}
            className="bg-gray-700 border-gray-600 text-white"
            maxLength={100}
          />
          <div className="text-xs text-gray-400 mt-1">
            {newIdea.title.length}/100 characters
          </div>
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={generateTitleSuggestions}
          disabled={isGeneratingTitle || !newIdea.pillar_id}
          className="border-gray-600 text-gray-300 mt-0"
        >
          {isGeneratingTitle ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Sparkles className="w-4 h-4" />
          )}
        </Button>
      </div>
      
      {showTitleSuggestions && titleSuggestions.length > 0 && (
        <div className="mt-2 space-y-1">
          <label className="text-xs text-gray-400">AI Suggestions:</label>
          {titleSuggestions.map((suggestion, index) => (
            <div key={index} className="flex items-center justify-between p-2 bg-gray-800 rounded text-sm">
              <span className="text-white flex-1 pr-2">{suggestion}</span>
              <Button
                type="button"
                size="sm"
                onClick={() => {
                  setNewIdea({ ...newIdea, title: suggestion });
                  setShowTitleSuggestions(false);
                }}
                className="bg-teal hover:bg-teal/90 text-white text-xs px-2 py-1"
              >
                Use
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TitleField;
