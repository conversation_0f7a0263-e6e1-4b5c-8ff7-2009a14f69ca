
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { NewIdea } from '../types';

interface PriorityDateFieldsProps {
  newIdea: NewIdea;
  setNewIdea: (idea: NewIdea) => void;
}

const PriorityDateFields: React.FC<PriorityDateFieldsProps> = ({
  newIdea,
  setNewIdea
}) => {
  const getDateFromToday = (days: number) => {
    const date = new Date();
    date.setDate(date.getDate() + days);
    return date.toISOString().split('T')[0];
  };

  const setQuickDate = (days: number) => {
    setNewIdea({ ...newIdea, scheduled_date: getDateFromToday(days) });
  };

  return (
    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="text-sm font-medium text-white">Priority</label>
        <Select value={newIdea.priority} onValueChange={(value) => setNewIdea({ ...newIdea, priority: value })}>
          <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
            <SelectValue />
          </SelectTrigger>
          <SelectContent className="bg-gray-700 border-gray-600">
            <SelectItem value="Low" className="text-white">Low</SelectItem>
            <SelectItem value="Medium" className="text-white">Medium</SelectItem>
            <SelectItem value="High" className="text-white">High</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div>
        <label className="text-sm font-medium text-white">Target Date</label>
        <div className="space-y-2">
          <Input
            type="date"
            value={newIdea.scheduled_date}
            onChange={(e) => setNewIdea({ ...newIdea, scheduled_date: e.target.value })}
            className="bg-gray-700 border-gray-600 text-white"
            min={new Date().toISOString().split('T')[0]}
          />
          <div className="flex gap-1">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setQuickDate(1)}
              className="text-xs px-2 py-1 text-gray-400 hover:text-white"
            >
              Tomorrow
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setQuickDate(7)}
              className="text-xs px-2 py-1 text-gray-400 hover:text-white"
            >
              Next Week
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setQuickDate(30)}
              className="text-xs px-2 py-1 text-gray-400 hover:text-white"
            >
              Next Month
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PriorityDateFields;
