
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { 
  CheckSquare, 
  Square, 
  Trash2, 
  Calendar, 
  Edit, 
  X,
  ArrowRight 
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { VideoIdea } from './types';

interface BulkOperationsProps {
  ideas: VideoIdea[];
  selectedIdeas: string[];
  onSelectIdea: (ideaId: string) => void;
  onSelectAll: () => void;
  onClearSelection: () => void;
  onBulkDelete: (ideaIds: string[]) => void;
  onBulkMoveToCalendar: (ideaIds: string[]) => void;
  onBulkStatusChange: (ideaIds: string[], newStatus: string) => void;
}

const BulkOperations: React.FC<BulkOperationsProps> = ({
  ideas,
  selectedIdeas,
  onSelectIdea,
  onSelectAll,
  onClearSelection,
  onBulkDelete,
  onBulkMoveToCalendar,
  onBulkStatusChange
}) => {
  const [bulkStatus, setBulkStatus] = useState('');

  const isAllSelected = ideas.length > 0 && selectedIdeas.length === ideas.length;
  const isPartiallySelected = selectedIdeas.length > 0 && selectedIdeas.length < ideas.length;

  const handleBulkStatusChange = () => {
    if (bulkStatus && selectedIdeas.length > 0) {
      onBulkStatusChange(selectedIdeas, bulkStatus);
      setBulkStatus('');
    }
  };

  if (selectedIdeas.length === 0) {
    return null;
  }

  return (
    <Card className="bg-gray-800 border-gray-600 p-4 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={isAllSelected ? onClearSelection : onSelectAll}
            className="text-gray-300 hover:text-white p-2"
          >
            {isAllSelected ? (
              <CheckSquare className="w-4 h-4" />
            ) : isPartiallySelected ? (
              <Square className="w-4 h-4 fill-current" />
            ) : (
              <Square className="w-4 h-4" />
            )}
          </Button>
          
          <Badge variant="secondary" className="bg-teal/20 text-teal border-teal/30">
            {selectedIdeas.length} selected
          </Badge>
        </div>

        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2">
            <Select value={bulkStatus} onValueChange={setBulkStatus}>
              <SelectTrigger className="w-40 bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Change status" />
              </SelectTrigger>
              <SelectContent className="bg-gray-700 border-gray-600">
                <SelectItem value="idea">Idea</SelectItem>
                <SelectItem value="planned">Planned</SelectItem>
                <SelectItem value="filming">Filming</SelectItem>
                <SelectItem value="editing">Editing</SelectItem>
                <SelectItem value="published">Published</SelectItem>
              </SelectContent>
            </Select>
            
            <Button
              size="sm"
              onClick={handleBulkStatusChange}
              disabled={!bulkStatus}
              className="bg-teal hover:bg-teal/90 text-white"
            >
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>

          <Button
            size="sm"
            variant="outline"
            onClick={() => onBulkMoveToCalendar(selectedIdeas)}
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            <Calendar className="w-4 h-4 mr-2" />
            Move to Calendar
          </Button>

          <Button
            size="sm"
            variant="outline"
            onClick={() => onBulkDelete(selectedIdeas)}
            className="border-red-600 text-red-400 hover:bg-red-600/10"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Delete
          </Button>

          <Button
            size="sm"
            variant="ghost"
            onClick={onClearSelection}
            className="text-gray-400 hover:text-white p-2"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default BulkOperations;
