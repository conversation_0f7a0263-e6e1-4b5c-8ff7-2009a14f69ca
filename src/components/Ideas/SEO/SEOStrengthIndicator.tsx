
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface SEOStrengthIndicatorProps {
  tagCount: number;
  hashtagCount: number;
}

const SEOStrengthIndicator: React.FC<SEOStrengthIndicatorProps> = ({
  tagCount,
  hashtagCount
}) => {
  const getSEOStrength = () => {
    if (tagCount >= 20) return { strength: 'Strong', color: 'text-green-500', bg: 'bg-green-900/20' };
    if (tagCount >= 10) return { strength: 'Medium', color: 'text-yellow-500', bg: 'bg-yellow-900/20' };
    return { strength: 'Weak', color: 'text-red-500', bg: 'bg-red-900/20' };
  };

  const seoData = getSEOStrength();

  return (
    <Card className={`${seoData.bg} border-gray-600`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <span className="text-white font-medium">SEO Strength:</span>
          <Badge className={`${seoData.color} font-bold`} variant="outline">
            {seoData.strength}
          </Badge>
        </div>
        <p className="text-gray-300 text-sm mt-2">
          {tagCount} tags • {hashtagCount} hashtags
        </p>
      </CardContent>
    </Card>
  );
};

export default SEOStrengthIndicator;
