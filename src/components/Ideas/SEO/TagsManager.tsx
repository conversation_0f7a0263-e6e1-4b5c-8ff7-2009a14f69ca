
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Sparkles, X, Plus } from 'lucide-react';

interface TagsManagerProps {
  tags: string[];
  onTagsChange: (tags: string[]) => void;
  onRegenerateTags: () => void;
  loading: boolean;
}

const TagsManager: React.FC<TagsManagerProps> = ({
  tags,
  onTagsChange,
  onRegenerateTags,
  loading
}) => {
  const [newTag, setNewTag] = useState('');

  const removeTag = (indexToRemove: number) => {
    onTagsChange(tags.filter((_, index) => index !== indexToRemove));
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      onTagsChange([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-lg font-medium text-white">Tags ({tags.length})</h4>
        <Button 
          onClick={onRegenerateTags} 
          disabled={loading}
          variant="outline" 
          size="sm"
        >
          <Sparkles className="w-4 h-4 mr-1" />
          Regenerate
        </Button>
      </div>

      {/* Tag Display */}
      <div className="flex flex-wrap gap-2 min-h-[60px] p-3 bg-gray-700 rounded-lg">
        {tags.map((tag, index) => (
          <Badge 
            key={index} 
            variant="secondary" 
            className="bg-teal/20 text-teal border-teal/30 flex items-center gap-1"
          >
            {tag}
            <button 
              onClick={() => removeTag(index)}
              className="hover:bg-red-500/20 rounded-full p-0.5"
            >
              <X className="w-3 h-3" />
            </button>
          </Badge>
        ))}
      </div>

      {/* Add Tag Input */}
      <div className="flex gap-2">
        <Input
          placeholder="Add custom tag..."
          value={newTag}
          onChange={(e) => setNewTag(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && addTag()}
          className="bg-gray-700 border-gray-600 text-white"
        />
        <Button onClick={addTag} size="sm" variant="outline">
          <Plus className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

export default TagsManager;
