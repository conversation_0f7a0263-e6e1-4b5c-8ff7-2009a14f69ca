
import React from 'react';
import { Button } from '@/components/ui/button';
import { Save } from 'lucide-react';

interface FormData {
  topic: string;
  title: string;
}

interface SEOCompletionViewProps {
  formData: FormData;
  tagCount: number;
  seoStrength: string;
  seoColor: string;
  onSaveToIdeasBank: () => void;
}

const SEOCompletionView: React.FC<SEOCompletionViewProps> = ({
  formData,
  tagCount,
  seoStrength,
  seoColor,
  onSaveToIdeasBank
}) => {
  return (
    <div className="space-y-6">
      <div className="p-6 bg-green-900/20 border border-green-500/20 rounded-lg">
        <h3 className="text-green-400 font-bold text-lg mb-4">🎉 Complete SEO-Optimized Video Plan Ready!</h3>
        <div className="space-y-3 text-sm">
          <p><strong className="text-white">Topic:</strong> <span className="text-gray-300">{formData.topic}</span></p>
          <p><strong className="text-white">Title:</strong> <span className="text-gray-300">{formData.title}</span></p>
          <p><strong className="text-white">Tags:</strong> <span className="text-gray-300">{tagCount} tags optimized for discovery</span></p>
          <p><strong className="text-white">SEO Strength:</strong> <span className={seoColor}>{seoStrength}</span></p>
        </div>
      </div>
      
      <Button onClick={onSaveToIdeasBank} className="bg-teal hover:bg-teal/90 text-white w-full">
        <Save className="w-4 h-4 mr-2" />
        Save Complete Video Plan to Ideas Bank
      </Button>
      
      <p className="text-center text-gray-400 text-sm">
        Your SEO-optimized video plan will be saved with all tags and hashtags included
      </p>
    </div>
  );
};

export default SEOCompletionView;
