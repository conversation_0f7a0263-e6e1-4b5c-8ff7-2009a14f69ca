
import React from 'react';
import { Textarea } from '@/components/ui/textarea';

interface HashtagsEditorProps {
  hashtags: string;
  onHashtagsChange: (hashtags: string) => void;
}

const HashtagsEditor: React.FC<HashtagsEditorProps> = ({
  hashtags,
  onHashtagsChange
}) => {
  return (
    <div className="space-y-3">
      <h4 className="text-lg font-medium text-white">Hashtags</h4>
      <Textarea
        placeholder="#hashtag1 #hashtag2 #hashtag3"
        value={hashtags}
        onChange={(e) => onHashtagsChange(e.target.value)}
        className="bg-gray-700 border-gray-600 text-white min-h-[60px]"
      />
      <p className="text-gray-400 text-sm">
        Use 3-5 hashtags for optimal reach. Include in your video description.
      </p>
    </div>
  );
};

export default HashtagsEditor;
