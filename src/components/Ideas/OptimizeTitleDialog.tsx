
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { Spa<PERSON>les, Loader2, BarChart3, CheckCircle, AlertTriangle, XCircle } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

interface OptimizeTitleDialogProps {
  isOpen: boolean;
  onClose: () => void;
  currentTitle?: string;
  description?: string;
  pillarName?: string;
  onSelectTitle?: (newTitle: string) => void;
}

interface TitleAnalysis {
  score: number;
  keywordPlacement: { score: number; status: 'good' | 'warning' | 'poor'; message: string };
  length: { score: number; status: 'good' | 'warning' | 'poor'; message: string };
  emotional: { score: number; status: 'good' | 'warning' | 'poor'; message: string };
  specificity: { score: number; status: 'good' | 'warning' | 'poor'; message: string };
  curiosity: { score: number; status: 'good' | 'warning' | 'poor'; message: string };
  clarity: { score: number; status: 'good' | 'warning' | 'poor'; message: string };
  improvements: string[];
  suggestedFix: string;
}

const OptimizeTitleDialog: React.FC<OptimizeTitleDialogProps> = ({
  isOpen,
  onClose,
  currentTitle = '',
  description,
  pillarName,
  onSelectTitle
}) => {
  const [activeMode, setActiveMode] = useState<'analyze' | 'generate'>('analyze');
  const [title, setTitle] = useState(currentTitle);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [analysis, setAnalysis] = useState<TitleAnalysis | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const analyzeTitle = async () => {
    if (!title.trim()) {
      toast.error('Please enter a title to analyze');
      return;
    }

    setIsLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('analyze-title', {
        body: { title: title.trim(), pillarName }
      });

      if (error) throw error;

      if (data?.analysis) {
        setAnalysis(data.analysis);
        toast.success('Title analysis complete!');
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error analyzing title:', error);
      toast.error('Failed to analyze title');
      setAnalysis(null);
    } finally {
      setIsLoading(false);
    }
  };

  const generateSuggestions = async () => {
    if (!title.trim()) {
      toast.error('Please enter a title to optimize');
      return;
    }

    setIsLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('optimize-title', {
        body: { title: title.trim(), description, pillarName }
      });

      if (error) throw error;

      if (data?.suggestions && Array.isArray(data.suggestions)) {
        setSuggestions(data.suggestions);
        toast.success('Title suggestions generated!');
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error generating title suggestions:', error);
      toast.error('Failed to generate title suggestions');
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectTitle = (selectedTitle: string) => {
    if (onSelectTitle) {
      onSelectTitle(selectedTitle);
    }
    onClose();
    toast.success('Title updated!');
  };

  const handleClose = () => {
    setSuggestions([]);
    setAnalysis(null);
    onClose();
  };

  const copyTitle = async (titleText: string) => {
    try {
      await navigator.clipboard.writeText(titleText);
      toast.success('Title copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy title');
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-500';
    if (score >= 60) return 'text-yellow-500';
    if (score >= 40) return 'text-orange-500';
    return 'text-red-500';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Needs Work';
    return 'Poor';
  };

  const getStatusIcon = (status: 'good' | 'warning' | 'poor') => {
    switch (status) {
      case 'good':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'poor':
        return <XCircle className="w-5 h-5 text-red-500" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-3xl bg-gray-800 border-gray-600 max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <BarChart3 className="w-5 h-5 mr-2 text-teal" />
            AI Title Optimizer
          </DialogTitle>
        </DialogHeader>
        
        <Tabs value={activeMode} onValueChange={(value) => setActiveMode(value as 'analyze' | 'generate')}>
          <TabsList className="grid w-full grid-cols-2 bg-gray-700">
            <TabsTrigger value="analyze" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
              Analyze My Title
            </TabsTrigger>
            <TabsTrigger value="generate" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
              Generate New Titles
            </TabsTrigger>
          </TabsList>

          <TabsContent value="analyze" className="space-y-6">
            <div>
              <label className="text-sm font-medium text-gray-300 mb-2 block">
                Enter your current video title *
              </label>
              <Input
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter your video title to analyze..."
                className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
              />
            </div>

            {pillarName && (
              <div className="bg-gray-700 p-3 rounded-lg">
                <p className="text-teal text-sm">Content Pillar: {pillarName}</p>
              </div>
            )}

            <Button
              onClick={analyzeTitle}
              disabled={isLoading || !title.trim()}
              className="w-full bg-teal hover:bg-teal/90"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Analyzing Title...
                </>
              ) : (
                <>
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Analyze Title (1 credit)
                </>
              )}
            </Button>

            {analysis && (
              <div className="space-y-6">
                {/* Score Card */}
                <div className="bg-gray-700 p-6 rounded-lg text-center">
                  <div className={`text-5xl font-bold mb-2 ${getScoreColor(analysis.score)}`}>
                    {analysis.score}/100
                  </div>
                  <div className={`text-lg font-medium ${getScoreColor(analysis.score)}`}>
                    {getScoreLabel(analysis.score)}
                  </div>
                  <Progress 
                    value={analysis.score} 
                    className="mt-4 h-3"
                  />
                </div>

                {/* Detailed Analysis */}
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h3 className="text-white font-medium mb-4">Detailed Analysis</h3>
                  <div className="space-y-3">
                    {[
                      { key: 'keywordPlacement', label: 'Keyword Placement', data: analysis.keywordPlacement },
                      { key: 'length', label: 'Length Optimization', data: analysis.length },
                      { key: 'emotional', label: 'Emotional Triggers', data: analysis.emotional },
                      { key: 'specificity', label: 'Specificity/Numbers', data: analysis.specificity },
                      { key: 'curiosity', label: 'Curiosity Gap', data: analysis.curiosity },
                      { key: 'clarity', label: 'Clarity', data: analysis.clarity }
                    ].map(({ key, label, data }) => (
                      <div key={key} className="flex items-center justify-between p-3 bg-gray-600 rounded">
                        <div className="flex items-center space-x-3">
                          {getStatusIcon(data.status)}
                          <span className="text-white font-medium">{label}</span>
                        </div>
                        <div className="text-right">
                          <span className="text-gray-300 text-sm">{data.score}/20</span>
                          <div className="text-xs text-gray-400">{data.message}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Improvement Suggestions */}
                {analysis.improvements.length > 0 && (
                  <div className="bg-gray-700 p-4 rounded-lg">
                    <h3 className="text-white font-medium mb-3">Your title would improve with:</h3>
                    <ul className="space-y-2">
                      {analysis.improvements.map((improvement, index) => (
                        <li key={index} className="text-gray-300 flex items-start">
                          <span className="text-teal mr-2">•</span>
                          {improvement}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Quick Fix */}
                {analysis.suggestedFix && (
                  <div className="bg-gray-700 p-4 rounded-lg">
                    <h3 className="text-white font-medium mb-3">Suggested Improvement:</h3>
                    <div className="bg-gray-600 p-3 rounded border-l-4 border-teal">
                      <p className="text-white">{analysis.suggestedFix}</p>
                    </div>
                    <div className="flex gap-2 mt-3">
                      <Button
                        size="sm"
                        onClick={() => handleSelectTitle(analysis.suggestedFix)}
                        className="bg-teal hover:bg-teal/90 text-white"
                      >
                        Use This Title
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyTitle(analysis.suggestedFix)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-600"
                      >
                        Copy
                      </Button>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <Button
                    onClick={() => setActiveMode('generate')}
                    variant="outline"
                    className="border-gray-600 text-gray-300 hover:bg-gray-700"
                  >
                    <Sparkles className="w-4 h-4 mr-2" />
                    Generate Alternatives
                  </Button>
                  <Button
                    onClick={() => {
                      setTitle('');
                      setAnalysis(null);
                    }}
                    variant="outline"
                    className="border-gray-600 text-gray-300 hover:bg-gray-700"
                  >
                    Try Another Title
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="generate" className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-300 mb-2 block">
                Your Video Title *
              </label>
              <Input
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter your video title to optimize..."
                className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
              />
            </div>

            {pillarName && (
              <div className="bg-gray-700 p-3 rounded-lg">
                <p className="text-teal text-sm">Content Pillar: {pillarName}</p>
              </div>
            )}

            <Button
              onClick={generateSuggestions}
              disabled={isLoading || !title.trim()}
              className="w-full bg-teal hover:bg-teal/90"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Generating Suggestions...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Generate Title Suggestions (1 credit)
                </>
              )}
            </Button>

            {suggestions.length > 0 && (
              <div className="space-y-3">
                <label className="text-sm font-medium text-gray-300">AI Suggestions:</label>
                {suggestions.map((suggestion, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded border border-gray-600">
                    <span className="text-white flex-1 pr-3">{suggestion}</span>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyTitle(suggestion)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-600"
                      >
                        Copy
                      </Button>
                      {onSelectTitle && (
                        <Button
                          size="sm"
                          onClick={() => handleSelectTitle(suggestion)}
                          className="bg-teal hover:bg-teal/90 text-white"
                        >
                          Use This
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>

        <div className="flex justify-end pt-4">
          <Button variant="ghost" onClick={handleClose} className="text-gray-300 hover:bg-gray-700">
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default OptimizeTitleDialog;
