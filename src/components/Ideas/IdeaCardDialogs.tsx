
import React from 'react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import OptimizeTitleDialog from './OptimizeTitleDialog';
import ScriptWriterDialog from './ScriptWriterDialog';
import DescriptionGeneratorDialog from './DescriptionGeneratorDialog';
import { VideoIdea } from './types';

interface IdeaCardDialogsProps {
  idea: VideoIdea;
  pillarName?: string;
  showOptimizeDialog: boolean;
  showScriptDialog: boolean;
  showDescriptionDialog: boolean;
  showDeleteDialog: boolean;
  onCloseOptimizeDialog: () => void;
  onCloseScriptDialog: () => void;
  onCloseDescriptionDialog: () => void;
  onCloseDeleteDialog: () => void;
  onSelectTitle: (newTitle: string) => void;
  onDelete: (id: string, title: string) => void;
}

const IdeaCardDialogs: React.FC<IdeaCardDialogsProps> = ({
  idea,
  pillarName,
  showOptimizeDialog,
  showScriptDialog,
  showDescriptionDialog,
  showDeleteDialog,
  onCloseOptimizeDialog,
  onCloseScriptDialog,
  onCloseDescriptionDialog,
  onCloseDeleteDialog,
  onSelectTitle,
  onDelete
}) => {
  return (
    <>
      <OptimizeTitleDialog
        isOpen={showOptimizeDialog}
        onClose={onCloseOptimizeDialog}
        currentTitle={idea.title}
        description={idea.description || undefined}
        pillarName={pillarName}
        onSelectTitle={onSelectTitle}
      />

      <ScriptWriterDialog
        isOpen={showScriptDialog}
        onClose={onCloseScriptDialog}
        ideaTitle={idea.title}
        ideaDescription={idea.description || undefined}
        pillarName={pillarName}
      />

      <DescriptionGeneratorDialog
        isOpen={showDescriptionDialog}
        onClose={onCloseDescriptionDialog}
        ideaTitle={idea.title}
        ideaDescription={idea.description || undefined}
        pillarName={pillarName}
      />

      <AlertDialog open={showDeleteDialog} onOpenChange={onCloseDeleteDialog}>
        <AlertDialogContent className="bg-gray-800 border-gray-600">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">Delete Video Idea</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-300">
              Are you sure you want to delete "{idea.title}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-gray-600 text-white hover:bg-gray-500">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => onDelete(idea.id, idea.title)}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default IdeaCardDialogs;
