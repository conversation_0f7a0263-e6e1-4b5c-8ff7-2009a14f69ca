
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import IdeaCard from './IdeaCard';
import { VideoIdea, ContentPillar } from './types';

interface IdeasByPillarProps {
  pillars: ContentPillar[];
  groupedIdeas: Record<string, VideoIdea[]>;
  onEditIdea: (idea: VideoIdea) => void;
  onDeleteIdea: (id: string, title: string) => void;
  onMoveToCalendar: (id: string, title: string) => void;
  onTitleUpdate?: (id: string, newTitle: string) => void;
}

const IdeasByPillar: React.FC<IdeasByPillarProps> = ({
  pillars,
  groupedIdeas,
  onEditIdea,
  onDeleteIdea,
  onMoveToCalendar,
  onTitleUpdate
}) => {
  return (
    <div className="space-y-8">
      {pillars.map(pillar => {
        const pillarIdeas = groupedIdeas[pillar.id] || [];
        
        if (pillarIdeas.length === 0) return null;

        return (
          <Card key={pillar.id} className="content-card">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <div 
                  className="w-4 h-4 rounded-full mr-3"
                  style={{ backgroundColor: pillar.color || '#37BEB0' }}
                />
                {pillar.name}
                <span className="ml-2 text-sm text-gray-400 font-normal">
                  ({pillarIdeas.length} {pillarIdeas.length === 1 ? 'idea' : 'ideas'})
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {pillarIdeas.map(idea => (
                  <IdeaCard
                    key={idea.id}
                    idea={idea}
                    pillarName={pillar.name}
                    onEdit={onEditIdea}
                    onDelete={onDeleteIdea}
                    onMoveToCalendar={onMoveToCalendar}
                    onTitleUpdate={onTitleUpdate}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        );
      })}

      {pillars.every(pillar => (groupedIdeas[pillar.id] || []).length === 0) && (
        <div className="text-center py-12">
          <div className="text-gray-400">
            <h3 className="text-lg font-medium mb-2">No ideas found</h3>
            <p>Try adjusting your search filters or add some new ideas!</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default IdeasByPillar;
