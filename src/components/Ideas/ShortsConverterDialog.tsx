import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Zap, Copy, Save, AlertCircle } from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface ShortsConverterDialogProps {
  isOpen: boolean;
  onClose: () => void;
  pillarName?: string;
}

const ShortsConverterDialog: React.FC<ShortsConverterDialogProps> = ({
  isOpen,
  onClose,
  pillarName = ''
}) => {
  const [longFormContent, setLongFormContent] = useState('');
  const [shortsTitle, setShortsTitle] = useState('');
  const [generatedShorts, setGeneratedShorts] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [user, setUser] = useState(null);
  const [connectionError, setConnectionError] = useState(false);

  // Get user on component mount
  React.useEffect(() => {
    const getUser = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        setUser(session?.user || null);
        setConnectionError(false);
      } catch (error) {
        console.error('Auth session error:', error);
        setConnectionError(true);
      }
    };
    
    getUser();
  }, []);

  // Local processing function for when server is unavailable
  const processLocallyWithRules = (content: string, title: string): string => {
    // Extract key sentences (first sentence, sentences with keywords, last sentence)
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    if (sentences.length === 0) return "Please provide more content to convert.";
    
    // Keep only essential sentences
    let shortContent = [];
    
    // Always include first sentence as hook
    if (sentences.length > 0) {
      shortContent.push(sentences[0].trim() + ".");
    }
    
    // Find sentences with important keywords
    const keywordSentences = sentences.filter(s => {
      const lower = s.toLowerCase();
      return (
        lower.includes("important") || 
        lower.includes("key") || 
        lower.includes("must") ||
        lower.includes("should") ||
        lower.includes("best") ||
        lower.includes("top") ||
        lower.includes("how to")
      );
    }).slice(0, 2); // Take up to 2 keyword sentences
    
    shortContent = [...shortContent, ...keywordSentences.map(s => s.trim() + ".")];
    
    // Add conclusion if available
    if (sentences.length > 2) {
      shortContent.push(sentences[sentences.length - 1].trim() + ".");
    }
    
    // Format as a shorts script
    let script = "";
    
    // Add title if provided
    if (title) {
      script += `TITLE: ${title}\n\n`;
    }
    
    script += "HOOK: " + shortContent[0] + "\n\n";
    script += "MAIN POINTS:\n";
    
    // Add remaining content as bullet points
    shortContent.slice(1, -1).forEach(sentence => {
      script += `• ${sentence}\n`;
    });
    
    // Add call to action
    script += "\nCTA: " + (shortContent[shortContent.length - 1] || "Like and follow for more tips!") + "\n";
    script += "\nNOTES: This shorts video should be 30-60 seconds. Use quick cuts and visual elements to maintain engagement.";
    
    return script;
  };

  const convertToShorts = async () => {
    if (!longFormContent.trim()) {
      toast.error('Please provide content to convert');
      return;
    }

    setIsGenerating(true);

    try {
      // First try server-based processing
      try {
        const { data, error } = await supabase.functions.invoke('convert-to-shorts', {
          body: {
            title: shortsTitle,
            longFormContent: longFormContent.trim(),
            pillar: pillarName
          }
        });

        if (error) throw error;

        setGeneratedShorts(data.script || '');
        setConnectionError(false);
        console.log('Shorts script generated successfully!');
      } catch (serverError) {
        console.warn('Server processing failed, falling back to local processing:', serverError);
        
        // Fall back to local processing
        const localResult = processLocallyWithRules(longFormContent, shortsTitle);
        setGeneratedShorts(localResult);
        setConnectionError(true);
        console.log('Shorts script generated in offline mode');
      }
    } catch (error) {
      console.error('Error converting to shorts:', error);
      toast.error('Failed to convert to shorts');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedShorts);
    toast.success('Copied to clipboard!');
  };

  const saveAsIdea = async () => {
    if (connectionError) {
      // Store in localStorage if offline
      try {
        const savedIdeas = JSON.parse(localStorage.getItem('offlineShortsIdeas') || '[]');
        savedIdeas.push({
          title: shortsTitle || 'Shorts Video',
          script: generatedShorts,
          createdAt: new Date().toISOString()
        });
        localStorage.setItem('offlineShortsIdeas', JSON.stringify(savedIdeas));
        toast.success('Saved offline. Will sync when connection is restored.');
        onClose();
      } catch (error) {
        console.error('Error saving to localStorage:', error);
        toast.error('Failed to save offline');
      }
      return;
    }
    
    if (!user) {
      toast.error('You must be logged in to save ideas');
      return;
    }
    
    try {
      await supabase.from('videos').insert({
        user_id: user.id,
        title: shortsTitle || 'Shorts Video',
        description: 'Converted from long-form content',
        status: 'idea',
        script: generatedShorts,
        is_short: true
      });
      
      toast.success('Saved as new shorts idea!');
      onClose();
    } catch (error) {
      console.error('Error saving shorts idea:', error);
      toast.error('Failed to save shorts idea');
      
      // Fall back to localStorage
      try {
        const savedIdeas = JSON.parse(localStorage.getItem('offlineShortsIdeas') || '[]');
        savedIdeas.push({
          title: shortsTitle || 'Shorts Video',
          script: generatedShorts,
          createdAt: new Date().toISOString()
        });
        localStorage.setItem('offlineShortsIdeas', JSON.stringify(savedIdeas));
        toast.success('Saved offline. Will sync when connection is restored.');
        onClose();
      } catch (localError) {
        console.error('Error saving to localStorage:', localError);
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-2xl bg-gray-900 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-white">Shorts Converter</DialogTitle>
        </DialogHeader>
        
        {connectionError && (
          <div className="bg-orange-500/20 border border-orange-500/30 rounded-md p-3 mb-4">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-orange-400 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <p className="text-orange-300 text-sm font-medium">Working in offline mode</p>
                <p className="text-orange-200/70 text-xs mt-1">
                  Server connection unavailable. Basic conversion is still available, but quality may be reduced.
                </p>
              </div>
            </div>
          </div>
        )}
        
        <div className="space-y-4">
          <div>
            <Label className="text-white text-sm font-medium mb-2 block">Shorts Title (Optional)</Label>
            <Input
              value={shortsTitle}
              onChange={(e) => setShortsTitle(e.target.value)}
              placeholder="Enter a title for your shorts video..."
              className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
            />
          </div>

          <div>
            <Label className="text-white text-sm font-medium mb-2 block">Long-Form Content *</Label>
            <Textarea
              value={longFormContent}
              onChange={(e) => setLongFormContent(e.target.value)}
              placeholder="Paste your long-form video script, idea, or content here..."
              className="min-h-[120px] bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
            />
          </div>

          <Button 
            onClick={convertToShorts}
            disabled={isGenerating || !longFormContent.trim()}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Converting...
              </>
            ) : (
              <>
                <Zap className="w-4 h-4 mr-2" />
                {connectionError ? 'Convert Offline' : 'Convert to Shorts'}
              </>
            )}
          </Button>

          {generatedShorts && (
            <div className="space-y-4">
              <div className="p-4 bg-gray-800 rounded-md border border-gray-700">
                <h3 className="text-white font-medium mb-2">Generated Shorts Script</h3>
                <div className="whitespace-pre-wrap text-gray-300 text-sm">
                  {generatedShorts}
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button 
                  onClick={copyToClipboard}
                  className="bg-gray-700 hover:bg-gray-600 text-white"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copy to Clipboard
                </Button>
                
                <Button 
                  onClick={saveAsIdea}
                  className="bg-teal-600 hover:bg-teal-700 text-white"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {connectionError ? 'Save Offline' : 'Save as Idea'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ShortsConverterDialog;
