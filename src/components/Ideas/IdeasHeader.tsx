
import React from 'react';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ContentPillar, NewIdea } from './types';
import AICreditsDisplay from './AICreditsDisplay';
import EnhancedAddIdeaForm from './EnhancedAddIdeaForm';

interface IdeasHeaderProps {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  newIdea: NewIdea;
  setNewIdea: (idea: NewIdea) => void;
  pillars: ContentPillar[];
  addIdea: () => void;
  showGenerateButton?: boolean;
}

const IdeasHeader: React.FC<IdeasHeaderProps> = ({
  isDialogOpen,
  setIsDialogOpen,
  newIdea,
  setNewIdea,
  pillars,
  addIdea,
  showGenerateButton = true
}) => {
  return (
    <div className="flex items-center justify-end gap-4">
      <AICreditsDisplay />
      
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button className="bg-teal hover:bg-teal/90 text-white">
            <Plus className="w-4 h-4 mr-2" />
            Add New Idea
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle className="text-white">Add New Video Idea</DialogTitle>
          </DialogHeader>
          <EnhancedAddIdeaForm
            newIdea={newIdea}
            setNewIdea={setNewIdea}
            pillars={pillars}
            onSubmit={addIdea}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default IdeasHeader;
