
import React from 'react';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { VideoIdea, ContentPillar } from './types';
import IdeasByPillar from './IdeasByPillar';
import KanbanBoard from './KanbanBoard';
import TabsNavigation from './TabsNavigation';
import IdeasFilters from './IdeasFilters';
import TabsActions from './TabsActions';

interface IdeasPageTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  groupedIdeas: { [key: string]: VideoIdea[] };
  pillars: ContentPillar[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterPillar: string;
  setFilterPillar: (pillar: string) => void;
  filterPriority: string;
  setFilterPriority: (priority: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  onEditIdea: (idea: VideoIdea) => void;
  onDeleteIdea: (id: string, title: string) => void;
  onMoveToCalendar: (id: string, title: string) => void;
  onTitleUpdate: (id: string, title: string) => void;
  onStatusChange: (id: string, status: string) => void;
  onAddIdea: () => void;
}

const IdeasPageTabs: React.FC<IdeasPageTabsProps> = ({
  activeTab,
  setActiveTab,
  groupedIdeas,
  pillars,
  searchTerm,
  setSearchTerm,
  filterPillar,
  setFilterPillar,
  filterPriority,
  setFilterPriority,
  filterStatus,
  setFilterStatus,
  onEditIdea,
  onDeleteIdea,
  onMoveToCalendar,
  onTitleUpdate,
  onStatusChange,
  onAddIdea
}) => {
  const allIdeas = Object.values(groupedIdeas).flat();

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsNavigation 
        activeTab={activeTab}
        setActiveTab={setActiveTab}
      />

      <div className="p-6 bg-gray-800/50 backdrop-blur-sm border-t-0 border border-gray-700 rounded-b-lg">
        <IdeasFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          filterPillar={filterPillar}
          setFilterPillar={setFilterPillar}
          filterPriority={filterPriority}
          setFilterPriority={setFilterPriority}
          filterStatus={filterStatus}
          setFilterStatus={setFilterStatus}
          pillars={pillars}
        />

        <TabsActions onAddIdea={onAddIdea} />

        <TabsContent value="all-ideas" className="mt-6">
          <IdeasByPillar
            groupedIdeas={groupedIdeas}
            pillars={pillars}
            onEditIdea={onEditIdea}
            onDeleteIdea={onDeleteIdea}
            onMoveToCalendar={onMoveToCalendar}
            onTitleUpdate={onTitleUpdate}
          />
        </TabsContent>

        <TabsContent value="kanban" className="mt-6">
          <KanbanBoard
            ideas={allIdeas}
            pillars={pillars}
            onEditIdea={onEditIdea}
            onDeleteIdea={onDeleteIdea}
            onMoveToCalendar={onMoveToCalendar}
            onTitleUpdate={onTitleUpdate}
            onStatusChange={onStatusChange}
            onAddNewIdea={onAddIdea}
          />
        </TabsContent>
      </div>
    </Tabs>
  );
};

export default IdeasPageTabs;
