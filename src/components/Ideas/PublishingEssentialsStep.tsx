import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>les, FileText, Hash, Copy, RefreshCw, CheckCircle } from 'lucide-react';
import { ContentPillar } from './types';
import TagsManager from './SEO/TagsManager';
import HashtagsEditor from './SEO/HashtagsEditor';
import SEOStrengthIndicator from './SEO/SEOStrengthIndicator';

interface PublishingEssentialsStepProps {
  formData: any;
  setFormData: (data: any) => void;
  loading: boolean;
  onGenerateDescription: () => void;
  onGenerateSEOTags: () => Promise<{ tags: string[]; hashtags: string }>;
  generatedDescriptions: string[];
  pillars: ContentPillar[];
  onSkipStep: () => void;
  onSaveIdea: (ideaData: any) => void;
}

const PublishingEssentialsStep: React.FC<PublishingEssentialsStepProps> = ({
  formData,
  setFormData,
  loading,
  onGenerateDescription,
  onGenerateSEOTags,
  generatedDescriptions,
  pillars,
  onSkipStep,
  onSaveIdea
}) => {
  const [selectedDescription, setSelectedDescription] = useState(formData.description || '');
  const [customDescription, setCustomDescription] = useState('');
  const [tags, setTags] = useState<string[]>(formData.tags || []);
  const [hashtags, setHashtags] = useState(formData.hashtags || '');
  const [seoGenerated, setSeoGenerated] = useState(false);

  // Auto-generate description on mount
  useEffect(() => {
    if (!generatedDescriptions.length && !loading && formData.title) {
      onGenerateDescription();
    }
  }, [formData.title, generatedDescriptions.length, loading, onGenerateDescription]);

  // Auto-generate SEO when description is set
  useEffect(() => {
    if ((selectedDescription || customDescription) && !seoGenerated && !loading) {
      handleGenerateSEO();
      setSeoGenerated(true);
    }
  }, [selectedDescription, customDescription, seoGenerated, loading]);

  const handleDescriptionSelect = (description: string) => {
    setSelectedDescription(description);
    setFormData({ ...formData, description });
  };

  const handleCustomDescriptionChange = (description: string) => {
    setCustomDescription(description);
    setSelectedDescription(description);
    setFormData({ ...formData, description });
  };

  const handleGenerateSEO = async () => {
    try {
      const result = await onGenerateSEOTags();
      setTags(result.tags);
      setHashtags(result.hashtags);
      setFormData({ 
        ...formData, 
        tags: result.tags, 
        hashtags: result.hashtags 
      });
    } catch (error) {
      console.error('Failed to generate SEO:', error);
    }
  };

  const handleTagsChange = (newTags: string[]) => {
    setTags(newTags);
    setFormData({ ...formData, tags: newTags });
  };

  const handleHashtagsChange = (newHashtags: string) => {
    setHashtags(newHashtags);
    setFormData({ ...formData, hashtags: newHashtags });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const handleFinishWizard = () => {
    // Update form data with final values
    const finalFormData = { 
      ...formData, 
      description: selectedDescription || customDescription,
      tags,
      hashtags
    };
    setFormData(finalFormData);
    
    console.log('Saving idea with data:', finalFormData);
    
    // Save the idea instead of calling onContinue
    onSaveIdea(finalFormData);
  };

  const hashtagCount = hashtags.split(' ').filter(h => h.startsWith('#')).length;

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-white flex items-center justify-center">
          <Hash className="w-5 h-5 mr-2 text-teal" />
          Publishing Essentials
        </h3>
        <p className="text-gray-300">Optimize your video for discovery and engagement</p>
      </div>

      {/* Context Summary */}
      {formData.title && (
        <Card className="bg-gray-800 border-gray-600">
          <CardContent className="p-4">
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-white font-medium">Title:</span>
                <p className="text-gray-300">{formData.title}</p>
              </div>
              {formData.hook && (
                <div>
                  <span className="text-white font-medium">Hook:</span>
                  <p className="text-gray-300">{formData.hook.substring(0, 100)}...</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="description" className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-gray-800">
          <TabsTrigger value="description" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
            <FileText className="w-4 h-4 mr-2" />
            Description
          </TabsTrigger>
          <TabsTrigger value="seo" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
            <Hash className="w-4 h-4 mr-2" />
            SEO & Tags
          </TabsTrigger>
        </TabsList>

        <TabsContent value="description" className="space-y-4">
          <Card className="bg-gray-800 border-gray-600">
            <CardHeader>
              <CardTitle className="text-white flex items-center justify-between">
                <span className="flex items-center">
                  <FileText className="w-5 h-5 mr-2 text-teal" />
                  Video Description
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onGenerateDescription}
                  disabled={loading}
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  <RefreshCw className="w-4 h-4 mr-1" />
                  Regenerate
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!generatedDescriptions.length && !loading && (
                <Button 
                  onClick={onGenerateDescription} 
                  className="bg-teal hover:bg-teal/90 text-white w-full"
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  Generate Description (2 credits)
                </Button>
              )}

              {loading && (
                <div className="flex items-center justify-center py-8">
                  <div className="w-6 h-6 border-4 border-teal border-t-transparent rounded-full animate-spin"></div>
                  <span className="ml-3 text-gray-300">Generating description...</span>
                </div>
              )}

              {generatedDescriptions.map((description, index) => (
                <Card 
                  key={index} 
                  className={`cursor-pointer transition-colors ${
                    selectedDescription === description 
                      ? 'bg-teal/20 border-teal/30' 
                      : 'bg-gray-700 border-gray-600 hover:bg-gray-600'
                  }`}
                  onClick={() => handleDescriptionSelect(description)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <pre className="text-white text-sm whitespace-pre-wrap flex-1">
                        {description.length > 200 ? `${description.substring(0, 200)}...` : description}
                      </pre>
                      <div className="flex items-center ml-4 space-x-2">
                        {selectedDescription === description && (
                          <Badge className="bg-teal text-black">Selected</Badge>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            copyToClipboard(description);
                          }}
                          className="border-gray-600 text-gray-300 hover:bg-gray-700"
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              <Card className="bg-gray-800 border-gray-600">
                <CardHeader>
                  <CardTitle className="text-white text-lg">Custom Description</CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Write your own description or modify the generated one..."
                    value={customDescription}
                    onChange={(e) => handleCustomDescriptionChange(e.target.value)}
                    className="bg-gray-700 border-gray-600 text-white min-h-[150px]"
                  />
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seo" className="space-y-4">
          <SEOStrengthIndicator 
            tagCount={tags.length} 
            hashtagCount={hashtagCount} 
          />

          {!seoGenerated && !loading && (
            <Button 
              onClick={handleGenerateSEO} 
              className="bg-teal hover:bg-teal/90 text-white w-full"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Generate SEO Tags (1 credit)
            </Button>
          )}

          <TagsManager
            tags={tags}
            onTagsChange={handleTagsChange}
            onRegenerateTags={handleGenerateSEO}
            loading={loading}
          />

          <HashtagsEditor
            hashtags={hashtags}
            onHashtagsChange={handleHashtagsChange}
          />
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={onSkipStep}
          className="border-gray-600 text-gray-300 hover:bg-gray-700"
        >
          Skip this step
        </Button>

        <Button
          onClick={handleFinishWizard}
          className="bg-teal hover:bg-teal/90 text-white"
        >
          <CheckCircle className="w-4 h-4 mr-2" />
          Complete & Save Idea
        </Button>
      </div>
    </div>
  );
};

export default PublishingEssentialsStep;
