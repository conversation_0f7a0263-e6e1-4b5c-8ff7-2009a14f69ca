
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Sparkles, Lightbulb, Target, MessageSquare } from 'lucide-react';
import { ContentPillar } from './types';

interface TopicObject {
  title?: string;
  text?: string;
  description?: string;
  reason?: string;
}

interface EnhancedStartingPointStepProps {
  startingPointOption: string;
  setStartingPointOption: (option: string) => void;
  selectedPillarForTopics: string;
  setSelectedPillarForTopics: (pillar: string) => void;
  manualTopic: string;
  setManualTopic: (topic: string) => void;
  videoTitle: string;
  setVideoTitle: (title: string) => void;
  roughIdea: string;
  setRoughIdea: (idea: string) => void;
  generatedTopics: TopicObject[];
  formData: any;
  setFormData: (data: any) => void;
  pillars: ContentPillar[];
  loading: boolean;
  onGenerateTopics: () => void;
  onContinue: () => void;
}

const EnhancedStartingPointStep: React.FC<EnhancedStartingPointStepProps> = ({
  startingPointOption,
  setStartingPointOption,
  selectedPillarForTopics,
  setSelectedPillarForTopics,
  manualTopic,
  setManualTopic,
  videoTitle,
  setVideoTitle,
  roughIdea,
  setRoughIdea,
  generatedTopics,
  formData,
  setFormData,
  pillars,
  loading,
  onGenerateTopics,
  onContinue
}) => {
  const safeGeneratedTopics = Array.isArray(generatedTopics) ? generatedTopics : [];

  const handleTopicSelection = (topic: TopicObject) => {
    const getTopicText = (topic: TopicObject): string => {
      if (typeof topic === 'string') return topic;
      return topic.title || topic.text || topic.description || String(topic);
    };

    const topicText = getTopicText(topic);
    setFormData({ 
      ...formData, 
      topic: topicText, 
      pillar_id: selectedPillarForTopics 
    });
    onContinue();
  };

  const handleContinueWithInput = () => {
    let topicText = '';
    
    if (startingPointOption === 'generate' && manualTopic) {
      topicText = manualTopic;
    } else if (startingPointOption === 'title' && videoTitle) {
      topicText = `Video about: ${videoTitle}`;
      setFormData({ ...formData, title: videoTitle });
    } else if (startingPointOption === 'idea' && roughIdea) {
      topicText = roughIdea;
    }

    if (topicText && selectedPillarForTopics) {
      setFormData({
        ...formData,
        topic: topicText,
        pillar_id: selectedPillarForTopics
      });
      onContinue();
    }
  };

  const canContinue = () => {
    if (!selectedPillarForTopics) return false;
    
    switch (startingPointOption) {
      case 'generate':
        return !!manualTopic;
      case 'title':
        return !!videoTitle;
      case 'idea':
        return !!roughIdea;
      default:
        return false;
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-white flex items-center justify-center">
          <Lightbulb className="w-5 h-5 mr-2 text-yellow" />
          Choose Your Starting Point
        </h3>
        <p className="text-gray-300">How would you like to begin creating your video?</p>
      </div>

      <RadioGroup value={startingPointOption} onValueChange={setStartingPointOption}>
        <div className="space-y-4">
          <Card className="bg-gray-800 border-gray-600">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <RadioGroupItem value="generate" id="generate" />
                <Label htmlFor="generate" className="text-white flex items-center">
                  <Sparkles className="w-4 h-4 mr-2 text-teal" />
                  Generate topics for me
                </Label>
              </div>
              {startingPointOption === 'generate' && (
                <div className="mt-4 space-y-4">
                  <Select value={selectedPillarForTopics} onValueChange={setSelectedPillarForTopics}>
                    <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                      <SelectValue placeholder="Choose content pillar" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-700 border-gray-600">
                      {pillars.map(pillar => (
                        <SelectItem key={pillar.id} value={pillar.id}>
                          {pillar.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Button 
                    onClick={onGenerateTopics} 
                    disabled={loading || !selectedPillarForTopics}
                    className="bg-teal hover:bg-teal/90 text-white w-full"
                  >
                    <Sparkles className="w-4 h-4 mr-2" />
                    Generate Topics (3 credits)
                  </Button>

                  {safeGeneratedTopics.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="text-lg font-medium text-white">Generated Topics:</h4>
                      {safeGeneratedTopics.map((topic, index) => (
                        <Card key={index} className="bg-gray-700 border-gray-600 cursor-pointer hover:bg-gray-600 transition-colors">
                          <CardContent className="p-4">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <p className="text-white font-medium">
                                  {topic.title || topic.text || topic.description || String(topic)}
                                </p>
                                {topic.reason && (
                                  <p className="text-gray-300 text-sm mt-1">{topic.reason}</p>
                                )}
                              </div>
                              <Button
                                size="sm"
                                onClick={() => handleTopicSelection(topic)}
                                className="bg-teal hover:bg-teal/90 text-white ml-4"
                              >
                                Select
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}

                  <div className="pt-4 border-t border-gray-600">
                    <Label className="text-white mb-2 block">Or enter your own topic:</Label>
                    <Input
                      placeholder="Enter your video topic..."
                      value={manualTopic}
                      onChange={(e) => setManualTopic(e.target.value)}
                      className="bg-gray-700 border-gray-600 text-white"
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="bg-gray-800 border-gray-600">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <RadioGroupItem value="title" id="title" />
                <Label htmlFor="title" className="text-white flex items-center">
                  <Target className="w-4 h-4 mr-2 text-orange" />
                  I have a video title already
                </Label>
              </div>
              {startingPointOption === 'title' && (
                <div className="mt-4 space-y-4">
                  <Select value={selectedPillarForTopics} onValueChange={setSelectedPillarForTopics}>
                    <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                      <SelectValue placeholder="Choose content pillar" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-700 border-gray-600">
                      {pillars.map(pillar => (
                        <SelectItem key={pillar.id} value={pillar.id}>
                          {pillar.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Input
                    placeholder="Enter your video title..."
                    value={videoTitle}
                    onChange={(e) => setVideoTitle(e.target.value)}
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="bg-gray-800 border-gray-600">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <RadioGroupItem value="idea" id="idea" />
                <Label htmlFor="idea" className="text-white flex items-center">
                  <MessageSquare className="w-4 h-4 mr-2 text-terracotta" />
                  I have a rough idea
                </Label>
              </div>
              {startingPointOption === 'idea' && (
                <div className="mt-4 space-y-4">
                  <Select value={selectedPillarForTopics} onValueChange={setSelectedPillarForTopics}>
                    <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                      <SelectValue placeholder="Choose content pillar" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-700 border-gray-600">
                      {pillars.map(pillar => (
                        <SelectItem key={pillar.id} value={pillar.id}>
                          {pillar.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Textarea
                    placeholder="Describe your rough idea or concept..."
                    value={roughIdea}
                    onChange={(e) => setRoughIdea(e.target.value)}
                    className="bg-gray-700 border-gray-600 text-white min-h-[100px]"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </RadioGroup>

      {(startingPointOption === 'title' || startingPointOption === 'idea' || (startingPointOption === 'generate' && manualTopic)) && (
        <Button
          onClick={handleContinueWithInput}
          disabled={!canContinue()}
          className="bg-teal hover:bg-teal/90 text-white w-full"
        >
          Continue to Title & Hook
        </Button>
      )}
    </div>
  );
};

export default EnhancedStartingPointStep;
