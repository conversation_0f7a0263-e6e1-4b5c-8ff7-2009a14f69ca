
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TooltipProvider } from '@/components/ui/tooltip';
import { Sparkles } from 'lucide-react';
import IdeaCardHeader from './IdeaCardHeader';
import IdeaCardContent from './IdeaCardContent';
import IdeaCardDialogs from './IdeaCardDialogs';
import AIWizardDataModal from './AIWizardDataModal';
import { VideoIdea } from './types';

interface IdeaCardProps {
  idea: VideoIdea;
  pillarName?: string;
  onEdit: (idea: VideoIdea) => void;
  onDelete: (id: string, title: string) => void;
  onMoveToCalendar: (id: string, title: string) => void;
  onTitleUpdate?: (id: string, newTitle: string) => void;
}

const IdeaCard: React.FC<IdeaCardProps> = ({
  idea,
  pillarName,
  onEdit,
  onDelete,
  onMoveToCalendar,
  onTitleUpdate
}) => {
  const [showOptimizeDialog, setShowOptimizeDialog] = useState(false);
  const [showScriptDialog, setShowScriptDialog] = useState(false);
  const [showDescriptionDialog, setShowDescriptionDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showAIWizardModal, setShowAIWizardModal] = useState(false);

  const handleOptimizeTitle = (newTitle: string) => {
    if (onTitleUpdate) {
      onTitleUpdate(idea.id, newTitle);
    }
  };

  // Check if this is an AI-generated idea
  const isAIGenerated = idea.description?.includes('🤖 AI-Generated Video Plan') || 
                       idea.description?.includes('Hook:') && idea.description?.includes('Thumbnail:');

  const handleCardClick = (e: React.MouseEvent) => {
    // Prevent opening modal if clicking on buttons or other interactive elements
    const target = e.target as HTMLElement;
    if (target.closest('button') || target.closest('[role="button"]')) {
      return;
    }
    
    setShowAIWizardModal(true);
  };

  return (
    <TooltipProvider>
      <Card 
        className="bg-gray-800 border-gray-600 transition-none h-full flex flex-col relative cursor-pointer hover:bg-gray-750"
        onClick={handleCardClick}
      >
        <IdeaCardHeader
          idea={idea}
          pillarName={pillarName}
          onEdit={onEdit}
          onDelete={onDelete}
          onTitleUpdate={onTitleUpdate}
          onShowOptimizeDialog={() => setShowOptimizeDialog(true)}
          onShowScriptDialog={() => setShowScriptDialog(true)}
          onShowDescriptionDialog={() => setShowDescriptionDialog(true)}
          onShowDeleteDialog={() => setShowDeleteDialog(true)}
          isAIGenerated={isAIGenerated}
        />
        
        <IdeaCardContent
          idea={idea}
          onMoveToCalendar={onMoveToCalendar}
        />

        <IdeaCardDialogs
          idea={idea}
          pillarName={pillarName}
          showOptimizeDialog={showOptimizeDialog}
          showScriptDialog={showScriptDialog}
          showDescriptionDialog={showDescriptionDialog}
          showDeleteDialog={showDeleteDialog}
          onCloseOptimizeDialog={() => setShowOptimizeDialog(false)}
          onCloseScriptDialog={() => setShowScriptDialog(false)}
          onCloseDescriptionDialog={() => setShowDescriptionDialog(false)}
          onCloseDeleteDialog={() => setShowDeleteDialog(false)}
          onSelectTitle={handleOptimizeTitle}
          onDelete={onDelete}
        />

        <AIWizardDataModal
          isOpen={showAIWizardModal}
          onClose={() => setShowAIWizardModal(false)}
          idea={idea}
          pillarName={pillarName}
        />
      </Card>
    </TooltipProvider>
  );
};

export default IdeaCard;
