
import React from 'react';
import { Draggable } from 'react-beautiful-dnd';
import { GripVertical } from 'lucide-react';
import { VideoIdea } from '../types';
import IdeaCard from '../IdeaCard';

interface DraggableIdeaCardProps {
  idea: VideoIdea;
  index: number;
  pillarName: string;
  onEdit: (idea: VideoIdea) => void;
  onDelete: (id: string, title: string) => void;
  onMoveToCalendar: (id: string, title: string) => void;
  onTitleUpdate?: (id: string, newTitle: string) => void;
}

const DraggableIdeaCard: React.FC<DraggableIdeaCardProps> = ({
  idea,
  index,
  pillarName,
  onEdit,
  onDelete,
  onMoveToCalendar,
  onTitleUpdate
}) => {
  // Ensure we have a valid string ID
  const draggableId = String(idea.id);
  
  return (
    <Draggable draggableId={draggableId} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={`transition-all duration-200 ease-out ${
            snapshot.isDragging 
              ? 'kanban-card-dragging rotate-2 scale-105 z-50 shadow-2xl' 
              : 'hover-lift'
          }`}
        >
          <div className="relative group">
            {/* Drag Handle */}
            <div 
              {...provided.dragHandleProps}
              className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-grab active:cursor-grabbing"
            >
              <div className="bg-gray-700/90 backdrop-blur-sm rounded p-1 hover:bg-gray-600/90 transition-colors shadow-lg">
                <GripVertical className="w-4 h-4 text-gray-300" />
              </div>
            </div>
            
            <IdeaCard
              idea={idea}
              pillarName={pillarName}
              onEdit={onEdit}
              onDelete={onDelete}
              onMoveToCalendar={onMoveToCalendar}
              onTitleUpdate={onTitleUpdate}
            />
          </div>
        </div>
      )}
    </Draggable>
  );
};

export default DraggableIdeaCard;
