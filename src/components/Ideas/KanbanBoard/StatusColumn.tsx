
import React from 'react';
import { Droppable } from 'react-beautiful-dnd';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { VideoIdea, ContentPillar } from '../types';
import DraggableIdeaCard from './DraggableIdeaCard';
import EmptyColumnState from './EmptyColumnState';

interface StatusColumnProps {
  column: {
    id: string;
    title: string;
    color: string;
    description: string;
    glowColor: string;
  };
  ideas: VideoIdea[];
  pillars: ContentPillar[];
  onEditIdea: (idea: VideoIdea) => void;
  onDeleteIdea: (id: string, title: string) => void;
  onMoveToCalendar: (id: string, title: string) => void;
  onTitleUpdate?: (id: string, newTitle: string) => void;
  onAddNewIdea: () => void;
}

const StatusColumn: React.FC<StatusColumnProps> = ({
  column,
  ideas,
  pillars,
  onEditIdea,
  onDeleteIdea,
  onMoveToCalendar,
  onTitleUpdate,
  onAddNewIdea
}) => {
  const getPillarName = (pillarId: string) => {
    return pillars.find(p => p.id === pillarId)?.name || 'Unknown Pillar';
  };

  // Filter and validate ideas to ensure they have proper IDs
  const validIdeas = ideas.filter(idea => idea && idea.id && typeof idea.id === 'string');

  return (
    <div className="flex-shrink-0 w-80">
      <Card className="h-full bg-gray-800 border-gray-600 transition-all duration-300 hover-lift">
        <CardHeader className="pb-3">
          <CardTitle className="text-white flex items-center justify-between">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${column.color} shadow-lg ${column.glowColor}`} />
              <div>
                <span>{column.title}</span>
                <Badge variant="secondary" className="ml-2 text-xs bg-gray-700 text-gray-300">
                  {validIdeas.length}
                </Badge>
              </div>
            </div>
            
            {column.id === 'idea' && (
              <Button
                size="sm"
                variant="ghost"
                onClick={onAddNewIdea}
                className="text-gray-400 hover:text-white p-1 hover:bg-gray-700 transition-colors"
              >
                <Plus className="w-4 h-4" />
              </Button>
            )}
          </CardTitle>
          <p className="text-sm text-gray-400">{column.description}</p>
        </CardHeader>
        
        <Droppable droppableId={column.id}>
          {(provided, snapshot) => (
            <CardContent 
              ref={provided.innerRef}
              {...provided.droppableProps}
              className={`space-y-4 max-h-[calc(100vh-300px)] overflow-y-auto transition-all duration-300 ease-in-out min-h-[200px] ${
                snapshot.isDraggingOver 
                  ? `kanban-dropzone-active bg-gray-700/50 border-2 border-dashed border-gray-500 ${column.glowColor} shadow-lg` 
                  : 'border-2 border-transparent'
              }`}
            >
              {validIdeas.length === 0 ? (
                <EmptyColumnState
                  column={column}
                  isDraggingOver={snapshot.isDraggingOver}
                  onAddNewIdea={onAddNewIdea}
                />
              ) : (
                validIdeas.map((idea, index) => (
                  <DraggableIdeaCard
                    key={idea.id}
                    idea={idea}
                    index={index}
                    pillarName={getPillarName(idea.pillar_id)}
                    onEdit={onEditIdea}
                    onDelete={onDeleteIdea}
                    onMoveToCalendar={onMoveToCalendar}
                    onTitleUpdate={onTitleUpdate}
                  />
                ))
              )}
              {provided.placeholder}
            </CardContent>
          )}
        </Droppable>
      </Card>
    </div>
  );
};

export default StatusColumn;
