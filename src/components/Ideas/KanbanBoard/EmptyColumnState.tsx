
import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface EmptyColumnStateProps {
  column: {
    id: string;
    title: string;
    color: string;
  };
  isDraggingOver: boolean;
  onAddNewIdea: () => void;
}

const EmptyColumnState: React.FC<EmptyColumnStateProps> = ({
  column,
  isDraggingOver,
  onAddNewIdea
}) => {
  return (
    <div className={`text-center py-12 text-gray-400 transition-all duration-300 ${
      isDraggingOver 
        ? 'text-gray-300 transform scale-105' 
        : ''
    }`}>
      <div className="mb-3">
        <div className={`w-12 h-12 rounded-full mx-auto mb-3 flex items-center justify-center ${column.color} opacity-20`}>
          <Plus className="w-6 h-6" />
        </div>
      </div>
      <p className="text-sm font-medium">
        {isDraggingOver ? 'Drop here!' : `No ${column.title.toLowerCase()} yet`}
      </p>
      {column.id === 'idea' && !isDraggingOver && (
        <Button
          variant="outline"
          size="sm"
          onClick={onAddNewIdea}
          className="mt-3 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500 transition-all"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Idea
        </Button>
      )}
    </div>
  );
};

export default EmptyColumnState;
