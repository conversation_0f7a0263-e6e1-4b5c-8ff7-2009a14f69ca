
import React, { useState } from 'react';
import { FileText, Clock, Loader2 } from 'lucide-react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';

interface ScriptWriterDialogProps {
  isOpen: boolean;
  onClose: () => void;
  ideaTitle: string;
  ideaDescription?: string;
  pillarName?: string;
  onScriptGenerated?: (script: string) => void;
}

const ScriptWriterDialog: React.FC<ScriptWriterDialogProps> = ({
  isOpen,
  onClose,
  ideaTitle,
  ideaDescription,
  pillarName,
  onScriptGenerated
}) => {
  const { user } = useAuth();
  const [videoTitle, setVideoTitle] = useState(ideaTitle);
  const [videoLength, setVideoLength] = useState<string>('');
  const [tone, setTone] = useState<string>('');
  const [generatedScript, setGeneratedScript] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [estimatedReadTime, setEstimatedReadTime] = useState<string>('');

  const generateScript = async () => {
    if (!user || !videoTitle.trim() || !videoLength || !tone) {
      toast.error('Please fill in video title, length, and tone');
      return;
    }

    setIsGenerating(true);

    try {
      const { data, error } = await supabase.functions.invoke('generate-script', {
        body: {
          title: videoTitle,
          description: ideaDescription || '',
          pillar: pillarName || '',
          videoLength,
          tone
        }
      });

      if (error) throw error;

      setGeneratedScript(data.script);
      setEstimatedReadTime(data.estimatedReadTime);
      
      if (onScriptGenerated) {
        onScriptGenerated(data.script);
      }

      toast.success('Script generated successfully!');
    } catch (error) {
      console.error('Error generating script:', error);
      toast.error('Failed to generate script');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedScript);
      toast.success('Script copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy script');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <FileText className="w-5 h-5 mr-2 text-teal" />
            AI Script Writer
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div>
            <label className="text-white text-sm font-medium mb-2 block">Video Title *</label>
            <Input
              value={videoTitle}
              onChange={(e) => setVideoTitle(e.target.value)}
              placeholder="Enter your video title..."
              className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
            />
          </div>

          {(ideaDescription || pillarName) && (
            <div className="bg-gray-700 p-4 rounded-lg">
              {ideaDescription && (
                <p className="text-gray-300 text-sm mb-2">{ideaDescription}</p>
              )}
              {pillarName && (
                <p className="text-teal text-sm">Content Pillar: {pillarName}</p>
              )}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-white text-sm font-medium mb-2 block">Video Length *</label>
              <Select value={videoLength} onValueChange={setVideoLength}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select video length" />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  <SelectItem value="short">Short (60 seconds)</SelectItem>
                  <SelectItem value="medium">Medium (5-10 minutes)</SelectItem>
                  <SelectItem value="long">Long (10-20 minutes)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-white text-sm font-medium mb-2 block">Tone *</label>
              <Select value={tone} onValueChange={setTone}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select tone" />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  <SelectItem value="educational">Educational</SelectItem>
                  <SelectItem value="entertaining">Entertaining</SelectItem>
                  <SelectItem value="inspirational">Inspirational</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button 
            onClick={generateScript}
            disabled={isGenerating || !videoTitle.trim() || !videoLength || !tone}
            className="bg-teal hover:bg-teal/90 text-white"
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <FileText className="w-4 h-4 mr-2" />
                Generate Script (5 credits)
              </>
            )}
          </Button>

          {generatedScript && (
            <Card className="bg-gray-700 border-gray-600">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white flex items-center">
                    <Clock className="w-4 h-4 mr-2" />
                    Generated Script
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    {estimatedReadTime && (
                      <span className="text-sm text-gray-300">
                        Read time: {estimatedReadTime}
                      </span>
                    )}
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={copyToClipboard}
                      className="border-teal text-teal hover:bg-teal hover:text-white"
                    >
                      Copy Script
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={generatedScript}
                  onChange={(e) => setGeneratedScript(e.target.value)}
                  className="min-h-[400px] text-white bg-gray-800 border-gray-600 resize-none focus:ring-2 focus:ring-teal focus:border-teal"
                  placeholder="Your generated script will appear here..."
                />
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ScriptWriterDialog;
