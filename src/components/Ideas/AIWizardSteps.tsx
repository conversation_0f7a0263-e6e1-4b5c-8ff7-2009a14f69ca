
import React from 'react';
import { CheckCircle, Circle, Minus } from 'lucide-react';

const steps = [
  { id: 'starting-point', name: 'Starting Point', icon: Circle },
  { id: 'title-hook', name: 'Title & Hook', icon: Circle },
  { id: 'script', name: '<PERSON><PERSON><PERSON>', icon: Circle },
  { id: 'publishing', name: 'Publishing Essentials', icon: Circle }
];

interface AIWizardStepsProps {
  currentStep: number;
  completedSteps: number[];
  skippedSteps?: number[];
}

const AIWizardSteps: React.FC<AIWizardStepsProps> = ({ 
  currentStep, 
  completedSteps, 
  skippedSteps = [] 
}) => {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-white">AI Creation Wizard</h3>
        <div className="text-sm text-gray-400">
          Step {currentStep + 1} of {steps.length}
        </div>
      </div>
      
      <div className="flex flex-wrap gap-2">
        {steps.map((step, index) => {
          const isCompleted = completedSteps.includes(index);
          const isSkipped = skippedSteps.includes(index);
          const isCurrent = index === currentStep;
          
          let IconComponent = Circle;
          let statusClass = 'bg-gray-700 text-gray-400 border border-gray-600';
          
          if (isCompleted && !isSkipped) {
            IconComponent = CheckCircle;
            statusClass = 'bg-green-900/20 text-green-400 border border-green-500/20';
          } else if (isSkipped) {
            IconComponent = Minus;
            statusClass = 'bg-yellow-900/20 text-yellow-400 border border-yellow-500/20';
          } else if (isCurrent) {
            statusClass = 'bg-teal/20 text-teal border border-teal/30';
          }
          
          return (
            <div
              key={step.id}
              className={`flex items-center px-3 py-2 rounded-lg text-sm ${statusClass}`}
            >
              <IconComponent className="w-4 h-4 mr-2" />
              {step.name}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export { steps };
export default AIWizardSteps;
