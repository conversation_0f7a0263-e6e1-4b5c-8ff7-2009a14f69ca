
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ImageIcon } from 'lucide-react';
import { VideoIdea, ContentPillar } from './types';
import { useThumbnailGeneration } from '@/hooks/ideas/useThumbnailGeneration';
import { useThumbnailActions } from '@/hooks/ideas/useThumbnailActions';
import ThumbnailInputForm from './ThumbnailInputForm';
import ThumbnailResults from './ThumbnailResults';

interface ThumbnailGeneratorProps {
  ideas: VideoIdea[];
  pillars: ContentPillar[];
  onAddIdea: (ideas: Array<{title: string; description: string}>, pillarId: string) => void;
}

const ThumbnailGenerator: React.FC<ThumbnailGeneratorProps> = ({ ideas, pillars, onAddIdea }) => {
  const {
    videoTitle,
    setVideoTitle,
    category,
    setCategory,
    colorScheme,
    setColorScheme,
    thumbnails,
    isGenerating,
    copiedThumbnail,
    setCopiedThumbnail,
    generateThumbnails,
    resetGenerator
  } = useThumbnailGeneration();

  const { downloadThumbnail, saveToIdea, copyThumbnailUrl } = useThumbnailActions(
    videoTitle,
    pillars,
    onAddIdea,
    setCopiedThumbnail
  );

  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <ImageIcon className="w-5 h-5" />
          AI Thumbnail Generator
        </CardTitle>
        <CardDescription className="text-gray-300">
          Create eye-catching thumbnails in seconds
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <ThumbnailInputForm
          videoTitle={videoTitle}
          setVideoTitle={setVideoTitle}
          category={category}
          setCategory={setCategory}
          colorScheme={colorScheme}
          setColorScheme={setColorScheme}
          isGenerating={isGenerating}
          onGenerate={generateThumbnails}
        />

        <ThumbnailResults
          thumbnails={thumbnails}
          copiedThumbnail={copiedThumbnail}
          onReset={resetGenerator}
          onDownload={downloadThumbnail}
          onCopy={copyThumbnailUrl}
          onSaveToIdea={saveToIdea}
        />
      </CardContent>
    </Card>
  );
};

export default ThumbnailGenerator;
