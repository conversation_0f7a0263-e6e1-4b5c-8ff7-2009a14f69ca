
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Sparkles } from 'lucide-react';
import IdeaCardActions from './IdeaCardActions';
import { VideoIdea } from './types';

interface IdeaCardHeaderProps {
  idea: VideoIdea;
  pillarName?: string;
  onEdit: (idea: VideoIdea) => void;
  onDelete: (id: string, title: string) => void;
  onTitleUpdate?: (id: string, newTitle: string) => void;
  onShowOptimizeDialog: () => void;
  onShowScriptDialog: () => void;
  onShowDescriptionDialog: () => void;
  onShowDeleteDialog: () => void;
  isAIGenerated?: boolean;
}

const IdeaCardHeader: React.FC<IdeaCardHeaderProps> = ({
  idea,
  pillarName,
  onEdit,
  onDelete,
  onTitleUpdate,
  onShowOptimizeDialog,
  onShowScriptDialog,
  onShowDescriptionDialog,
  onShowDeleteDialog,
  isAIGenerated
}) => {
  const getPriorityColor = (priority: string | null) => {
    switch (priority) {
      case 'High': return 'bg-red-100 text-red-800 border-red-200';
      case 'Medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string | null) => {
    switch (status) {
              case 'idea': return 'bg-teal-100 text-teal-800 border-teal-200';
      case 'planned': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'filming': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'editing': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'published': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <CardHeader className="pb-4">
      <div className="flex items-start justify-between mb-3">
        <div className="flex flex-wrap gap-2">
          <Badge className={getStatusColor(idea.status)}>
            {idea.status || 'idea'}
          </Badge>
          <Badge className={getPriorityColor(idea.priority)}>
            {idea.priority || 'Medium'}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          {/* AI-Generated Badge - Aligned with action buttons */}
          {isAIGenerated && (
            <Badge variant="secondary" className="bg-yellow/20 text-yellow border-yellow/30 text-xs px-2 py-1">
              <Sparkles className="w-3 h-3 mr-1" />
              AI
            </Badge>
          )}
          
          <IdeaCardActions
            idea={idea}
            onEdit={onEdit}
            onShowOptimizeDialog={onShowOptimizeDialog}
            onShowScriptDialog={onShowScriptDialog}
            onShowDescriptionDialog={onShowDescriptionDialog}
            onShowDeleteDialog={onShowDeleteDialog}
          />
        </div>
      </div>
      
      <CardTitle className="text-white text-xl leading-tight mb-3">
        {idea.title}
      </CardTitle>
    </CardHeader>
  );
};

export default IdeaCardHeader;
