
import React from 'react';
import { CheckCircle, Circle, Minus } from 'lucide-react';

interface StepNavigationDotsProps {
  steps: Array<{ id: number; title: string; icon: string; description: string }>;
  currentStep: number;
  completedSteps: number[];
  skippedSteps: number[];
  onStepClick: (stepIndex: number) => void;
}

const StepNavigationDots: React.FC<StepNavigationDotsProps> = ({
  steps,
  currentStep,
  completedSteps,
  skippedSteps,
  onStepClick
}) => {
  return (
    <div className="flex justify-center space-x-4 mb-6">
      {steps.map((step, index) => {
        const isCompleted = completedSteps.includes(index);
        const isSkipped = skippedSteps.includes(index);
        const isCurrent = index === currentStep;
        const isClickable = index <= Math.max(...completedSteps, -1) + 1;
        
        let IconComponent = Circle;
        let statusClass = 'bg-gray-700 text-gray-400 border-gray-600';
        
        if (isCompleted && !isSkipped) {
          IconComponent = CheckCircle;
          statusClass = 'bg-green-900/20 text-green-400 border-green-500/20';
        } else if (isSkipped) {
          IconComponent = Minus;
          statusClass = 'bg-yellow-900/20 text-yellow-400 border-yellow-500/20';
        } else if (isCurrent) {
          statusClass = 'bg-teal/20 text-teal border-teal/30';
        }

        return (
          <button
            key={step.id}
            onClick={() => isClickable && onStepClick(index)}
            disabled={!isClickable}
            className={`flex flex-col items-center p-3 rounded-lg border transition-colors ${statusClass} ${
              isClickable ? 'cursor-pointer hover:opacity-80' : 'cursor-not-allowed opacity-50'
            }`}
          >
            <IconComponent className="w-6 h-6 mb-2" />
            <span className="text-xs font-medium">{step.title}</span>
          </button>
        );
      })}
    </div>
  );
};

export default StepNavigationDots;
