
import React from 'react';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LayoutGrid, Kanban } from 'lucide-react';

interface TabsNavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const TabsNavigation: React.FC<TabsNavigationProps> = ({ activeTab, setActiveTab }) => {
  return (
    <TabsList className="grid grid-cols-2 bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-t-lg rounded-b-none p-1">
      <TabsTrigger 
        value="all-ideas"
        className={`flex items-center space-x-2 ${activeTab === 'all-ideas' ? 'bg-gray-700 text-white' : 'text-gray-400'}`}
      >
        <LayoutGrid className="w-4 h-4" />
        <span>All Ideas</span>
      </TabsTrigger>
      
      <TabsTrigger 
        value="kanban"
        className={`flex items-center space-x-2 ${activeTab === 'kanban' ? 'bg-gray-700 text-white' : 'text-gray-400'}`}
      >
        <Kanban className="w-4 h-4" />
        <span>Kanban Board</span>
      </TabsTrigger>
    </TabsList>
  );
};

export default TabsNavigation;
