
export interface VideoIdea {
  id: string;
  title: string;
  description?: string;
  status: 'idea' | 'planned' | 'in_progress' | 'ready' | 'published';
  pillar_id?: string;
  scheduled_date?: string;
  published_at?: string;
  views?: number;
  likes?: number;
  thumbnail_url?: string;
  youtube_id?: string;
  calendar_notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface ContentPillar {
  id: string;
  name: string;
  color: string | null;
}

export interface NewIdea {
  title: string;
  description: string;
  pillar_id: string;
  priority: string;
  scheduled_date: string;
}
