
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, Sparkles, FileText, Hash, MessageSquare, Video, Tag, Info, Calendar } from 'lucide-react';
import { VideoIdea, ContentPillar } from './types';
import { toast } from 'sonner';

interface AIWizardDataModalProps {
  isOpen: boolean;
  onClose: () => void;
  idea: VideoIdea | null;
  pillarName?: string;
}

const AIWizardDataModal: React.FC<AIWizardDataModalProps> = ({
  isOpen,
  onClose,
  idea,
  pillarName
}) => {
  if (!idea) return null;

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Content copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy content');
    }
  };

  // Parse AI-generated description to extract structured data
  const parseAIDescription = (description: string) => {
    if (!description || !description.includes('🤖 AI-Generated Video Plan')) {
      return null;
    }

    const sections = description.split('\n\n');
    const data: any = {};

    sections.forEach(section => {
      if (section.includes('**Topic:**')) {
        data.topic = section.replace('**Topic:**', '').trim();
      } else if (section.includes('**Hook:**')) {
        data.hook = section.replace('**Hook:**', '').trim();
      } else if (section.includes('**Script Outline:**')) {
        data.script = section.replace('**Script Outline:**', '').trim();
      } else if (section.includes('**SEO Tags:**')) {
        data.seoTags = section.replace('**SEO Tags:**', '').trim();
      } else if (section.includes('**Hashtags:**')) {
        data.hashtags = section.replace('**Hashtags:**', '').trim();
      }
    });

    return data;
  };

  const aiData = parseAIDescription(idea.description || '');
  const isAIGenerated = !!aiData;

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not scheduled';
    return new Date(dateString).toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const getPriorityColor = (priority: string | null) => {
    switch (priority) {
      case 'High': return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'Medium': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'Low': return 'bg-green-500/20 text-green-400 border-green-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusColor = (status: string | null) => {
    switch (status) {
              case 'idea': return 'bg-teal-500/20 text-teal-400 border-teal-500/30';
      case 'planned': return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'filming': return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'editing': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'published': return 'bg-green-500/20 text-green-400 border-green-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  if (!isAIGenerated) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl bg-gray-800 border-gray-600">
          <DialogHeader>
            <DialogTitle className="text-white flex items-center">
              <Info className="w-5 h-5 mr-2 text-teal" />
              Video Idea Details
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* Title */}
            <Card className="bg-gray-700 border-gray-600">
              <CardHeader>
                <CardTitle className="text-white text-lg flex items-center justify-between">
                  Title
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(idea.title)}
                    className="border-gray-600 text-gray-300 hover:bg-gray-600"
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 text-lg font-medium">{idea.title}</p>
              </CardContent>
            </Card>

            {/* Metadata */}
            <div className="grid grid-cols-2 gap-4">
              <Card className="bg-gray-700 border-gray-600">
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <h4 className="text-white font-medium text-sm">Content Pillar</h4>
                    {pillarName ? (
                      <Badge className="bg-teal/20 text-teal border-teal/30">
                        {pillarName}
                      </Badge>
                    ) : (
                      <p className="text-gray-400 text-sm">Not assigned</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-700 border-gray-600">
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <h4 className="text-white font-medium text-sm">Status & Priority</h4>
                    <div className="flex gap-2">
                      <Badge className={getStatusColor(idea.status)}>
                        {idea.status || 'idea'}
                      </Badge>
                      <Badge className={getPriorityColor(idea.priority)}>
                        {idea.priority || 'Medium'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Scheduled Date */}
            {idea.scheduled_date && (
              <Card className="bg-gray-700 border-gray-600">
                <CardHeader>
                  <CardTitle className="text-white text-lg flex items-center">
                    <Calendar className="w-5 h-5 mr-2" />
                    Scheduled Date
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-300">{formatDate(idea.scheduled_date)}</p>
                </CardContent>
              </Card>
            )}

            {/* Description */}
            {idea.description && (
              <Card className="bg-gray-700 border-gray-600">
                <CardHeader>
                  <CardTitle className="text-white text-lg flex items-center justify-between">
                    Description
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(idea.description || '')}
                      className="border-gray-600 text-gray-300 hover:bg-gray-600"
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-300 whitespace-pre-wrap">{idea.description}</p>
                </CardContent>
              </Card>
            )}

            {/* Copy All Button */}
            <div className="pt-4 border-t border-gray-600">
              <Button
                onClick={() => {
                  const allContent = `
🎬 VIDEO IDEA

📝 Title: ${idea.title}

📊 Content Pillar: ${pillarName || 'Not assigned'}

📅 Status: ${idea.status || 'idea'}

⭐ Priority: ${idea.priority || 'Medium'}

📅 Scheduled: ${formatDate(idea.scheduled_date)}

📄 Description: ${idea.description || 'No description'}
                  `.trim();
                  copyToClipboard(allContent);
                }}
                className="w-full bg-teal hover:bg-teal/90 text-black"
              >
                <Copy className="w-4 h-4 mr-2" />
                Copy All Information
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-gray-800 border-gray-600">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <Sparkles className="w-5 h-5 mr-2 text-yellow" />
            AI-Generated Video Plan
            <Badge variant="secondary" className="ml-2 bg-yellow/20 text-yellow border-yellow/30">
              AI Wizard
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Video Title & Pillar */}
          <Card className="bg-gray-700 border-gray-600">
            <CardHeader>
              <CardTitle className="text-white text-lg">{idea.title}</CardTitle>
              {pillarName && (
                <Badge className="bg-teal/20 text-teal border-teal/30 w-fit">
                  {pillarName}
                </Badge>
              )}
            </CardHeader>
          </Card>

          <Tabs defaultValue="content" className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-gray-700">
              <TabsTrigger value="content" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
                <Video className="w-4 h-4 mr-2" />
                Content
              </TabsTrigger>
              <TabsTrigger value="script" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
                <FileText className="w-4 h-4 mr-2" />
                Script
              </TabsTrigger>
            </TabsList>

            <TabsContent value="content" className="space-y-4">
              {aiData.topic && (
                <Card className="bg-gray-700 border-gray-600">
                  <CardHeader>
                    <CardTitle className="text-white text-lg flex items-center justify-between">
                      Topic
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(aiData.topic)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-600"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-300">{aiData.topic}</p>
                  </CardContent>
                </Card>
              )}

              {aiData.hook && (
                <Card className="bg-gray-700 border-gray-600">
                  <CardHeader>
                    <CardTitle className="text-white text-lg flex items-center justify-between">
                      Hook
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(aiData.hook)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-600"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-300">{aiData.hook}</p>
                  </CardContent>
                </Card>
              )}

              {aiData.seoTags && (
                <Card className="bg-gray-700 border-gray-600">
                  <CardHeader>
                    <CardTitle className="text-white text-lg flex items-center justify-between">
                      <span className="flex items-center">
                        <Tag className="w-5 h-5 mr-2" />
                        SEO Tags
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(aiData.seoTags)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-600"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-300">{aiData.seoTags}</p>
                  </CardContent>
                </Card>
              )}

              {aiData.hashtags && (
                <Card className="bg-gray-700 border-gray-600">
                  <CardHeader>
                    <CardTitle className="text-white text-lg flex items-center justify-between">
                      <span className="flex items-center">
                        <Hash className="w-5 h-5 mr-2" />
                        Hashtags
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(aiData.hashtags)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-600"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-300">{aiData.hashtags}</p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="script" className="space-y-4">
              {aiData.script && (
                <Card className="bg-gray-700 border-gray-600">
                  <CardHeader>
                    <CardTitle className="text-white text-lg flex items-center justify-between">
                      Full Script
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(aiData.script)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-600"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className="text-gray-300 whitespace-pre-wrap text-sm">{aiData.script}</pre>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AIWizardDataModal;
