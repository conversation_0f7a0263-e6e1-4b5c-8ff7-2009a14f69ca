
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { Sparkles, Loader2, Plus } from 'lucide-react';
import { ContentPillar } from './types';

interface GeneratedIdea {
  title: string;
  description: string;
}

interface GenerateIdeasDialogProps {
  pillars: ContentPillar[];
  onGenerateIdeas: (ideas: GeneratedIdea[], pillarId: string) => void;
}

const GenerateIdeasDialog: React.FC<GenerateIdeasDialogProps> = ({
  pillars,
  onGenerateIdeas
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedPillarId, setSelectedPillarId] = useState('');
  const [generatedIdeas, setGeneratedIdeas] = useState<GeneratedIdea[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const generateIdeas = async () => {
    if (!selectedPillarId) {
      toast.error('Please select a content pillar');
      return;
    }

    setIsLoading(true);
    try {
      const selectedPillar = pillars.find(p => p.id === selectedPillarId);
      
      const { data, error } = await supabase.functions.invoke('generate-ideas', {
        body: { 
          pillarName: selectedPillar?.name,
          pillarDescription: '' // Using empty string since description is not available on ContentPillar type
        }
      });

      if (error) throw error;

      setGeneratedIdeas(data.ideas || []);
    } catch (error) {
      console.error('Error generating ideas:', error);
      toast.error('Failed to generate ideas');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddAllIdeas = () => {
    if (generatedIdeas.length > 0 && selectedPillarId) {
      onGenerateIdeas(generatedIdeas, selectedPillarId);
      handleClose();
      toast.success(`Added ${generatedIdeas.length} ideas to your bank!`);
    }
  };

  const handleClose = () => {
    setGeneratedIdeas([]);
    setSelectedPillarId('');
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="border-gray-600 text-gray-300">
          <Sparkles className="w-4 h-4 mr-2" />
          AI Generate Ideas
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <Sparkles className="w-5 h-5 mr-2 text-yellow" />
            AI Generate Ideas
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-white">Select Content Pillar</label>
            <Select value={selectedPillarId} onValueChange={setSelectedPillarId}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Choose a content pillar" />
              </SelectTrigger>
              <SelectContent>
                {pillars.map((pillar) => (
                  <SelectItem key={pillar.id} value={pillar.id}>
                    {pillar.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {!isLoading && generatedIdeas.length === 0 && (
            <div className="text-center py-8">
              <Button
                onClick={generateIdeas}
                disabled={!selectedPillarId}
                className="bg-teal hover:bg-teal/90 text-white"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Generate 5 Ideas
              </Button>
            </div>
          )}

          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin text-teal" />
              <span className="ml-2 text-gray-300">Generating creative ideas...</span>
            </div>
          )}

          {generatedIdeas.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-white">Generated Ideas:</label>
                <Button
                  onClick={handleAddAllIdeas}
                  className="bg-teal hover:bg-teal/90 text-white"
                  size="sm"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add All Ideas
                </Button>
              </div>
              
              {generatedIdeas.map((idea, index) => (
                <div key={index} className="p-4 bg-gray-700 rounded border border-gray-600">
                  <h4 className="text-white font-medium mb-2">{idea.title}</h4>
                  <p className="text-gray-300 text-sm">{idea.description}</p>
                </div>
              ))}
            </div>
          )}

          <div className="flex justify-between pt-4">
            {generatedIdeas.length > 0 && (
              <Button
                variant="outline"
                onClick={generateIdeas}
                disabled={isLoading}
                className="border-gray-600 text-gray-300"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Generate More
              </Button>
            )}
            <Button variant="ghost" onClick={handleClose} className="text-gray-300 ml-auto">
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default GenerateIdeasDialog;
