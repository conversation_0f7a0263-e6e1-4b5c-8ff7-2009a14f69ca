
import { useEffect } from 'react';

interface UseTitleAndHookEffectsProps {
  topic: string;
  title: string;
  parsedTitlesLength: number;
  hooksLength: number;
  loading: boolean;
  onGenerateTitles: () => void;
  onGenerateHooks: () => void;
}

export const useTitleAndHookEffects = ({
  topic,
  title,
  parsedTitlesLength,
  hooksLength,
  loading,
  onGenerateTitles,
  onGenerateHooks
}: UseTitleAndHookEffectsProps) => {
  // Auto-generate titles when we have a topic but no titles
  useEffect(() => {
    console.log('Title generation effect triggered:', { topic, parsedTitlesLength, loading });
    if (topic && parsedTitlesLength === 0 && !loading) {
      console.log('Auto-generating titles for topic:', topic);
      onGenerateTitles();
    }
  }, [topic, parsedTitlesLength, loading, onGenerateTitles]);

  // Auto-generate hooks when we have a title but no hooks
  useEffect(() => {
    console.log('Hook generation effect triggered:', { title, hooksLength, loading });
    if (title && hooksLength === 0 && !loading) {
      console.log('Auto-generating hooks for title:', title);
      onGenerateHooks();
    }
  }, [title, hooksLength, loading, onGenerateHooks]);
};
