
export const parseGeneratedTitles = (titles: string[]): string[] => {
  if (!titles || titles.length === 0) return [];
  
  // Handle array of titles
  if (titles.length > 1) {
    return titles.filter(title => title && title.trim().length > 0);
  }
  
  // Handle single string that might contain multiple titles
  if (titles.length === 1) {
    const titleText = titles[0];
    
    // If it's already a reasonable single title, return it
    if (titleText.length < 200 && !titleText.includes('\n') && !titleText.match(/^\d+\./)) {
      return [titleText];
    }
    
    // Try to split by various patterns
    let splitTitles = [];
    
    // Split by numbered lists (1., 2., etc.)
    if (titleText.match(/^\d+\./m)) {
      splitTitles = titleText
        .split(/\d+\.\s*/)
        .filter(title => title.trim().length > 0);
    }
    // Split by line breaks
    else if (titleText.includes('\n')) {
      splitTitles = titleText
        .split('\n')
        .filter(title => title.trim().length > 0);
    }
    // Split by double quotes
    else if (titleText.includes('"')) {
      splitTitles = titleText
        .split('"')
        .filter((title, index) => index % 2 === 1 && title.trim().length > 0);
    }
    
    // Clean up the titles
    if (splitTitles.length > 0) {
      return splitTitles
        .map(title => title.replace(/^\*\*|\*\*$/g, '').trim()) // Remove markdown bold
        .filter(title => title.length > 10); // Filter out very short fragments
    }
    
    // If no splitting worked, return the original
    return [titleText];
  }
  
  return titles;
};
