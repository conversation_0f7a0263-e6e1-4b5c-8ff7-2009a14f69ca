
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface TopicContextProps {
  topic: string;
}

const TopicContext: React.FC<TopicContextProps> = ({ topic }) => {
  if (!topic) return null;

  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardContent className="p-4">
        <p className="text-gray-300 text-sm">
          <span className="text-white font-medium">Topic:</span> {topic}
        </p>
      </CardContent>
    </Card>
  );
};

export default TopicContext;
