
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Zap, RefreshCw } from 'lucide-react';

interface HooksSectionProps {
  hooks: string[];
  selectedHook: string;
  selectedTitle: string;
  loading: boolean;
  onHookSelect: (hook: string) => void;
  onRegenerateHooks: () => void;
}

const HooksSection: React.FC<HooksSectionProps> = ({
  hooks,
  selectedHook,
  selectedTitle,
  loading,
  onHookSelect,
  onRegenerateHooks
}) => {
  console.log('HooksSection props:', { hooks, selectedHook, selectedTitle, loading });

  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white flex items-center justify-between">
          <span className="flex items-center">
            <Zap className="w-5 h-5 mr-2 text-orange" />
            Video Hooks
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={onRegenerateHooks}
            disabled={loading || !selectedTitle}
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            <RefreshCw className="w-4 h-4 mr-1" />
            Regenerate
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {!selectedTitle && (
          <div className="text-center py-8">
            <p className="text-gray-400">Select a title first to generate hooks</p>
          </div>
        )}

        {loading && selectedTitle && hooks.length === 0 && (
          <div className="flex items-center justify-center py-8">
            <div className="w-6 h-6 border-4 border-orange border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-3 text-gray-300">Generating hooks...</span>
          </div>
        )}

        {hooks.length > 0 && hooks.map((hook, index) => (
          <div key={index} className="space-y-2">
            <Card 
              className={`cursor-pointer transition-all duration-200 ${
                selectedHook === hook 
                  ? 'bg-orange/20 border-orange/50 shadow-lg' 
                  : 'bg-gray-700 border-gray-600 hover:bg-gray-600 hover:border-gray-500'
              }`}
              onClick={() => {
                console.log('Hook clicked:', hook);
                onHookSelect(hook);
              }}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between gap-3">
                  <p className="text-white text-sm leading-relaxed flex-1">{hook}</p>
                  <div className="flex items-center gap-2 flex-shrink-0">
                    {selectedHook === hook && (
                      <Badge className="bg-orange text-black text-xs">Selected</Badge>
                    )}
                    <Button
                      size="sm"
                      variant={selectedHook === hook ? "default" : "outline"}
                      onClick={(e) => {
                        e.stopPropagation();
                        console.log('Hook select button clicked:', hook);
                        onHookSelect(hook);
                      }}
                      className={selectedHook === hook 
                        ? "bg-orange hover:bg-orange/90 text-black" 
                        : "border-gray-500 text-gray-300 hover:bg-gray-600"
                      }
                    >
                      {selectedHook === hook ? 'Selected' : 'Select'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        ))}

        {selectedTitle && hooks.length === 0 && !loading && (
          <div className="text-center py-8">
            <p className="text-gray-400">Click "Regenerate" to generate hook options</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default HooksSection;
