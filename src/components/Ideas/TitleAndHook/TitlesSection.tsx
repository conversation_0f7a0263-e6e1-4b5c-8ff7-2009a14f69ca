
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Target, RefreshCw } from 'lucide-react';

interface TitlesSectionProps {
  titles: string[];
  selectedTitle: string;
  loading: boolean;
  onTitleSelect: (title: string) => void;
  onRegenerateTitles: () => void;
}

const TitlesSection: React.FC<TitlesSectionProps> = ({
  titles,
  selectedTitle,
  loading,
  onTitleSelect,
  onRegenerateTitles
}) => {
  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white flex items-center justify-between">
          <span className="flex items-center">
            <Target className="w-5 h-5 mr-2 text-teal" />
            Video Titles
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={onRegenerateTitles}
            disabled={loading}
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            <RefreshCw className="w-4 h-4 mr-1" />
            Regenerate
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {loading && titles.length === 0 && (
          <div className="flex items-center justify-center py-8">
            <div className="w-6 h-6 border-4 border-teal border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-3 text-gray-300">Generating titles...</span>
          </div>
        )}

        {titles.map((title, index) => (
          <Card 
            key={index} 
            className={`cursor-pointer transition-colors ${
              selectedTitle === title 
                ? 'bg-teal/20 border-teal/30' 
                : 'bg-gray-700 border-gray-600 hover:bg-gray-600'
            }`}
            onClick={() => onTitleSelect(title)}
          >
            <CardContent className="p-3">
              <div className="flex items-start justify-between">
                <p className="text-white text-sm font-medium flex-1">{title}</p>
                {selectedTitle === title && (
                  <Badge className="bg-teal text-black ml-2">Selected</Badge>
                )}
              </div>
            </CardContent>
          </Card>
        ))}

        {titles.length === 0 && !loading && (
          <div className="text-center py-8">
            <p className="text-gray-400">Click "Regenerate" to generate title options</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TitlesSection;
