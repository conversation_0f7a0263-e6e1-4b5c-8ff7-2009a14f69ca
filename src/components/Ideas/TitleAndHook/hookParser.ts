
export const parseGeneratedHooks = (hooks: string[]): string[] => {
  console.log('Parsing hooks:', hooks);
  
  if (!hooks || hooks.length === 0) {
    return [];
  }

  const parsedHooks: string[] = [];

  hooks.forEach((hook) => {
    if (typeof hook !== 'string') {
      return;
    }

    // Look for hook content within quotes in the 🎤 **Host:** sections
    const hostMatches = hook.match(/🎤\s*\*\*Host:\*\*\s*"([^"]+)"/g);
    
    if (hostMatches && hostMatches.length > 0) {
      // Extract the quoted text from each match
      hostMatches.forEach(match => {
        const quotedText = match.match(/"([^"]+)"/);
        if (quotedText && quotedText[1]) {
          parsedHooks.push(quotedText[1].trim());
        }
      });
    } else {
      // Fallback: try to extract any quoted text
      const fallbackMatches = hook.match(/"([^"]+)"/g);
      if (fallbackMatches && fallbackMatches.length > 0) {
        fallbackMatches.forEach(match => {
          const cleanMatch = match.replace(/"/g, '').trim();
          if (cleanMatch.length > 10) { // Only include substantial hooks
            parsedHooks.push(cleanMatch);
          }
        });
      } else {
        // Last resort: clean up the hook text and use as-is if it's reasonable length
        const cleanedHook = hook
          .replace(/\*\*Hook Script \d+:\*\*/g, '')
          .replace(/\[.*?\]/g, '')
          .replace(/🎤\s*\*\*Host:\*\*/g, '')
          .replace(/---/g, '')
          .replace(/\*\*/g, '')
          .trim();
        
        if (cleanedHook.length > 20 && cleanedHook.length < 200) {
          parsedHooks.push(cleanedHook);
        }
      }
    }
  });

  console.log('Parsed hooks result:', parsedHooks);
  return parsedHooks;
};
