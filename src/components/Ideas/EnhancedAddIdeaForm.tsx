
import React, { useEffect } from 'react';
import TitleField from './AddIdeaForm/TitleField';
import PillarSelect from './AddIdeaForm/PillarSelect';
import DescriptionField from './AddIdeaForm/DescriptionField';
import PriorityDateFields from './AddIdeaForm/PriorityDateFields';
import { Button } from '@/components/ui/button';
import { ContentPillar, NewIdea } from './types';

interface EnhancedAddIdeaFormProps {
  newIdea: NewIdea;
  setNewIdea: (idea: NewIdea) => void;
  pillars: ContentPillar[];
  onSubmit: () => void;
}

const EnhancedAddIdeaForm: React.FC<EnhancedAddIdeaFormProps> = ({
  newIdea,
  setNewIdea,
  pillars,
  onSubmit
}) => {
  // Debug logging
  console.log('EnhancedAddIdeaForm - pillars:', pillars);
  console.log('EnhancedAddIdeaForm - newIdea:', newIdea);
  
  // Set default pillar if available and none selected
  useEffect(() => {
    if (Array.isArray(pillars) && pillars.length > 0 && !newIdea.pillar_id) {
      setNewIdea(prev => ({ ...prev, pillar_id: pillars[0].id }));
    }
  }, [pillars, newIdea.pillar_id, setNewIdea]);
  
  return (
    <div className="space-y-4">
      <TitleField
        newIdea={newIdea}
        setNewIdea={setNewIdea}
        pillars={pillars}
      />

      <PillarSelect
        newIdea={newIdea}
        setNewIdea={setNewIdea}
        pillars={pillars}
      />

      <DescriptionField
        newIdea={newIdea}
        setNewIdea={setNewIdea}
        pillars={pillars}
      />

      <PriorityDateFields
        newIdea={newIdea}
        setNewIdea={setNewIdea}
      />

      <Button 
        onClick={onSubmit} 
        className="w-full bg-teal hover:bg-teal/90 text-white"
        disabled={!newIdea.title || !newIdea.pillar_id}
      >
        Create Idea
      </Button>
    </div>
  );
};

export default EnhancedAddIdeaForm;
