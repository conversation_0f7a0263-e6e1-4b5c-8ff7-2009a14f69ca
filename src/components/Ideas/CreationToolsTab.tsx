
import React from 'react';
import { Wand2 } from 'lucide-react';
import { VideoIdea, ContentPillar } from './types';
import ScriptWriterDialog from './ScriptWriterDialog';
import OptimizeTitleDialog from './OptimizeTitleDialog';
import DescriptionGeneratorDialog from './DescriptionGeneratorDialog';
import EnhancedAIWizard from './EnhancedAIWizard';
import CreationToolsGrid from './CreationTools/CreationToolsGrid';
import QuickStats from './CreationTools/QuickStats';
import { useCreationToolsState } from '@/hooks/ideas/useCreationToolsState';

interface CreationToolsTabProps {
  ideas: VideoIdea[];
  pillars: ContentPillar[];
  onAddIdea: (idea: any) => void;
}

const CreationToolsTab: React.FC<CreationToolsTabProps> = ({ ideas, pillars, onAddIdea }) => {
  const {
    showScriptWriter,
    setShowScriptWriter,
    showTitleOptimizer,
    setShowTitleOptimizer,
    showDescriptionGenerator,
    setShowDescriptionGenerator,
    showScriptGenerator,
    setShowScriptGenerator
  } = useCreationToolsState();

  return (
    <div className="space-y-8">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-white flex items-center justify-center">
          <Wand2 className="w-6 h-6 mr-3 text-orange" />
          Creator Studio
        </h2>
        <p className="text-gray-300">
          AI-powered content creation tools
        </p>
      </div>

      <CreationToolsGrid
        onScriptWriter={() => setShowScriptWriter(true)}
        onTitleOptimizer={() => setShowTitleOptimizer(true)}
        onDescriptionGenerator={() => setShowDescriptionGenerator(true)}
        onScriptGenerator={() => setShowScriptGenerator(true)}
      />

      <QuickStats ideas={ideas} pillars={pillars} />

      {/* Core Dialogs */}
      <EnhancedAIWizard
        isOpen={showScriptGenerator}
        onClose={() => setShowScriptGenerator(false)}
        pillars={pillars}
        onIdeaGenerated={onAddIdea}
        title="Script Generator"
      />
      
      <ScriptWriterDialog
        isOpen={showScriptWriter}
        onClose={() => setShowScriptWriter(false)}
        ideaTitle=""
        pillarName=""
      />

      <OptimizeTitleDialog
        isOpen={showTitleOptimizer}
        onClose={() => setShowTitleOptimizer(false)}
      />
      
      <DescriptionGeneratorDialog
        isOpen={showDescriptionGenerator}
        onClose={() => setShowDescriptionGenerator(false)}
      />
    </div>
  );
};

export default CreationToolsTab;
