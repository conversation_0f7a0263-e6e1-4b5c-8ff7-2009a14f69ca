
import React from 'react';
import { Edit2, <PERSON>rk<PERSON>, MoreVertical, FileText, List, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { VideoIdea } from './types';

interface IdeaCardActionsProps {
  idea: VideoIdea;
  onEdit: (idea: VideoIdea) => void;
  onShowOptimizeDialog: () => void;
  onShowScriptDialog: () => void;
  onShowDescriptionDialog: () => void;
  onShowDeleteDialog: () => void;
}

const IdeaCardActions: React.FC<IdeaCardActionsProps> = ({
  idea,
  onEdit,
  onShowOptimizeDialog,
  onShowScriptDialog,
  onShowDescriptionDialog,
  onShowDeleteDialog
}) => {
  return (
    <div className="flex items-center space-x-2 ml-4">
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={onShowOptimizeDialog}
            className="text-yellow p-2"
          >
            <Sparkles className="w-4 h-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Optimize title with AI</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(idea)}
            className="text-gray-400 p-2"
          >
            <Edit2 className="w-4 h-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Edit idea</p>
        </TooltipContent>
      </Tooltip>

      <DropdownMenu>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-400 p-2"
              >
                <MoreVertical className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>More actions</p>
          </TooltipContent>
        </Tooltip>
        <DropdownMenuContent align="end" className="w-48 bg-gray-700 border-gray-600">
          <DropdownMenuItem 
            onClick={onShowScriptDialog}
            className="text-white cursor-pointer"
          >
            <FileText className="w-4 h-4 mr-2" />
            Generate Script (5 credits)
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={onShowDescriptionDialog}
            className="text-white cursor-pointer"
          >
            <List className="w-4 h-4 mr-2" />
            Generate Description (2 credits)
          </DropdownMenuItem>
          <DropdownMenuSeparator className="bg-gray-600" />
          <DropdownMenuItem 
            onClick={onShowDeleteDialog}
            className="text-red-400 cursor-pointer"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Delete Idea
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default IdeaCardActions;
