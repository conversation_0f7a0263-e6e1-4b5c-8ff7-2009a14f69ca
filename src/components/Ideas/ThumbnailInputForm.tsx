
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ThumbnailInputFormProps {
  videoTitle: string;
  setVideoTitle: (title: string) => void;
  category: string;
  setCategory: (category: string) => void;
  colorScheme: string;
  setColorScheme: (scheme: string) => void;
  isGenerating: boolean;
  onGenerate: () => void;
}

const ThumbnailInputForm: React.FC<ThumbnailInputFormProps> = ({
  videoTitle,
  setVideoTitle,
  category,
  setCategory,
  colorScheme,
  setColorScheme,
  isGenerating,
  onGenerate
}) => {
  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Video Title
        </label>
        <Input
          placeholder="Enter your video title"
          value={videoTitle}
          onChange={(e) => setVideoTitle(e.target.value)}
          className="bg-gray-700 border-gray-600 text-white placeholder-gray-400"
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Category
          </label>
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent className="bg-gray-700 border-gray-600">
              <SelectItem value="tech-review">Tech Review</SelectItem>
              <SelectItem value="gaming">Gaming</SelectItem>
              <SelectItem value="vlog">Vlog</SelectItem>
              <SelectItem value="tutorial">Tutorial</SelectItem>
              <SelectItem value="entertainment">Entertainment</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Color Scheme
          </label>
          <Select value={colorScheme} onValueChange={setColorScheme}>
            <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
              <SelectValue placeholder="Select colors" />
            </SelectTrigger>
            <SelectContent className="bg-gray-700 border-gray-600">
              <SelectItem value="brand">Brand Colors</SelectItem>
              <SelectItem value="youtube-red">YouTube Red</SelectItem>
              <SelectItem value="dark-mode">Dark Mode</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Button
        onClick={onGenerate}
        disabled={!videoTitle.trim() || !category || !colorScheme || isGenerating}
        className="w-full bg-teal hover:bg-teal/90"
      >
        {isGenerating ? 'Generating Thumbnails...' : 'Generate Thumbnails'}
      </Button>
    </div>
  );
};

export default ThumbnailInputForm;
