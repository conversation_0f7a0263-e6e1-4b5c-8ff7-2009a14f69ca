
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { VideoIdea } from './types';
import { ContentPillar } from '@/types/pillar';
import { Loader2 } from 'lucide-react';

interface EditIdeaDialogProps {
  editingIdea: VideoIdea | null;
  setEditingIdea: (idea: VideoIdea | null) => void;
  updateIdea: (id: string, updates: Partial<VideoIdea>) => Promise<void>;
  pillars: ContentPillar[];
}

const EditIdeaDialog: React.FC<EditIdeaDialogProps> = ({
  editingIdea,
  setEditingIdea,
  updateIdea,
  pillars
}) => {
  const [isUpdating, setIsUpdating] = useState(false);

  if (!editingIdea) return null;

  const handleUpdate = async () => {
    if (!editingIdea) return;
    
    setIsUpdating(true);
    try {
      await updateIdea(editingIdea.id, editingIdea);
      // Dialog will be closed by the updateIdea function which calls setEditingIdea(null)
    } catch (error) {
      console.error('Error updating idea:', error);
      setIsUpdating(false);
    }
  };

  return (
    <Dialog open={!!editingIdea} onOpenChange={(open) => !open && setEditingIdea(null)}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-white">Edit Video Idea</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-white">Video Title</label>
            <Input
              value={editingIdea.title}
              onChange={(e) => setEditingIdea({ ...editingIdea, title: e.target.value })}
              className="bg-gray-700 border-gray-600 text-white"
            />
          </div>
          <div>
            <label className="text-sm font-medium text-white">Description</label>
            <Textarea
              value={editingIdea.description || ''}
              onChange={(e) => setEditingIdea({ ...editingIdea, description: e.target.value })}
              className="bg-gray-700 border-gray-600 text-white"
              rows={4}
            />
          </div>
          <div>
            <label className="text-sm font-medium text-white">Content Pillar</label>
            <Select 
              value={editingIdea.pillar_id || ''} 
              onValueChange={(value) => setEditingIdea({ ...editingIdea, pillar_id: value })}
            >
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Select a pillar" />
              </SelectTrigger>
              <SelectContent>
                {pillars.map((pillar) => (
                  <SelectItem key={pillar.id} value={pillar.id}>
                    {pillar.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="text-sm font-medium text-white">Priority</label>
            <Select 
              value={editingIdea.priority || 'Medium'} 
              onValueChange={(value) => setEditingIdea({ ...editingIdea, priority: value })}
            >
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Low">Low</SelectItem>
                <SelectItem value="Medium">Medium</SelectItem>
                <SelectItem value="High">High</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="text-sm font-medium text-white">Status</label>
            <Select 
              value={editingIdea.status || 'idea'} 
              onValueChange={(value) => setEditingIdea({ ...editingIdea, status: value })}
            >
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="idea">Idea</SelectItem>
                <SelectItem value="planned">Planned</SelectItem>
                <SelectItem value="filming">Filming</SelectItem>
                <SelectItem value="editing">Editing</SelectItem>
                <SelectItem value="published">Published</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="text-sm font-medium text-white">Target Date</label>
            <Input
              type="date"
              value={editingIdea.scheduled_date || ''}
              onChange={(e) => setEditingIdea({ ...editingIdea, scheduled_date: e.target.value })}
              className="bg-gray-700 border-gray-600 text-white"
            />
          </div>
          <Button 
            onClick={handleUpdate} 
            className="w-full bg-teal hover:bg-teal/90 text-white"
            disabled={isUpdating}
          >
            {isUpdating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Updating...
              </>
            ) : (
              'Update Idea'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditIdeaDialog;
