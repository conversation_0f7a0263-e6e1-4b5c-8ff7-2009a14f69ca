
import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Lightbulb, Target, Edit, Hash } from 'lucide-react';
import AICreditsDisplay from './AICreditsDisplay';

interface WizardHeaderProps {
  currentStep: number;
  steps: Array<{ id: number; title: string; icon: string; description: string }>;
  progress: number;
}

const iconMap = {
  'Lightbulb': Lightbulb,
  'Target': Target,
  'Edit': Edit,
  'Hash': Hash
};

const WizardHeader: React.FC<WizardHeaderProps> = ({ currentStep, steps, progress }) => {
  const currentStepData = steps[currentStep];
  const IconComponent = iconMap[currentStepData?.icon as keyof typeof iconMap] || Lightbulb;
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-white flex items-center">
            <IconComponent className="w-5 h-5 mr-2 text-teal" />
            {currentStepData?.title}
          </h3>
          <p className="text-gray-400 text-sm">{currentStepData?.description}</p>
        </div>
        <AICreditsDisplay />
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-gray-300">Progress</span>
          <span className="text-gray-300">{Math.round(progress)}%</span>
        </div>
        <Progress value={progress} className="w-full" />
      </div>
    </div>
  );
};

export default WizardHeader;
