
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { <PERSON>rkles, Edit, Clock, RefreshCw, Copy, Expand } from 'lucide-react';
import { toast } from 'sonner';

interface EnhancedScriptStepProps {
  formData: any;
  setFormData: (data: any) => void;
  loading: boolean;
  onGenerateScript: (length: string) => void;
  generatedScripts: string[];
  onSkipStep: () => void;
  onContinue: () => void;
}

const EnhancedScriptStep: React.FC<EnhancedScriptStepProps> = ({
  formData,
  setFormData,
  loading,
  onGenerateScript,
  generatedScripts,
  onSkipStep,
  onContinue
}) => {
  const [selectedLength, setSelectedLength] = useState('medium');
  const [selectedScript, setSelectedScript] = useState(formData.script || '');
  const [customScript, setCustomScript] = useState('');
  const [expandedScript, setExpandedScript] = useState<number | null>(null);

  const videoLengths = [
    { value: 'short', label: '60 seconds', description: 'Quick, punchy content' },
    { value: 'medium', label: '5-10 minutes', description: 'Standard tutorial length' },
    { value: 'long', label: '10-20 minutes', description: 'In-depth explanation' }
  ];

  const handleGenerateScript = () => {
    console.log('Generating script with length:', selectedLength);
    onGenerateScript(selectedLength);
  };

  const handleScriptSelect = (script: string) => {
    setSelectedScript(script);
    setFormData({ ...formData, script });
  };

  const handleCustomScriptChange = (script: string) => {
    setCustomScript(script);
    setSelectedScript(script);
    setFormData({ ...formData, script });
  };

  const handleContinue = () => {
    if (selectedScript || customScript) {
      setFormData({ 
        ...formData, 
        script: selectedScript || customScript 
      });
      onContinue();
    }
  };

  const copyToClipboard = async (script: string) => {
    try {
      await navigator.clipboard.writeText(script);
      toast.success('Script copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy script');
    }
  };

  const getScriptPreview = (script: string, maxLength: number = 200) => {
    return script.length > maxLength ? script.substring(0, maxLength) + '...' : script;
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-white flex items-center justify-center">
          <Edit className="w-5 h-5 mr-2 text-teal" />
          Script Creation
        </h3>
        <p className="text-gray-300">Generate a complete, detailed script for your video</p>
      </div>

      {/* Context Cards */}
      <div className="grid md:grid-cols-2 gap-4">
        {formData.title && (
          <Card className="bg-gray-800 border-gray-600">
            <CardContent className="p-4">
              <p className="text-gray-300 text-sm">
                <span className="text-white font-medium">Title:</span> {formData.title}
              </p>
            </CardContent>
          </Card>
        )}
        {formData.hook && (
          <Card className="bg-gray-800 border-gray-600">
            <CardContent className="p-4">
              <p className="text-gray-300 text-sm">
                <span className="text-white font-medium">Hook:</span> {formData.hook.substring(0, 100)}...
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Video Length Selection */}
      <Card className="bg-gray-800 border-gray-600">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Clock className="w-5 h-5 mr-2 text-orange" />
            Target Video Length
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Select value={selectedLength} onValueChange={setSelectedLength}>
            <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
              <SelectValue placeholder="Choose video length" />
            </SelectTrigger>
            <SelectContent className="bg-gray-700 border-gray-600">
              {videoLengths.map(length => (
                <SelectItem key={length.value} value={length.value}>
                  <div className="flex flex-col">
                    <span>{length.label}</span>
                    <span className="text-xs text-gray-400">{length.description}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Script Generation */}
      <Card className="bg-gray-800 border-gray-600">
        <CardHeader>
          <CardTitle className="text-white flex items-center justify-between">
            <span className="flex items-center">
              <Edit className="w-5 h-5 mr-2 text-teal" />
              Generated Scripts
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleGenerateScript}
              disabled={loading}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              Regenerate
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {!generatedScripts.length && !loading && (
            <Button 
              onClick={handleGenerateScript} 
              className="bg-teal hover:bg-teal/90 text-white w-full"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Generate Complete Script (5 credits)
            </Button>
          )}

          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="w-6 h-6 border-4 border-teal border-t-transparent rounded-full animate-spin"></div>
              <span className="ml-3 text-gray-300">Generating complete script...</span>
            </div>
          )}

          {generatedScripts.map((script, index) => (
            <Card 
              key={index} 
              className={`transition-colors ${
                selectedScript === script 
                  ? 'bg-teal/20 border-teal/30' 
                  : 'bg-gray-700 border-gray-600 hover:bg-gray-600'
              }`}
            >
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {expandedScript === index ? (
                        <pre className="text-white text-sm whitespace-pre-wrap font-mono bg-gray-800 p-3 rounded max-h-96 overflow-y-auto">
                          {script}
                        </pre>
                      ) : (
                        <div 
                          className="text-white text-sm cursor-pointer"
                          onClick={() => handleScriptSelect(script)}
                        >
                          <pre className="whitespace-pre-wrap font-mono">
                            {getScriptPreview(script, 300)}
                          </pre>
                        </div>
                      )}
                    </div>
                    {selectedScript === script && (
                      <Badge className="bg-teal text-black ml-4">Selected</Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2 pt-2 border-t border-gray-600">
                    <Button
                      size="sm"
                      variant={selectedScript === script ? "default" : "outline"}
                      onClick={() => handleScriptSelect(script)}
                      className={selectedScript === script 
                        ? "bg-teal hover:bg-teal/90 text-black" 
                        : "border-gray-500 text-gray-300 hover:bg-gray-600"
                      }
                    >
                      {selectedScript === script ? 'Selected' : 'Select'}
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setExpandedScript(expandedScript === index ? null : index)}
                      className="border-gray-500 text-gray-300 hover:bg-gray-600"
                    >
                      <Expand className="w-4 h-4 mr-1" />
                      {expandedScript === index ? 'Collapse' : 'Expand'}
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(script)}
                      className="border-gray-500 text-gray-300 hover:bg-gray-600"
                    >
                      <Copy className="w-4 h-4 mr-1" />
                      Copy
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </CardContent>
      </Card>

      {/* Custom Script Input */}
      <Card className="bg-gray-800 border-gray-600">
        <CardHeader>
          <CardTitle className="text-white">Or Write Your Own Script</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Write your own complete script or modify the generated one..."
            value={customScript}
            onChange={(e) => handleCustomScriptChange(e.target.value)}
            className="bg-gray-700 border-gray-600 text-white min-h-[300px] font-mono"
          />
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={onSkipStep}
          className="border-gray-600 text-gray-300 hover:bg-gray-700"
        >
          Skip this step
        </Button>

        <Button
          onClick={handleContinue}
          disabled={!selectedScript && !customScript}
          className="bg-teal hover:bg-teal/90 text-white"
        >
          Continue to SEO & Tags
        </Button>
      </div>
    </div>
  );
};

export default EnhancedScriptStep;
