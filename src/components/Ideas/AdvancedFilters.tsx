
import React, { useState } from 'react';
import { Search, Filter, X, Calendar, Clock, Users, Tag } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { ContentPillar } from './types';

interface AdvancedFiltersProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterPillar: string;
  setFilterPillar: (pillar: string) => void;
  filterPriority: string;
  setFilterPriority: (priority: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  pillars: ContentPillar[];
  activeFilters: string[];
  onClearFilters: () => void;
}

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  filterPillar,
  setFilterPillar,
  filterPriority,
  setFilterPriority,
  filterStatus,
  setFilterStatus,
  pillars,
  activeFilters,
  onClearFilters
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [audienceFilter, setAudienceFilter] = useState('all');
  const [durationFilter, setDurationFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');

  const audiences = ['Beginner', 'Intermediate', 'Advanced', 'All Levels'];
  const durations = ['Short (< 10 min)', 'Medium (10-20 min)', 'Long (> 20 min)'];

  return (
    <Card className="bg-gray-800 border-gray-600 p-6">
      <div className="space-y-4">
        {/* Main Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search ideas by title, description, or keywords..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-gray-700 border-gray-600 text-white"
          />
        </div>

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-3 items-center">
          <Select value={filterPillar} onValueChange={setFilterPillar}>
            <SelectTrigger className="w-48 bg-gray-700 border-gray-600 text-white">
              <SelectValue placeholder="All Pillars" />
            </SelectTrigger>
            <SelectContent className="bg-gray-700 border-gray-600">
              <SelectItem value="all">All Pillars</SelectItem>
              {pillars.map(pillar => (
                <SelectItem key={pillar.id} value={pillar.id}>
                  <div className="flex items-center">
                    <div 
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: pillar.color || '#37BEB0' }}
                    />
                    {pillar.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filterPriority} onValueChange={setFilterPriority}>
            <SelectTrigger className="w-32 bg-gray-700 border-gray-600 text-white">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent className="bg-gray-700 border-gray-600">
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="High">High</SelectItem>
              <SelectItem value="Medium">Medium</SelectItem>
              <SelectItem value="Low">Low</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-32 bg-gray-700 border-gray-600 text-white">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent className="bg-gray-700 border-gray-600">
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="idea">Idea</SelectItem>
              <SelectItem value="planned">Planned</SelectItem>
              <SelectItem value="filming">Filming</SelectItem>
              <SelectItem value="editing">Editing</SelectItem>
              <SelectItem value="published">Published</SelectItem>
            </SelectContent>
          </Select>

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700">
                <Filter className="w-4 h-4 mr-2" />
                Advanced
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 bg-gray-800 border-gray-600 p-4">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-white flex items-center mb-2">
                    <Users className="w-4 h-4 mr-2" />
                    Target Audience
                  </label>
                  <Select value={audienceFilter} onValueChange={setAudienceFilter}>
                    <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-700 border-gray-600">
                      <SelectItem value="all">All Audiences</SelectItem>
                      {audiences.map(audience => (
                        <SelectItem key={audience} value={audience}>{audience}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-white flex items-center mb-2">
                    <Clock className="w-4 h-4 mr-2" />
                    Video Duration
                  </label>
                  <Select value={durationFilter} onValueChange={setDurationFilter}>
                    <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-700 border-gray-600">
                      <SelectItem value="all">All Durations</SelectItem>
                      {durations.map(duration => (
                        <SelectItem key={duration} value={duration}>{duration}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-white flex items-center mb-2">
                    <Calendar className="w-4 h-4 mr-2" />
                    Date Created
                  </label>
                  <Select value={dateFilter} onValueChange={setDateFilter}>
                    <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-700 border-gray-600">
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {activeFilters.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-4 h-4 mr-1" />
              Clear ({activeFilters.length})
            </Button>
          )}
        </div>

        {/* Active Filters Display */}
        {activeFilters.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {activeFilters.map((filter, index) => (
              <Badge key={index} variant="secondary" className="bg-teal/20 text-teal border-teal/30">
                {filter}
              </Badge>
            ))}
          </div>
        )}
      </div>
    </Card>
  );
};

export default AdvancedFilters;
