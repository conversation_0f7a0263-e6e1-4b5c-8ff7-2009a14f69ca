import React from 'react';
import { ContentPillar } from './types';
import { FormData } from './WizardFormData';
import EnhancedStartingPointStep from './EnhancedStartingPointStep';
import TitleAndHookStep from './TitleAndHookStep';
import EnhancedScriptStep from './EnhancedScriptStep';
import PublishingEssentialsStep from './PublishingEssentialsStep';
import SEOTagsStep from './SEOTagsStep';
import WizardSummaryStep from './WizardSummaryStep';

interface WizardStepRendererProps {
  currentStep: number;
  completedSteps: number[];
  skippedSteps: number[];
  
  // Starting Point Props
  startingPointOption: string;
  setStartingPointOption: (option: string) => void;
  selectedPillarForTopics: string;
  setSelectedPillarForTopics: (pillar: string) => void;
  manualTopic: string;
  setManualTopic: (topic: string) => void;
  videoTitle: string;
  setVideoTitle: (title: string) => void;
  roughIdea: string;
  setRoughIdea: (idea: string) => void;
  generatedTopics: any[];
  
  // Generated Content
  generatedTitles: string[];
  generatedHooks: string[];
  generatedScripts: string[];
  generatedDescriptions: string[];
  
  // Form Data
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  pillars: ContentPillar[];
  loading: boolean;
  
  // Actions
  onGenerateTopics: () => void;
  onGenerateTitles: () => void;
  onGenerateHooks: () => void;
  onGenerateScript: (length: string) => void;
  onGenerateDescription: () => void;
  onGenerateSEOTags: () => Promise<{ tags: string[]; hashtags: string }>;
  onSkipStep: () => void;
  onContinue: () => void;
  onSaveToIdeasBank: () => void;
  onGenerateMore: (section: string) => void;
  onCopyAll: () => void;
  onCopySection: (content: string) => void;
}

const WizardStepRenderer: React.FC<WizardStepRendererProps> = ({
  currentStep,
  completedSteps,
  skippedSteps,
  startingPointOption,
  setStartingPointOption,
  selectedPillarForTopics,
  setSelectedPillarForTopics,
  manualTopic,
  setManualTopic,
  videoTitle,
  setVideoTitle,
  roughIdea,
  setRoughIdea,
  generatedTopics,
  generatedTitles,
  generatedHooks,
  generatedScripts,
  generatedDescriptions,
  formData,
  setFormData,
  pillars,
  loading,
  onGenerateTopics,
  onGenerateTitles,
  onGenerateHooks,
  onGenerateScript,
  onGenerateDescription,
  onGenerateSEOTags,
  onSkipStep,
  onContinue,
  onSaveToIdeasBank,
  onGenerateMore,
  onCopyAll,
  onCopySection
}) => {
  // Step 0: Starting Point
  if (currentStep === 0) {
    return (
      <EnhancedStartingPointStep
        startingPointOption={startingPointOption}
        setStartingPointOption={setStartingPointOption}
        selectedPillarForTopics={selectedPillarForTopics}
        setSelectedPillarForTopics={setSelectedPillarForTopics}
        manualTopic={manualTopic}
        setManualTopic={setManualTopic}
        videoTitle={videoTitle}
        setVideoTitle={setVideoTitle}
        roughIdea={roughIdea}
        setRoughIdea={setRoughIdea}
        generatedTopics={generatedTopics}
        formData={formData}
        setFormData={setFormData}
        pillars={pillars}
        loading={loading}
        onGenerateTopics={onGenerateTopics}
        onContinue={onContinue}
      />
    );
  }

  // Step 1: Title & Hook
  if (currentStep === 1) {
    return (
      <TitleAndHookStep
        formData={formData}
        setFormData={setFormData}
        loading={loading}
        onGenerateTitles={onGenerateTitles}
        onGenerateHooks={onGenerateHooks}
        generatedTitles={generatedTitles}
        generatedHooks={generatedHooks}
        onTitleSelect={(title: string) => {
          setFormData(prev => ({ ...prev, title }));
        }}
        onHookSelect={(hook: string) => {
          setFormData(prev => ({ ...prev, hook }));
        }}
        onContinue={onContinue}
      />
    );
  }

  // Step 2: Script
  if (currentStep === 2) {
    return (
      <EnhancedScriptStep
        formData={formData}
        setFormData={setFormData}
        loading={loading}
        onGenerateScript={onGenerateScript}
        generatedScripts={generatedScripts}
        onSkipStep={onSkipStep}
        onContinue={onContinue}
      />
    );
  }

  // Step 3: Publishing Essentials (SEO & Tags) - This is the final step
  if (currentStep === 3) {
    return (
      <PublishingEssentialsStep
        formData={formData}
        setFormData={setFormData}
        loading={loading}
        onGenerateDescription={onGenerateDescription}
        onGenerateSEOTags={onGenerateSEOTags}
        generatedDescriptions={generatedDescriptions}
        pillars={pillars}
        onSkipStep={onSkipStep}
        onSaveIdea={onSaveToIdeasBank}
      />
    );
  }

  return null;
};

export default WizardStepRenderer;
