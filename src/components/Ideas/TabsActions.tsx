
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, LayoutGrid, List } from 'lucide-react';

interface TabsActionsProps {
  onAddIdea: () => void;
}

const TabsActions: React.FC<TabsActionsProps> = ({
  onAddIdea
}) => {
  return (
    <div className="flex items-center space-x-4">
      <Button
        onClick={onAddIdea}
        className="bg-teal hover:bg-teal/90 text-white"
      >
        <Plus className="w-4 h-4 mr-2" />
        Add Idea
      </Button>
    </div>
  );
};

export { TabsActions };
export default TabsActions;
