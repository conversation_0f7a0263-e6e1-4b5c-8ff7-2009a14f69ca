
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { VideoIdea, ContentPillar } from '../types';

interface QuickStatsProps {
  ideas: VideoIdea[];
  pillars: ContentPillar[];
}

const QuickStats: React.FC<QuickStatsProps> = ({ ideas, pillars }) => {
  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white">Quick Stats</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-teal">{ideas.length}</div>
            <div className="text-sm text-gray-400">Total Ideas</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange">{pillars.length}</div>
            <div className="text-sm text-gray-400">Content Pillars</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow">
              {ideas.filter(idea => idea.status === 'planned').length}
            </div>
            <div className="text-sm text-gray-400">Ready to Film</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-500">
              {ideas.filter(idea => idea.ai_generated).length}
            </div>
            <div className="text-sm text-gray-400">AI Generated</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickStats;
