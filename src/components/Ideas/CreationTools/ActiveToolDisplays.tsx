
import React from 'react';
import { Button } from '@/components/ui/button';
import CommentAssistant from '@/components/CreationTools/CommentAssistant';
import { VideoIdea, ContentPillar } from '../types';

interface ActiveToolDisplaysProps {
  showCommentAssistant: boolean;
  ideas: VideoIdea[];
  pillars: ContentPillar[];
  onAddIdea: (idea: any) => void;
  onCloseCommentAssistant: () => void;
}

const ActiveToolDisplays: React.FC<ActiveToolDisplaysProps> = ({
  showCommentAssistant,
  ideas,
  pillars,
  onAddIdea,
  onCloseCommentAssistant
}) => {
  return (
    <>
      {showCommentAssistant && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-white">Comment Assistant</h3>
            <Button
              variant="ghost"
              onClick={onCloseCommentAssistant}
              className="text-gray-400"
            >
              Close
            </Button>
          </div>
          <CommentAssistant />
        </div>
      )}
    </>
  );
};

export default ActiveToolDisplays;
