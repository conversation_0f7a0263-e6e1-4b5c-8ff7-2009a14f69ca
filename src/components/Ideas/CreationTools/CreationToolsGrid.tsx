
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, Sparkles, MessageSquare, PenTool } from 'lucide-react';

interface Tool {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  action: () => void;
}

interface CreationToolsGridProps {
  onScriptWriter: () => void;
  onTitleOptimizer: () => void;
  onDescriptionGenerator: () => void;
  onScriptGenerator: () => void;
}

const CreationToolsGrid: React.FC<CreationToolsGridProps> = ({
  onScriptWriter,
  onTitleOptimizer,
  onDescriptionGenerator,
  onScriptGenerator
}) => {
  const tools: Tool[] = [
    {
      title: 'Script Generator',
      description: 'Generate complete video scripts with titles, outlines, and talking points in one workflow',
      icon: PenTool,
      color: 'bg-teal-500',
      action: onScriptGenerator
    },
    {
      title: 'Title Optimizer',
      description: 'Optimize your video titles for maximum click-through rates',
      icon: Sparkles,
      color: 'bg-purple-500',
      action: onTitleOptimizer
    },
    {
      title: 'Script Writer',
      description: 'Generate detailed video scripts with AI assistance',
      icon: FileText,
      color: 'bg-blue-500',
      action: onScriptWriter
    },
    {
      title: 'Description Generator',
      description: 'Generate SEO-optimized video descriptions',
      icon: MessageSquare,
      color: 'bg-orange-500',
      action: onDescriptionGenerator
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {tools.map((tool, index) => (
        <Card key={index} className="hover:bg-gray-800/50 transition-colors cursor-pointer">
          <CardHeader>
            <CardTitle className="flex items-center">
              <tool.icon className={`w-5 h-5 mr-2 text-${tool.color.split('-')[1]}`} />
              {tool.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-400 mb-4">
              {tool.description}
            </p>
            <Button 
              onClick={tool.action}
              className={`w-full ${tool.color} hover:${tool.color}/90`}
            >
              Use Tool
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default CreationToolsGrid;
