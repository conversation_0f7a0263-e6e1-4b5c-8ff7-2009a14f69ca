
import React from 'react';
import { CheckCircle, Circle } from 'lucide-react';

interface Step {
  id: string;
  name: string;
  icon: any;
}

interface WizardProgressProps {
  steps: Step[];
  currentStep: number;
  completedSteps: number[];
}

const WizardProgress: React.FC<WizardProgressProps> = ({ steps, currentStep, completedSteps }) => {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-white">AI Creation Wizard</h3>
        <div className="text-sm text-gray-400">
          Step {currentStep + 1} of {steps.length}
        </div>
      </div>
      
      <div className="flex flex-wrap gap-2">
        {steps.map((step, index) => {
          const isCompleted = completedSteps.includes(index);
          const isCurrent = index === currentStep;
          const IconComponent = isCompleted ? CheckCircle : Circle;
          
          return (
            <div
              key={step.id}
              className={`flex items-center px-3 py-2 rounded-lg text-sm ${
                isCompleted
                  ? 'bg-green-900/20 text-green-400 border border-green-500/20'
                  : isCurrent
                  ? 'bg-teal/20 text-teal border border-teal/30'
                  : 'bg-gray-700 text-gray-400 border border-gray-600'
              }`}
            >
              <IconComponent className="w-4 h-4 mr-2" />
              {step.name}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default WizardProgress;
