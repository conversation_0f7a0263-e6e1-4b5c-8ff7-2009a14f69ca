
import React from 'react';
import { Button } from '@/components/ui/button';
import { Target } from 'lucide-react';
import { parseGeneratedTitles } from './TitleAndHook/titleParser';
import { parseGeneratedHooks } from './TitleAndHook/hookParser';
import { useTitleAndHookEffects } from './TitleAndHook/useTitleAndHookEffects';
import TopicContext from './TitleAndHook/TopicContext';
import TitlesSection from './TitleAndHook/TitlesSection';
import HooksSection from './TitleAndHook/HooksSection';

interface TitleAndHookStepProps {
  formData: any;
  setFormData: (data: any) => void;
  loading: boolean;
  onGenerateTitles: () => void;
  onGenerateHooks: () => void;
  generatedTitles: string[];
  generatedHooks: string[];
  onTitleSelect: (title: string) => void;
  onHookSelect: (hook: string) => void;
  onContinue: () => void;
}

const TitleAndHookStep: React.FC<TitleAndHookStepProps> = ({
  formData,
  setFormData,
  loading,
  onGenerateTitles,
  onGenerateHooks,
  generatedTitles,
  generatedHooks,
  onTitleSelect,
  onHookSelect,
  onContinue
}) => {
  console.log('TitleAndHookStep rendered with:', { 
    formData, 
    generatedTitles, 
    generatedHooks,
    loading 
  });

  const parsedTitles = parseGeneratedTitles(generatedTitles);
  const parsedHooks = parseGeneratedHooks(generatedHooks);
  
  console.log('Parsed titles:', parsedTitles);
  console.log('Parsed hooks:', parsedHooks);

  useTitleAndHookEffects({
    topic: formData.topic,
    title: formData.title,
    parsedTitlesLength: parsedTitles.length,
    hooksLength: parsedHooks.length,
    loading,
    onGenerateTitles,
    onGenerateHooks
  });

  const handleTitleSelect = (title: string) => {
    console.log('Title selected:', title);
    onTitleSelect(title);
    // Update form data immediately
    const updatedFormData = { ...formData, title };
    setFormData(updatedFormData);
    console.log('Form data updated with title:', updatedFormData);
  };

  const handleHookSelect = (hook: string) => {
    console.log('Hook selected:', hook);
    onHookSelect(hook);
    // Update form data immediately
    const updatedFormData = { ...formData, hook };
    setFormData(updatedFormData);
    console.log('Form data updated with hook:', updatedFormData);
  };

  const handleContinue = () => {
    console.log('Continue button clicked with formData:', formData);
    if (formData.title && formData.hook) {
      console.log('Proceeding to next step');
      onContinue();
    } else {
      console.warn('Cannot continue: missing title or hook', { 
        title: formData.title, 
        hook: formData.hook 
      });
    }
  };

  const canContinue = formData.title && formData.hook;
  console.log('Can continue:', canContinue, { 
    title: !!formData.title, 
    hook: !!formData.hook,
    titleValue: formData.title,
    hookValue: formData.hook
  });

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-white flex items-center justify-center">
          <Target className="w-5 h-5 mr-2 text-teal" />
          Title & Hook Creation
        </h3>
        <p className="text-gray-300">Create compelling titles and engaging hooks</p>
      </div>

      <TopicContext topic={formData.topic} />

      <div className="grid md:grid-cols-2 gap-6">
        <TitlesSection
          titles={parsedTitles}
          selectedTitle={formData.title}
          loading={loading}
          onTitleSelect={handleTitleSelect}
          onRegenerateTitles={onGenerateTitles}
        />

        <HooksSection
          hooks={parsedHooks}
          selectedHook={formData.hook}
          selectedTitle={formData.title}
          loading={loading}
          onHookSelect={handleHookSelect}
          onRegenerateHooks={onGenerateHooks}
        />
      </div>

      {/* Show current selections for debugging */}
      {formData.title && (
        <div className="bg-gray-700 p-3 rounded">
          <p className="text-sm text-gray-300">Selected Title: <span className="text-white">{formData.title}</span></p>
        </div>
      )}
      
      {formData.hook && (
        <div className="bg-gray-700 p-3 rounded">
          <p className="text-sm text-gray-300">Selected Hook: <span className="text-white">{formData.hook}</span></p>
        </div>
      )}

      <div className="flex justify-end">
        <Button
          onClick={handleContinue}
          disabled={!canContinue || loading}
          className="bg-teal hover:bg-teal/90 text-white"
        >
          Continue to SEO & Tags
        </Button>
      </div>
    </div>
  );
};

export default TitleAndHookStep;
