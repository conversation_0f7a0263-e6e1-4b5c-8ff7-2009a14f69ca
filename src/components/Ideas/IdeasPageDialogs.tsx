
import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import EditIdeaDialog from './EditIdeaDialog';
import EnhancedAddIdeaForm from './EnhancedAddIdeaForm';
import { VideoIdea, ContentPillar, NewIdea } from './types';

interface IdeasPageDialogsProps {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  showAIWizard?: boolean;
  setShowAIWizard?: (show: boolean) => void;
  editingIdea: VideoIdea | null;
  setEditingIdea: (idea: VideoIdea | null) => void;
  newIdea: NewIdea;
  setNewIdea: (idea: NewIdea) => void;
  pillars: ContentPillar[];
  addIdea: () => void;
  updateIdea: (ideaId: string, updates: Partial<VideoIdea>) => Promise<void>;
  onIdeaGenerated?: (ideaData: any) => void;
}

const IdeasPageDialogs: React.FC<IdeasPageDialogsProps> = ({
  isDialogOpen,
  setIsDialogOpen,
  showAIWizard,
  setShowAIWizard,
  editingIdea,
  setEditingIdea,
  newIdea,
  setNewIdea,
  pillars,
  addIdea,
  updateIdea,
  onIdeaGenerated
}) => {
  return (
    <>
      {/* Add Idea Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-white">Add New Video Idea</DialogTitle>
          </DialogHeader>
          <EnhancedAddIdeaForm
            newIdea={newIdea}
            setNewIdea={setNewIdea}
            pillars={pillars}
            onSubmit={addIdea}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Idea Dialog */}
      <EditIdeaDialog
        editingIdea={editingIdea}
        setEditingIdea={setEditingIdea}
        updateIdea={updateIdea}
        pillars={pillars}
      />
    </>
  );
};

export default IdeasPageDialogs;
