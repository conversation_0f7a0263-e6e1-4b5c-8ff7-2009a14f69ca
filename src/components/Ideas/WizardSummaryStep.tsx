import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Copy, Save, Sparkles, Target, Edit, FileText, Hash } from 'lucide-react';
import { ContentPillar } from './types';

interface WizardSummaryStepProps {
  formData: any;
  skippedSteps: number[];
  pillars: ContentPillar[];
  onSaveToIdeasBank: () => void;
  onGenerateMore: (section: string) => void;
  onCopyAll: () => void;
  onCopySection: (content: string) => void;
}

const WizardSummaryStep: React.FC<WizardSummaryStepProps> = ({
  formData,
  skippedSteps,
  pillars,
  onSaveToIdeasBank,
  onGenerateMore,
  onCopyAll,
  onCopySection
}) => {
  const pillar = pillars.find(p => p.id === formData.pillar_id);

  const sections = [
    {
      icon: Target,
      title: 'Title',
      content: formData.title,
      section: 'title',
      skipped: !formData.title
    },
    {
      icon: Sparkles,
      title: 'Hook',
      content: formData.hook,
      section: 'hook',
      skipped: !formData.hook
    },
    {
      icon: Edit,
      title: 'Script',
      content: formData.script,
      section: 'script',
      skipped: !formData.script
    },
    {
      icon: FileText,
      title: 'Description',
      content: formData.description,
      section: 'description',
      skipped: !formData.description
    },
    {
      icon: Hash,
      title: 'SEO Tags',
      content: formData.tags?.join(', ') || '',
      section: 'seo',
      skipped: !formData.tags?.length
    }
  ];

  const allContent = `
🎬 VIDEO CONTENT PLAN

📝 Title: ${formData.title || 'Not set'}

🎯 Hook: ${formData.hook || 'Not set'}

📋 Script: ${formData.script || 'Not set'}

📄 Description: ${formData.description || 'Not set'}

🏷️ Tags: ${formData.tags?.join(', ') || 'Not set'}

#️⃣ Hashtags: ${formData.hashtags || 'Not set'}

📊 Content Pillar: ${pillar?.name || 'Not set'}
  `.trim();

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-white flex items-center justify-center">
          <Save className="w-5 h-5 mr-2 text-yellow" />
          Content Summary
        </h3>
        <p className="text-gray-300">Review your generated content and save to Ideas Bank</p>
      </div>

      {/* Pillar and Topic Info */}
      <Card className="bg-gray-800 border-gray-600">
        <CardContent className="p-4">
          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-white font-medium">Content Pillar:</span>
              <p className="text-gray-300">{pillar?.name || 'Not selected'}</p>
            </div>
            <div>
              <span className="text-white font-medium">Original Topic:</span>
              <p className="text-gray-300">{formData.topic || 'Not set'}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid md:grid-cols-2 gap-4">
        <Button
          onClick={onCopyAll}
          className="bg-gray-700 hover:bg-gray-600 text-white border border-gray-600"
        >
          <Copy className="w-4 h-4 mr-2" />
          Copy All Content
        </Button>
        <Button
          onClick={onSaveToIdeasBank}
          className="bg-blue hover:bg-blue-dark text-white"
        >
          <Save className="w-4 h-4 mr-2" />
          Save to Ideas Bank
        </Button>
      </div>

      {/* Content Sections */}
      <div className="space-y-4">
        {sections.map((section, index) => {
          const IconComponent = section.icon;
          
          return (
            <Card key={index} className="bg-gray-800 border-gray-600">
              <CardHeader>
                <CardTitle className="text-white flex items-center justify-between">
                  <span className="flex items-center">
                    <IconComponent className="w-5 h-5 mr-2 text-teal" />
                    {section.title}
                    {section.skipped && (
                      <Badge variant="outline" className="ml-2 text-yellow-400 border-yellow-400">
                        Skipped
                      </Badge>
                    )}
                  </span>
                  <div className="flex space-x-2">
                    {section.content && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onCopySection(section.content)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-700"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onGenerateMore(section.section)}
                      className="border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      <Sparkles className="w-4 h-4 mr-1" />
                      Generate More
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {section.skipped ? (
                  <div className="text-center py-8">
                    <p className="text-gray-400">This section was skipped</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onGenerateMore(section.section)}
                      className="mt-2 border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      Generate Now
                    </Button>
                  </div>
                ) : (
                  <div className="bg-gray-700 p-4 rounded-lg">
                    <pre className="text-white text-sm whitespace-pre-wrap">
                      {section.content && section.content.length > 400
                        ? `${section.content.substring(0, 400)}...`
                        : section.content || 'No content generated'}
                    </pre>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}

        {/* Hashtags Section */}
        {formData.hashtags && (
          <Card className="bg-gray-800 border-gray-600">
            <CardHeader>
              <CardTitle className="text-white flex items-center justify-between">
                <span className="flex items-center">
                  <Hash className="w-5 h-5 mr-2 text-terracotta" />
                  Hashtags
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onCopySection(formData.hashtags)}
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  <Copy className="w-4 h-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-700 p-4 rounded-lg">
                <p className="text-white text-sm">{formData.hashtags}</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Final Save Button */}
      <div className="text-center pt-6">
        <Button
          onClick={onSaveToIdeasBank}
          className="bg-blue hover:bg-blue-dark text-white text-lg px-8 py-3"
        >
          <Save className="w-5 h-5 mr-2" />
          Save Complete Idea to Bank
        </Button>
      </div>
    </div>
  );
};

export default WizardSummaryStep;
