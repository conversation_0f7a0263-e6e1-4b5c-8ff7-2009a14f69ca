
import { useState } from 'react';

interface FormData {
  topic: string;
  pillar_id: string;
  title: string;
  hook: string;
  thumbnail: string;
  script: string;
  description: string;
  tags: string[];
  hashtags: string;
}

export const useWizardFormData = () => {
  const [formData, setFormData] = useState<FormData>({
    topic: '',
    pillar_id: '',
    title: '',
    hook: '',
    thumbnail: '',
    script: '',
    description: '',
    tags: [],
    hashtags: ''
  });

  const resetFormData = () => {
    setFormData({
      topic: '',
      pillar_id: '',
      title: '',
      hook: '',
      thumbnail: '',
      script: '',
      description: '',
      tags: [],
      hashtags: ''
    });
  };

  return {
    formData,
    setFormData,
    resetFormData
  };
};

export type { FormData };
