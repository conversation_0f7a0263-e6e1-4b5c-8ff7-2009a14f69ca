
import React from 'react';
import { ContentPillar } from './types';
import AIWizardContainer from './AIWizardContainer';
import { useAIWizard } from '@/hooks/ideas/useAIWizard';
import { useAIContentGeneration } from '@/hooks/ideas/useAIContentGeneration';

interface AIGeneratedIdeasTabProps {
  pillars: ContentPillar[];
  addGeneratedIdea: (ideaData: any) => void;
}

const AIGeneratedIdeasTab: React.FC<AIGeneratedIdeasTabProps> = ({ pillars, addGeneratedIdea }) => {
  const wizardState = useAIWizard();
  const contentGeneration = useAIContentGeneration(
    pillars,
    wizardState.setLoading,
    wizardState.setGeneratedTopics,
    wizardState.setGeneratedTitles,
    wizardState.logAIUsage
  );

  const handleContinue = () => {
    wizardState.goToNextStep();
  };

  const handleSkipStep = () => {
    wizardState.skipStep(wizardState.currentStep);
  };

  const saveToIdeasBank = () => {
    if (!wizardState.formData.title && !wizardState.formData.topic) return;

    const description = `🤖 AI-Generated Video Plan

📝 Hook: ${wizardState.formData.hook || 'Not set'}

📋 Script: ${wizardState.formData.script || 'Not set'}

📄 Description: ${wizardState.formData.description || 'Not set'}

🏷️ Tags: ${wizardState.formData.tags?.length > 0 ? wizardState.formData.tags.join(', ') : 'Not set'}

#️⃣ Hashtags: ${wizardState.formData.hashtags || 'Not set'}`;
    
    addGeneratedIdea({
      title: wizardState.formData.title || wizardState.formData.topic,
      description,
      pillar_id: wizardState.formData.pillar_id,
      topic: wizardState.formData.topic,
      hook: wizardState.formData.hook,
      script: wizardState.formData.script,
      seo_tags: wizardState.formData.tags,
      hashtags: wizardState.formData.hashtags
    });

    wizardState.resetWizard();
  };

  const handleGenerateMore = (section: string) => {
    // Route back to appropriate step and clear content
    switch (section) {
      case 'title':
      case 'hook':
        wizardState.setCurrentStep(1);
        wizardState.clearGeneratedContent('titles');
        wizardState.clearGeneratedContent('hooks');
        break;
      case 'script':
        wizardState.setCurrentStep(2);
        wizardState.clearGeneratedContent('scripts');
        break;
      case 'description':
      case 'seo':
        wizardState.setCurrentStep(3);
        wizardState.clearGeneratedContent('descriptions');
        break;
    }
  };

  const handleCopyAll = () => {
    const allContent = `
🎬 VIDEO CONTENT PLAN

📝 Title: ${wizardState.formData.title || 'Not set'}

🎯 Hook: ${wizardState.formData.hook || 'Not set'}

📋 Script: ${wizardState.formData.script || 'Not set'}

📄 Description: ${wizardState.formData.description || 'Not set'}

🏷️ Tags: ${wizardState.formData.tags?.join(', ') || 'Not set'}

#️⃣ Hashtags: ${wizardState.formData.hashtags || 'Not set'}
    `.trim();

    navigator.clipboard.writeText(allContent);
  };

  const handleCopySection = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  return (
    <AIWizardContainer
      currentStep={wizardState.currentStep}
      completedSteps={wizardState.completedSteps}
      skippedSteps={wizardState.skippedSteps}
      loading={wizardState.loading}
      
      startingPointOption={wizardState.startingPointOption}
      setStartingPointOption={wizardState.setStartingPointOption}
      selectedPillarForTopics={wizardState.selectedPillarForTopics}
      setSelectedPillarForTopics={wizardState.setSelectedPillarForTopics}
      manualTopic={wizardState.manualTopic}
      setManualTopic={wizardState.setManualTopic}
      videoTitle={wizardState.videoTitle}
      setVideoTitle={wizardState.setVideoTitle}
      roughIdea={wizardState.roughIdea}
      setRoughIdea={wizardState.setRoughIdea}
      generatedTopics={wizardState.generatedTopics}
      
      generatedTitles={wizardState.generatedTitles}
      generatedHooks={wizardState.generatedHooks}
      generatedScripts={wizardState.generatedScripts}
      generatedDescriptions={wizardState.generatedDescriptions}
      
      formData={wizardState.formData}
      setFormData={wizardState.setFormData}
      pillars={pillars}
      
      onGenerateTopics={() => contentGeneration.generateTopics(wizardState.selectedPillarForTopics)}
      onGenerateTitles={async () => {
        const prompt = `Generate 5 compelling YouTube video titles for the topic: "${wizardState.formData.topic}". Make them click-worthy and engaging.`;
        const titles = await contentGeneration.generateContent('title-generation', prompt, 2);
        wizardState.setGeneratedTitles(titles);
      }}
      onGenerateHooks={async () => {
        const prompt = `Generate 3 video hook scripts (first 15 seconds) for the video title: "${wizardState.formData.title}". Make them attention-grabbing and engaging.`;
        const hooks = await contentGeneration.generateContent('hook-generation', prompt, 2);
        wizardState.setGeneratedHooks(hooks);
      }}
      onGenerateScript={async (length: string) => {
        const prompt = `Generate a complete video script outline for: "${wizardState.formData.title}" with hook: "${wizardState.formData.hook}". Target length: ${length} minutes. Include main points and transitions.`;
        const scripts = await contentGeneration.generateContent('script-generation', prompt, 5);
        wizardState.setGeneratedScripts(scripts);
      }}
      onGenerateDescription={async () => {
        const prompt = `Generate a YouTube video description for: "${wizardState.formData.title}". Include timestamps, key points, and call-to-action.`;
        const descriptions = await contentGeneration.generateContent('description-generation', prompt, 2);
        wizardState.setGeneratedDescriptions(descriptions);
      }}
      onGenerateSEOTags={() => contentGeneration.generateSEOTags(wizardState.formData, pillars)}
      onSkipStep={handleSkipStep}
      onContinue={handleContinue}
      onGoToStep={wizardState.goToStep}
      onSaveToIdeasBank={saveToIdeasBank}
      onGenerateMore={handleGenerateMore}
      onCopyAll={handleCopyAll}
      onCopySection={handleCopySection}
    />
  );
};

export default AIGeneratedIdeasTab;
