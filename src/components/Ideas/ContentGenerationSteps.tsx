import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Spark<PERSON>, ArrowRight } from 'lucide-react';

interface Step {
  id: number;
  title: string;
}

interface FormData {
  topic: string;
  pillar_id: string;
  title: string;
  hook: string;
  thumbnail: string;
  script: string;
  description: string;
  tags: string[];
  hashtags: string;
}

interface ContentGenerationStepsProps {
  currentStep: number;
  completedSteps: number[];
  steps: Step[];
  formData: FormData;
  generatedOptions: string[];
  loading: boolean;
  onStepComplete: (stepIndex: number, selectedValue: string) => void;
  onGenerateTitles: () => void;
  onGenerateHooks: () => void;
  onGenerateThumbnailIdeas: () => void;
  onGenerateScript: () => void;
  onGenerateDescription: () => void;
  onSaveToIdeasBank: () => void;
  onGenerateContent: (type: string, prompt: string, creditsUsed: number) => Promise<string[]>;
}

const ContentGenerationSteps: React.FC<ContentGenerationStepsProps> = ({
  currentStep,
  completedSteps,
  steps,
  formData,
  generatedOptions,
  loading,
  onStepComplete,
  onGenerateTitles,
  onGenerateHooks,
  onGenerateThumbnailIdeas,
  onGenerateScript,
  onGenerateDescription,
  onSaveToIdeasBank
}) => {
  // Step 1: Title Generation
  if (currentStep === 1 && !completedSteps.includes(1)) {
    return (
      <div className="space-y-4">
        {completedSteps.includes(0) && (
          <div className="p-4 bg-green-900/20 border border-green-500/20 rounded-lg">
            <p className="text-green-400 font-medium">✓ Topic selected</p>
            <p className="text-gray-300 text-sm mt-1">Topic: {formData.topic}</p>
          </div>
        )}
        
        <Button onClick={onGenerateTitles} disabled={loading} className="bg-teal hover:bg-teal/90 text-white">
          <Sparkles className="w-4 h-4 mr-2" />
          Generate Titles (2 credits)
        </Button>
      </div>
    );
  }

  // Generated Options for Steps 1-5
  if (currentStep >= 1 && currentStep <= 5 && generatedOptions.length > 0) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-white">Choose your preferred option:</h3>
        <div className="space-y-3">
          {generatedOptions.map((option, index) => (
            <Card key={index} className="bg-gray-700 border-gray-600 cursor-pointer hover:bg-gray-600 transition-colors">
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <p className="text-white flex-1">{String(option)}</p>
                  <Button
                    size="sm"
                    onClick={() => onStepComplete(currentStep, String(option))}
                    className="bg-teal hover:bg-teal/90 text-white ml-4"
                  >
                    Select
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Helper function to get form data field based on step
  const getFormDataField = (stepIndex: number): string => {
    const fieldMap: { [key: number]: keyof FormData } = {
      1: 'title',
      2: 'hook',
      3: 'thumbnail',
      4: 'script',
      5: 'description'
    };
    return String(formData[fieldMap[stepIndex]] || '');
  };

  // Completed Step Display & Next Step Buttons (Steps 1-5)
  if (completedSteps.includes(currentStep) && currentStep >= 1 && currentStep <= 5) {
    return (
      <div className="space-y-4">
        <div className="p-4 bg-green-900/20 border border-green-500/20 rounded-lg">
          <p className="text-green-400 font-medium">✓ {steps[currentStep].title} completed</p>
          <p className="text-gray-300 text-sm mt-1">
            Selected: {getFormDataField(currentStep)}
          </p>
        </div>
        
        {currentStep === 1 && (
          <Button onClick={onGenerateHooks} disabled={loading} className="bg-teal hover:bg-teal/90 text-white">
            <ArrowRight className="w-4 h-4 mr-2" />
            Generate Hooks (2 credits)
          </Button>
        )}
        
        {currentStep === 2 && (
          <Button onClick={onGenerateThumbnailIdeas} disabled={loading} className="bg-teal hover:bg-teal/90 text-white">
            <ArrowRight className="w-4 h-4 mr-2" />
            Generate Thumbnail Ideas (1 credit)
          </Button>
        )}
        
        {currentStep === 3 && (
          <Button onClick={onGenerateScript} disabled={loading} className="bg-teal hover:bg-teal/90 text-white">
            <ArrowRight className="w-4 h-4 mr-2" />
            Generate Script (5 credits)
          </Button>
        )}
        
        {currentStep === 4 && (
          <Button onClick={onGenerateDescription} disabled={loading} className="bg-teal hover:bg-teal/90 text-white">
            <ArrowRight className="w-4 h-4 mr-2" />
            Generate Description (2 credits)
          </Button>
        )}
      </div>
    );
  }

  return null;
};

export default ContentGenerationSteps;
