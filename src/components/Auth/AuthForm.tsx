import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/lib/supabase';
import { testSupabaseConnection } from '@/lib/supabase';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';

// AuthFormProps type definition
type AuthFormProps = {
  mode: 'signin' | 'signup';
  onToggleMode: () => void;
};

const AuthForm = ({ mode, onToggleMode }: AuthFormProps) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    console.log('Attempting to sign in with:', { email });

    try {
      if (mode === 'signin') {
        console.log('Calling supabase.auth.signInWithPassword');
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        console.log('Sign in response:', { success: !!data.session, error: error?.message });
        
        if (error) throw error;
        console.log('Sign in successful, navigating to dashboard');
        navigate('/dashboard');
      } else {
        const { error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              name,
            },
          },
        });

        if (error) throw error;
        toast.success('Account created! Please check your email for verification.');
      }
    } catch (error: any) {
      console.error('Authentication error:', error);
      toast.error(error.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    const result = await testSupabaseConnection();
    if (result) {
      toast.success('Connection to database successful');
    } else {
      toast.error('Failed to connect to database');
    }
  };

  return (
    <Card className="w-full" style={{ width: "100%" }}>
      <CardHeader className="pb-2">
        <CardTitle className="text-2xl font-bold">{mode === 'signin' ? 'Sign In' : 'Create Account'}</CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        <form onSubmit={handleSubmit} className="space-y-5">
          {mode === 'signup' && (
            <div>
              <label className="text-base font-medium mb-1 block">Name</label>
              <Input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Your name"
                required
                className="h-11 text-base"
              />
            </div>
          )}
          <div>
            <label className="text-base font-medium mb-1 block">Email</label>
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
              className="h-11 text-base"
            />
          </div>
          <div>
            <label className="text-base font-medium mb-1 block">Password</label>
            <Input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              required
              minLength={6}
              className="h-11 text-base"
            />
          </div>
          <Button type="submit" className="w-full h-11 text-base mt-2" disabled={loading}>
            {loading ? 'Please wait...' : (mode === 'signin' ? 'Sign In' : 'Create Account')}
          </Button>
        </form>
        <Button 
          type="button" 
          variant="outline" 
          onClick={testConnection} 
          className="mt-2"
        >
          Test Connection
        </Button>
        <div className="mt-6 text-center">
          <button
            type="button"
            onClick={onToggleMode}
            className="text-base text-blue-500 hover:underline"
          >
            {mode === 'signin' ? "Don't have an account? Sign up" : 'Already have an account? Sign in'}
          </button>
        </div>
      </CardContent>
    </Card>
  );
};

export default AuthForm;
