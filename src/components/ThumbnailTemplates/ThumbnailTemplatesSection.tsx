
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { ChevronDown, Palette, Download, Info } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import ThumbnailTemplateCard from './ThumbnailTemplateCard';
import QuickTemplateSelector from './QuickTemplateSelector';

interface ContentPillar {
  id: string;
  name: string;
  color: string;
}

interface ThumbnailTemplatesSectionProps {
  pillars: ContentPillar[];
}

const ThumbnailTemplatesSection = ({ pillars }: ThumbnailTemplatesSectionProps) => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [isInstructionsOpen, setIsInstructionsOpen] = useState(false);

  const instructions = [
    "Download your template settings as CSS/hex codes",
    "Import colors into Canva, Photoshop, or your design tool",
    "Apply border styles and text overlays consistently",
    "Use the same template for all videos in each pillar",
    "Maintain visual consistency across your channel"
  ];

  return (
    <Card className="bg-gray-700 border-gray-600 mt-8">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Palette className="w-5 h-5 text-teal" />
            <div>
              <CardTitle className="text-white">Thumbnail Templates</CardTitle>
              <p className="text-gray-300 text-sm mt-1">Consistent branding for each content pillar</p>
            </div>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Info className="w-4 h-4 text-gray-400" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Create consistent thumbnail styles for each content pillar</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        
        <div className="flex items-center space-x-2 pt-4">
          <Switch
            id="thumbnail-templates"
            checked={isEnabled}
            onCheckedChange={setIsEnabled}
          />
          <Label htmlFor="thumbnail-templates" className="text-white">
            Enable thumbnail templates
          </Label>
          {isEnabled && (
            <Badge variant="outline" className="text-teal border-teal ml-2">
              Active
            </Badge>
          )}
        </div>
      </CardHeader>

      {isEnabled && (
        <CardContent className="space-y-6">
          {/* Quick Templates */}
          <QuickTemplateSelector pillars={pillars} />

          {/* Individual Pillar Templates */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Pillar Templates</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {pillars.map((pillar) => (
                <ThumbnailTemplateCard key={pillar.id} pillar={pillar} />
              ))}
            </div>
          </div>

          {/* Export Instructions */}
          <div className="border-t border-gray-600 pt-6">
            <Collapsible open={isInstructionsOpen} onOpenChange={setIsInstructionsOpen}>
              <CollapsibleTrigger asChild>
                <Button 
                  variant="outline" 
                  className="w-full border-gray-500 text-white hover:bg-gray-600 flex items-center justify-between"
                >
                  <div className="flex items-center space-x-2">
                    <Download className="w-4 h-4" />
                    <span>How to use these templates</span>
                  </div>
                  <ChevronDown className={`w-4 h-4 transition-transform ${isInstructionsOpen ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-4">
                <div className="bg-gray-800 rounded-lg p-4">
                  <h4 className="text-white font-medium mb-3">Implementation Steps:</h4>
                  <ol className="space-y-2">
                    {instructions.map((instruction, index) => (
                      <li key={index} className="text-gray-300 text-sm flex items-start space-x-2">
                        <span className="text-teal font-medium">{index + 1}.</span>
                        <span>{instruction}</span>
                      </li>
                    ))}
                  </ol>
                  <div className="mt-4 pt-4 border-t border-gray-600">
                    <Button className="bg-teal hover:bg-teal/90 text-white">
                      <Download className="w-4 h-4 mr-2" />
                      Export All Settings
                    </Button>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default ThumbnailTemplatesSection;
