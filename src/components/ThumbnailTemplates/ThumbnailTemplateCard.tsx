import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';

interface ContentPillar {
  id: string;
  name: string;
  color: string;
}

interface ThumbnailTemplateCardProps {
  pillar: ContentPillar;
}

interface TemplateSettings {
  borderColor: string;
  borderStyle: 'none' | 'solid' | 'corner' | 'bottom';
  borderWidth: number;
  showTextOverlay: boolean;
  badgePosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  badgeStyle: 'solid' | 'outline' | 'gradient';
}

const ThumbnailTemplateCard = ({ pillar }: ThumbnailTemplateCardProps) => {
  const [settings, setSettings] = useState<TemplateSettings>({
    borderColor: pillar.color,
    borderStyle: 'solid',
    borderWidth: 4,
    showTextOverlay: true,
    badgePosition: 'top-left',
    badgeStyle: 'solid'
  });

  const updateSetting = <K extends keyof TemplateSettings>(
    key: K,
    value: TemplateSettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const getBorderStyles = () => {
    if (settings.borderStyle === 'none') return {};
    
    const borderColor = settings.borderColor;
    const borderWidth = settings.borderWidth;
    
    switch (settings.borderStyle) {
      case 'solid':
        return { border: `${borderWidth}px solid ${borderColor}` };
      case 'corner':
        return {
          background: `linear-gradient(${borderColor}, ${borderColor}) top left,
                      linear-gradient(${borderColor}, ${borderColor}) top right,
                      linear-gradient(${borderColor}, ${borderColor}) bottom left,
                      linear-gradient(${borderColor}, ${borderColor}) bottom right`,
          backgroundSize: `${borderWidth * 3}px ${borderWidth * 3}px`,
          backgroundRepeat: 'no-repeat'
        };
      case 'bottom':
        return { borderBottom: `${borderWidth}px solid ${borderColor}` };
      default:
        return {};
    }
  };

  const getBadgeStyles = () => {
    const baseStyles = "absolute px-2 py-1 text-xs font-medium rounded";
    const position = {
      'top-left': 'top-2 left-2',
      'top-right': 'top-2 right-2',
      'bottom-left': 'bottom-2 left-2',
      'bottom-right': 'bottom-2 right-2'
    }[settings.badgePosition];

    const style = {
      'solid': `text-white`,
      'outline': `border-2 text-white bg-black/50`,
      'gradient': `bg-gradient-to-r text-white`
    }[settings.badgeStyle];

    return `${baseStyles} ${position} ${style}`;
  };

  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader className="pb-3">
        <div className="flex items-center space-x-3">
          <div
            className="w-4 h-4 rounded-full"
            style={{ backgroundColor: pillar.color }}
          />
          <CardTitle className="text-white text-lg">{pillar.name}</CardTitle>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Live Preview */}
        <div className="space-y-2">
          <Label className="text-white font-medium">Preview</Label>
          <div className="relative">
            <div
              className="w-full aspect-video bg-gray-600 rounded overflow-hidden relative"
              style={getBorderStyles()}
            >
              {/* Placeholder content */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-gray-400">
                  <div className="text-2xl mb-2">📹</div>
                  <div className="text-sm">Sample Thumbnail</div>
                </div>
              </div>
              
              {/* Text overlay badge */}
              {settings.showTextOverlay && (
                <div
                  className={getBadgeStyles()}
                  style={
                    settings.badgeStyle === 'solid'
                      ? { backgroundColor: settings.borderColor }
                      : settings.badgeStyle === 'outline'
                      ? { borderColor: settings.borderColor }
                      : settings.badgeStyle === 'gradient'
                      ? { background: `linear-gradient(to right, ${settings.borderColor}, ${settings.borderColor}B3)` }
                      : undefined
                  }
                >
                  {pillar.name}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Border Color */}
        <div className="space-y-2">
          <Label className="text-white">Border Color</Label>
          <div className="flex items-center space-x-2">
            <input
              type="color"
              value={settings.borderColor}
              onChange={(e) => updateSetting('borderColor', e.target.value)}
              className="w-8 h-8 rounded border border-gray-600"
            />
            <span className="text-gray-300 text-sm">{settings.borderColor}</span>
          </div>
        </div>

        {/* Border Style */}
        <div className="space-y-2">
          <Label className="text-white">Border Style</Label>
          <Select value={settings.borderStyle} onValueChange={(value) => updateSetting('borderStyle', value as any)}>
            <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-700 border-gray-600">
              <SelectItem value="none">None</SelectItem>
              <SelectItem value="solid">Solid</SelectItem>
              <SelectItem value="corner">Corner Accent</SelectItem>
              <SelectItem value="bottom">Bottom Bar</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Border Width */}
        {settings.borderStyle !== 'none' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-white">Border Width</Label>
              <span className="text-gray-300 text-sm">{settings.borderWidth}px</span>
            </div>
            <Slider
              value={[settings.borderWidth]}
              onValueChange={(value) => updateSetting('borderWidth', value[0])}
              max={10}
              min={0}
              step={1}
              className="w-full"
            />
          </div>
        )}

        {/* Text Overlay */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Switch
              id={`overlay-${pillar.id}`}
              checked={settings.showTextOverlay}
              onCheckedChange={(checked) => updateSetting('showTextOverlay', checked)}
            />
            <Label htmlFor={`overlay-${pillar.id}`} className="text-white">
              Show pillar name badge
            </Label>
          </div>

          {settings.showTextOverlay && (
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label className="text-white text-sm">Position</Label>
                <Select value={settings.badgePosition} onValueChange={(value) => updateSetting('badgePosition', value as any)}>
                  <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-700 border-gray-600">
                    <SelectItem value="top-left">Top Left</SelectItem>
                    <SelectItem value="top-right">Top Right</SelectItem>
                    <SelectItem value="bottom-left">Bottom Left</SelectItem>
                    <SelectItem value="bottom-right">Bottom Right</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-white text-sm">Style</Label>
                <Select value={settings.badgeStyle} onValueChange={(value) => updateSetting('badgeStyle', value as any)}>
                  <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-700 border-gray-600">
                    <SelectItem value="solid">Solid</SelectItem>
                    <SelectItem value="outline">Outline</SelectItem>
                    <SelectItem value="gradient">Gradient</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ThumbnailTemplateCard;
