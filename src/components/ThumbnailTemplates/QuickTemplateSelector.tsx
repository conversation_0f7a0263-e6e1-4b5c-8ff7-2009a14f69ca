import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Palette, Zap, Minus, BarChart3 } from 'lucide-react';

interface ContentPillar {
  id: string;
  name: string;
  color: string;
}

interface QuickTemplateSelectorProps {
  pillars: ContentPillar[];
}

const QuickTemplateSelector = ({ pillars }: QuickTemplateSelectorProps) => {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('custom');

  const templates = [
    {
      id: 'bold',
      name: 'Bold',
      description: '8px solid borders',
      icon: <BarChart3 className="w-4 h-4" />,
      preview: 'border-4 border-teal'
    },
    {
      id: 'subtle',
      name: 'Subtle',
      description: 'Corner accents only',
      icon: <Zap className="w-4 h-4" />,
      preview: 'corner-accent'
    },
    {
      id: 'minimal',
      name: 'Minimal',
      description: 'Thin bottom bar',
      icon: <Minus className="w-4 h-4" />,
      preview: 'border-b-2 border-teal'
    },
    {
      id: 'custom',
      name: 'Custom',
      description: 'User defined',
      icon: <Palette className="w-4 h-4" />,
      preview: 'border-dashed border-2 border-gray-500'
    }
  ];

  const applyTemplate = (templateId: string) => {
    setSelectedTemplate(templateId);
    // In a real implementation, this would update all pillar templates
    console.log(`Applying ${templateId} template to all pillars`);
  };

  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader className="pb-3">
        <CardTitle className="text-white text-lg flex items-center space-x-2">
          <Palette className="w-5 h-5 text-teal" />
          <span>Quick Templates</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {templates.map((template) => (
            <Button
              key={template.id}
              variant={selectedTemplate === template.id ? "default" : "outline"}
              className={`h-auto p-3 flex flex-col items-center space-y-2 ${
                selectedTemplate === template.id 
                  ? 'bg-blue hover:bg-blue-dark text-white' 
                  : 'border-gray-500 text-white hover:bg-gray-700'
              }`}
              onClick={() => applyTemplate(template.id)}
            >
              <div className="flex items-center space-x-1">
                {template.icon}
                <span className="font-medium">{template.name}</span>
              </div>
              
              {/* Template preview */}
              <div className="w-full h-8 bg-gray-600 rounded relative overflow-hidden">
                <div className={`absolute inset-0 ${
                  template.id === 'bold' ? 'border-4 border-teal' :
                  template.id === 'subtle' ? 'bg-gradient-to-r from-teal/30 via-transparent to-teal/30' :
                  template.id === 'minimal' ? 'border-b-2 border-teal' :
                  'border-dashed border-2 border-gray-500'
                }`}>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-2 h-2 bg-gray-400 rounded"></div>
                  </div>
                </div>
              </div>
              
              <span className="text-xs text-gray-300 text-center">{template.description}</span>
              
              {selectedTemplate === template.id && (
                <Badge variant="outline" className="text-teal border-teal text-xs">
                  Active
                </Badge>
              )}
            </Button>
          ))}
        </div>
        
        {selectedTemplate !== 'custom' && (
          <div className="mt-4 pt-4 border-t border-gray-600">
            <Button 
              className="w-full bg-blue hover:bg-blue-dark text-white"
              onClick={() => applyTemplate(selectedTemplate)}
            >
              Apply {templates.find(t => t.id === selectedTemplate)?.name} to All Pillars
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default QuickTemplateSelector;
