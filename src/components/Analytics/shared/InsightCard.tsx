import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';

interface InsightCardProps {
  title: string;
  description: string;
  icon?: LucideIcon;
  type?: 'info' | 'warning' | 'success' | 'error';
  className?: string;
}

const InsightCard: React.FC<InsightCardProps> = ({
  title,
  description,
  icon: Icon,
  type = 'info',
  className = ''
}) => {
  const getTypeStyles = () => {
    switch (type) {
      case 'warning':
        return 'border-yellow-500/50 bg-yellow-500/10';
      case 'success':
        return 'border-green-500/50 bg-green-500/10';
      case 'error':
        return 'border-red-500/50 bg-red-500/10';
      default:
        return 'border-blue-500/50 bg-blue-500/10';
    }
  };

  const getIconColor = () => {
    switch (type) {
      case 'warning':
        return 'text-yellow-400';
      case 'success':
        return 'text-green-400';
      case 'error':
        return 'text-red-400';
      default:
        return 'text-blue-400';
    }
  };

  return (
    <Card className={`${getTypeStyles()} ${className}`}>
      <CardHeader className="flex flex-row items-center space-y-0 pb-2">
        {Icon && <Icon className={`h-4 w-4 mr-2 ${getIconColor()}`} />}
        <CardTitle className="text-sm font-medium text-white">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-300">{description}</p>
      </CardContent>
    </Card>
  );
};

export default InsightCard;
