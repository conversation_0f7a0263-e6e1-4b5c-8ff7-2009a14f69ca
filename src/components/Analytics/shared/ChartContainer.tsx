import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface ChartContainerProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  description?: string;
}

const ChartContainer: React.FC<ChartContainerProps> = ({
  title,
  children,
  className = '',
  description
}) => {
  return (
    <Card className={`bg-gray-800/30 border-gray-700 ${className}`}>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-white">{title}</CardTitle>
        {description && (
          <p className="text-sm text-gray-400">{description}</p>
        )}
      </CardHeader>
      <CardContent>
        <div className="w-full h-64">
          {children}
        </div>
      </CardContent>
    </Card>
  );
};

export default ChartContainer;
