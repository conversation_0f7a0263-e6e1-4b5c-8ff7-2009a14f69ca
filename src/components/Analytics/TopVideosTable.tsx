import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatNumber } from '@/lib/utils';

interface TopVideosTableProps {
  data: any;
}

const TopVideosTable: React.FC<TopVideosTableProps> = ({ data }) => {
  // If no data or no top videos, show a message
  if (!data || !data.topVideos || data.topVideos.length === 0) {
    return (
      <div className="text-center py-4 text-muted-foreground">
        No video data available
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Video Title</TableHead>
            <TableHead className="text-right">Views</TableHead>
            <TableHead className="text-right">Engagement</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.topVideos.map((video: any) => (
            <TableRow key={video.id}>
              <TableCell className="font-medium">{video.title}</TableCell>
              <TableCell className="text-right">{formatNumber(video.views)}</TableCell>
              <TableCell className="text-right">
                {video.engagement !== undefined ? `${video.engagement.toFixed(1)}%` : 'N/A'}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default TopVideosTable;
