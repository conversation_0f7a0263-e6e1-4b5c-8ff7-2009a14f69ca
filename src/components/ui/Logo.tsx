import React from 'react';

interface LogoProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showText?: boolean;
  textPosition?: 'right' | 'bottom';
  onClick?: () => void;
}

const Logo: React.FC<LogoProps> = ({
  size = 'md',
  className = '',
  showText = false,
  textPosition = 'right',
  onClick
}) => {
  const sizeClasses = {
    xs: 'w-6 h-6',
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  const logoElement = (
    <img
      src="/MCH_Icon_FullColor.svg"
      alt="MCH Logo - MyContentHub"
      className={`object-contain transition-opacity hover:opacity-80 ${sizeClasses[size]} ${className}`}
      loading="lazy"
    />
  );

  const textElement = showText && (
    <div className={textPosition === 'bottom' ? 'text-center' : ''}>
      <h1 className={`font-bold text-white ${size === 'xs' ? 'text-sm' : size === 'sm' ? 'text-base' : size === 'md' ? 'text-lg' : size === 'lg' ? 'text-xl' : 'text-2xl'}`}>
        MyContentHub
      </h1>
      <p className={`text-gray-400 ${size === 'xs' ? 'text-xs' : 'text-sm'}`}>
        YouTube Strategy Platform
      </p>
    </div>
  );

  if (onClick) {
    return (
      <button
        onClick={onClick}
        className={`flex ${textPosition === 'bottom' ? 'flex-col' : 'flex-row'} items-center ${textPosition === 'right' ? 'space-x-3' : textPosition === 'bottom' ? 'space-y-2' : ''} transition-opacity hover:opacity-80`}
      >
        {logoElement}
        {textElement}
      </button>
    );
  }

  return (
    <div className={`flex ${textPosition === 'bottom' ? 'flex-col' : 'flex-row'} items-center ${textPosition === 'right' ? 'space-x-3' : textPosition === 'bottom' ? 'space-y-2' : ''}`}>
      {logoElement}
      {textElement}
    </div>
  );
};

export default Logo; 