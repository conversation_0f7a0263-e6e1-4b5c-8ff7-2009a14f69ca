import React from 'react';
import { Link } from 'react-router-dom';

// Simple interfaces
export interface SimpleSidebarItem {
  title: string;
  path?: string;
}

export interface SimpleSidebarGroup {
  title: string;
  items: SimpleSidebarItem[];
}

interface SimpleSidebarProps {
  groups: SimpleSidebarGroup[];
}

// Simple component with named export
export function SimpleSidebar({ groups }: SimpleSidebarProps) {
  return (
    <aside className="w-64 h-full bg-gray-900">
      <div className="p-4">
        {groups.map((group) => (
          <div key={group.title} className="mb-4">
            <h3 className="text-sm text-gray-400 mb-2">{group.title}</h3>
            <div>
              {group.items.map(item => (
                <Link
                  key={item.title}
                  to={item.path || '#'}
                  className="block py-2 text-gray-300 hover:text-white"
                >
                  {item.title}
                </Link>
              ))}
            </div>
          </div>
        ))}
      </div>
    </aside>
  );
}

// Simple placeholder components with named exports
export const SimpleContent = ({ children }: { children: React.ReactNode }) => <div>{children}</div>;
export const SimpleFooter = ({ children }: { children: React.ReactNode }) => <div>{children}</div>;
export const SimpleHeader = ({ children }: { children: React.ReactNode }) => <div>{children}</div>;
export const SimpleInset = ({ children, className }: { children: React.ReactNode, className?: string }) => (
  <div className={className}>{children}</div>
);