import React from 'react';
import { AlertTriangle, AlertCircle, Info } from 'lucide-react';
import { cn } from '@/lib/utils';

type ErrorSeverity = 'error' | 'warning' | 'info';

interface ErrorMessageProps {
  title?: string;
  message: string;
  severity?: ErrorSeverity;
  className?: string;
  compact?: boolean;
}

export function ErrorMessage({
  title,
  message,
  severity = 'error',
  className,
  compact = false
}: ErrorMessageProps) {
  const icons = {
    error: <AlertTriangle className="w-5 h-5 text-red-500" />,
    warning: <AlertCircle className="w-5 h-5 text-amber-500" />,
    info: <Info className="w-5 h-5 text-blue-500" />
  };

  const bgColors = {
    error: 'bg-red-950/30 border-red-800/50',
    warning: 'bg-amber-950/30 border-amber-800/50',
    info: 'bg-blue-950/30 border-blue-800/50'
  };

  if (compact) {
    return (
      <div className={cn("flex items-center gap-2 text-sm", className)}>
        {icons[severity]}
        <span className="text-gray-300">{message}</span>
      </div>
    );
  }

  return (
    <div className={cn(`p-4 rounded-md border ${bgColors[severity]}`, className)}>
      <div className="flex items-start gap-3">
        {icons[severity]}
        <div>
          {title && <h4 className="font-medium text-white mb-1">{title}</h4>}
          <p className="text-gray-300 text-sm">{message}</p>
        </div>
      </div>
    </div>
  );
}