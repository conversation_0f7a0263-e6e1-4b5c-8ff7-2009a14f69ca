import React from 'react';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { LucideIcon } from 'lucide-react';

export interface TabItem {
  id: string;
  label: string;
  icon?: LucideIcon;
  count?: number;
}

interface TabNavigationProps {
  tabs: TabItem[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  variant?: 'default' | 'pills' | 'underlined';
  className?: string;
  contentClassName?: string;
  children: React.ReactNode;
}

export function TabNavigation({
  tabs,
  activeTab,
  onTabChange,
  variant = 'default',
  className = '',
  contentClassName = '',
  children
}: TabNavigationProps) {
  const variantClasses = {
    default: "bg-gray-700/50 rounded-lg p-1",
    pills: "glass-effect border border-gray-700",
    underlined: "border-b border-gray-700 pb-0"
  };

  const triggerClasses = {
    default: "data-[state=active]:bg-blue-600 data-[state=active]:text-white",
    pills: "data-[state=active]:bg-blue-600 rounded-md",
    underlined: "data-[state=active]:border-b-2 data-[state=active]:border-blue-500 rounded-none"
  };

  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className={`w-full ${className}`}>
      <TabsList className={`${variantClasses[variant]} w-full`}>
        {tabs.map((tab) => (
          <TabsTrigger 
            key={tab.id}
            value={tab.id} 
            className={`flex items-center gap-2 py-2 ${triggerClasses[variant]}`}
          >
            {tab.icon && <tab.icon className="w-4 h-4" />}
            {tab.label}
            {tab.count !== undefined && (
              <span className="ml-1 text-xs bg-gray-600 px-1.5 py-0.5 rounded-full">
                {tab.count}
              </span>
            )}
          </TabsTrigger>
        ))}
      </TabsList>
      
      <div className={`mt-6 ${contentClassName}`}>
        {children}
      </div>
    </Tabs>
  );
}