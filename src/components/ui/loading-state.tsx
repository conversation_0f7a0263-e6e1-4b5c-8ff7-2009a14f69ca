import React from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingStateProps {
  isLoading: boolean;
  children: React.ReactNode;
  variant?: 'overlay' | 'skeleton' | 'inline';
  text?: string;
  className?: string;
}

export function LoadingState({
  isLoading,
  children,
  variant = 'overlay',
  text = 'Loading...',
  className
}: LoadingStateProps) {
  if (!isLoading) {
    return <>{children}</>;
  }

  if (variant === 'inline') {
    return (
      <div className={cn("flex items-center justify-center py-8", className)}>
        <Loader2 className="w-6 h-6 text-blue-500 animate-spin mr-2" />
        <span className="text-gray-300">{text}</span>
      </div>
    );
  }

  if (variant === 'skeleton') {
    return (
      <div className={cn("space-y-3", className)}>
        <div className="h-6 bg-gray-700/50 rounded animate-pulse w-1/3"></div>
        <div className="h-4 bg-gray-700/50 rounded animate-pulse w-full"></div>
        <div className="h-4 bg-gray-700/50 rounded animate-pulse w-5/6"></div>
        <div className="h-4 bg-gray-700/50 rounded animate-pulse w-4/6"></div>
      </div>
    );
  }

  // Default overlay
  return (
    <div className="relative">
      {children}
      <div className="absolute inset-0 bg-gray-900/70 flex items-center justify-center rounded-md backdrop-blur-sm">
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-blue-500 animate-spin mx-auto mb-2" />
          <span className="text-gray-200 text-sm">{text}</span>
        </div>
      </div>
    </div>
  );
}