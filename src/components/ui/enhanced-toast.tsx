
import { toast as sonnerToast } from 'sonner'
import { CheckCircle, XCircle, AlertTriangle, Info, Loader2 } from 'lucide-react'

interface ToastOptions {
  title?: string
  description?: string
  action?: {
    label: string
    onClick: () => void
  }
  duration?: number
}

const createToast = (type: 'success' | 'error' | 'warning' | 'info' | 'loading', message: string, options?: ToastOptions) => {
  const icons = {
    success: CheckCircle,
    error: XCircle,
    warning: AlertTriangle,
    info: Info,
    loading: Loader2
  }

  const Icon = icons[type]
  const iconColors = {
    success: 'text-green-500',
    error: 'text-red-500',
    warning: 'text-yellow-500',
    info: 'text-teal-500',
    loading: 'text-gray-500'
  }

  return sonnerToast(message, {
    description: options?.description,
    icon: <Icon className={`h-4 w-4 ${iconColors[type]} ${type === 'loading' ? 'animate-spin' : ''}`} />,
    action: options?.action,
    duration: options?.duration || (type === 'loading' ? Infinity : 4000),
  })
}

export const enhancedToast = {
  success: (message: string, options?: ToastOptions) => 
    createToast('success', message, options),
  
  error: (message: string, options?: ToastOptions) => 
    createToast('error', message, options),
  
  warning: (message: string, options?: ToastOptions) => 
    createToast('warning', message, options),
  
  info: (message: string, options?: ToastOptions) => 
    createToast('info', message, options),
  
  loading: (message: string, options?: ToastOptions) => 
    createToast('loading', message, options),

  promise: <T,>(
    promise: Promise<T>,
    {
      loading: loadingMessage,
      success: successMessage,
      error: errorMessage,
    }: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
    }
  ) => {
    return sonnerToast.promise(promise, {
      loading: loadingMessage,
      success: successMessage,
      error: errorMessage,
    })
  },

  dismiss: (toastId?: string | number) => sonnerToast.dismiss(toastId)
}
