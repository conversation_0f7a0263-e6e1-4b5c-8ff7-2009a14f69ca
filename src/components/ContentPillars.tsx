
import React, { useState, useEffect } from 'react';
import { Target, Plus, Edit, Trash2, TrendingUp, Eye } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useUser } from '@supabase/auth-helpers-react';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL! as string;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY! as string;
const supabase = createClient(supabaseUrl, supabaseKey);

const ContentPillars = () => {
  const [activeTab, setActiveTab] = useState("strategy");
  const [pillars, setPillars] = useState([]);
  const { user } = useUser();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newPillar, setNewPillar] = useState({ name: '', description: '', targetPercentage: 0 });

  useEffect(() => {
    const fetchPillarsData = async () => {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from('content_pillars')
          .select('*')
          .eq('user_id', user.id);
          
        if (error) throw error;
        
        // Get video counts for each pillar
        const { data: videos, error: videosError } = await supabase
          .from('videos')
          .select('id, pillar_id, views, status')
          .eq('user_id', user.id)
          .eq('status', 'published')
          .not('youtube_video_id', 'is', null);
          
        if (videosError) throw videosError;
        
        // Add video counts to pillars
        const pillarsWithCounts = data.map(pillar => {
          const pillarVideos = videos.filter(v => v.pillar_id === pillar.id);
          const videoCount = pillarVideos.length;
          const totalVideos = videos.length;
          const actualPercentage = totalVideos > 0 ? Math.round((videoCount / totalVideos) * 100) : 0;
          
          return {
            ...pillar,
            videoCount: videoCount,
            actualPercentage: actualPercentage
          };
        });
        
        setPillars(pillarsWithCounts);
      } catch (error) {
        console.error('Error fetching pillars:', error);
      }
    };
    
    fetchPillarsData();
  }, [user]);

  const pieData = pillars.map(pillar => ({
    name: pillar.name,
    value: pillar.actualPercentage,
    color: pillar.color
  }));

  const performanceData = pillars.map(pillar => ({
    name: pillar.name,
    target: pillar.targetPercentage,
    actual: pillar.actualPercentage,
    videos: pillar.videoCount
  }));

  const addPillar = () => {
    if (newPillar.name && newPillar.description) {
      const colors = ['#37BEB0', '#E76F51', '#FF7B25', '#F59F0A', '#8B5CF6', '#06B6D4'];
      setPillars([...pillars, {
        id: Date.now(),
        ...newPillar,
        actualPercentage: 0,
        videoCount: 0,
        color: colors[pillars.length % colors.length]
      }]);
      setNewPillar({ name: '', description: '', targetPercentage: 0 });
      setIsDialogOpen(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-white">Content Pillars</h1>
          <p className="text-gray-400 mt-1">Organize your content strategy around key themes</p>
        </div>
      </div>

      {/* Tabs Navigation */}
      <Tabs defaultValue="strategy" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="bg-[#6b46c1] border-[#5b35b5] border p-1">
          <TabsTrigger value="strategy" className="data-[state=active]:bg-[#5b35b5] data-[state=active]:text-white">
            Strategy Overview
          </TabsTrigger>
          <TabsTrigger value="published" className="data-[state=active]:bg-[#5b35b5] data-[state=active]:text-white">
            Published Content
          </TabsTrigger>
          <TabsTrigger value="assignment" className="data-[state=active]:bg-[#5b35b5] data-[state=active]:text-white">
            Needs Assignment
          </TabsTrigger>
          <TabsTrigger value="schedule" className="data-[state=active]:bg-[#5b35b5] data-[state=active]:text-white">
            Optimal Schedule
          </TabsTrigger>
        </TabsList>
      </Tabs>

      <TabsContent value="strategy" className="mt-6">
        {/* Strategy Overview Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <div className="bg-[#6b46c1] border-[#5b35b5] rounded-lg border lg:col-span-2">
            <CardHeader>
              <div className="flex items-center gap-2">
                <Target className="w-5 h-5 text-teal" />
                <CardTitle className="text-white">Strategy Recommendations Available</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 mb-4">
                Get personalized insights about your content strategy, pillar balance, and growth
                opportunities based on your current performance data.
              </p>
              <Button className="glass-button bg-teal/20 hover:bg-teal/30 text-white border-teal/30">
                <Eye className="w-4 h-4 mr-2" />
                View Pillar Insights
              </Button>
            </CardContent>
          </div>
          <div className="bg-[#6b46c1] border-[#5b35b5] rounded-lg border">
            <CardHeader>
              <CardTitle className="text-white">Content Balance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {performanceData.map((item, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: pieData[index]?.color }}></div>
                      <div className="flex justify-between w-full">
                        <span className="text-sm font-medium">{item.name}</span>
                        <span className="text-sm text-gray-400">{item.actual}% / {item.target}%</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex-1">
                        <Progress value={item.actual} max={100} className="h-2" />
                      </div>
                      <span className={`text-xs font-medium ${
                        Math.abs(item.actual - item.target) <= 5 
                          ? 'text-green-400' 
                          : item.actual > item.target 
                            ? 'text-yellow-400' 
                            : 'text-red-400'
                      }`}>
                        {item.actual > item.target 
                          ? `${item.actual - item.target}% Over` 
                          : Math.abs(item.actual - item.target) <= 5 
                            ? 'On Target' 
                            : `${item.target - item.actual}% Under`}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </div>
        </div>

        {/* Pillar Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {pillars.map(pillar => {
            const variance = pillar.actualPercentage - pillar.targetPercentage;
            const isUnder = variance < 0;
            
            return (
              <Card key={pillar.id} className="bg-[#6b46c1] border-[#5b35b5] rounded-lg border">
                <CardHeader className="pb-2">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full" style={{ backgroundColor: pillar.color }}></div>
                    <CardTitle className="text-white text-lg">{pillar.name}</CardTitle>
                  </div>
                  <p className="text-sm text-gray-400">Target: {pillar.targetPercentage}% of content</p>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{pillar.actualPercentage}% / {pillar.targetPercentage}%</span>
                    </div>
                    <Progress value={pillar.actualPercentage} max={pillar.targetPercentage > 0 ? pillar.targetPercentage : 100} className="h-2" />
                    <div className="flex justify-between items-center">
                      <span className={`text-sm font-medium ${isUnder ? 'text-yellow-400' : 'text-green-400'}`}>
                        {isUnder ? `${Math.abs(variance)}% Under` : `${variance}% Over`}
                      </span>
                      <span className="text-sm text-gray-400">{pillar.videoCount} videos</span>
                    </div>
                    <p className="text-sm text-gray-400">
                      {isUnder ? 'Need more content in this pillar.' : 'Great balance! Keep it up.'}
                    </p>
                    <p className="text-sm text-gray-400">
                      Avg views: {pillar.avgViews || 0}
                    </p>
                  </div>
                  <div className="mt-4 flex justify-between">
                    <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                      <Eye className="w-4 h-4 mr-1" />
                      View Videos
                    </Button>
                    <div className="flex space-x-1">
                      <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white p-1">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white p-1">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
          
          {/* Add New Pillar Card */}
          <Card className="bg-[#6b46c1] border-[#5b35b5] rounded-lg border flex flex-col items-center justify-center p-6">
            <div className="text-center space-y-4">
              <div className="bg-blue-500/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto">
                <Plus className="w-6 h-6 text-blue-400" />
              </div>
              <h3 className="text-lg font-medium text-white">Add New Pillar</h3>
              <p className="text-sm text-gray-400">
                {pillars.length}/unlimited pillars (ai_pro)
              </p>
              <Button className="bg-blue-500 hover:bg-blue-600 text-white">
                <Plus className="w-4 h-4 mr-2" />
                Create Pillar
              </Button>
            </div>
          </Card>
        </div>
      </TabsContent>

      {/* Tab content containers */}
      <TabsContent value="published" className="mt-6">
        <div className="bg-[#6b46c1] border-[#5b35b5] rounded-lg border p-8 text-center">
          <h3 className="text-white text-xl mb-2">Published Content</h3>
          <p className="text-gray-400">View and manage your published content across pillars</p>
        </div>
      </TabsContent>

      <TabsContent value="assignment" className="mt-6">
        <div className="bg-[#6b46c1] border-[#5b35b5] rounded-lg border p-8 text-center">
          <h3 className="text-white text-xl mb-2">Needs Assignment</h3>
          <p className="text-gray-400">Content that needs to be assigned to pillars</p>
        </div>
      </TabsContent>

      <TabsContent value="schedule" className="mt-6">
        <div className="bg-[#6b46c1] border-[#5b35b5] rounded-lg border p-8 text-center">
          <h3 className="text-white text-xl mb-2">Optimal Schedule</h3>
          <p className="text-gray-400">Recommended publishing schedule for balanced content</p>
        </div>
      </TabsContent>
    </div>
  );
};

export default ContentPillars;
