
import React from 'react';
import { TooltipProvider } from '@/components/ui/tooltip';
import { useAuth } from '@/hooks/useAuth';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
} from '@/components/ui/sidebar';
import Sidebar<PERSON>ogo from '@/components/Layout/SidebarLogo';
import SidebarNavigation from '@/components/Layout/SidebarNavigation';
import SidebarTierIndicator from '@/components/Layout/SidebarTierIndicator';
import SidebarYouTubeStatus from '@/components/Layout/SidebarYouTubeStatus';

import SidebarUserSection from '@/components/Layout/SidebarUserSection';
import SidebarBackground from '@/components/Layout/SidebarBackground';
import { supabase } from '@/lib/supabase';

export function AppSidebar() {
  const { user } = useAuth();
  const [userProfile, setUserProfile] = React.useState<any>(null);

  React.useEffect(() => {
    if (user) {
      const fetchProfile = async () => {
        const { data } = await supabase
          .from('users')
          .select('youtube_channel_id, youtube_channel_name')
          .eq('id', user.id)
          .single();
        setUserProfile(data);
      };
      fetchProfile();
    }
  }, [user]);

  return (
    <TooltipProvider>
      <div className="sidebar-container flex flex-col relative overflow-hidden shadow-2xl bg-transparent">
        {/* Sidebar Background */}
        <SidebarBackground />

        {/* Logo Section */}
        <div className="bg-transparent p-6 relative z-10">
          <SidebarLogo />
        </div>

        {/* Content Section */}
        <div className="bg-transparent relative z-10 flex-1 overflow-y-auto px-4">
          {/* Tier Indicator */}
          <div className="py-3">
            <SidebarTierIndicator />
          </div>

          {/* YouTube Connection Status */}
          <div className="py-6">
            <SidebarYouTubeStatus />
          </div>

          {/* Navigation */}
          <div className="pb-6">
            <SidebarNavigation />
          </div>
        </div>

        {/* Footer Section */}
        <div className="bg-transparent relative z-10 mt-auto p-4">
          <SidebarUserSection user={user} />
        </div>
      </div>
    </TooltipProvider>
  );
}
