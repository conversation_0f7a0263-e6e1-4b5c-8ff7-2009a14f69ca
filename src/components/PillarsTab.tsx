import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger, Ta<PERSON>Content } from '@/components/ui/tabs';

interface PillarsTabProps {
  activeTab: string;
  setActiveTab: (value: string) => void;
  children: React.ReactNode;
}

const PillarsTab: React.FC<PillarsTabProps> = ({ activeTab, setActiveTab, children }) => {
  return (
    <Tabs defaultValue="strategy" value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="glass-effect border border-gray-700">
        <TabsTrigger value="strategy" className="data-[state=active]:bg-blue-600">
          Strategy Overview
        </TabsTrigger>
        <TabsTrigger value="published" className="data-[state=active]:bg-blue-600">
          Published Content
        </TabsTrigger>
        <TabsTrigger value="assignment" className="data-[state=active]:bg-blue-600">
          Needs Assignment
        </TabsTrigger>
        <TabsTrigger value="schedule" className="data-[state=active]:bg-blue-600">
          Optimal Schedule
        </TabsTrigger>
      </TabsList>
      
      {children}
    </Tabs>
  );
};

export default PillarsTab;