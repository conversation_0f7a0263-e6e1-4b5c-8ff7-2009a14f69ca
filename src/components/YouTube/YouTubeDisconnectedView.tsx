
import React from 'react';
import { Youtube, AlertTriangle, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface YouTubeDisconnectedViewProps {
  isConnecting: boolean;
  onOpenWizard: () => void;
  onQuickConnect: () => void;
  hasChannel?: boolean;
  channelName?: string;
  tokenError?: boolean;
}

const YouTubeDisconnectedView = ({ 
  isConnecting, 
  onOpenWizard, 
  onQuickConnect,
  hasChannel = false,
  channelName,
  tokenError = false
}: YouTubeDisconnectedViewProps) => {
  if (hasChannel && tokenError) {
    return (
      <div className="space-y-4">
        <div className="p-4 bg-orange/10 border border-orange/30 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <AlertTriangle className="w-5 h-5 text-orange" />
            <span className="text-orange font-medium">Connection Expired</span>
          </div>
          <p className="text-gray-300 text-sm mb-3">
            Your YouTube connection has expired. Click "Reconnect YouTube" above to restore access to your channel.
          </p>
          {channelName && (
            <p className="text-gray-400 text-xs">
              Previously connected: {channelName}
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-center py-6">
        <Youtube className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h4 className="text-white font-medium mb-2">No YouTube Channel Connected</h4>
        <p className="text-gray-400 text-sm mb-6">
          Connect your YouTube channel to access powerful analytics, content insights, and automated data syncing.
        </p>
        
        <div className="space-y-3">
          <Button
            onClick={onQuickConnect}
            disabled={isConnecting}
            className="w-full bg-teal hover:bg-teal/90 text-white"
          >
            <Youtube className="w-4 h-4 mr-2" />
            {isConnecting ? 'Connecting...' : 'Quick Connect'}
            <Zap className="w-4 h-4 ml-2" />
          </Button>
          
          <Button
            onClick={onOpenWizard}
            disabled={isConnecting}
            variant="outline"
            className="w-full border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            Learn More & Connect
          </Button>
        </div>
      </div>

      <div className="bg-teal/10 border border-teal/30 rounded-lg p-4">
        <h5 className="text-teal font-medium mb-2">🚀 What you'll unlock:</h5>
        <ul className="text-gray-300 text-sm space-y-1">
          <li>• Automatic video data sync</li>
          <li>• Performance analytics by content pillar</li>
          <li>• Content strategy insights</li>
          <li>• Subscriber growth tracking</li>
        </ul>
      </div>
    </div>
  );
};

export default YouTubeDisconnectedView;
