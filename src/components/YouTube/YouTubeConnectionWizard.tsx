
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Youtube, CheckCircle, Shield, BarChart3, Lightbulb, ArrowRight, ExternalLink } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

interface YouTubeConnectionWizardProps {
  isOpen: boolean;
  onClose: () => void;
  onConnect: () => void;
}

const YouTubeConnectionWizard = ({ isOpen, onClose, onConnect }: YouTubeConnectionWizardProps) => {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  const steps = [
    {
      title: "Why Connect YouTube?",
      content: (
        <div className="space-y-6">
          <div className="text-center">
            <Youtube className="w-16 h-16 mx-auto text-red-500 mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">
              Unlock Powerful Content Insights
            </h3>
            <p className="text-gray-300">
              Connect your YouTube channel to transform your content strategy with data-driven insights.
            </p>
          </div>
          
          <div className="grid gap-4">
            <div className="flex items-start space-x-3 p-4 bg-gray-800/50 rounded-lg">
              <BarChart3 className="w-6 h-6 text-teal mt-1" />
              <div>
                <h4 className="font-medium text-white">Performance Analytics</h4>
                <p className="text-sm text-gray-300">Track which content pillars drive the most engagement and growth</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 p-4 bg-gray-800/50 rounded-lg">
              <Lightbulb className="w-6 h-6 text-teal mt-1" />
              <div>
                <h4 className="font-medium text-white">Smart Recommendations</h4>
                <p className="text-sm text-gray-300">Get AI-powered content suggestions based on your best-performing videos</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 p-4 bg-gray-800/50 rounded-lg">
              <CheckCircle className="w-6 h-6 text-teal mt-1" />
              <div>
                <h4 className="font-medium text-white">Automatic Organization</h4>
                <p className="text-sm text-gray-300">Import and categorize all your videos by content pillar instantly</p>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "Privacy & Permissions",
      content: (
        <div className="space-y-6">
          <div className="text-center">
            <Shield className="w-16 h-16 mx-auto text-green-400 mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">
              Your Data is Safe
            </h3>
            <p className="text-gray-300">
              We only request the minimum permissions needed and never modify your content.
            </p>
          </div>
          
          <Card className="bg-gray-800/50 border-gray-600">
            <CardContent className="p-4">
              <h4 className="font-medium text-white mb-3">What we'll access:</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm text-gray-300">Basic channel information (name, subscriber count)</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm text-gray-300">Video metadata (titles, views, upload dates)</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm text-gray-300">Analytics data (performance metrics)</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-red-900/20 border-red-500/30">
            <CardContent className="p-4">
              <h4 className="font-medium text-red-400 mb-2">What we'll never do:</h4>
              <div className="space-y-1 text-sm text-gray-300">
                <p>• Modify or delete your videos</p>
                <p>• Post content on your behalf</p>
                <p>• Share your data with third parties</p>
                <p>• Access private information</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    },
    {
      title: "Ready to Connect",
      content: (
        <div className="space-y-6">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto bg-teal/20 rounded-full flex items-center justify-center mb-4">
              <Youtube className="w-8 h-8 text-teal" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              Connect Your Channel
            </h3>
            <p className="text-gray-300">
              Click below to securely connect your YouTube channel through Google's OAuth system.
            </p>
          </div>
          
          <div className="bg-teal/10 border border-teal/30 rounded-lg p-4">
            <h4 className="font-medium text-teal mb-2">What happens next:</h4>
            <div className="space-y-2 text-sm text-gray-300">
              <p>1. You'll be redirected to Google for authorization</p>
              <p>2. We'll import your channel data and videos</p>
              <p>3. Your content will be automatically organized by pillars</p>
              <p>4. You'll see personalized insights on your dashboard</p>
            </div>
          </div>
          
          <Button
            onClick={onConnect}
            className="w-full bg-teal hover:bg-teal/90 text-white py-3 text-lg font-medium"
          >
            <Youtube className="w-5 h-5 mr-2" />
            Connect YouTube Channel
            <ExternalLink className="w-4 h-4 ml-2" />
          </Button>
        </div>
      )
    }
  ];

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl bg-gray-900 border-gray-600">
        <DialogHeader>
          <DialogTitle className="text-white text-center">
            {steps[currentStep - 1].title}
          </DialogTitle>
        </DialogHeader>
        
        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex items-center justify-between text-sm text-gray-400 mb-2">
            <span>Step {currentStep} of {totalSteps}</span>
            <span>{Math.round((currentStep / totalSteps) * 100)}% complete</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-teal h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>
        
        {/* Step Content */}
        <div className="min-h-[400px]">
          {steps[currentStep - 1].content}
        </div>
        
        {/* Navigation */}
        <div className="flex justify-between pt-6 border-t border-gray-600">
          <Button
            variant="ghost"
            onClick={currentStep === 1 ? onClose : handlePrev}
            className="text-gray-300 hover:text-white"
          >
            {currentStep === 1 ? 'Cancel' : 'Previous'}
          </Button>
          
          {currentStep < totalSteps && (
            <Button
              onClick={handleNext}
              className="bg-teal hover:bg-teal/90 text-white"
            >
              Next
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default YouTubeConnectionWizard;
