
import React from 'react';
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import VideoRow from './VideoRow';
import { ImportedVideo } from './types';

interface VideoTableProps {
  videos: ImportedVideo[];
  pillars: any[];
  onPillarChange: (videoId: string, pillarId: string) => void;
  onSaveVideos: () => void;
  getSuggestedPillarName: (pillarId: string) => string;
  isImporting: boolean;
}

const VideoTable = ({ 
  videos, 
  pillars, 
  onPillarChange, 
  onSaveVideos, 
  getSuggestedPillarName, 
  isImporting 
}: VideoTableProps) => {
  const assignedCount = videos.filter(v => v.selectedPillar).length;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <p className="text-sm text-gray-300">
          {videos.length} videos imported. {assignedCount} assigned to pillars.
        </p>
        <Button
          onClick={onSaveVideos}
          disabled={isImporting || assignedCount === 0}
          className="bg-teal hover:bg-teal/90 text-white"
        >
          {isImporting ? 'Saving...' : `Save ${assignedCount} Videos`}
        </Button>
      </div>

      {videos.length === 0 ? (
        <div className="text-center py-8 text-gray-400">
          <p>No videos found to import.</p>
          <p className="text-sm mt-1">Make sure your YouTube channel has published videos.</p>
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-white">Video</TableHead>
              <TableHead className="text-white">Performance</TableHead>
              <TableHead className="text-white">Suggested Pillar</TableHead>
              <TableHead className="text-white">Assign to Pillar</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {videos.map((video) => (
              <VideoRow
                key={video.id}
                video={video}
                pillars={pillars}
                onPillarChange={onPillarChange}
                getSuggestedPillarName={getSuggestedPillarName}
              />
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
};

export default VideoTable;
