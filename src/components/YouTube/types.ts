
export interface ImportedVideo {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  publishedAt: string;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  suggestedPillar?: string;
  selectedPillar?: string;
}

export interface VideoTableProps {
  videos: ImportedVideo[];
  pillars: any[];
  onPillarChange: (videoId: string, pillarId: string) => void;
  onSkipVideo: (videoId: string) => void;
}
