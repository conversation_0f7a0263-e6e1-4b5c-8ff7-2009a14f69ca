
import React from 'react';
import { CheckCircle, Zap } from 'lucide-react';

interface YouTubeConnectedViewProps {
  user: any;
  isDisconnecting: boolean;
  onDisconnect: () => void;
}

const YouTubeConnectedView = ({ user, isDisconnecting, onDisconnect }: YouTubeConnectedViewProps) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <CheckCircle className="w-5 h-5 text-green-400" />
        <span className="text-white font-medium">Connected to: {user.youtube_channel_name}</span>
      </div>
      
      {user.youtube_thumbnail_url && (
        <div className="flex items-center space-x-3 p-4 bg-gray-800/50 rounded-lg border border-gray-700">
          <img 
            src={user.youtube_thumbnail_url} 
            alt="Channel thumbnail"
            className="w-12 h-12 rounded-full"
          />
          <div className="flex-1">
            <div className="text-sm text-gray-300">
              <p className="font-medium text-white">{user.youtube_channel_name}</p>
              <p>Subscribers: {user.youtube_subscriber_baseline?.toLocaleString() || 'Syncing...'}</p>
              <p className="text-xs text-gray-400">
                Last sync: {user.last_youtube_sync ? new Date(user.last_youtube_sync).toLocaleDateString() : 'Never'}
              </p>
            </div>
          </div>
          <Zap className="w-5 h-5 text-teal" />
        </div>
      )}
      
      <div className="bg-teal/10 border border-teal/30 rounded-lg p-4">
        <p className="text-teal text-sm font-medium mb-2">✨ Your data is syncing!</p>
        <p className="text-gray-300 text-xs">
          Video data and analytics will be automatically updated. Check your dashboard for insights.
        </p>
      </div>
    </div>
  );
};

export default YouTubeConnectedView;
