
import React from 'react';
import { Youtube, CheckCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface YouTubeConnectionStatusProps {
  isConnected: boolean;
}

const YouTubeConnectionStatus = ({ isConnected }: YouTubeConnectionStatusProps) => {
  return (
    <div className="flex items-center justify-between text-white">
      <div className="flex items-center">
        <Youtube className="w-5 h-5 mr-2 text-red-500" />
        YouTube Integration
      </div>
      {isConnected && (
        <Badge className="bg-green-500 text-white">
          <CheckCircle className="w-3 h-3 mr-1" />
          Connected
        </Badge>
      )}
    </div>
  );
};

export default YouTubeConnectionStatus;
