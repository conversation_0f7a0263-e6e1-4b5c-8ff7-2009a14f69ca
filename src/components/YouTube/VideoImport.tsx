
import React from 'react';
import { Download, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Lightbulb, Youtube } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useVideoImport } from './hooks/useVideoImport';

interface VideoImportProps {
  user: any;
}

const VideoImport = ({ user }: VideoImportProps) => {
  const {
    videos,
    pillars,
    isImporting,
    isLoading,
    importProgress,
    handleImportVideos,
    handlePillarChange,
    handleSkipVideo,
    handleSaveVideos,
    getSuggestedPillarName,
    getAssignedCount
  } = useVideoImport(user);

  const hasYouTubeConnection = user?.youtube_channel_id && user?.youtube_access_token;

  if (!hasYouTubeConnection) {
    return null; // YouTube connection component handles this state
  }

  if (videos.length === 0) {
    return (
      <Card className="dashboard-card">
        <CardHeader>
          <CardTitle className="flex items-center text-white">
            <Download className="w-5 h-5 mr-2 text-teal" />
            Import Your Videos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-teal/10 border border-teal/20 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Lightbulb className="w-5 h-5 text-teal mt-0.5" />
                <div>
                  <h4 className="text-white font-medium mb-1">Get Instant Insights</h4>
                  <p className="text-gray-300 text-sm">
                    Import your YouTube videos to see which content pillars are driving the most growth. 
                    Our AI will automatically categorize your videos based on titles and descriptions.
                  </p>
                </div>
              </div>
            </div>
            
            {pillars.length === 0 ? (
              <div className="bg-orange/10 border border-orange/20 rounded-lg p-4">
                <p className="text-orange text-sm">
                  Please create content pillars first before importing videos.
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                <p className="text-gray-300 text-sm">
                  Ready to import up to 50 of your most recent videos from <strong>{user.youtube_channel_name}</strong>
                </p>
                <Button
                  onClick={handleImportVideos}
                  disabled={isLoading}
                  className="bg-teal hover:bg-teal/90 text-white w-full"
                >
                  <Youtube className="w-4 h-4 mr-2" />
                  {isLoading ? 'Importing Videos...' : 'Import Videos from YouTube'}
                </Button>
                
                {isLoading && (
                  <div className="space-y-2">
                    <Progress value={importProgress} className="h-2" />
                    <p className="text-xs text-gray-400 text-center">
                      {importProgress < 25 && 'Connecting to YouTube...'}
                      {importProgress >= 25 && importProgress < 50 && 'Fetching your videos...'}
                      {importProgress >= 50 && importProgress < 75 && 'Analyzing content...'}
                      {importProgress >= 75 && 'Almost done...'}
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="dashboard-card">
      <CardHeader>
        <CardTitle className="flex items-center justify-between text-white">
          <div className="flex items-center">
            <CheckCircle className="w-5 h-5 mr-2 text-green-400" />
            Review Video Import
          </div>
          <Badge variant="secondary" className="bg-teal/20 text-teal">
            {getAssignedCount()}/{videos.length} assigned
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-600">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-white font-medium">Videos Ready for Import</h4>
            <div className="flex space-x-2">
              <Button
                onClick={handleSaveVideos}
                disabled={isImporting || getAssignedCount() === 0}
                className="bg-teal hover:bg-teal/90 text-white"
              >
                {isImporting ? 'Importing...' : `Import ${getAssignedCount()} Videos`}
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
          <p className="text-gray-300 text-sm mb-4">
            Our AI has suggested content pillars for each video. Review and adjust assignments before importing.
          </p>
        </div>

        <div className="space-y-3 max-h-96 overflow-y-auto">
          {videos.map((video) => (
            <div
              key={video.id}
              className="bg-gray-800/30 rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors"
            >
              <div className="flex items-start space-x-4">
                <img
                  src={video.thumbnailUrl}
                  alt={video.title}
                  className="w-20 h-15 rounded object-cover flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <h4 className="text-white font-medium text-sm truncate mb-1">
                    {video.title}
                  </h4>
                  <div className="flex items-center space-x-4 text-xs text-gray-400 mb-3">
                    <span>{video.viewCount.toLocaleString()} views</span>
                    <span>{new Date(video.publishedAt).toLocaleDateString()}</span>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="flex-1">
                      <Select
                        value={video.selectedPillar || ''}
                        onValueChange={(value) => handlePillarChange(video.id, value)}
                      >
                        <SelectTrigger className="h-8 text-xs bg-gray-700 border-gray-600">
                          <SelectValue placeholder="Select pillar..." />
                        </SelectTrigger>
                        <SelectContent>
                          {pillars.map((pillar) => (
                            <SelectItem key={pillar.id} value={pillar.id} className="text-xs">
                              <div className="flex items-center space-x-2">
                                <div
                                  className="w-2 h-2 rounded-full"
                                  style={{ backgroundColor: pillar.color }}
                                />
                                <span>{pillar.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {video.suggestedPillar && (
                      <Badge variant="outline" className="text-xs border-teal/30 text-teal">
                        AI: {getSuggestedPillarName(video.suggestedPillar)}
                      </Badge>
                    )}
                    
                    <Button
                      onClick={() => handleSkipVideo(video.id)}
                      variant="ghost"
                      size="sm"
                      className="h-8 px-2 text-gray-400 hover:text-white"
                    >
                      <SkipForward className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {videos.length > 0 && (
          <div className="text-center">
            <p className="text-xs text-gray-400">
              After importing, you'll be redirected to see your content pillar distribution
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VideoImport;
