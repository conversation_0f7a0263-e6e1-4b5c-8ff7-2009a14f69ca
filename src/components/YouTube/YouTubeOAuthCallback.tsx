
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

const YouTubeOAuthCallback = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    const handleOAuthCallback = async () => {
      try {
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');

        if (error) {
          throw new Error(`OAuth error: ${error}`);
        }

        if (!code || !state) {
          throw new Error('Missing authorization code or state');
        }

        const stateData = JSON.parse(decodeURIComponent(state));
        const userId = stateData.userId;

        // Exchange code for tokens (this would typically be done on the backend)
        // For demo purposes, we'll simulate successful connection
        console.log('Processing OAuth callback with code:', code);
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Mock successful YouTube API response
        const mockChannelData = {
          youtube_channel_id: 'UC_mock_channel_id_123',
          youtube_access_token: 'mock_access_token_from_oauth',
          youtube_refresh_token: 'mock_refresh_token_from_oauth',
          youtube_channel_name: 'My Awesome YouTube Channel',
          youtube_subscriber_baseline: 15750,
          youtube_thumbnail_url: 'https://images.unsplash.com/photo-1611605698335-8b1569810432?w=40&h=40&fit=crop&crop=face',
          last_youtube_sync: new Date().toISOString()
        };

        const { error: updateError } = await supabase
          .from('users')
          .update(mockChannelData)
          .eq('id', userId);

        if (updateError) throw updateError;

        toast.success('YouTube channel connected successfully! 🎉');
        navigate('/dashboard');
      } catch (error) {
        console.error('OAuth callback error:', error);
        toast.error('Failed to connect YouTube channel. Please try again.');
        navigate('/dashboard');
      } finally {
        setIsProcessing(false);
      }
    };

    handleOAuthCallback();
  }, [searchParams, navigate]);

  if (isProcessing) {
    return (
      <div className="min-h-screen bg-dark-gray flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-teal border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-white mb-2">Connecting YouTube Channel...</h2>
          <p className="text-gray-300">Please wait while we set up your connection</p>
        </div>
      </div>
    );
  }

  return null;
};

export default YouTubeOAuthCallback;
