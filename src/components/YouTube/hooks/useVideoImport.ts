
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { youtubeApiService } from '@/services/youtubeApiService';
import { ImportedVideo } from '../types';

export const useVideoImport = (user: any) => {
  const [videos, setVideos] = useState<ImportedVideo[]>([]);
  const [pillars, setPillars] = useState<any[]>([]);
  const [isImporting, setIsImporting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [importProgress, setImportProgress] = useState(0);

  useEffect(() => {
    fetchPillars();
  }, []);

  const fetchPillars = async () => {
    try {
      const { data, error } = await supabase
        .from('content_pillars')
        .select('*')
        .eq('user_id', user.id);

      if (error) throw error;
      setPillars(data || []);
    } catch (error) {
      console.error('Error fetching pillars:', error);
    }
  };

  const handleImportVideos = async () => {
    if (!user?.youtube_access_token || !user?.youtube_channel_id) {
      toast.error('Please connect your YouTube account first');
      return;
    }

    if (pillars.length === 0) {
      toast.error('Please create content pillars first');
      return;
    }

    setIsLoading(true);
    setImportProgress(0);

    try {
      console.log('Fetching videos from YouTube API...');
      setImportProgress(25);

      const fetchedVideos = await youtubeApiService.getVideos(
        user.youtube_access_token,
        user.youtube_channel_id
      );

      console.log(`Fetched ${fetchedVideos.length} videos`);
      setImportProgress(50);

      // Generate smart pillar suggestions
      const videosWithSuggestions = fetchedVideos.map(video => {
        const suggestedPillar = youtubeApiService.suggestPillar(video, pillars);
        return {
          ...video,
          suggestedPillar,
          selectedPillar: suggestedPillar // Default to suggested pillar
        };
      });

      setImportProgress(75);
      setVideos(videosWithSuggestions);
      setImportProgress(100);

      if (fetchedVideos.length === 0) {
        toast.info('No videos found on your YouTube channel. Make sure your channel has published videos.');
      } else {
        toast.success(`Found ${fetchedVideos.length} videos from your channel! Review the pillar assignments below.`);
      }
    } catch (error) {
      console.error('Error importing videos:', error);
      toast.error('Failed to import videos. Please check your YouTube connection and try again.');
    } finally {
      setIsLoading(false);
      setImportProgress(0);
    }
  };

  const handlePillarChange = (videoId: string, pillarId: string) => {
    setVideos(videos.map(video => 
      video.id === videoId ? { ...video, selectedPillar: pillarId } : video
    ));
  };

  const handleSkipVideo = (videoId: string) => {
    setVideos(videos.filter(video => video.id !== videoId));
  };

  const handleSaveVideos = async () => {
    const videosToSave = videos.filter(video => video.selectedPillar);
    
    if (videosToSave.length === 0) {
      toast.error('Please select at least one video to import');
      return;
    }

    setIsImporting(true);
    let imported = 0;

    try {
      // Import videos in smaller batches to avoid timeouts
      const batchSize = 10;
      for (let i = 0; i < videosToSave.length; i += batchSize) {
        const batch = videosToSave.slice(i, i + batchSize);
        
        const videosToInsert = batch.map(video => ({
          user_id: user.id,
          title: video.title,
          description: video.description,
          youtube_video_id: video.id,
          youtube_thumbnail_url: video.thumbnailUrl,
          views: video.viewCount,
          like_count: video.likeCount,
          comment_count: video.commentCount,
          published_at: video.publishedAt,
          pillar_id: video.selectedPillar,
          status: 'published'
        }));

        const { error } = await supabase
          .from('videos')
          .insert(videosToInsert);

        if (error) {
          console.error('Batch insert error:', error);
          throw error;
        }

        imported += batch.length;
        console.log(`Imported ${imported}/${videosToSave.length} videos`);
      }

      toast.success(`Successfully imported ${imported} videos! 🎉`);
      setVideos([]);
      
      // Small delay before redirecting
      setTimeout(() => {
        window.location.href = '/pillars';
      }, 2000);
      
    } catch (error) {
      console.error('Error saving videos:', error);
      toast.error(`Failed to save videos. Imported ${imported} out of ${videosToSave.length}.`);
    } finally {
      setIsImporting(false);
    }
  };

  const getSuggestedPillarName = (pillarId: string) => {
    const pillar = pillars.find(p => p.id === pillarId);
    return pillar?.name || 'No suggestion';
  };

  const getAssignedCount = () => {
    return videos.filter(video => video.selectedPillar).length;
  };

  return {
    videos,
    pillars,
    isImporting,
    isLoading,
    importProgress,
    handleImportVideos,
    handlePillarChange,
    handleSkipVideo,
    handleSaveVideos,
    getSuggestedPillarName,
    getAssignedCount
  };
};
