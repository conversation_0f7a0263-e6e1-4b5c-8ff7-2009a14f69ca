
// @ts-nocheck

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import YouTubeConnectionWizard from './YouTubeConnectionWizard';
import YouTubeConnectionStatus from './YouTubeConnectionStatus';
import YouTubeConnectedView from './YouTubeConnectedView';
import YouTubeDisconnectedView from './YouTubeDisconnectedView';
import YouTubeConnectionHeader from '@/components/Settings/YouTube/YouTubeConnectionHeader';
import { generateOAuthUrl } from '@/utils/youtubeOAuth';
import { supabase } from '@/lib/supabase';
import { useYouTubeConnection } from '@/hooks/useYouTubeConnection';

interface YouTubeConnectProps {
  user?: any;
  onConnectionUpdate?: () => void;
  showWizard?: boolean;
}

const YouTubeConnect = ({ onConnectionUpdate, showWizard = false }: YouTubeConnectProps) => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [wizardOpen, setWizardOpen] = useState(showWizard);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  
  const { youtubeData, isConnected, needsReconnection, tokenStatus, isLoading, checkTokenStatus, refreshConnection } = useYouTubeConnection();

  // Check if we're returning from OAuth and need aggressive refresh
  useEffect(() => {
    const recentOAuth = localStorage.getItem('youtube_oauth_completed');
    if (recentOAuth) {
      console.log('🎯 Detected recent OAuth completion, forcing aggressive refresh...');

      // Force connection refresh first
      refreshConnection().then(() => {
        console.log('🎯 Connection refresh completed, now checking tokens...');
        // Then check tokens after a delay
        setTimeout(() => {
          checkTokenStatus();
        }, 1000);
      });

      // Clear the flag
      localStorage.removeItem('youtube_oauth_completed');
    }
  }, []); // Only run on mount

  // Force a fresh token check when component mounts or when returning from OAuth
  useEffect(() => {
    if (youtubeData?.youtube_access_token) {
      console.log('🔄 Scheduling fresh token validation check...');

      // Add a small delay to ensure cache refresh has completed
      // This helps when returning from OAuth callback
      const timeoutId = setTimeout(() => {
        console.log('🔄 Executing delayed token validation check...');
        checkTokenStatus();
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [youtubeData?.youtube_access_token, checkTokenStatus]);

  // Log connection status for debugging
  useEffect(() => {
    console.log('=== YOUTUBE CONNECTION DEBUG ===');
    console.log('YouTube Data:', {
      hasChannelId: !!youtubeData?.youtube_channel_id,
      channelName: youtubeData?.youtube_channel_name,
      hasAccessToken: !!youtubeData?.youtube_access_token,
      subscriberBaseline: youtubeData?.youtube_subscriber_baseline,
      lastSync: youtubeData?.last_youtube_sync
    });
    console.log('Token Status:', tokenStatus);
    console.log('Is Connected:', isConnected);
    console.log('Has Token Error:', !tokenStatus.isValid && youtubeData?.youtube_channel_id);
  }, [youtubeData, tokenStatus, isConnected]);

  // Use the more accurate needsReconnection status from the hook
  const shouldShowReconnect = needsReconnection;

  const handleConnect = async () => {
    setIsConnecting(true);
    setConnectionError(null);
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Please log in first to connect YouTube');
      }

      const oauthUrl = generateOAuthUrl();
      console.log('Redirecting to YouTube OAuth:', oauthUrl);
      
      setTimeout(() => {
        window.location.href = oauthUrl;
      }, 100);
      
    } catch (error) {
      console.error('Error initiating YouTube connection:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to start YouTube connection';
      setConnectionError(errorMessage);
      toast.error(errorMessage);
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    if (!youtubeData?.id) {
      toast.error('User not found');
      return;
    }

    setIsDisconnecting(true);
    
    try {
      const { error } = await supabase
        .from('users')
        .update({
          youtube_channel_id: null,
          youtube_access_token: null,
          youtube_refresh_token: null,
          youtube_channel_name: null,
          youtube_subscriber_baseline: null,
          last_youtube_sync: null,
          youtube_thumbnail_url: null
        })
        .eq('id', youtubeData.id);

      if (error) throw error;

      toast.success('YouTube channel disconnected successfully');
      onConnectionUpdate?.();
    } catch (error) {
      console.error('Error disconnecting YouTube:', error);
      toast.error('Failed to disconnect YouTube channel');
    } finally {
      setIsDisconnecting(false);
    }
  };

  if (isLoading) {
    return (
      <Card className="dashboard-card">
        <CardContent className="flex items-center justify-center p-8">
          <div className="w-6 h-6 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
        </CardContent>
      </Card>
    );
  }



  return (
    <>
      <Card className="dashboard-card">
        <CardHeader className="pb-4">
          <YouTubeConnectionHeader
            isConnected={isConnected}
            isConnecting={isConnecting}
            isDisconnecting={isDisconnecting}
            showOnboardingHighlight={false}
            onConnect={handleConnect}
            onDisconnect={handleDisconnect}
            shouldShowReconnect={shouldShowReconnect}
            tokenError={needsReconnection ? tokenStatus.error : undefined}
          />
        </CardHeader>
        <CardContent>
          {connectionError && (
            <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
              <p className="text-red-400 text-sm">{connectionError}</p>
            </div>
          )}
          
          {/* Show connection status and debug info */}
          {youtubeData?.youtube_channel_id && (
            <div className="mb-4 p-3 bg-gray-800/50 rounded-lg">
              <h4 className="text-white text-sm font-medium mb-2">Connection Status</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-gray-400">Token Valid:</span>{' '}
                  <span className={tokenStatus.isValid ? 'text-green-400' : 'text-red-400'}>
                    {tokenStatus.isValid ? 'Yes' : 'No'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">Channel:</span>{' '}
                  <span className="text-white">{youtubeData?.youtube_channel_name || 'Unknown'}</span>
                </div>
                <div>
                  <span className="text-gray-400">Subscribers:</span>{' '}
                  <span className="text-white">
                    {youtubeData?.youtube_subscriber_baseline?.toLocaleString() || 'Not synced'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">Last Sync:</span>{' '}
                  <span className="text-white">
                    {youtubeData?.last_youtube_sync 
                      ? new Date(youtubeData.last_youtube_sync).toLocaleDateString()
                      : 'Never'
                    }
                  </span>
                </div>
              </div>
            </div>
          )}
          
          {/* Show connected view only if truly connected */}
          {isConnected ? (
            <YouTubeConnectedView
              user={youtubeData}
              isDisconnecting={isDisconnecting}
              onDisconnect={handleDisconnect}
            />
          ) : (
            <YouTubeDisconnectedView
              isConnecting={isConnecting}
              onOpenWizard={() => setWizardOpen(true)}
              onQuickConnect={handleConnect}
              hasChannel={!!youtubeData?.youtube_channel_id}
              channelName={youtubeData?.youtube_channel_name}
              tokenError={needsReconnection}
            />
          )}
        </CardContent>
      </Card>

      <YouTubeConnectionWizard
        isOpen={wizardOpen}
        onClose={() => setWizardOpen(false)}
        onConnect={() => {
          setWizardOpen(false);
          handleConnect();
        }}
      />
    </>
  );
};

export default YouTubeConnect;
