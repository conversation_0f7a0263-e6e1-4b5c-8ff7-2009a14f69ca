
import React from 'react';
import { TableCell, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Eye, ThumbsUp, MessageCircle } from 'lucide-react';

interface Video {
  id: string;
  title: string;
  thumbnailUrl: string;
  publishedAt: string;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  suggestedPillar?: string;
  selectedPillar?: string;
}

interface VideoRowProps {
  video: Video;
  pillars: any[];
  onPillarChange: (videoId: string, pillarId: string) => void;
  getSuggestedPillarName: (pillarId: string) => string;
}

const VideoRow = ({ video, pillars, onPillarChange, getSuggestedPillarName }: VideoRowProps) => {
  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <TableRow className="border-gray-600">
      <TableCell>
        <div className="flex items-start space-x-3">
          <img 
            src={video.thumbnailUrl} 
            alt={video.title}
            className="w-16 h-12 object-cover rounded"
          />
          <div>
            <h4 className="font-medium text-sm text-white line-clamp-2">{video.title}</h4>
            <p className="text-xs text-gray-400">
              {new Date(video.publishedAt).toLocaleDateString()}
            </p>
          </div>
        </div>
      </TableCell>
      <TableCell>
        <div className="space-y-1">
          <div className="flex items-center text-xs text-gray-300">
            <Eye className="w-3 h-3 mr-1" />
            {formatNumber(video.viewCount)}
          </div>
          <div className="flex items-center text-xs text-gray-300">
            <ThumbsUp className="w-3 h-3 mr-1" />
            {formatNumber(video.likeCount)}
          </div>
          <div className="flex items-center text-xs text-gray-300">
            <MessageCircle className="w-3 h-3 mr-1" />
            {formatNumber(video.commentCount)}
          </div>
        </div>
      </TableCell>
      <TableCell>
        {video.suggestedPillar ? (
          <Badge variant="outline" className="border-teal text-teal">
            {getSuggestedPillarName(video.suggestedPillar)}
          </Badge>
        ) : (
          <span className="text-gray-400 text-sm">No suggestion</span>
        )}
      </TableCell>
      <TableCell>
        <Select
          value={video.selectedPillar || ''}
          onValueChange={(value) => onPillarChange(video.id, value)}
        >
          <SelectTrigger className="w-48 bg-gray-700 border-gray-600 text-white">
            <SelectValue placeholder="Select pillar" />
          </SelectTrigger>
          <SelectContent className="bg-gray-700 border-gray-600">
            {pillars.map((pillar) => (
              <SelectItem key={pillar.id} value={pillar.id} className="text-white hover:bg-gray-600">
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: pillar.color }}
                  />
                  <span>{pillar.name}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </TableCell>
    </TableRow>
  );
};

export default VideoRow;
