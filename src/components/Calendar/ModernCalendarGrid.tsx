import { useState, useEffect } from 'react';
import { isToday } from 'date-fns';
import ModernDayCell from './ModernDayCell';
import VideoEditModal from './VideoEditModal';
import CalendarHeader from './CalendarHeader';
import TimelineView from './TimelineView';
import { useCalendarDays } from '@/hooks/calendar/useCalendarDays';
import { VideoIdea } from '@/components/Ideas/types';
import { ContentPillar } from '@/types/pillar';
import { AlertTriangle } from 'lucide-react';

interface ModernCalendarGridProps {
  currentDate: Date;
  videos: VideoIdea[];
  pillars: ContentPillar[];
  onDayClick: (date: Date) => void;
  view: 'month' | 'week' | 'timeline';
  onVideoUpdated?: () => void;
  visiblePillars: Set<string>;
  showPublished: boolean;
}

const ModernCalendarGrid = ({ 
  currentDate, 
  videos, 
  pillars, 
  onDayClick, 
  view, 
  onVideoUpdated,
  visiblePillars,
  showPublished
}: ModernCalendarGridProps) => {
  const [selectedVideo, setSelectedVideo] = useState<VideoIdea | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Use the hook directly instead of managing days state
  const days = useCalendarDays(currentDate);

  // Validate inputs
  useEffect(() => {
    if (!Array.isArray(videos)) {
      setError("Invalid videos data");
      console.error("Invalid videos data:", videos);
    } else if (!Array.isArray(pillars)) {
      setError("Invalid pillars data");
      console.error("Invalid pillars data:", pillars);
    } else {
      setError(null);
    }
  }, [videos, pillars]);

  // If there's an error, show an error message
  if (error) {
    return (
      <div className="dashboard-card p-8 text-center">
        <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
        <p className="text-white text-lg">Error loading calendar: {error}</p>
        <p className="text-gray-400 mt-2">Please try refreshing the page</p>
      </div>
    );
  }

  // If days array is empty, show loading
  if (days.length === 0 && view !== 'timeline') {
    return (
      <div className="dashboard-card p-8 text-center">
        <div className="w-8 h-8 border-4 border-teal border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-white">Generating calendar...</p>
      </div>
    );
  }

  // Convert VideoIdea[] to the format expected by TimelineView
  const timelineVideos = videos.map(video => ({
    id: video.id,
    title: video.title,
    status: video.status || 'idea',
    scheduled_date: video.scheduled_date || '',
    calendar_notes: video.calendar_notes || video.description || '',
    pillar_id: video.pillar_id || '',
    published_at: video.published_at,
    views: video.views || 0,
    youtube_video_id: video.youtube_id,
    youtube_thumbnail_url: video.thumbnail_url,
    description: video.description
  }));

  // If timeline view, render timeline component
  if (view === 'timeline') {
    return (
      <>
        <TimelineView
          currentDate={currentDate}
          videos={timelineVideos}
          pillars={pillars}
          onVideoClick={(video, e) => {
            e.stopPropagation();
            // Convert back to VideoIdea for the modal
            const originalVideo = videos.find(v => v.id === video.id);
            if (originalVideo) {
              setSelectedVideo(originalVideo);
              setIsEditModalOpen(true);
            }
          }}
          visiblePillars={visiblePillars}
          showPublished={showPublished}
        />
        
        <VideoEditModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          video={selectedVideo}
          pillars={pillars}
          onSave={(_video: VideoIdea) => {
            if (onVideoUpdated) onVideoUpdated();
            setIsEditModalOpen(false);
          }}
        />
      </>
    );
  }

  const handleVideoUpdated = () => {
    if (onVideoUpdated) {
      onVideoUpdated();
    }
  };

  return (
    <>
      <div className="dashboard-card relative overflow-hidden">
        {/* Subtle background pattern - keeping this for depth */}
        <div className="absolute inset-0 opacity-[0.02]">
          <div className="absolute inset-0 bg-gradient-to-br from-teal-400/10 via-transparent to-orange/10" />
          <div className="absolute top-0 left-1/4 w-32 h-32 bg-gradient-radial from-teal-400/5 to-transparent rounded-full blur-3xl" />
          <div className="absolute bottom-0 right-1/4 w-24 h-24 bg-gradient-radial from-orange/5 to-transparent rounded-full blur-2xl" />
        </div>

        {/* Calendar Header */}
        <div className="relative">
          <CalendarHeader />
        </div>

        {/* Calendar Grid - Shorter with thin lines between days */}
        <div className={`relative p-2 sm:p-4 md:p-6 grid grid-cols-7 gap-0 border-l border-t border-gray-600/30 ${view === 'month' ? 'grid-rows-6' : 'grid-rows-1'}`}>
          {days.map((dayData, index) => {
            const { date, isCurrentMonth: isDayInCurrentMonth } = dayData;
            const isOtherMonth = view === 'month' && !isDayInCurrentMonth;

            return (
              <ModernDayCell
                key={index}
                date={date}
                isCurrentMonth={!isOtherMonth}
                isToday={isToday(date)}
                videos={videos}
                pillars={pillars}
                onDayClick={onDayClick}
                onVideoUpdated={handleVideoUpdated}
                visiblePillars={visiblePillars}
                showPublished={showPublished}
              />
            );
          })}
        </div>
      </div>

      {/* Video Edit Modal */}
      <VideoEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        video={selectedVideo}
        pillars={pillars}
        onSave={(_video: VideoIdea) => {
          handleVideoUpdated();
          setIsEditModalOpen(false);
        }}
      />
    </>
  );
};

export default ModernCalendarGrid;
