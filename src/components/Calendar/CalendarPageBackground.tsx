
import React from 'react';
import { COLOR_PALETTE } from '@/constants/pillarColors';

const CalendarPageBackground = () => {
  // Using the brand colors from your color palette
  const sidebarTeal = '#0cc0df'; // Brighter blue/teal from sidebar
  const terracotta = COLOR_PALETTE[1]; // #E76F51
  const orange = COLOR_PALETTE[2]; // #FF7B25
  const goldenYellow = COLOR_PALETTE[3]; // #F59F0A
  
  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none">
      {/* Dark overlay to improve text readability */}
      <div 
        className="absolute inset-0 w-full h-full bg-black opacity-40"
      />
      
      {/* Main gradient background - significantly increased teal area */}
      <div 
        className="absolute inset-0 w-full h-full"
        style={{
          background: `
            linear-gradient(135deg, 
              rgba(12, 192, 223, 0.6) 0%, 
              rgba(12, 192, 223, 0.5) 45%, 
              rgba(12, 192, 223, 0.4) 65%, 
              rgba(231, 111, 81, 0.4) 80%, 
              rgba(255, 123, 37, 0.5) 90%, 
              rgba(245, 159, 10, 0.6) 98%)
          `,
          opacity: 0.7
        }}
      />
      
      {/* Enhanced overlay with dominant teal presence */}
      <div
        className="absolute inset-0 w-full h-full"
        style={{
          background: `
            radial-gradient(circle at 20% 20%, rgba(12, 192, 223, 0.3) 0%, transparent 60%),
            radial-gradient(circle at 50% 50%, rgba(12, 192, 223, 0.25) 0%, transparent 55%),
            radial-gradient(circle at 70% 30%, rgba(12, 192, 223, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 85% 85%, rgba(231, 111, 81, 0.2) 0%, transparent 25%),
            radial-gradient(circle at 95% 95%, rgba(245, 159, 10, 0.2) 0%, transparent 25%)
          `,
          opacity: 0.5
        }}
      />
      
      {/* Add a subtle noise texture overlay */}
      <div 
        className="absolute inset-0 w-full h-full opacity-10"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
          backgroundSize: '200px 200px'
        }}
      />
    </div>
  );
};

export default CalendarPageBackground;
