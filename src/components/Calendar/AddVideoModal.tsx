
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';

interface ContentPillar {
  id: string;
  name: string;
  color: string;
}

interface AddVideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  onVideoAdded: () => void;
  selectedDate: Date | null;
  pillars: ContentPillar[];
}

const AddVideoModal = ({ isOpen, onClose, onVideoAdded, selectedDate, pillars }: AddVideoModalProps) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    title: '',
    pillar_id: '',
    status: 'planned',
    calendar_notes: '',
    scheduled_date: selectedDate || new Date()
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !formData.title || !formData.pillar_id) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const { error } = await supabase
        .from('videos')
        .insert({
          user_id: user.id,
          title: formData.title,
          pillar_id: formData.pillar_id,
          status: formData.status,
          calendar_notes: formData.calendar_notes,
          scheduled_date: formData.scheduled_date.toISOString().split('T')[0]
        });

      if (error) throw error;

      toast.success('Video scheduled successfully!');
      setFormData({
        title: '',
        pillar_id: '',
        status: 'planned',
        calendar_notes: '',
        scheduled_date: selectedDate || new Date()
      });
      onVideoAdded();
    } catch (error) {
      console.error('Error scheduling video:', error);
      toast.error('Failed to schedule video');
    }
  };

  React.useEffect(() => {
    if (selectedDate) {
      setFormData(prev => ({ ...prev, scheduled_date: selectedDate }));
    }
  }, [selectedDate]);

  const statusOptions = [
    { value: 'idea', label: 'Idea' },
    { value: 'planned', label: 'Planned' },
    { value: 'in progress', label: 'In Progress' },
    { value: 'editing', label: 'Editing' },
    { value: 'ready', label: 'Ready' },
    { value: 'published', label: 'Published' }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="calendar-modal sm:max-w-[600px] p-0 overflow-hidden">
        <div className="p-6">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-white">
              Schedule New Video
            </DialogTitle>
            <DialogDescription className="text-gray-400">
              {selectedDate ? (
                <>Schedule a video for {selectedDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}</>
              ) : (
                <>Add a new video to your content calendar</>
              )}
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="text-sm font-medium text-white">Video Title *</label>
              <Input
                placeholder="Enter video title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                required
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>

            <div>
              <label className="text-sm font-medium text-white">Content Pillar *</label>
              <Select value={formData.pillar_id} onValueChange={(value) => setFormData({ ...formData, pillar_id: value })}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select a pillar" />
                </SelectTrigger>
                <SelectContent>
                  {pillars.map((pillar) => (
                    <SelectItem key={pillar.id} value={pillar.id}>
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: pillar.color }}
                        ></div>
                        <span>{pillar.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium text-white">Status</label>
              <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value })}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium text-white">Scheduled Date</label>
              <div className="mt-2">
                <Calendar
                  mode="single"
                  selected={formData.scheduled_date}
                  onSelect={(date) => date && setFormData({ ...formData, scheduled_date: date })}
                  className="rounded-md border border-gray-600 bg-gray-700"
                />
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-white">Notes</label>
              <Textarea
                placeholder="Optional notes about this video"
                value={formData.calendar_notes}
                onChange={(e) => setFormData({ ...formData, calendar_notes: e.target.value })}
                rows={3}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>

            <div className="flex space-x-2 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onClose} 
                className="flex-1 bg-gray-700 border-gray-600 text-white hover:bg-gray-600 hover:border-gray-500"
              >
                Cancel
              </Button>
              <Button type="submit" className="flex-1 bg-teal hover:bg-teal/90 text-white">
                Schedule Video
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddVideoModal;
