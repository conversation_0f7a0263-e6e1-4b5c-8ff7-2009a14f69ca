
import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import VideoEditModal from './VideoEditModal';
import AIWizardDataModal from '@/components/Ideas/AIWizardDataModal';
import VideoCard from '@/components/Videos/VideoCard';
import { useVideoFiltering } from '@/hooks/calendar/useVideoFiltering';
import { useDragAndDrop } from '@/hooks/calendar/useDragAndDrop';
import { VideoIdea } from '@/components/Ideas/types';
import { ContentPillar } from '@/types/pillar';

interface ModernDayCellProps {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  videos: VideoIdea[];
  pillars: ContentPillar[];
  onDayClick: (date: Date) => void;
  onVideoUpdated: () => void;
  visiblePillars: Set<string>;
  showPublished: boolean;
}

const ModernDayCell = ({
  date,
  isCurrentMonth,
  isToday,
  videos,
  pillars,
  onDayClick,
  onVideoUpdated,
  visiblePillars,
  showPublished
}: ModernDayCellProps) => {
  const [editingVideo, setEditingVideo] = useState<VideoIdea | null>(null);
  const [viewingVideo, setViewingVideo] = useState<VideoIdea | null>(null);
  const { getVideosForDate } = useVideoFiltering(videos, visiblePillars, showPublished);
  const { handleDragStart, handleDragOver, handleDrop } = useDragAndDrop(onVideoUpdated);
  
  const dayVideos = getVideosForDate(date);

  const handleVideoClick = (video: VideoIdea) => {
    setViewingVideo(video);
  };

  const handleEditClick = (video: VideoIdea) => {
    setEditingVideo(video);
  };

  const getStatusColor = (status: string | null) => {
    switch (status) {
      case 'idea': return 'bg-teal-500/20 text-teal-400 border-teal-500/30';
      case 'planned': return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'filming': return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'editing': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'published': return 'bg-green-500/20 text-green-400 border-green-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getPillarName = (pillarId: string | null) => {
    if (!pillarId) return undefined;
    return pillars.find(p => p.id === pillarId)?.name;
  };

  return (
    <>
      <div
        className={`calendar-day-cell min-h-[60px] sm:min-h-[70px] md:min-h-[80px] p-1 sm:p-2 md:p-3 cursor-pointer relative group border-r border-b border-gray-600/30 hover:bg-white/5 transition-colors ${
          isCurrentMonth ? '' : 'opacity-50'
        } ${
          isToday ? 'ring-2 ring-teal-400/30 shadow-teal-400/20 bg-teal-400/5' : ''
        }`}
        onClick={() => onDayClick(date)}
        onDragOver={handleDragOver}
        onDrop={(e) => handleDrop(e, date)}
      >
        <div className="flex items-center justify-between mb-1 sm:mb-2">
          <span className={`text-xs sm:text-sm font-medium ${
            isCurrentMonth ? 'text-white' : 'text-gray-500'
          } ${isToday ? 'text-teal' : ''}`}>
            {date.getDate()}
          </span>
          {dayVideos.length === 0 && isCurrentMonth && (
            <Plus className="w-3 h-3 text-gray-400 opacity-0 group-hover:opacity-100" />
          )}
        </div>
        
        {/* Video pills with improved responsive layout */}
        <div className="flex flex-wrap gap-1">
          {dayVideos.slice(0, 3).map(video => (
            <VideoCard
              key={video.id}
              video={video}
              pillars={pillars}
              isCompact={true}
              onVideoClick={handleVideoClick}
              onDragStart={handleDragStart}
              onStatusChanged={onVideoUpdated}
              onVideoUpdated={onVideoUpdated}
            />
          ))}
          
          {dayVideos.length > 3 && (
            <div className="flex items-center justify-center w-5 h-5 sm:w-6 sm:h-6 bg-gray-800/60 border border-gray-600 rounded-full text-[10px] sm:text-xs text-gray-300 font-medium">
              +{dayVideos.length - 3}
            </div>
          )}
        </div>
      </div>

      {/* View Video Details Modal */}
      <AIWizardDataModal
        isOpen={!!viewingVideo}
        onClose={() => setViewingVideo(null)}
        idea={viewingVideo}
        pillarName={viewingVideo?.pillar_id ? getPillarName(viewingVideo.pillar_id) : undefined}
      />

      {/* Edit Video Modal */}
      <VideoEditModal
        isOpen={!!editingVideo}
        onClose={() => setEditingVideo(null)}
        video={editingVideo}
        pillars={pillars}
        onSave={(_updatedVideo) => {
          onVideoUpdated();
          setEditingVideo(null);
        }}
      />
    </>
  );
};

export default ModernDayCell;
