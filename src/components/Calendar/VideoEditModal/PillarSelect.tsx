
import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ContentPillar } from '@/types/pillar';

interface PillarSelectProps {
  value: string;
  onChange: (value: string) => void;
  pillars: ContentPillar[];
}

const PillarSelect = ({ value, onChange, pillars }: PillarSelectProps) => {
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-gray-200">Content Pillar</label>
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
          <SelectValue placeholder="Select pillar" />
        </SelectTrigger>
        <SelectContent className="bg-gray-800 border-gray-600">
          {pillars.map((pillar) => (
            <SelectItem key={pillar.id} value={pillar.id} className="text-white hover:bg-gray-700">
              <div className="flex items-center">
                <div
                  className="w-3 h-3 rounded-full mr-2 border border-white/20"
                  style={{ backgroundColor: pillar.color }}
                />
                {pillar.name}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default PillarSelect;
