
import React from 'react';
import { Save, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ActionButtonsProps {
  onCancel: () => void;
  onSave: () => void;
  isLoading: boolean;
}

const ActionButtons = ({ onCancel, onSave, isLoading }: ActionButtonsProps) => {
  return (
    <div className="flex justify-end space-x-3 pt-6 border-t border-gray-600">
      <Button
        type="button"
        variant="outline"
        onClick={onCancel}
        className="border-gray-500 text-gray-300 hover:bg-gray-600 hover:border-gray-400"
      >
        Cancel
      </Button>
      <Button
        type="submit"
        disabled={isLoading}
        className="bg-gradient-to-r from-teal to-teal/90 hover:from-teal/90 hover:to-teal/80 text-white"
        onClick={onSave}
      >
        {isLoading ? (
          <>
            <Clock className="w-4 h-4 mr-2 animate-spin" />
            Saving...
          </>
        ) : (
          <>
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </>
        )}
      </Button>
    </div>
  );
};

export default ActionButtons;
