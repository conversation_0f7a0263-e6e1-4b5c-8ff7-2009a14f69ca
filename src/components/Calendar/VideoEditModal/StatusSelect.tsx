
import React from 'react';
import { Clock, Video, TrendingUp, Lightbulb, Edit3, CheckCircle } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface StatusOption {
  value: string;
  label: string;
  icon: React.ComponentType<any>;
  color: string;
}

interface StatusSelectProps {
  value: string;
  onChange: (value: string) => void;
}

const statusOptions: StatusOption[] = [
  { value: 'idea', label: 'Idea', icon: Lightbulb, color: 'text-gray-400' },
  { value: 'planned', label: 'Planned', icon: Clock, color: 'text-teal-400' },
  { value: 'in progress', label: 'In Progress', icon: Edit3, color: 'text-yellow-400' },
  { value: 'editing', label: 'Editing', icon: Video, color: 'text-orange-400' },
  { value: 'ready', label: 'Ready', icon: CheckCircle, color: 'text-green-400' },
  { value: 'published', label: 'Published', icon: TrendingUp, color: 'text-purple-400' }
];

const getStatusIcon = (status: string) => {
  const statusOption = statusOptions.find(opt => opt.value === status);
  return statusOption ? statusOption.icon : Clock;
};

const getStatusColor = (status: string) => {
  const statusOption = statusOptions.find(opt => opt.value === status);
  return statusOption ? statusOption.color : 'text-gray-400';
};

const StatusSelect = ({ value, onChange }: StatusSelectProps) => {
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-gray-200">Status</label>
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
          <div className="flex items-center">
            {value && (
              <>
                {React.createElement(getStatusIcon(value), {
                  className: `w-4 h-4 mr-2 ${getStatusColor(value)}`
                })}
              </>
            )}
            <SelectValue placeholder="Select status" />
          </div>
        </SelectTrigger>
        <SelectContent className="bg-gray-800 border-gray-600">
          {statusOptions.map((option) => (
            <SelectItem key={option.value} value={option.value} className="text-white hover:bg-gray-700">
              <div className="flex items-center">
                <option.icon className={`w-4 h-4 mr-2 ${option.color}`} />
                {option.label}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default StatusSelect;
