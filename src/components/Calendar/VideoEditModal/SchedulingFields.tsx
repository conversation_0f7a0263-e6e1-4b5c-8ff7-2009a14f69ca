
import React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface SchedulingFieldsProps {
  scheduledDate: string;
  calendarNotes: string;
  onScheduledDateChange: (value: string) => void;
  onCalendarNotesChange: (value: string) => void;
}

const SchedulingFields = ({ 
  scheduledDate, 
  calendarNotes, 
  onScheduledDateChange, 
  onCalendarNotesChange 
}: SchedulingFieldsProps) => {
  return (
    <>
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-200">Scheduled Date</label>
        <Input
          type="date"
          value={scheduledDate}
          onChange={(e) => onScheduledDateChange(e.target.value)}
          className="bg-gray-800 border-gray-600 text-white focus:border-teal"
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-200">Calendar Notes</label>
        <Textarea
          value={calendarNotes}
          onChange={(e) => onCalendarNotesChange(e.target.value)}
          className="bg-gray-800 border-gray-600 text-white focus:border-teal"
          placeholder="Add notes for calendar planning"
          rows={3}
        />
      </div>
    </>
  );
};

export default SchedulingFields;
