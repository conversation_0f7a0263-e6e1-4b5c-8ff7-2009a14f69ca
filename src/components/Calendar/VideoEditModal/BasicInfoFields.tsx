
import React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface BasicInfoFieldsProps {
  title: string;
  description: string;
  onTitleChange: (value: string) => void;
  onDescriptionChange: (value: string) => void;
}

const BasicInfoFields = ({ 
  title, 
  description, 
  onTitleChange, 
  onDescriptionChange 
}: BasicInfoFieldsProps) => {
  return (
    <>
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-200">Title</label>
        <Input
          value={title}
          onChange={(e) => onTitleChange(e.target.value)}
          className="bg-gray-800 border-gray-600 text-white focus:border-teal"
          placeholder="Enter video title"
          required
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-200">Description</label>
        <Textarea
          value={description}
          onChange={(e) => onDescriptionChange(e.target.value)}
          className="bg-gray-800 border-gray-600 text-white focus:border-teal min-h-[100px]"
          placeholder="Enter video description"
        />
      </div>
    </>
  );
};

export default BasicInfoFields;
