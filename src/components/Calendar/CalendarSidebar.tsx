
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Plus, Target, Calendar, TrendingUp } from 'lucide-react';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip } from 'recharts';

interface Video {
  id: string;
  title: string;
  status: string;
  scheduled_date: string;
  pillar_id: string;
}

interface ContentPillar {
  id: string;
  name: string;
  color: string;
  target_percentage: number;
}

interface CalendarSidebarProps {
  videos: Video[];
  pillars: ContentPillar[];
  currentDate: Date;
}

const statusIcons = {
  'idea': '💡',
  'scripting': '📝',
  'filming': '🎬',
  'editing': '✂️',
  'scheduled': '📅',
  'published': '✅'
};

const CalendarSidebar = ({ videos, pillars, currentDate }: CalendarSidebarProps) => {
  // Filter videos for current month
  const currentMonthVideos = videos.filter(video => {
    const videoDate = video.scheduled_date ? new Date(video.scheduled_date) : null;
    return videoDate && 
           videoDate.getMonth() === currentDate.getMonth() && 
           videoDate.getFullYear() === currentDate.getFullYear();
  });

  // Calculate pillar distribution
  const pillarDistribution = pillars.map(pillar => {
    const count = currentMonthVideos.filter(video => video.pillar_id === pillar.id).length;
    return {
      name: pillar.name,
      value: count,
      color: pillar.color
    };
  }).filter(item => item.value > 0);

  // Calculate status breakdown
  const statusBreakdown = Object.keys(statusIcons).map(status => {
    const count = currentMonthVideos.filter(video => video.status === status).length;
    return {
      status,
      count,
      icon: statusIcons[status as keyof typeof statusIcons]
    };
  }).filter(item => item.count > 0);

  // Calculate publishing frequency
  const publishingFrequency = currentMonthVideos.filter(video => video.status === 'published').length;
  const weeksInMonth = Math.ceil(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate() / 7);
  const avgPerWeek = publishingFrequency / weeksInMonth;

  // Monthly goals progress (example - in real app this would come from goals table)
  const monthlyGoal = 8; // videos per month
  const progress = (publishingFrequency / monthlyGoal) * 100;

  return (
    <div className="space-y-6">
      {/* Monthly Goals Progress */}
      <Card className="glass-effect border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-sm flex items-center">
            <Target className="w-4 h-4 mr-2 text-teal" />
            Monthly Goal Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-300">Published Videos</span>
            <span className="text-white font-medium">{publishingFrequency}/{monthlyGoal}</span>
          </div>
          <Progress value={progress} className="h-2" />
          <div className="text-xs text-gray-400">
            {progress >= 100 ? '🎉 Goal achieved!' : `${monthlyGoal - publishingFrequency} more to reach goal`}
          </div>
        </CardContent>
      </Card>

      {/* Pillar Distribution Chart */}
      <Card className="glass-effect border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-sm">Pillar Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          {pillarDistribution.length > 0 ? (
            <div className="space-y-4">
              <div className="h-32">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pillarDistribution}
                      cx="50%"
                      cy="50%"
                      innerRadius={20}
                      outerRadius={50}
                      dataKey="value"
                    >
                      {pillarDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="space-y-1">
                {pillarDistribution.map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-xs">
                    <div className="flex items-center space-x-2">
                      <div 
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: item.color }}
                      />
                      <span className="text-gray-300">{item.name}</span>
                    </div>
                    <span className="text-white font-medium">{item.value}</span>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center text-gray-400 text-sm py-8">
              No content scheduled this month
            </div>
          )}
        </CardContent>
      </Card>

      {/* Content Status Breakdown */}
      <Card className="glass-effect border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-sm">Content Status</CardTitle>
        </CardHeader>
        <CardContent>
          {statusBreakdown.length > 0 ? (
            <div className="space-y-2">
              {statusBreakdown.map((item, index) => (
                <div key={index} className="flex items-center justify-between py-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{item.icon}</span>
                    <span className="text-gray-300 text-sm capitalize">{item.status}</span>
                  </div>
                  <span className="text-white font-medium text-sm">{item.count}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-400 text-sm py-4">
              No content this month
            </div>
          )}
        </CardContent>
      </Card>

      {/* Publishing Frequency */}
      <Card className="glass-effect border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-sm flex items-center">
            <TrendingUp className="w-4 h-4 mr-2 text-teal" />
            Publishing Frequency
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-gray-300 text-sm">This Month</span>
            <span className="text-white font-medium">{publishingFrequency} videos</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-300 text-sm">Weekly Average</span>
            <span className="text-white font-medium">{avgPerWeek.toFixed(1)} videos</span>
          </div>
          <div className="text-xs text-gray-400">
            {avgPerWeek >= 2 ? '🔥 Great consistency!' : 
             avgPerWeek >= 1 ? '👍 Good pace' : 
             '📈 Room for improvement'}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CalendarSidebar;
