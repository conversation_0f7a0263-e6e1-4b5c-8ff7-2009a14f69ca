
import React from 'react';
import { ChevronLeft, ChevronRight, Calendar, Clock, LayoutGrid, List } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ModernCalendarHeaderProps {
  currentDate: Date;
  view: 'month' | 'week' | 'timeline';
  onNavigateMonth: (direction: 'prev' | 'next') => void;
  onViewChange: (view: 'month' | 'week' | 'timeline') => void;
  onGoToToday: () => void;
}

const ModernCalendarHeader = ({ 
  currentDate, 
  view, 
  onNavigateMonth, 
  onViewChange, 
  onGoToToday 
}: ModernCalendarHeaderProps) => {
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const today = new Date();
  const isCurrentMonth = currentDate.getMonth() === today.getMonth() && 
                         currentDate.getFullYear() === today.getFullYear();

  return (
    <div className="dashboard-card relative overflow-hidden">
      <div className="relative p-6">
        <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
          {/* Left Section - Month Navigation */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onNavigateMonth('prev')}
                className="glass-button bg-gray-700/50 border-gray-500/50 text-white hover:bg-gray-600/60 hover:border-gray-400/60 transition-all duration-300 hover:scale-110 hover:shadow-lg backdrop-blur-sm"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              
              <div className="glass-effect text-center bg-gradient-to-r from-gray-600/60 to-gray-700/60 px-8 py-4 rounded-xl backdrop-blur-sm shadow-inner">
                <h2 className="text-3xl font-bold text-white bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                  {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
                </h2>
                <p className="text-sm text-gray-300 mt-1 opacity-80">
                  {currentDate.toLocaleDateString('en-US', { weekday: 'long' })} • Content planning
                </p>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => onNavigateMonth('next')}
                className="glass-button bg-gray-700/50 border-gray-500/50 text-white hover:bg-gray-600/60 hover:border-gray-400/60 transition-all duration-300 hover:scale-110 hover:shadow-lg backdrop-blur-sm"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>

            {!isCurrentMonth && (
              <Button
                variant="outline"
                size="sm"
                onClick={onGoToToday}
                className="glass-button bg-teal/20 border-teal/50 text-teal-300 hover:bg-teal/30 hover:border-teal/70 transition-all duration-300 hover:shadow-lg hover:shadow-teal/20 backdrop-blur-sm"
              >
                <Calendar className="w-4 h-4 mr-2" />
                Today
              </Button>
            )}
          </div>

          {/* Right Section - View Options */}
          <div className="flex items-center space-x-4">
            <span className="text-gray-400 text-sm font-medium">View:</span>
            <div className="glass-effect flex bg-gray-800/60 rounded-xl p-1.5 backdrop-blur-sm shadow-inner">
              <Button
                variant={view === 'month' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewChange('month')}
                className={view === 'month' ? 
                  'bg-teal hover:bg-teal/90 text-white shadow-lg shadow-teal/20 border-0' : 
                  'text-gray-300 hover:text-white hover:bg-gray-700/60 border-0'}
              >
                <LayoutGrid className="w-4 h-4 mr-1.5" />
                Month
              </Button>
              <Button
                variant={view === 'week' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewChange('week')}
                className={view === 'week' ? 
                  'bg-teal hover:bg-teal/90 text-white shadow-lg shadow-teal/20 border-0' : 
                  'text-gray-300 hover:text-white hover:bg-gray-700/60 border-0'}
              >
                <Calendar className="w-4 h-4 mr-1.5" />
                Week
              </Button>
              <Button
                variant={view === 'timeline' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewChange('timeline')}
                className={view === 'timeline' ? 
                  'bg-teal hover:bg-teal/90 text-white shadow-lg shadow-teal/20 border-0' : 
                  'text-gray-300 hover:text-white hover:bg-gray-700/60 border-0'}
              >
                <List className="w-4 h-4 mr-1.5" />
                Timeline
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernCalendarHeader;
