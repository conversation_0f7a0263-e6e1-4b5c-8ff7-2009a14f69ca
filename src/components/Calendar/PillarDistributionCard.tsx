
import React from 'react';
import { Target } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface Video {
  id: string;
  title: string;
  status: string;
  scheduled_date: string;
  pillar_id: string;
}

interface ContentPillar {
  id: string;
  name: string;
  color: string;
  target_percentage: number;
}

interface PillarDistributionCardProps {
  videos: Video[];
  pillars: ContentPillar[];
  currentDate: Date;
}

const PillarDistributionCard = ({ videos, pillars, currentDate }: PillarDistributionCardProps) => {
  const currentMonthVideos = videos.filter(video => {
    const videoDate = new Date(video.scheduled_date);
    return videoDate.getMonth() === currentDate.getMonth() &&
           videoDate.getFullYear() === currentDate.getFullYear();
  });

  const getPillarDistribution = () => {
    const distribution = pillars.map(pillar => {
      const pillarVideos = currentMonthVideos.filter(video => video.pillar_id === pillar.id);
      const count = pillarVideos.length;
      const percentage = currentMonthVideos.length > 0 ? (count / currentMonthVideos.length) * 100 : 0;
      
      return {
        ...pillar,
        count,
        percentage: Math.round(percentage)
      };
    });

    return distribution;
  };

  const pillarDistribution = getPillarDistribution();
  const totalVideos = currentMonthVideos.length;

  return (
    <Card className="bg-gradient-to-br from-gray-700 to-gray-800 border-gray-600 shadow-xl">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-white">
          <div className="p-2 bg-terracotta/20 rounded-lg mr-3">
            <Target className="w-5 h-5 text-terracotta" />
          </div>
          <div>
            <div className="text-lg">Content Balance</div>
            <div className="text-sm text-gray-300 font-normal">
              {totalVideos > 0 ? `${totalVideos} videos this month` : 'Target distribution'}
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-6">
          {pillarDistribution.map(pillar => {
            const isOnTarget = Math.abs(pillar.percentage - pillar.target_percentage) <= 5;
            const isOverTarget = pillar.percentage > pillar.target_percentage + 5;
            const isUnderTarget = pillar.percentage < pillar.target_percentage - 5;
            
            return (
              <div key={pillar.id} className="space-y-3 pb-4 border-b border-gray-600/30 last:border-b-0 last:pb-0">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <div
                      className="w-4 h-4 rounded-full shadow-md border border-white/20 flex-shrink-0"
                      style={{ backgroundColor: pillar.color }}
                    />
                    <span className="text-sm font-medium text-white truncate flex-1">
                      {pillar.name}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2 flex-shrink-0">
                    {totalVideos > 0 ? (
                      <span className="text-xs text-gray-400 min-w-0">
                        {pillar.count} videos
                      </span>
                    ) : (
                      <span className="text-xs text-gray-500 min-w-0">
                        {pillar.target_percentage}% target
                      </span>
                    )}
                    {totalVideos > 0 && (
                      <>
                        {isOnTarget && <div className="w-2 h-2 bg-green-400 rounded-full flex-shrink-0" title="On target" />}
                        {isOverTarget && <div className="w-2 h-2 bg-orange-400 rounded-full flex-shrink-0" title="Over target" />}
                        {isUnderTarget && <div className="w-2 h-2 bg-red-400 rounded-full flex-shrink-0" title="Under target" />}
                      </>
                    )}
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between text-xs">
                    {totalVideos > 0 ? (
                      <>
                        <span className="text-gray-300">Current: {pillar.percentage}%</span>
                        <span className="text-gray-400">Target: {pillar.target_percentage}%</span>
                      </>
                    ) : (
                      <>
                        <span className="text-gray-400">No videos yet</span>
                        <span className="text-gray-400">Target: {pillar.target_percentage}%</span>
                      </>
                    )}
                  </div>
                  <div className="relative px-0">
                    <Progress 
                      value={totalVideos > 0 ? pillar.percentage : 0} 
                      className="h-3 bg-gray-800 w-full"
                    />
                    <div 
                      className="absolute top-0 w-1 h-3 bg-white/60 rounded-full"
                      style={{ left: `${pillar.target_percentage}%` }}
                      title={`Target: ${pillar.target_percentage}%`}
                    />
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {totalVideos === 0 && pillars.length > 0 && (
          <div className="mt-4 p-3 bg-gray-700/30 rounded-lg">
            <p className="text-xs text-gray-400 text-center">
              Schedule videos to see your content balance vs targets
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PillarDistributionCard;
