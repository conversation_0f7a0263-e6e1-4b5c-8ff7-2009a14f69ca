
import React from 'react';
import <PERSON>llarFilter from './PillarFilter';
import { Eye, EyeOff, Sidebar, SidebarOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ContentPillar {
  id: string;
  name: string;
  color: string;
  target_percentage: number;
}

interface CalendarControlsBarProps {
  pillars: ContentPillar[];
  visiblePillars: Set<string>;
  showPublished: boolean;
  sidebarCollapsed: boolean;
  onTogglePillar: (pillarId: string) => void;
  onToggleAll: () => void;
  onTogglePublished: () => void;
  onToggleSidebar: () => void;
}

const CalendarControlsBar = ({
  pillars,
  visiblePillars,
  showPublished,
  sidebarCollapsed,
  onTogglePillar,
  onToggleAll,
  onTogglePublished,
  onToggleSidebar
}: CalendarControlsBarProps) => {
  const allVisible = visiblePillars.size === pillars.length;
  
  return (
    <div className="dashboard-card p-6 backdrop-blur-sm shadow-xl">
      <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between">
        {/* Left Section - Pillar Filter */}
        <div className="flex-1 w-full lg:w-auto">
          <div className="flex items-center space-x-3 mb-3">
            <h3 className="text-white font-medium">Filter Content</h3>
            {allVisible ? (
              <span className="text-sm text-teal-400 bg-teal-400/10 px-2 py-1 rounded-md">
                All Pillars Visible
              </span>
            ) : (
              <span className="text-sm text-orange-400 bg-orange-400/10 px-2 py-1 rounded-md">
                {visiblePillars.size} of {pillars.length} Pillars
              </span>
            )}
          </div>
          <PillarFilter
            pillars={pillars}
            visiblePillars={visiblePillars}
            onTogglePillar={onTogglePillar}
            onToggleAll={onToggleAll}
          />
        </div>
        
        {/* Right Section - Controls */}
        <div className="flex items-center space-x-4">
          {/* Published Videos Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={onTogglePublished}
            className={`glass-button transition-all duration-300 ${
              showPublished 
                ? 'bg-green-600/20 border-green-500/50 text-green-300 hover:bg-green-600/30 hover:shadow-lg hover:shadow-green-500/20' 
                : 'bg-gray-700/50 border-gray-500/50 text-gray-400 hover:bg-gray-600/60'
            }`}
          >
            {showPublished ? (
              <>
                <Eye className="w-4 h-4 mr-2" />
                Show Published
              </>
            ) : (
              <>
                <EyeOff className="w-4 h-4 mr-2" />
                Hide Published
              </>
            )}
          </Button>

          {/* Sidebar Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={onToggleSidebar}
            className="glass-button bg-gray-700/50 border-gray-500/50 text-gray-300 hover:bg-gray-600/60 hover:text-white transition-all duration-300"
          >
            {sidebarCollapsed ? (
              <>
                <SidebarOpen className="w-4 h-4 mr-2" />
                Show Sidebar
              </>
            ) : (
              <>
                <Sidebar className="w-4 h-4 mr-2" />
                Hide Sidebar
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CalendarControlsBar;
