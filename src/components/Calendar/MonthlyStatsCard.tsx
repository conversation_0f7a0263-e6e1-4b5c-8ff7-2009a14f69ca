
import React from 'react';
import { TrendingUp } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

interface Video {
  id: string;
  title: string;
  status: string;
  scheduled_date: string;
  pillar_id: string;
}

interface MonthlyStatsCardProps {
  videos: Video[];
  currentDate: Date;
}

const MonthlyStatsCard = ({ videos, currentDate }: MonthlyStatsCardProps) => {
  const currentMonthVideos = videos.filter(video => {
    const videoDate = new Date(video.scheduled_date);
    return videoDate.getMonth() === currentDate.getMonth() &&
           videoDate.getFullYear() === currentDate.getFullYear();
  });

  return (
    <Card className="bg-gradient-to-br from-gray-700 to-gray-800 border-gray-600 shadow-xl">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-white">
          <div className="p-2 bg-orange/20 rounded-lg mr-3">
            <TrendingUp className="w-5 h-5 text-orange" />
          </div>
          <div>
            <div className="text-lg">Monthly Stats</div>
            <div className="text-sm text-gray-300 font-normal">{currentDate.toLocaleDateString('en-US', { month: 'long' })}</div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="flex flex-col items-center justify-center p-3 bg-teal/10 rounded-lg border border-teal/20 aspect-square">
            <div className="text-2xl font-bold text-teal mb-1 text-center">
              {currentMonthVideos.length}
            </div>
            <div className="text-xs text-gray-300 font-medium text-center">
              Total Scheduled
            </div>
          </div>
          <div className="flex flex-col items-center justify-center p-3 bg-orange/10 rounded-lg border border-orange/20 aspect-square">
            <div className="text-2xl font-bold text-orange mb-1 text-center">
              {currentMonthVideos.filter(v => v.status === 'published').length}
            </div>
            <div className="text-xs text-gray-300 font-medium text-center">
              Published
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col items-center justify-center p-3 bg-yellow/10 rounded-lg border border-yellow/20 aspect-square">
            <div className="text-2xl font-bold text-yellow mb-1 text-center">
              {currentMonthVideos.filter(v => v.status === 'ready').length}
            </div>
            <div className="text-xs text-gray-400 text-center">
              Ready to Publish
            </div>
          </div>
                  <div className="flex flex-col items-center justify-center p-3 bg-teal-400/10 rounded-lg border border-teal-400/20 aspect-square">
          <div className="text-2xl font-bold text-teal-400 mb-1 text-center">
              {currentMonthVideos.filter(v => v.status === 'editing').length}
            </div>
            <div className="text-xs text-gray-400 text-center">
              In Editing
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MonthlyStatsCard;
