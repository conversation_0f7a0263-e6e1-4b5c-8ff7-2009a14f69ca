
import React, { useEffect } from 'react';
import ModernCalendarGrid from './ModernCalendarGrid';
import CalendarSidebar from './CalendarSidebar';
import CalendarControlsBar from './CalendarControlsBar';
import { useCalendarState } from '@/hooks/calendar/useCalendarState';
import { VideoIdea } from '@/components/Ideas/types';
import { ContentPillar } from '@/types/pillar';
import { AlertTriangle } from 'lucide-react';

interface CalendarLayoutProps {
  currentDate: Date;
  videos: VideoIdea[];
  pillars: ContentPillar[];
  view: 'month' | 'week' | 'timeline';
  onDayClick: (date: Date) => void;
  onVideoUpdated: () => void;
}

const CalendarLayout = ({ 
  currentDate, 
  videos, 
  pillars, 
  view, 
  onDayClick, 
  onVideoUpdated 
}: CalendarLayoutProps) => {
  // Safety check for data
  const safeVideos = Array.isArray(videos) ? videos : [];
  const safePillars = Array.isArray(pillars) ? pillars : [];
  
  const {
    visiblePillars,
    showPublished,
    sidebarCollapsed,
    handleTogglePillar,
    handleToggleAll,
    handleTogglePublished,
    handleToggleSidebar
  } = useCalendarState(safePillars);

  // Safely filter videos
  const filteredVideos = safeVideos.filter(video => 
    (!video.pillar_id || visiblePillars.has(video.pillar_id)) &&
    (showPublished || video.status !== 'published')
  );

  // Check if we have valid data
  if (!Array.isArray(videos) || !Array.isArray(pillars)) {
    return (
      <div className="content-card p-8 text-center">
        <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
        <p className="text-white text-lg">Unable to load calendar data</p>
        <p className="text-gray-400 mt-2">Please try refreshing the page</p>
        <div className="mt-4 text-xs text-gray-500 text-left max-w-lg mx-auto bg-gray-800 p-4 rounded">
          <p>Debug info:</p>
          <p>videos: {typeof videos} {Array.isArray(videos) ? `(array: ${videos.length})` : '(not array)'}</p>
          <p>pillars: {typeof pillars} {Array.isArray(pillars) ? `(array: ${pillars.length})` : '(not array)'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-full overflow-hidden">
      <div className={`grid gap-4 md:gap-6 transition-all duration-300 ${
        sidebarCollapsed ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-4'
      }`}>
        <div className={`space-y-4 md:space-y-6 ${sidebarCollapsed ? 'col-span-1' : 'lg:col-span-3'}`}>
          {/* Enhanced Controls Bar */}
          <CalendarControlsBar
            pillars={safePillars}
            visiblePillars={visiblePillars}
            showPublished={showPublished}
            sidebarCollapsed={sidebarCollapsed}
            onTogglePillar={handleTogglePillar}
            onToggleAll={handleToggleAll}
            onTogglePublished={handleTogglePublished}
            onToggleSidebar={handleToggleSidebar}
          />
          
          {/* Calendar Grid with overflow handling */}
          <div className="w-full overflow-x-auto pb-2">
            <div className="min-w-[700px] lg:min-w-0">
              <ModernCalendarGrid
                currentDate={currentDate}
                videos={safeVideos}
                pillars={safePillars}
                onDayClick={onDayClick}
                view={view}
                onVideoUpdated={onVideoUpdated}
                visiblePillars={visiblePillars}
                showPublished={showPublished}
              />
            </div>
          </div>
        </div>

        {/* Enhanced Sidebar - conditionally rendered */}
        {!sidebarCollapsed && (
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <CalendarSidebar
                videos={filteredVideos}
                pillars={safePillars}
                currentDate={currentDate}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CalendarLayout;
