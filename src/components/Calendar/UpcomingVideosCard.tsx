
import React, { useState, useEffect } from 'react';
import { Calendar, Video, Clock, TrendingUp } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface Video {
  id: string;
  title: string;
  status: string;
  scheduled_date: string;
  pillar_id: string;
}

interface ContentPillar {
  id: string;
  name: string;
  color: string;
  target_percentage: number;
}

interface UpcomingVideosCardProps {
  videos: Video[];
  pillars: ContentPillar[];
}

const UpcomingVideosCard = ({ videos, pillars }: UpcomingVideosCardProps) => {
  const upcomingVideos = videos
    .filter(video => new Date(video.scheduled_date) >= new Date())
    .sort((a, b) => new Date(a.scheduled_date).getTime() - new Date(b.scheduled_date).getTime())
    .slice(0, 5);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const diffDays = Math.ceil((date.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays <= 7) return `${diffDays} days`;
    
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const getPillarName = (pillarId: string) => {
    const pillar = pillars.find(p => p.id === pillarId);
    return pillar?.name || 'Unknown';
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'ready': return <Video className="w-3 h-3" />;
      case 'editing': return <Clock className="w-3 h-3" />;
      case 'published': return <TrendingUp className="w-3 h-3" />;
      default: return <Calendar className="w-3 h-3" />;
    }
  };

  return (
    <Card className="bg-gradient-to-br from-gray-700 to-gray-800 border-gray-600 shadow-xl">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-white">
          <div className="p-2 bg-teal/20 rounded-lg mr-3">
            <Calendar className="w-5 h-5 text-teal" />
          </div>
          <div>
            <div className="text-lg">Upcoming Videos</div>
            <div className="text-sm text-gray-300 font-normal">Next 5 scheduled</div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {upcomingVideos.length === 0 ? (
          <div className="text-center py-6">
            <Video className="w-12 h-12 text-gray-500 mx-auto mb-3 opacity-50" />
            <p className="text-gray-400 text-sm">No upcoming videos scheduled</p>
            <p className="text-gray-500 text-xs mt-1">Start planning your content!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {upcomingVideos.map((video, index) => (
              <div key={video.id} className="group hover:bg-gray-600/30 p-3 rounded-lg transition-all duration-200 border-l-4 hover:shadow-lg" 
                   style={{ borderLeftColor: pillars.find(p => p.id === video.pillar_id)?.color || '#37BEB0' }}>
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-semibold text-white group-hover:text-teal-300 transition-colors truncate">
                      {video.title}
                    </div>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-xs text-gray-400">{formatDate(video.scheduled_date)}</span>
                      <span className="text-gray-500">•</span>
                      <span className="text-xs text-gray-400">{getPillarName(video.pillar_id)}</span>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 ml-2">#{index + 1}</div>
                </div>
                <div className="flex items-center justify-between">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    video.status === 'ready' ? 'bg-green-900/50 text-green-300 border border-green-800' :
                    video.status === 'editing' ? 'bg-orange-900/50 text-orange-300 border border-orange-800' :
                    video.status === 'planned' ? 'bg-teal-900/50 text-teal-300 border border-teal-800' :
                    'bg-gray-900/50 text-gray-300 border border-gray-700'
                  }`}>
                    {getStatusIcon(video.status)}
                    <span className="ml-1">{video.status}</span>
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default UpcomingVideosCard;
