
import React, { useState } from 'react';
import { ChevronDown, Clock, Video, TrendingUp, Lightbulb, Edit3, CheckCircle } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

interface QuickStatusDropdownProps {
  videoId: string;
  currentStatus: string;
  onStatusChanged: () => void;
}

const QuickStatusDropdown = ({ videoId, currentStatus, onStatusChanged }: QuickStatusDropdownProps) => {
  const { user } = useAuth();
  const [isUpdating, setIsUpdating] = useState(false);

  const statusOptions = [
    { value: 'idea', label: 'Idea', icon: Lightbulb, color: 'text-gray-400' },
    { value: 'planned', label: 'Planned', icon: Clock, color: 'text-teal-400' },
    { value: 'in progress', label: 'In Progress', icon: Edit3, color: 'text-yellow-400' },
    { value: 'editing', label: 'Editing', icon: Video, color: 'text-orange-400' },
    { value: 'ready', label: 'Ready', icon: CheckCircle, color: 'text-green-400' },
    { value: 'published', label: 'Published', icon: TrendingUp, color: 'text-purple-400' }
  ];

  const handleStatusChange = async (newStatus: string) => {
    if (!user || newStatus === currentStatus) return;

    setIsUpdating(true);
    try {
      const { error } = await supabase
        .from('videos')
        .update({ status: newStatus })
        .eq('id', videoId)
        .eq('user_id', user.id);

      if (error) throw error;

      toast.success(`Status updated to ${statusOptions.find(s => s.value === newStatus)?.label}`);
      onStatusChanged();
    } catch (error) {
      console.error('Error updating video status:', error);
      toast.error('Failed to update status');
    } finally {
      setIsUpdating(false);
    }
  };

  const getCurrentStatusOption = () => {
    return statusOptions.find(option => option.value === currentStatus);
  };

  const currentOption = getCurrentStatusOption();

  return (
    <div className="relative" onClick={(e) => e.stopPropagation()}>
      <Select value={currentStatus} onValueChange={handleStatusChange} disabled={isUpdating}>
        <SelectTrigger className="h-8 w-full bg-gray-800/90 border-gray-600/50 text-xs hover:bg-gray-700/90 transition-colors">
          <div className="flex items-center text-xs">
            {currentOption && (
              <>
                <currentOption.icon className={`w-3 h-3 mr-1 ${currentOption.color}`} />
                <span className="text-gray-200 truncate">{currentOption.label}</span>
              </>
            )}
          </div>
        </SelectTrigger>
        <SelectContent className="bg-gray-800 border-gray-600 min-w-[140px]">
          {statusOptions.map((option) => (
            <SelectItem 
              key={option.value} 
              value={option.value} 
              className="text-white hover:bg-gray-700 text-xs"
            >
              <div className="flex items-center">
                <option.icon className={`w-3 h-3 mr-2 ${option.color}`} />
                {option.label}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default QuickStatusDropdown;
