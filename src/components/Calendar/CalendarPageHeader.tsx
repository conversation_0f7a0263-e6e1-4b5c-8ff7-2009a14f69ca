
import React from 'react';

interface ContentPillar {
  id: string;
  name: string;
  color: string;
  target_percentage: number;
}

interface CalendarMetrics {
  totalThisMonth: number;
  plannedNextMonth: number;
  averagePerWeek: number;
}

interface CalendarPageHeaderProps {
  metrics: CalendarMetrics;
  pillarsCount: number;
  onScheduleClick: () => void;
}

const CalendarPageHeader = ({ metrics, pillarsCount, onScheduleClick }: CalendarPageHeaderProps) => {
  return (
    <div className="dashboard-card relative overflow-hidden">
      <div className="relative p-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2 bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
              Content Calendar
            </h1>
            <p className="text-gray-300">Plan, schedule, and track your content journey</p>
          </div>
          <button
            onClick={onScheduleClick}
            className="glass-button bg-gradient-to-r from-teal to-teal/80 hover:from-teal/90 hover:to-teal/70 text-white px-6 py-3 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-teal/20 font-medium"
          >
            Schedule Video
          </button>
        </div>

        {/* Enhanced Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="dashboard-card rounded-xl p-6 transition-all duration-300">
            <div className="text-sm text-gray-400 mb-1">This Month</div>
            <div className="text-3xl font-bold text-white mb-1">{metrics.totalThisMonth}</div>
            <div className="text-xs text-gray-500">videos scheduled</div>
          </div>
          <div className="dashboard-card rounded-xl p-6 transition-all duration-300">
            <div className="text-sm text-gray-400 mb-1">Next Month</div>
            <div className="text-3xl font-bold text-white mb-1">{metrics.plannedNextMonth}</div>
            <div className="text-xs text-gray-500">videos planned</div>
          </div>
          <div className="dashboard-card rounded-xl p-6 transition-all duration-300">
            <div className="text-sm text-gray-400 mb-1">Weekly Average</div>
            <div className="text-3xl font-bold text-white mb-1">{metrics.averagePerWeek.toFixed(1)}</div>
            <div className="text-xs text-gray-500">videos per week</div>
          </div>
          <div className="dashboard-card rounded-xl p-6 transition-all duration-300">
            <div className="text-sm text-gray-400 mb-1">Content Pillars</div>
            <div className="text-3xl font-bold text-white mb-1">{pillarsCount}</div>
            <div className="text-xs text-gray-500">active pillars</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CalendarPageHeader;
