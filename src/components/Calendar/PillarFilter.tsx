
import React from 'react';
import { Filter, <PERSON>, EyeOff, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ContentPillar {
  id: string;
  name: string;
  color: string;
}

interface PillarFilterProps {
  pillars: ContentPillar[];
  visiblePillars: Set<string>;
  onTogglePillar: (pillarId: string) => void;
  onToggleAll: () => void;
}

const PillarFilter = ({ pillars, visiblePillars, onTogglePillar, onToggleAll }: PillarFilterProps) => {
  const allVisible = visiblePillars.size === pillars.length;
  const noneVisible = visiblePillars.size === 0;

  if (pillars.length === 0) {
    return (
      <div className="bg-gradient-to-r from-gray-700 to-gray-800 rounded-xl border border-gray-600 p-4">
        <div className="flex items-center space-x-2 text-gray-400">
          <Filter className="w-5 h-5" />
          <span className="text-sm">No content pillars yet</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Quick Toggle Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-teal" />
          <span className="text-sm text-gray-300">Content Pillars</span>
        </div>
        <div className="flex space-x-2">
          {!allVisible && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleAll}
              className="text-teal-400 hover:text-teal-300 hover:bg-teal-400/10 text-xs"
            >
              <CheckCircle className="w-3 h-3 mr-1" />
              Show All
            </Button>
          )}
          {!noneVisible && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleAll}
              className="text-gray-400 hover:text-gray-300 hover:bg-gray-600/50 text-xs"
            >
              <EyeOff className="w-3 h-3 mr-1" />
              Hide All
            </Button>
          )}
        </div>
      </div>

      {/* Pillar Toggle Buttons */}
      <div className="flex flex-wrap gap-2">
        {pillars.map((pillar) => {
          const isVisible = visiblePillars.has(pillar.id);
          return (
            <button
              key={pillar.id}
              onClick={() => onTogglePillar(pillar.id)}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 border ${
                isVisible 
                  ? 'bg-gray-600/50 hover:bg-gray-600/70 border-gray-500 shadow-sm' 
                  : 'bg-gray-800/50 hover:bg-gray-800/70 opacity-50 border-gray-600'
              }`}
            >
              <div
                className={`w-3 h-3 rounded-full border transition-all duration-200 ${
                  isVisible ? 'border-white/40 shadow-sm' : 'border-gray-600'
                }`}
                style={{ 
                  backgroundColor: isVisible ? pillar.color : 'transparent',
                  boxShadow: isVisible ? `0 0 8px ${pillar.color}40` : 'none'
                }}
              />
              <span className={`text-sm font-medium transition-colors duration-200 ${
                isVisible ? 'text-white' : 'text-gray-500'
              }`}>
                {pillar.name}
              </span>
              {isVisible && (
                <CheckCircle className="w-3 h-3 text-teal-400" />
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default PillarFilter;
