
import React from 'react';
import { ChevronLeft, ChevronRight, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface CalendarNavigationProps {
  currentDate: Date;
  view: 'month' | 'week';
  onNavigateMonth: (direction: 'prev' | 'next') => void;
  onViewChange: (view: 'month' | 'week') => void;
  onGoToToday: () => void;
}

const CalendarNavigation = ({ 
  currentDate, 
  view, 
  onNavigateMonth, 
  onViewChange, 
  onGoToToday 
}: CalendarNavigationProps) => {
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const today = new Date();
  const isCurrentMonth = currentDate.getMonth() === today.getMonth() && 
                         currentDate.getFullYear() === today.getFullYear();

  return (
    <div className="bg-gradient-to-r from-gray-700 to-gray-800 rounded-xl border border-gray-600 shadow-lg">
      <div className="p-6">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onNavigateMonth('prev')}
              className="bg-gray-700 border-gray-500 text-white hover:bg-gray-600 hover:border-gray-400 transition-all duration-200 hover:scale-105"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            <div className="text-center bg-gradient-to-r from-gray-600 to-gray-700 px-6 py-3 rounded-lg border border-gray-500">
              <h2 className="text-2xl font-bold text-white">
                {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
              </h2>
              <p className="text-sm text-gray-300 mt-1">
                {currentDate.toLocaleDateString('en-US', { weekday: 'long' })} planning view
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onNavigateMonth('next')}
              className="bg-gray-700 border-gray-500 text-white hover:bg-gray-600 hover:border-gray-400 transition-all duration-200 hover:scale-105"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
          
          <div className="flex items-center space-x-3">
            {!isCurrentMonth && (
              <Button
                variant="outline"
                size="sm"
                onClick={onGoToToday}
                className="bg-teal hover:bg-teal/90 text-white border-teal"
              >
                <Calendar className="w-4 h-4 mr-1" />
                Today
              </Button>
            )}
            
            <div className="flex items-center space-x-3">
              <span className="text-gray-400 text-sm">View:</span>
              <div className="flex bg-gray-800 rounded-lg p-1 border border-gray-600">
                <Button
                  variant={view === 'month' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => onViewChange('month')}
                  className={view === 'month' ? 
                    'bg-teal hover:bg-teal/90 text-white shadow-md' : 
                    'text-gray-300 hover:text-white hover:bg-gray-700'}
                >
                  Month
                </Button>
                <Button
                  variant={view === 'week' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => onViewChange('week')}
                  className={view === 'week' ? 
                    'bg-teal hover:bg-teal/90 text-white shadow-md' : 
                    'text-gray-300 hover:text-white hover:bg-gray-700'}
                >
                  Week
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CalendarNavigation;
