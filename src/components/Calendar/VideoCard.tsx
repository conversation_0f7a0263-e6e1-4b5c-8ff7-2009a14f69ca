
import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, Edit, Eye, MoreVertical } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import VideoEditModal from './VideoEditModal';
import AIWizardDataModal from '@/components/Ideas/AIWizardDataModal';
import { VideoIdea } from '@/components/Ideas/types';
import { ContentPillar } from '@/types/pillar';

interface VideoCardProps {
  video: VideoIdea;
  pillars: ContentPillar[];
  onVideoUpdated: () => void;
  onVideoClick: (video: VideoIdea, e: React.MouseEvent) => void;
  onDragStart: (e: React.DragEvent, video: VideoIdea) => void;
  onStatusChanged: () => void;
  isOverdue?: boolean;
  isToday?: boolean;
}

const VideoCard = ({ 
  video, 
  pillars, 
  onVideoUpdated, 
  onVideoClick, 
  onDragStart, 
  onStatusChanged,
  isOverdue = false,
  isToday = false 
}: VideoCardProps) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  const pillar = pillars.find(p => p.id === video.pillar_id);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'idea': return 'bg-teal-500/20 text-teal-400 border-teal-500/30';
      case 'planned': return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'filming': return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'editing': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'published': return 'bg-green-500/20 text-green-400 border-green-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not scheduled';
    return new Date(dateString).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const handleClick = (e: React.MouseEvent) => {
    onVideoClick(video, e);
  };

  const handleDragStart = (e: React.DragEvent) => {
    onDragStart(e, video);
  };

  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsEditModalOpen(true);
  };

  return (
    <>
      <Card 
        className={`bg-gray-700 border-gray-600 hover:bg-gray-650 transition-colors cursor-pointer ${
          isOverdue ? 'border-red-500/50' : ''
        } ${isToday ? 'border-yellow-500/50' : ''}`}
        onClick={handleClick}
        draggable
        onDragStart={handleDragStart}
      >
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-3">
            <Badge className={`text-xs ${getStatusColor(video.status || 'idea')}`}>
              {video.status || 'idea'}
            </Badge>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsViewModalOpen(true);
                }}
              >
                <Eye className="w-3 h-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={handleEditClick}
              >
                <Edit className="w-3 h-3" />
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreVertical className="w-3 h-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-40 bg-gray-800 border-gray-700">
                  <DropdownMenuItem 
                    className="text-white hover:bg-gray-700 cursor-pointer"
                    onClick={handleEditClick}
                  >
                    <Edit className="w-3 h-3 mr-2" />
                    Edit Details
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <h4 className="text-white font-medium text-sm mb-2 line-clamp-2">
            {video.title}
          </h4>

          {pillar && (
            <div className="flex items-center mb-2">
              <div 
                className="w-2 h-2 rounded-full mr-2" 
                style={{ backgroundColor: pillar.color }}
              />
              <span className="text-xs text-gray-300">{pillar.name}</span>
            </div>
          )}

          <div className="flex items-center gap-3 text-xs text-gray-400">
            <div className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(video.scheduled_date)}</span>
            </div>
            {video.view_count && (
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                <span>{video.view_count.toLocaleString()} views</span>
              </div>
            )}
          </div>

          {video.description && (
            <p className="text-xs text-gray-400 mt-2 line-clamp-2">
              {video.description}
            </p>
          )}
        </CardContent>
      </Card>

      {/* View Video Details Modal */}
      <AIWizardDataModal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        idea={video}
        pillarName={pillar?.name}
      />

      {/* Edit Video Modal */}
      <VideoEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        video={video}
        pillars={pillars}
        onVideoUpdated={() => {
          onVideoUpdated();
          onStatusChanged();
        }}
      />
    </>
  );
};

export default VideoCard;
