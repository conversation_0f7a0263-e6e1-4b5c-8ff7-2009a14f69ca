
import React from 'react';

const CalendarHeader = () => {
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className="grid grid-cols-7 border-b border-gray-600 bg-gradient-to-r from-gray-600 to-gray-700">
      {dayNames.map((day, index) => (
        <div key={day} className={`p-4 text-center font-semibold text-white ${
          index === 0 || index === 6 ? 'bg-gray-800/30' : ''
        }`}>
          <div className="text-sm">{day}</div>
        </div>
      ))}
    </div>
  );
};

export default CalendarHeader;
