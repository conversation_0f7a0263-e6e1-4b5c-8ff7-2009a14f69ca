
import React, { useState, useEffect } from 'react';
import { Calendar, Star, Clock, Plus } from 'lucide-react';
import VideoCard from './VideoCard';
import QuickStatusDropdown from './QuickStatusDropdown';
import { getPillarColor, isOverdue, getActivityLevel } from '@/utils/calendarUtils';
import { VideoIdea } from '@/components/Ideas/types';
import { ContentPillar } from '@/types/pillar';

interface DayCellProps {
  date: Date;
  dayVideos: VideoIdea[];
  pillars: ContentPillar[];
  isOtherMonth: boolean;
  isToday: boolean;
  isWeekend: boolean;
  isPastDate: boolean;
  onDayClick: (date: Date) => void;
  onVideoClick: (video: VideoIdea, e: React.MouseEvent) => void;
  onDragStart: (e: React.DragEvent, video: VideoIdea) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent, date: Date) => void;
  onVideoUpdated: () => void;
}

const DayCell = ({ 
  date, 
  dayVideos, 
  pillars, 
  isOtherMonth, 
  isToday, 
  isWeekend, 
  isPastDate,
  onDayClick,
  onVideoClick,
  onDragStart,
  onDragOver,
  onDrop,
  onVideoUpdated
}: DayCellProps) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const uniquePillars = [...new Set(dayVideos.map(v => v.pillar_id))];
  const activityLevel = getActivityLevel(dayVideos.length);
  const hasOverdueVideos = dayVideos.some(video => isOverdue({
    status: video.status || 'idea',
    scheduled_date: video.scheduled_date || ''
  }, date));
  const hasDueTodayVideos = isToday && dayVideos.some(video => 
    video.status !== 'published' && video.scheduled_date
  );

  return (
    <div
      className={`min-h-[120px] border-r border-b border-gray-600/50 p-3 cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.02] hover:z-10 relative group ${
        isOtherMonth ? 'bg-black/30 opacity-50' :
        isWeekend ? 'bg-black' :
        'bg-black'
      } ${
        isToday ? 'bg-black border-teal-400/50 ring-2 ring-teal-400/30 shadow-teal-400/20' : ''
      } ${
        hasOverdueVideos ? 'bg-black border-red-400/50' :
        hasDueTodayVideos ? 'bg-black border-orange-400/50' :
        activityLevel === 'high' ? 'bg-black border-purple-400/50' :
        activityLevel === 'medium' ? 'bg-black border-teal-400/50' :
        activityLevel === 'low' ? 'bg-black border-green-400/50' : ''
      }`}
      onClick={() => onDayClick(date)}
      onDragOver={onDragOver}
      onDrop={(e) => onDrop(e, date)}
    >
      {/* Day number and indicators */}
      <div className="flex items-center justify-between mb-3">
        <div className={`text-lg font-bold transition-colors duration-200 ${
          isOtherMonth ? 'text-gray-500' : 
          isToday ? 'text-teal-300 text-xl' : 
          isPastDate ? 'text-gray-400' :
          'text-gray-200 group-hover:text-white'
        }`}>
          {date.getDate()}
        </div>
        
        {/* Today indicator */}
        {isToday && (
          <div className="absolute top-2 right-2">
            <Star className="w-4 h-4 text-yellow-400 fill-current animate-pulse" />
          </div>
        )}

        {/* Due today indicator */}
        {hasDueTodayVideos && (
          <div className="absolute top-2 left-2">
            <Clock className="w-4 h-4 text-orange-400 animate-pulse" />
          </div>
        )}
      </div>
      
      {/* Video pills arranged in a flexible grid */}
      <div className="flex flex-wrap gap-1.5">
        {dayVideos.slice(0, 6).map(video => (
          <VideoCard
            key={video.id}
            video={video}
            pillars={pillars}
            isOverdue={isOverdue({
              status: video.status || 'idea',
              scheduled_date: video.scheduled_date || ''
            }, date)}
            isToday={isToday && Boolean(video.scheduled_date)}
            onVideoClick={onVideoClick}
            onDragStart={onDragStart}
            onStatusChanged={onVideoUpdated}
            onVideoUpdated={onVideoUpdated}
          />
        ))}
        
        {dayVideos.length > 6 && (
          <div className="flex items-center justify-center w-8 h-8 bg-gray-800/60 border border-gray-600 rounded-full text-xs text-gray-300 font-medium">
            +{dayVideos.length - 6}
          </div>
        )}
      </div>

      {/* Hover overlay for empty days */}
      {dayVideos.length === 0 && !isOtherMonth && (
        <div className="absolute inset-0 bg-teal/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <Plus className="w-6 h-6 text-teal-300 mx-auto mb-1 opacity-80" />
            <div className="text-teal-300 text-xs font-medium opacity-80">
              Add video
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DayCell;
