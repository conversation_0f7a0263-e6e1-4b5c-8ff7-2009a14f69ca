
import React from 'react';
import { Calendar, Clock, Video, TrendingUp, Edit, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Video {
  id: string;
  title: string;
  status: string;
  scheduled_date: string;
  calendar_notes: string;
  pillar_id: string;
  description?: string;
  youtube_video_id?: string;
  views?: number;
  published_at?: string;
  youtube_thumbnail_url?: string;
}

interface ContentPillar {
  id: string;
  name: string;
  color: string;
}

interface TimelineViewProps {
  currentDate: Date;
  videos: Video[];
  pillars: ContentPillar[];
  onVideoClick: (video: Video, e: React.MouseEvent) => void;
  visiblePillars: Set<string>;
  showPublished: boolean;
}

const TimelineView = ({ 
  currentDate, 
  videos, 
  pillars, 
  onVideoClick,
  visiblePillars,
  showPublished
}: TimelineViewProps) => {
  const getPillarColor = (pillarId: string) => {
    const pillar = pillars.find(p => p.id === pillarId);
    return pillar?.color || '#37BEB0';
  };

  const getPillarName = (pillarId: string) => {
    const pillar = pillars.find(p => p.id === pillarId);
    return pillar?.name || 'Uncategorized';
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'published': return <TrendingUp className="w-4 h-4" />;
      case 'ready': return <Video className="w-4 h-4" />;
      case 'editing': return <Edit className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'published': return 'text-purple-400 bg-purple-400/20 border-purple-400/30';
      case 'ready': return 'text-green-400 bg-green-400/20 border-green-400/30';
      case 'editing': return 'text-orange-400 bg-orange-400/20 border-orange-400/30';
      case 'planned': return 'text-teal-400 bg-teal-400/20 border-teal-400/30';
      default: return 'text-gray-400 bg-gray-400/20 border-gray-400/30';
    }
  };

  // Filter and sort videos
  const filteredVideos = videos
    .filter(video => {
      // Filter by pillar visibility
      if (video.pillar_id && !visiblePillars.has(video.pillar_id)) return false;
      
      // Filter by published visibility
      if (!showPublished && video.status === 'published') return false;
      
      return true;
    })
    .sort((a, b) => {
      const dateA = a.published_at || a.scheduled_date;
      const dateB = b.published_at || b.scheduled_date;
      if (!dateA && !dateB) return 0;
      if (!dateA) return 1;
      if (!dateB) return -1;
      return new Date(dateA).getTime() - new Date(dateB).getTime();
    });

  // Group videos by date
  const groupedVideos = filteredVideos.reduce((groups, video) => {
    const date = video.published_at 
      ? new Date(video.published_at).toDateString()
      : video.scheduled_date 
      ? new Date(video.scheduled_date).toDateString()
      : 'No Date';
    
    if (!groups[date]) groups[date] = [];
    groups[date].push(video);
    return groups;
  }, {} as Record<string, Video[]>);

  const today = new Date().toDateString();

  return (
    <div className="space-y-6">
      {Object.entries(groupedVideos).map(([dateString, dateVideos]) => {
        const isToday = dateString === today;
        const isPast = dateString !== 'No Date' && new Date(dateString) < new Date();
        
        return (
          <div key={dateString} className="relative">
            {/* Date Header */}
            <div className={`sticky top-0 z-10 bg-gradient-to-r from-gray-800 to-gray-700 rounded-xl border p-4 mb-4 backdrop-blur-sm ${
              isToday ? 'border-teal-400/50 bg-gradient-to-r from-teal-900/30 to-teal-800/20' :
              isPast ? 'border-gray-600/30 opacity-75' :
              'border-gray-600/50'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Calendar className={`w-5 h-5 ${isToday ? 'text-teal-400' : 'text-gray-400'}`} />
                  <h3 className={`text-lg font-semibold ${
                    isToday ? 'text-teal-300' : 'text-white'
                  }`}>
                    {dateString === 'No Date' ? 'Unscheduled' : 
                     isToday ? 'Today' :
                     new Date(dateString).toLocaleDateString('en-US', { 
                       weekday: 'long', 
                       year: 'numeric', 
                       month: 'long', 
                       day: 'numeric' 
                     })}
                  </h3>
                  {isToday && (
                    <div className="bg-teal-400/20 border border-teal-400/30 rounded-full px-2 py-1">
                      <span className="text-xs text-teal-300 font-medium">Today</span>
                    </div>
                  )}
                </div>
                <div className="text-sm text-gray-400">
                  {dateVideos.length} video{dateVideos.length !== 1 ? 's' : ''}
                </div>
              </div>
            </div>

            {/* Videos List */}
            <div className="grid gap-4">
              {dateVideos.map((video, index) => (
                <div 
                  key={video.id}
                  className="group relative bg-gradient-to-r from-gray-700/80 to-gray-800/60 rounded-xl border border-gray-600/50 p-6 hover:border-gray-500/70 transition-all duration-300 hover:shadow-xl hover:scale-[1.01] cursor-pointer backdrop-blur-sm"
                  onClick={(e) => onVideoClick(video, e)}
                >
                  {/* Video Content */}
                  <div className="flex items-start space-x-4">
                    {/* Pillar Indicator */}
                    <div 
                      className="w-1 h-16 rounded-full shadow-lg flex-shrink-0"
                      style={{ backgroundColor: getPillarColor(video.pillar_id) }}
                    />

                    {/* Video Details */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h4 className="text-lg font-semibold text-white group-hover:text-teal-300 transition-colors duration-200 mb-1">
                            {video.title}
                          </h4>
                          <p className="text-sm text-gray-400 mb-2">
                            {getPillarName(video.pillar_id)}
                          </p>
                          {video.calendar_notes && (
                            <p className="text-sm text-gray-300 opacity-80">
                              {video.calendar_notes}
                            </p>
                          )}
                        </div>

                        {/* Status Badge */}
                        <div className={`inline-flex items-center space-x-1.5 px-3 py-1.5 rounded-lg border text-sm font-medium ${getStatusColor(video.status)}`}>
                          {getStatusIcon(video.status)}
                          <span className="capitalize">{video.status}</span>
                        </div>
                      </div>

                      {/* Video Metrics */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-sm text-gray-400">
                          {video.published_at && (
                            <div className="flex items-center space-x-1">
                              <Clock className="w-4 h-4" />
                              <span>Published {new Date(video.published_at).toLocaleDateString()}</span>
                            </div>
                          )}
                          {video.views && (
                            <div className="flex items-center space-x-1">
                              <Eye className="w-4 h-4" />
                              <span>{video.views.toLocaleString()} views</span>
                            </div>
                          )}
                        </div>

                        {/* Quick Actions */}
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-gray-700/50 border-gray-500/50 text-white hover:bg-gray-600/60"
                            onClick={(e) => {
                              e.stopPropagation();
                              onVideoClick(video, e);
                            }}
                          >
                            Edit
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Hover Glow Effect */}
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-teal-400/5 to-orange/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                </div>
              ))}
            </div>
          </div>
        );
      })}

      {filteredVideos.length === 0 && (
        <div className="text-center py-16">
          <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-300 mb-2">No videos found</h3>
          <p className="text-gray-500">Try adjusting your filters or add some videos to your calendar.</p>
        </div>
      )}
    </div>
  );
};

export default TimelineView;
