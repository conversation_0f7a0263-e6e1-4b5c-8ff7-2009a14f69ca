
import React, { useState } from 'react';
import VideoEditModal from './VideoEditModal';
import CalendarHeader from './CalendarHeader';
import DayCell from './DayCell';
import { useCalendarDays } from '@/hooks/calendar/useCalendarDays';
import { useVideoFiltering } from '@/hooks/calendar/useVideoFiltering';
import { useDragAndDrop } from '@/hooks/calendar/useDragAndDrop';
import { isToday, isWeekend } from 'date-fns';
import { isPastDate } from '@/utils/calendarUtils';
import { VideoIdea } from '@/components/Ideas/types';
import { ContentPillar } from '@/types/pillar';

interface CalendarGridProps {
  currentDate: Date;
  videos: VideoIdea[];
  pillars: ContentPillar[];
  onDayClick: (date: Date) => void;
  view: 'month' | 'week';
  onVideoUpdated?: () => void;
  visiblePillars: Set<string>;
  showPublished: boolean;
}

const CalendarGrid = ({ 
  currentDate, 
  videos, 
  pillars, 
  onDayClick, 
  view, 
  onVideoUpdated,
  visiblePillars,
  showPublished
}: CalendarGridProps) => {
  const [selectedVideo, setSelectedVideo] = useState<VideoIdea | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const days = useCalendarDays(currentDate);
  const { getVideosForDate } = useVideoFiltering(videos, visiblePillars, showPublished);
  const { handleDragStart, handleDragOver, handleDrop } = useDragAndDrop(onVideoUpdated);

  const handleVideoClick = (video: VideoIdea, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedVideo(video);
    setIsEditModalOpen(true);
  };

  const handleVideoUpdated = () => {
    if (onVideoUpdated) {
      onVideoUpdated();
    }
  };

  return (
    <>
      <div className="bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl border border-gray-600 overflow-hidden shadow-2xl">
        {/* Calendar Header */}
        <CalendarHeader />

        {/* Calendar Grid */}
        <div className={`grid grid-cols-7 ${view === 'month' ? 'grid-rows-6' : 'grid-rows-1'}`}>
          {days.map((dayData, index) => {
            const { date, isCurrentMonth: isDayInCurrentMonth } = dayData;
            const dayVideos = getVideosForDate(date);
            const isOtherMonth = view === 'month' && !isDayInCurrentMonth;
            
            return (
              <DayCell
                key={index}
                date={date}
                dayVideos={dayVideos}
                pillars={pillars}
                isOtherMonth={isOtherMonth}
                isToday={isToday(date)}
                isWeekend={isWeekend(date)}
                isPastDate={isPastDate(date)}
                onDayClick={onDayClick}
                onVideoClick={handleVideoClick}
                onDragStart={handleDragStart}
                onDragOver={handleDragOver}
                onDrop={handleDrop}
                onVideoUpdated={handleVideoUpdated}
              />
            );
          })}
        </div>
      </div>

      {/* Video Edit Modal */}
      <VideoEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        video={selectedVideo}
        pillars={pillars}
        onSave={(_video: VideoIdea) => {
          handleVideoUpdated();
          setIsEditModalOpen(false);
        }}
      />
    </>
  );
};

export default CalendarGrid;
