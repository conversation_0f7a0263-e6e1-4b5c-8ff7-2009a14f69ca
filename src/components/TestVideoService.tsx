import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/hooks/useAuth';
import { dataService } from '@/services/dataService';
import { FEATURE_FLAGS } from '@/config/featureFlags';
import { supabase } from '@/lib/supabase';

export default function TestVideoService() {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (name: string, success: boolean, data: any = null, error: any = null) => {
    setTestResults(prev => [
      {
        name,
        success,
        data: data ? JSON.stringify(data, null, 2) : null,
        error: error ? JSON.stringify(error, null, 2) : null,
        timestamp: new Date().toISOString()
      },
      ...prev
    ]);
  };

  const runDirectTest = async () => {
    if (!user) return;
    setIsLoading(true);
    
    try {
      // Direct Supabase query
      const { data, error } = await supabase
        .from('videos')
        .select('*')
        .eq('user_id', user.id)
        .limit(5);
        
      if (error) throw error;
      
      addResult('Direct Supabase Query', true, { count: data.length, sample: data.slice(0, 2) });
    } catch (error) {
      addResult('Direct Supabase Query', false, null, error);
    } finally {
      setIsLoading(false);
    }
  };

  const runServiceTest = async () => {
    if (!user) return;
    setIsLoading(true);
    
    try {
      // Data service query
      const data = await dataService.videos.getAll(user.id);
      
      if (data) {
        addResult('Data Service Query', true, { count: data.length, sample: data.slice(0, 2) });
      } else {
        throw new Error('No data returned from data service');
      }
    } catch (error) {
      addResult('Data Service Query', false, null, error);
    } finally {
      setIsLoading(false);
    }
  };

  const runPublishedTest = async () => {
    if (!user) return;
    setIsLoading(true);
    
    try {
      // Data service query for published videos
      const data = await dataService.videos.getPublished(user.id);
      
      if (data) {
        addResult('Published Videos Query', true, { count: data.length, sample: data.slice(0, 2) });
      } else {
        throw new Error('No data returned from data service');
      }
    } catch (error) {
      addResult('Published Videos Query', false, null, error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleFeatureFlag = () => {
    // This is just for testing - in a real app, you'd update the config file
    // @ts-ignore - we're intentionally modifying the constant for testing
    FEATURE_FLAGS.USE_DATA_SERVICE_VIDEOS = !FEATURE_FLAGS.USE_DATA_SERVICE_VIDEOS;
    addResult(
      'Toggle Feature Flag', 
      true, 
      { USE_DATA_SERVICE_VIDEOS: FEATURE_FLAGS.USE_DATA_SERVICE_VIDEOS }
    );
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Video Data Service Test</h1>
      
      <div className="flex flex-wrap gap-4 mb-8">
        <Button onClick={runDirectTest} disabled={isLoading}>
          Test Direct Query
        </Button>
        <Button onClick={runServiceTest} disabled={isLoading}>
          Test Data Service
        </Button>
        <Button onClick={runPublishedTest} disabled={isLoading}>
          Test Published Videos
        </Button>
        <Button onClick={toggleFeatureFlag} variant="outline">
          Toggle Feature Flag ({FEATURE_FLAGS.USE_DATA_SERVICE_VIDEOS ? 'ON' : 'OFF'})
        </Button>
      </div>
      
      <div className="space-y-4">
        {testResults.map((result, index) => (
          <Card key={index} className={result.success ? "border-green-500" : "border-red-500"}>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex justify-between">
                <span>{result.name}</span>
                <span>{result.success ? '✅ Success' : '❌ Failed'}</span>
              </CardTitle>
              <div className="text-xs text-gray-500">{result.timestamp}</div>
            </CardHeader>
            <CardContent>
              {result.data && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium mb-1">Data:</h3>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
                    {result.data}
                  </pre>
                </div>
              )}
              {result.error && (
                <div>
                  <h3 className="text-sm font-medium mb-1">Error:</h3>
                  <pre className="text-xs bg-red-50 p-2 rounded overflow-auto max-h-40">
                    {result.error}
                  </pre>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}