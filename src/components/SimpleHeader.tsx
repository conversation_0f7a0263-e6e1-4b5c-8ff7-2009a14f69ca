import React from 'react';
import { useLocation } from 'react-router-dom';

export function SimpleHeader() {
  const location = useLocation();
  
  // Get page title based on current path
  const getPageTitle = () => {
    const path = location.pathname;
    if (path.includes('/dashboard')) return 'Dashboard';
    if (path.includes('/pillars')) return 'Content Pillars';
    if (path.includes('/ideas')) return 'Ideas Bank';
    if (path.includes('/calendar')) return 'Content Calendar';
    if (path.includes('/whiteboard')) return 'Whiteboard';
    if (path.includes('/test')) return 'Test Page';
    return 'MyContentHub';
  };

  return (
    <header className="bg-white shadow-sm p-4 mb-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">{getPageTitle()}</h2>
        <div className="flex items-center space-x-4">
          <button className="px-4 py-2 bg-teal-500 text-white rounded hover:bg-teal-600">
            New Video
          </button>
          <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
        </div>
      </div>
    </header>
  );
}
