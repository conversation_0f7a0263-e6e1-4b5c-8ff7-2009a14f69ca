
import React from 'react';
import { ContentPillar } from '@/types/pillar';
import { useNeedsAssignment } from './NeedsAssignment/useNeedsAssignment';
import BulkActionsCard from './NeedsAssignment/BulkActionsCard';
import VideoListItem from './NeedsAssignment/VideoListItem';
import { YouTubeNotConnectedState, AllVideosAssignedState, LoadingState } from './NeedsAssignment/EmptyStates';

interface NeedsAssignmentTabProps {
  pillars: ContentPillar[];
}

const NeedsAssignmentTab = ({ pillars }: NeedsAssignmentTabProps) => {
  const {
    videos,
    selectedVideos,
    bulkPillar,
    setBulkPillar,
    isLoading,
    isAssigning,
    userProfile,
    assignPillar,
    bulkAssignPillars,
    toggleVideoSelection,
    selectAll
  } = useNeedsAssignment();

  if (isLoading) {
    return <LoadingState />;
  }

  if (!userProfile?.youtube_channel_id) {
    return <YouTubeNotConnectedState />;
  }

  if (videos.length === 0) {
    return <AllVideosAssignedState />;
  }

  return (
    <div className="space-y-6">
      <BulkActionsCard
        videosCount={videos.length}
        selectedCount={selectedVideos.size}
        bulkPillar={bulkPillar}
        setBulkPillar={setBulkPillar}
        isAssigning={isAssigning}
        pillars={pillars}
        onSelectAll={selectAll}
        onBulkAssign={bulkAssignPillars}
      />

      <div className="space-y-3">
        {videos.map(video => (
          <VideoListItem
            key={video.id}
            video={video}
            isSelected={selectedVideos.has(video.id)}
            pillars={pillars}
            onToggleSelection={toggleVideoSelection}
            onAssignPillar={assignPillar}
          />
        ))}
      </div>
    </div>
  );
};

export default NeedsAssignmentTab;
