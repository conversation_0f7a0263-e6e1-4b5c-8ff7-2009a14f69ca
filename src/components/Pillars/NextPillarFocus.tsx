
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Eye, TrendingUp, AlertTriangle, Target, Lightbulb } from 'lucide-react';
import { ContentPillar } from '@/types/pillar';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface NextPillarFocusProps {
  pillars: ContentPillar[];
}

const NextPillarFocus = ({ pillars }: NextPillarFocusProps) => {
  const [showInsights, setShowInsights] = useState(false);

  if (!pillars || pillars.length === 0) return null;

  const generatePillarInsights = () => {
    const insights = [];

    // Find pillars that are under-performing their targets
    const underPerformingPillars = pillars.filter(p => 
      p.actual_percentage < p.target_percentage - 5
    );

    // Find pillars that are over-performing their targets
    const overPerformingPillars = pillars.filter(p => 
      p.actual_percentage > p.target_percentage + 5
    );

    // Find the best performing pillar by average views
    const bestPerformer = pillars.reduce((best, current) => 
      (current.avg_views || 0) > (best.avg_views || 0) ? current : best
    );

    // Generate insights based on data
    if (underPerformingPillars.length > 0) {
      const pillar = underPerformingPillars[0];
      insights.push({
        type: 'opportunity',
        icon: TrendingUp,
        title: `Boost Your ${pillar.name} Content`,
        description: `You're ${pillar.target_percentage - pillar.actual_percentage}% behind your target for ${pillar.name}. Creating 2-3 more videos in this pillar could help balance your strategy.`,
        action: 'Plan More Content',
        priority: 'high'
      });
    }

    if (overPerformingPillars.length > 0) {
      const pillar = overPerformingPillars[0];
      insights.push({
        type: 'balance',
        icon: AlertTriangle,
        title: `Rebalance ${pillar.name} Content`,
        description: `You're ${pillar.actual_percentage - pillar.target_percentage}% over your target for ${pillar.name}. Consider focusing on other pillars to maintain balance.`,
        action: 'Adjust Strategy',
        priority: 'medium'
      });
    }

    if (bestPerformer && (bestPerformer.avg_views || 0) > 0) {
      insights.push({
        type: 'success',
        icon: Target,
        title: `${bestPerformer.name} is Your Top Performer`,
        description: `Your ${bestPerformer.name} content averages ${bestPerformer.avg_views?.toLocaleString()} views. Consider creating more content around this theme.`,
        action: 'Expand Success',
        priority: 'high'
      });
    }

    // Add general strategy insight
    const totalPercentage = pillars.reduce((sum, p) => sum + p.target_percentage, 0);
    if (Math.abs(totalPercentage - 100) > 5) {
      insights.push({
        type: 'strategy',
        icon: Lightbulb,
        title: 'Strategy Alignment Needed',
        description: `Your pillar targets total ${totalPercentage}%. Adjust your targets to total 100% for optimal content balance.`,
        action: 'Adjust Targets',
        priority: 'medium'
      });
    }

    return insights;
  };

  const insights = generatePillarInsights();

  const getIconColor = (type: string) => {
    switch (type) {
      case 'opportunity': return 'text-green-400';
      case 'balance': return 'text-orange-400';
      case 'success': return 'text-teal';
      case 'strategy': return 'text-yellow';
      default: return 'text-gray-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'medium': return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'low': return 'bg-green-500/20 text-green-400 border-green-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  return (
    <>
      <Card className="bg-gray-700/50 backdrop-blur-sm border-2 border-teal/30 bg-teal/5 h-full flex flex-col">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-lg flex items-center gap-2">
            <span className="text-teal">💡</span>
            Strategy Recommendations Available
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 flex-1 flex flex-col justify-between">
          <div className="text-gray-300">
            Get personalized insights about your content strategy, pillar balance, and growth opportunities based on your current performance data.
          </div>
          
          <Button 
            onClick={() => setShowInsights(true)}
            className="bg-teal hover:bg-teal/90 text-white flex items-center gap-2 mt-auto"
          >
            <Eye className="w-4 h-4" />
            View Pillar Insights
          </Button>
        </CardContent>
      </Card>

      <Dialog open={showInsights} onOpenChange={setShowInsights}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-3">
              <Lightbulb className="w-6 h-6 text-teal" />
              <span>Pillar Strategy Insights</span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <p className="text-gray-300">
              Based on your current pillar performance and targets, here are personalized recommendations to optimize your content strategy:
            </p>

            {insights.length === 0 ? (
              <div className="text-center py-8 text-gray-400">
                <Target className="w-12 h-12 mx-auto mb-4" />
                <p>Your pillar strategy looks well balanced!</p>
                <p className="text-sm mt-1">Keep creating content according to your current targets.</p>
              </div>
            ) : (
              <div className="grid gap-4">
                {insights.map((insight, index) => {
                  const IconComponent = insight.icon;
                  return (
                    <div key={index} className="border border-gray-600 rounded-lg p-6 bg-gray-800/50">
                      <div className="flex items-start space-x-4">
                        <div className={`p-3 rounded-lg bg-gray-700/50 ${getIconColor(insight.type)}`}>
                          <IconComponent className="w-6 h-6" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="text-white font-semibold text-lg">{insight.title}</h3>
                            <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getPriorityColor(insight.priority)}`}>
                              {insight.priority.charAt(0).toUpperCase() + insight.priority.slice(1)} Priority
                            </span>
                          </div>
                          <p className="text-gray-300 mb-4 leading-relaxed">{insight.description}</p>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" className="bg-teal hover:bg-teal/90 text-white">
                              {insight.action}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            <div className="bg-teal/10 border border-teal/30 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Lightbulb className="w-5 h-5 text-teal mt-0.5" />
                <div>
                  <h3 className="text-white font-medium mb-2">Pro Tip</h3>
                  <p className="text-gray-300 text-sm">
                    Monitor your pillar performance monthly and adjust your content plan based on what's working. 
                    Focus on creating more content for high-performing pillars while maintaining your target balance.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default NextPillarFocus;
