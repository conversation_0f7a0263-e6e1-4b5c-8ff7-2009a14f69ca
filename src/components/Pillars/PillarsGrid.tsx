import React, { useMemo } from 'react';
import { ContentPillar } from '@/types/pillar';
import EnhancedPillarCard from './EnhancedPillarCard';
import AddPillarCard from './AddPillarCard';
import PillarsEmptyState from './PillarsEmptyState';

interface PillarsGridProps {
  pillars: ContentPillar[];
  onEdit?: (pillar: ContentPillar) => void;
  onDelete?: (pillarId: string, pillarName: string) => void;
  onDeletePillar?: (pillarId: string, pillarName: string) => void;
  onEditPillar?: (pillar: ContentPillar) => void;
  onViewVideos?: (pillarId: string) => void;
  onViewAnalytics?: (pillar: ContentPillar) => void;
  canAddMore?: boolean;
  onAddNew?: () => void;
  userTier?: string;
  pillarLimit?: number;
}

const PillarsGrid = ({
  pillars = [], // Add default empty array
  onEdit,
  onDelete,
  onDeletePillar,
  onEditPillar,
  onViewVideos,
  onViewAnalytics,
  canAddMore,
  onAddNew,
  userTier,
  pillarLimit
}: PillarsGridProps) => {
  // Add null check
  if (!pillars) {
    return <div className="text-center py-8">No pillars data available</div>;
  }

  // Use the appropriate delete handler
  const handleDelete = onDeletePillar || onDelete;
  const handleEdit = onEditPillar || onEdit;

  if (pillars.length === 0 && onAddNew) {
    return <PillarsEmptyState onAddPillar={onAddNew} />;
  }

  // Memoize the grid to prevent unnecessary re-renders
  const pillarGrid = useMemo(() => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {pillars.map((pillar) => (
        <EnhancedPillarCard
          key={pillar.id}
          pillar={pillar}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onViewVideos={onViewVideos}
          onViewAnalytics={onViewAnalytics}
        />
      ))}
      {/* Add Pillar Card */}
      {onAddNew && (
        <AddPillarCard
          onAddPillar={onAddNew}
          userTier={userTier}
          currentCount={pillars.length}
          limit={pillarLimit}
        />
      )}
    </div>
  ), [pillars, handleEdit, handleDelete, onViewVideos, onViewAnalytics, onAddNew, userTier, pillarLimit]);

  return pillarGrid;
};

export default React.memo(PillarsGrid);
