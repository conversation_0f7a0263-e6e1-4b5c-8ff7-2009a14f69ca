
import React from 'react';
import { Trash2, Edit, MoreVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Progress } from '@/components/ui/progress';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ContentPillar } from '@/types/pillar';
import PillarPerformanceIndicator from './PillarPerformanceIndicator';

interface EnhancedPillarCardProps {
  pillar: ContentPillar;
  onDelete: (pillarId: string, pillarName: string) => void;
  onEdit: (pillar: ContentPillar) => void;
  onViewVideos?: (pillarId: string) => void;
  onViewAnalytics?: (pillar: ContentPillar) => void;
}

const EnhancedPillarCard = ({ pillar, onDelete, onEdit, onViewVideos, onViewAnalytics }: EnhancedPillarCardProps) => {
  const handleCardClick = (e: React.MouseEvent) => {
    // Prevent click if clicking on interactive elements
    const target = e.target as HTMLElement;
    const isInteractiveElement = target.closest('button') || target.closest('[role="menuitem"]') || target.closest('[role="dialog"]');
    
    if (!isInteractiveElement && onViewAnalytics) {
      onViewAnalytics(pillar);
    }
  };

  return (
    <div 
      className="group glass-effect rounded-xl border border-gray-600 p-6 hover:shadow-xl hover:shadow-gray-900/20 hover:border-teal/50 transition-all duration-300 relative overflow-hidden cursor-pointer"
      onClick={handleCardClick}
    >
      {/* Background gradient accent */}
      <div 
        className="absolute top-0 left-0 w-full h-1 opacity-80"
        style={{ backgroundColor: pillar.color }}
      />
      
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          <div
            className="w-5 h-5 rounded-full shadow-sm border border-white/20"
            style={{ backgroundColor: pillar.color }}
          />
          <div className="min-w-0 flex-1">
            <h3 
              className="text-lg font-semibold truncate"
              style={{ color: pillar.color }}
            >
              {pillar.name}
            </h3>
            <p className="text-sm text-gray-400">
              Target: {pillar.target_percentage}% of content
            </p>
          </div>
        </div>
        
        {/* Actions Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="opacity-0 group-hover:opacity-100 transition-opacity p-1 h-auto text-gray-400 hover:text-white"
            >
              <MoreVertical className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="bg-gray-800 border-gray-600">
            <DropdownMenuItem 
              onClick={(e) => {
                e.stopPropagation();
                onEdit(pillar);
              }}
              className="text-gray-300 hover:text-white hover:bg-gray-700"
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit Pillar
            </DropdownMenuItem>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <DropdownMenuItem 
                  onSelect={(e) => e.preventDefault()}
                  className="text-red-400 hover:text-red-300 hover:bg-red-900/20"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete Pillar
                </DropdownMenuItem>
              </AlertDialogTrigger>
              <AlertDialogContent className="bg-gray-800 border-gray-600">
                <AlertDialogHeader>
                  <AlertDialogTitle className="text-white">Delete Pillar</AlertDialogTitle>
                  <AlertDialogDescription className="text-gray-300">
                    Are you sure you want to delete the "{pillar.name}" pillar? This action cannot be undone and will remove all associated videos from this pillar.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel className="bg-gray-700 text-gray-300 hover:bg-gray-600">
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => onDelete(pillar.id, pillar.name)}
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {/* Progress Bar with Enhanced Visual */}
      <div className="space-y-3 mb-4">
        <div className="relative">
          <div className="flex justify-between text-xs text-gray-400 mb-1">
            <span>Progress</span>
            <span>{pillar.actual_percentage}% / {pillar.target_percentage}%</span>
          </div>
          <div className="relative">
            <div className="w-full bg-gray-600/50 rounded-full h-3 overflow-hidden">
              <div 
                className="h-3 rounded-full transition-all duration-500 ease-out relative"
                style={{ 
                  width: `${Math.min(pillar.actual_percentage, 100)}%`,
                  backgroundColor: pillar.color,
                  boxShadow: `0 0 10px ${pillar.color}40`
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent to-white/20 rounded-full" />
              </div>
            </div>
            {/* Target indicator line */}
            {pillar.target_percentage > 0 && (
              <div 
                className="absolute top-0 h-3 w-0.5 bg-white/80 rounded-full shadow-sm"
                style={{ left: `${Math.min(pillar.target_percentage, 100)}%` }}
                title={`Target: ${pillar.target_percentage}%`}
              />
            )}
          </div>
        </div>
      </div>
      
      {/* Performance Indicator */}
      <PillarPerformanceIndicator
        actual={pillar.actual_percentage}
        target={pillar.target_percentage}
        videoCount={pillar.video_count}
        avgViews={pillar.avg_views}
      />
      
      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-600/50">
        <div className="text-xs text-gray-400">
          Click to view detailed analytics
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          className="border-teal text-teal hover:bg-teal hover:text-white"
          onClick={(e) => {
            e.stopPropagation();
            onViewVideos?.(pillar.id);
          }}
        >
          View Videos
        </Button>
      </div>
    </div>
  );
};

export default EnhancedPillarCard;
