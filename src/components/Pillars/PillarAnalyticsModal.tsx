
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { ContentPillar } from '@/types/pillar';
import AnalyticsOverviewCards from './Analytics/AnalyticsOverviewCards';
import AnalyticsCharts from './Analytics/AnalyticsCharts';
import EngagementMetrics from './Analytics/EngagementMetrics';
import InsightsCard from './Analytics/InsightsCard';
import PerformanceBadges from './Analytics/PerformanceBadges';
import QuickStatsFooter from './Analytics/QuickStatsFooter';

interface PillarAnalyticsModalProps {
  pillar: ContentPillar | null;
  isOpen: boolean;
  onClose: () => void;
}

const PillarAnalyticsModal = ({ pillar, isOpen, onClose }: PillarAnalyticsModalProps) => {
  if (!pillar) return null;

  // Use real data only - no mock data
  const enhancedPillar: ContentPillar = {
    ...pillar,
    avg_watch_time: pillar.avg_watch_time || 0,
    engagement_rate: pillar.engagement_rate || 0,
    ctr: pillar.ctr || 0,
    subscriber_conversion: pillar.subscriber_conversion || 0,
    optimal_video_length: pillar.optimal_video_length || 'No data',
    top_traffic_source: pillar.top_traffic_source || { name: 'No data', percentage: 0 },
    is_trending: pillar.is_trending || false,
    is_top_performer: pillar.is_top_performer || false,
    is_high_revenue: pillar.is_high_revenue || false,
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] bg-gray-800 border-gray-600 overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div>
            <DialogTitle className="text-white flex items-center">
              <div 
                className="w-4 h-4 rounded-full mr-3"
                style={{ backgroundColor: enhancedPillar.color }}
              />
              {enhancedPillar.name} Analytics
            </DialogTitle>
            <PerformanceBadges pillar={enhancedPillar} />
          </div>
        </DialogHeader>
        
        <div className="flex-1 overflow-y-auto pr-2 space-y-6">
          {/* Overview Cards */}
          <div className="transition-all duration-300">
            <AnalyticsOverviewCards pillar={enhancedPillar} />
          </div>

          {/* Performance Charts */}
          <div className="transition-all duration-300">
            <AnalyticsCharts pillar={enhancedPillar} />
          </div>

          {/* Engagement Metrics (replaces Target Progress) */}
          <div className="transition-all duration-300">
            <EngagementMetrics pillar={enhancedPillar} />
          </div>

          {/* Insights */}
          <div className="transition-all duration-300">
            <InsightsCard pillar={enhancedPillar} />
          </div>

          {/* Quick Stats Footer */}
          <QuickStatsFooter pillar={enhancedPillar} />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PillarAnalyticsModal;
