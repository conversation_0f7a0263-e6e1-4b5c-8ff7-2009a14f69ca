
import React from 'react';
import { TrendingUp, Trash2, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Progress } from '@/components/ui/progress';
import { ContentPillar } from '@/types/pillar';

interface PillarCardProps {
  pillar: ContentPillar;
  onDelete: (pillarId: string, pillarName: string) => void;
  onEdit: (pillar: ContentPillar) => void;
  onViewVideos?: (pillarId: string) => void;
  onViewAnalytics?: (pillar: ContentPillar) => void;
}

const PillarCard = ({ pillar, onDelete, onEdit, onViewVideos, onViewAnalytics }: PillarCardProps) => {
  const getPerformanceColor = (actual: number, target: number) => {
    const diff = Math.abs(actual - target);
    if (diff <= 5) return 'text-green-400';
    if (diff <= 10) return 'text-orange';
    return 'text-red-400';
  };

  const getPerformanceStatus = (actual: number, target: number) => {
    const diff = actual - target;
    if (Math.abs(diff) <= 5) return 'On Target';
    return diff > 0 ? 'Over Target' : 'Under Target';
  };

  const performanceColor = getPerformanceColor(pillar.actual_percentage, pillar.target_percentage);
  const performanceStatus = getPerformanceStatus(pillar.actual_percentage, pillar.target_percentage);

  // Add console log to debug the pillar data
  console.log('Pillar data in PillarCard:', pillar);

  // Add a function to format percentages
  const formatPercentage = (value: number | null | undefined) => {
    if (value === null || value === undefined) return '0%';
    return `${value}%`;
  };

  return (
    <div 
      className="bg-gray-700 rounded-lg border border-gray-600 p-6 hover:shadow-lg hover:border-teal/50 transition-all duration-300 relative cursor-pointer group"
      onClick={() => onViewAnalytics?.(pillar)}
    >
      {/* Action Buttons */}
      <div className="absolute top-4 right-4 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button
          variant="ghost"
          size="sm"
          className="p-1 h-auto text-gray-400 hover:text-teal hover:bg-teal/20"
          onClick={(e) => {
            e.stopPropagation();
            onEdit(pillar);
          }}
        >
          <Edit className="w-4 h-4" />
        </Button>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="p-1 h-auto text-gray-400 hover:text-red-400 hover:bg-red-900/20"
              onClick={(e) => e.stopPropagation()}
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Pillar</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete the "{pillar.name}" pillar? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => onDelete(pillar.id, pillar.name)}
                className="bg-red-600 hover:bg-red-700"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <div className="flex items-center space-x-3 mb-4">
        <div
          className="w-4 h-4 rounded-full"
          style={{ backgroundColor: pillar.color }}
        ></div>
        <h3 
          className="text-lg font-semibold pr-16"
          style={{ color: pillar.color }}
        >
          {pillar.name}
        </h3>
      </div>
      
      <div className="space-y-3">
        <div className="text-sm text-gray-400">
          {pillar.video_count || 0} videos published • Target: {pillar.target_percentage}% of content
        </div>
        
        {/* Performance Data */}
        <div className={`text-sm font-medium ${performanceColor}`}>
          Actual: {pillar.actual_percentage || 0}% vs Target: {pillar.target_percentage}%
        </div>
        
        <div className="text-sm text-gray-400 flex items-center space-x-2">
          <TrendingUp className="w-4 h-4" />
          <span>📈 Avg views: {(pillar.avg_views || 0).toLocaleString()}</span>
        </div>
        
        {/* Progress Bar with Pillar Color */}
        <div className="relative group space-y-2">
          <div className="flex justify-between text-xs text-gray-400">
            <span>Progress</span>
            <span>{pillar.actual_percentage || 0}%</span>
          </div>
          <div className="relative">
            <div className="w-full bg-gray-600 rounded-full h-3">
              <div 
                className="h-3 rounded-full transition-all duration-300"
                style={{ 
                  width: `${Math.min(pillar.actual_percentage || 0, 100)}%`,
                  backgroundColor: pillar.color 
                }}
              />
            </div>
            {/* Target indicator line */}
            {pillar.target_percentage > 0 && (
              <div 
                className="absolute top-0 h-3 w-0.5 bg-white/80 rounded-full"
                style={{ left: `${Math.min(pillar.target_percentage, 100)}%` }}
                title={`Target: ${pillar.target_percentage}%`}
              />
            )}
          </div>
          
          {/* Tooltip */}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
            Click to view detailed analytics
          </div>
        </div>
        
        <div className="flex items-center justify-between pt-2">
          <span className={`text-sm font-medium ${performanceColor}`}>
            {performanceStatus}
          </span>
          <Button 
            variant="outline" 
            size="sm" 
            className="border-teal text-teal hover:bg-teal hover:text-white"
            onClick={(e) => {
              e.stopPropagation();
              onViewVideos?.(pillar.id);
            }}
          >
            View Videos
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PillarCard;
