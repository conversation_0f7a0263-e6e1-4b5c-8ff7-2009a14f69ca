
import React from 'react';
import { Check } from 'lucide-react';

// Define the brand colors directly in the component to ensure they're used
const BRAND_COLOR_PALETTE = [
  '#37BEB0', // Primary Teal
  '#E76F51', // Terracotta 
  '#FF7B25', // Orange
  '#F59F0A', // Golden Yellow
  '#8B3A2F', // Dark Terracotta
  '#E6F7F5', // Light Teal
];

interface EnhancedColorPickerProps {
  selectedColor: string;
  onColorChange: (color: string) => void;
  showLabel?: boolean;
}

const EnhancedColorPicker = ({ selectedColor, onColorChange, showLabel = true }: EnhancedColorPickerProps) => {
  console.log('EnhancedColorPicker - selectedColor:', selectedColor);
  console.log('EnhancedColorPicker - BRAND_COLOR_PALETTE:', BRAND_COLOR_PALETTE);
  
  return (
    <div className="space-y-3">
      {showLabel && (
        <label className="text-sm font-medium text-white">Pillar Color</label>
      )}
      <div className="flex flex-wrap gap-3">
        {BRAND_COLOR_PALETTE.map((color, index) => {
          const isSelected = selectedColor === color;
          console.log(`EnhancedColorPicker - Color ${color} at index ${index} - isSelected:`, isSelected);
          
          return (
            <button
              key={`${color}-${index}`}
              type="button"
              className={`relative w-12 h-12 rounded-xl transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white/50 ${
                isSelected
                  ? 'ring-2 ring-white scale-110 shadow-lg shadow-gray-900/20' 
                  : 'hover:ring-1 hover:ring-gray-300 shadow-sm'
              }`}
              style={{ 
                backgroundColor: color,
                border: '2px solid rgba(255,255,255,0.1)'
              }}
              onClick={() => {
                console.log('EnhancedColorPicker - Color selected:', color);
                onColorChange(color);
              }}
              title={`Select ${color}`}
            >
              {isSelected && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Check 
                    className="w-5 h-5 drop-shadow-sm" 
                    style={{
                      color: color === '#E6F7F5' ? '#000000' : '#ffffff'
                    }}
                  />
                </div>
              )}
              <div className="absolute inset-0 rounded-xl bg-white/0 hover:bg-white/10 transition-colors duration-200" />
            </button>
          );
        })}
      </div>
      <p className="text-xs text-gray-400">
        Choose a color that represents your content pillar theme
      </p>
    </div>
  );
};

export default EnhancedColorPicker;
