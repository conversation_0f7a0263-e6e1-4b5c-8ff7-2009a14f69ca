
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Clock, User } from 'lucide-react';
import { ContentPillar } from '@/types/pillar';
import { formatTime } from '@/utils/analyticsUtils';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';

// Import mock data service
import { shouldUseMockData } from '@/services/mockDataService';

interface EngagementMetricsProps {
  pillar: ContentPillar;
}

const EngagementMetrics = ({ pillar }: EngagementMetricsProps) => {
  const { user } = useAuth();

  const { data: engagementData } = useQuery({
    queryKey: ['pillar-engagement', pillar.id],
    queryFn: async () => {
      // Check if we should use mock data
      if (shouldUseMockData()) {
        console.log('🎬 Mock data for pillar engagement disabled');
        return {
          engagementRate: 0,
          likeToViewRatio: 0,
          commentToViewRatio: 0,
          totalLikes: 0,
          totalComments: 0
        };
      }

      const { data: videos, error } = await supabase
        .from('videos')
        .select('views, like_count, comment_count')
        .eq('pillar_id', pillar.id)
        .eq('user_id', user?.id)
        .not('youtube_video_id', 'is', null);

      if (error) throw error;

      const totalViews = videos?.reduce((sum, video) => sum + (video.views || 0), 0) || 0;
      const totalLikes = videos?.reduce((sum, video) => sum + (video.like_count || 0), 0) || 0;
      const totalComments = videos?.reduce((sum, video) => sum + (video.comment_count || 0), 0) || 0;
      
      // Calculate engagement rate
      const engagementRate = totalViews > 0 ? ((totalLikes + totalComments) / totalViews) * 100 : 0;
      
      // Calculate CTR (estimated based on engagement)
      const ctr = totalViews > 0 ? Math.min(((totalLikes / totalViews) * 100 * 2), 15) : 0;
      
      // Calculate subscriber conversion (estimated)
      const subscriberConversion = engagementRate > 0 ? Math.min(engagementRate * 0.3, 5) : 0;

      return {
        engagementRate,
        ctr,
        subscriberConversion
      };
    },
    enabled: !!pillar.id && (!!user?.id || shouldUseMockData())
  });

  // Use calculated data or fallback values
  const engagementRate = engagementData?.engagementRate || 0;
  const engagementScore = Math.min(Math.round(engagementRate * 1.4), 10);
  const watchTime = formatTime(pillar.avg_watch_time || 342); // Default 5:42
  const ctr = engagementData?.ctr || 0;
  const subscriberConversion = engagementData?.subscriberConversion || 0;

  const CircularProgress = ({ value, max = 10 }: { value: number; max?: number }) => {
    const percentage = (value / max) * 100;
    const strokeDasharray = 2 * Math.PI * 45; // radius = 45
    const strokeDashoffset = strokeDasharray - (strokeDasharray * percentage) / 100;

    return (
      <div className="relative w-20 h-20">
        <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 100 100">
          <circle
            cx="50"
            cy="50"
            r="45"
            stroke="rgb(55 65 81)" // gray-700
            strokeWidth="8"
            fill="transparent"
          />
          <circle
            cx="50"
            cy="50"
            r="45"
            stroke="#37BEB0" // teal
            strokeWidth="8"
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-1000 ease-out"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-white font-bold text-sm">
            {value.toFixed(1)}/{max}
          </span>
        </div>
      </div>
    );
  };

  return (
    <Card className="bg-gray-700 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white text-sm">Engagement Metrics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Engagement Score */}
          <div className="flex flex-col items-center text-center">
            <CircularProgress value={engagementScore} />
            <p className="text-white font-medium mt-2">Engagement Score</p>
            <p className="text-gray-400 text-xs">
              {engagementRate > 0 ? 'Based on real data' : 'No data available'}
            </p>
          </div>

          {/* Average Watch Time */}
          <div className="flex flex-col items-center text-center">
            <div className="w-20 h-20 bg-teal/20 rounded-full flex items-center justify-center mb-2 group-hover:bg-teal/30 transition-colors">
              <Clock className="w-8 h-8 text-teal" />
            </div>
            <p className="text-white font-bold text-lg">{watchTime}</p>
            <p className="text-gray-400 text-xs">Avg Watch Time</p>
          </div>

          {/* CTR */}
          <div className="flex flex-col items-center text-center">
            <div className="w-20 h-20 bg-orange/20 rounded-full flex items-center justify-center mb-2 group-hover:bg-orange/30 transition-colors">
              <svg className="w-8 h-8 text-orange" fill="currentColor" viewBox="0 0 24 24">
                <path d="M13.64 21.97c0.39 0.39 1.02 0.39 1.41 0l6.36-6.36c0.39-0.39 0.39-1.02 0-1.41L14.05 7.84c-0.39-0.39-1.02-0.39-1.41 0s-0.39 1.02 0 1.41L17.17 14H3c-0.55 0-1 0.45-1 1s0.45 1 1 1h14.17l-4.53 4.56c-0.39 0.39-0.39 1.02 0 1.41z"/>
              </svg>
            </div>
            <p className="text-white font-bold text-lg">{ctr.toFixed(1)}%</p>
            <p className="text-gray-400 text-xs">
              {ctr > 0 ? 'Estimated CTR' : 'No data'}
            </p>
          </div>

          {/* Subscriber Conversion */}
          <div className="flex flex-col items-center text-center">
            <div className="w-20 h-20 bg-yellow/20 rounded-full flex items-center justify-center mb-2 group-hover:bg-yellow/30 transition-colors">
              <User className="w-8 h-8 text-yellow" />
            </div>
            <p className="text-white font-bold text-lg">{subscriberConversion.toFixed(1)}%</p>
            <p className="text-gray-400 text-xs">
              {subscriberConversion > 0 ? 'Subscriber Conv.' : 'No data'}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default EngagementMetrics;
