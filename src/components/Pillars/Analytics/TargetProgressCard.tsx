
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { ContentPillar } from '@/types/pillar';
import { getPerformanceStatus, calculatePercentageDiff } from '@/utils/analyticsUtils';
import ProgressBar from '@/components/Analytics/shared/ProgressBar';

interface TargetProgressCardProps {
  pillar: ContentPillar;
}

const TargetProgressCard = ({ pillar }: TargetProgressCardProps) => {
  const performance = getPerformanceStatus(pillar.actual_percentage || 0, pillar.target_percentage);

  return (
    <Card className="bg-gray-700 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white text-sm">Content Distribution Progress</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Current: {pillar.actual_percentage}%</span>
            <span className="text-gray-400">Target: {pillar.target_percentage}%</span>
          </div>
          
          <ProgressBar
            current={pillar.actual_percentage || 0}
            target={pillar.target_percentage}
            color={pillar.color}
          />
          
          <div className="text-center">
            <span className={`text-sm font-medium ${performance.color}`}>
              {calculatePercentageDiff(pillar.actual_percentage || 0, pillar.target_percentage)} vs target
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TargetProgressCard;
