
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { TrendingUp, Target, BarChart3 } from 'lucide-react';
import { ContentPillar } from '@/types/pillar';
import { getPerformanceStatus, getRecommendation } from '@/utils/analyticsUtils';
import InsightCard from '@/components/Analytics/shared/InsightCard';

interface InsightsCardProps {
  pillar: ContentPillar;
}

const InsightsCard = ({ pillar }: InsightsCardProps) => {
  const status = getPerformanceStatus(pillar.actual_percentage || 0, pillar.target_percentage);
  const recommendation = getRecommendation(pillar.actual_percentage || 0, pillar.target_percentage);
  const subscriberConversion = pillar.subscriber_conversion || 2.3;

  return (
    <Card className="bg-gray-700 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white text-sm flex items-center">
          <BarChart3 className="w-4 h-4 mr-2" />
          Key Insights
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <InsightCard
            icon={TrendingUp}
            iconColor="text-teal"
            backgroundColor="bg-teal/10"
            borderColor="border-teal/30"
            title="Best Performance"
            description={`${pillar.best_day} shows ${pillar.best_day_boost}% higher views than average`}
          />
          
          <InsightCard
            icon={Target}
            iconColor="text-orange"
            backgroundColor="bg-orange/10"
            borderColor="border-orange/30"
            title="Content Balance"
            description={`${status.status.toLowerCase()} - consider ${recommendation}`}
          />

          <InsightCard
            icon={TrendingUp}
            iconColor="text-green-400"
            backgroundColor="bg-green/10"
            borderColor="border-green/30"
            title="Growth Driver 🚀"
            description={`This pillar converts ${subscriberConversion}x more subscribers than your channel average`}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default InsightsCard;
