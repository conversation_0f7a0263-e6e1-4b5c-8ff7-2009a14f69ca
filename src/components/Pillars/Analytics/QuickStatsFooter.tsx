
import React from 'react';
import { ContentPillar } from '@/types/pillar';

interface QuickStatsFooterProps {
  pillar: ContentPillar;
}

const QuickStatsFooter = ({ pillar }: QuickStatsFooterProps) => {
  const bestDay = pillar.best_day || 'Wednesday';
  const optimalLength = pillar.optimal_video_length || '8-12 mins';
  const topSource = pillar.top_traffic_source || { name: 'Search', percentage: 45 };
  const bestTime = '2-4 PM'; // Mock data

  return (
    <div className="border-t border-gray-600 pt-4 mt-6">
      <div className="flex items-center space-x-2 mb-2">
        <span className="text-sm font-medium text-white">📊 Quick Stats</span>
      </div>
      <div className="text-sm text-gray-400 leading-relaxed">
        <span>Best day: <span className="text-gray-300">{bestDay}</span></span>
        <span className="mx-2">•</span>
        <span>Optimal length: <span className="text-gray-300">{optimalLength}</span></span>
        <span className="mx-2">•</span>
        <span>Top source: <span className="text-gray-300">{topSource.name} ({topSource.percentage}%)</span></span>
        <span className="mx-2">•</span>
        <span>Best time: <span className="text-gray-300">{bestTime}</span></span>
      </div>
    </div>
  );
};

export default QuickStatsFooter;
