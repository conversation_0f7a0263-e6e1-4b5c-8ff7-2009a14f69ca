
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { ContentPillar } from '@/types/pillar';

interface PerformanceBadgesProps {
  pillar: ContentPillar;
}

const PerformanceBadges = ({ pillar }: PerformanceBadgesProps) => {
  const badges = [];

  if (pillar.is_trending) {
    badges.push({
      text: "🔥 Trending Up",
      className: "bg-orange/20 text-orange border-orange/30"
    });
  }

  if (pillar.is_top_performer) {
    badges.push({
      text: "👑 Top Performer",
      className: "bg-yellow/20 text-yellow border-yellow/30"
    });
  }

  if (pillar.is_high_revenue) {
    badges.push({
      text: "💰 High Revenue",
      className: "bg-green-500/20 text-green-400 border-green-500/30"
    });
  }

  if (badges.length === 0) return null;

  return (
    <div className="flex flex-wrap gap-2 mt-2">
      {badges.map((badge, index) => (
        <Badge
          key={index}
          className={`text-xs px-2 py-1 ${badge.className}`}
          variant="outline"
        >
          {badge.text}
        </Badge>
      ))}
    </div>
  );
};

export default PerformanceBadges;
