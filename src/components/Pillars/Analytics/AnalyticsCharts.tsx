
import React from 'react';
import { <PERSON>Chart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, LineChart, Line } from 'recharts';
import { ContentPillar } from '@/types/pillar';
import { getChartColors } from '@/utils/analyticsUtils';
import ChartContainer from '@/components/Analytics/shared/ChartContainer';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';



interface AnalyticsChartsProps {
  pillar: ContentPillar;
}

const AnalyticsCharts = ({ pillar }: AnalyticsChartsProps) => {
  const { user } = useAuth();
  const chartColors = getChartColors();
  
  const { data: chartData } = useQuery({
    queryKey: ['pillar-charts', pillar.id],
    queryFn: async () => {
      const { data: videos, error } = await supabase
        .from('videos')
        .select('views, published_at')
        .eq('pillar_id', pillar.id)
        .eq('user_id', user?.id)
        .not('youtube_video_id', 'is', null)
        .not('published_at', 'is', null);

      if (error) throw error;

      // Group videos by month for performance over time
      const monthlyData: { [key: string]: { views: number; videos: number } } = {};
      const dailyData: { [key: string]: number[] } = {
        Mon: [], Tue: [], Wed: [], Thu: [], Fri: [], Sat: [], Sun: []
      };

      videos?.forEach(video => {
        if (video.published_at && video.views) {
          const date = new Date(video.published_at);
          const month = date.toLocaleDateString('en-US', { month: 'short' });
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          // Monthly data
          if (!monthlyData[month]) {
            monthlyData[month] = { views: 0, videos: 0 };
          }
          monthlyData[month].views += video.views;
          monthlyData[month].videos += 1;

          // Daily data
          if (dailyData[dayName]) {
            dailyData[dayName].push(video.views);
          }
        }
      });

      // Convert to chart format
      const performanceData = Object.entries(monthlyData).map(([month, data]) => ({
        month,
        views: data.views,
        videos: data.videos
      }));

      const dailyAvgData = Object.entries(dailyData).map(([day, views]) => ({
        day,
        avgViews: views.length > 0 ? Math.round(views.reduce((a, b) => a + b, 0) / views.length) : 0
      }));

      return { performanceData, dailyAvgData };
    },
    enabled: !!pillar.id && !!user?.id
  });

  const performanceData = chartData?.performanceData || [];
  const dailyData = chartData?.dailyAvgData || [];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <ChartContainer title="Performance Over Time">
        {performanceData.length > 0 ? (
          <LineChart data={performanceData}>
            <CartesianGrid strokeDasharray="3 3" stroke={chartColors.grid} />
            <XAxis 
              dataKey="month" 
              tick={{ fontSize: 12, fill: chartColors.text }}
              stroke={chartColors.text}
            />
            <YAxis 
              tick={{ fontSize: 12, fill: chartColors.text }}
              stroke={chartColors.text}
            />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: chartColors.tooltip.background, 
                border: `1px solid ${chartColors.tooltip.border}`,
                borderRadius: '8px',
                color: chartColors.tooltip.color
              }}
            />
            <Line 
              type="monotone" 
              dataKey="views" 
              stroke={pillar.color}
              strokeWidth={3}
              dot={{ fill: pillar.color, strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        ) : (
          <div className="flex items-center justify-center h-64 text-gray-400">
            <div className="text-center">
              <p>No video data available</p>
              <p className="text-sm mt-1">Sync your YouTube channel to see performance charts</p>
            </div>
          </div>
        )}
      </ChartContainer>

      <ChartContainer title="Daily Performance">
        {dailyData.some(d => d.avgViews > 0) ? (
          <BarChart data={dailyData}>
            <CartesianGrid strokeDasharray="3 3" stroke={chartColors.grid} />
            <XAxis 
              dataKey="day" 
              tick={{ fontSize: 12, fill: chartColors.text }}
              stroke={chartColors.text}
            />
            <YAxis 
              tick={{ fontSize: 12, fill: chartColors.text }}
              stroke={chartColors.text}
            />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: chartColors.tooltip.background, 
                border: `1px solid ${chartColors.tooltip.border}`,
                borderRadius: '8px',
                color: chartColors.tooltip.color
              }}
            />
            <Bar 
              dataKey="avgViews" 
              fill={pillar.color}
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        ) : (
          <div className="flex items-center justify-center h-64 text-gray-400">
            <div className="text-center">
              <p>No daily performance data</p>
              <p className="text-sm mt-1">Publish more videos to see daily trends</p>
            </div>
          </div>
        )}
      </ChartContainer>
    </div>
  );
};

export default AnalyticsCharts;
