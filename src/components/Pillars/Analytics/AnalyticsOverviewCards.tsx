
import React from 'react';
import { Eye, Video, TrendingUp, Clock, Heart } from 'lucide-react';
import { ContentPillar } from '@/types/pillar';
import { formatNumber } from '@/utils/analyticsUtils';
import MetricCard from '@/components/Analytics/shared/MetricCard';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';



interface AnalyticsOverviewCardsProps {
  pillar: ContentPillar;
}

const AnalyticsOverviewCards = ({ pillar }: AnalyticsOverviewCardsProps) => {
  const { user } = useAuth();
  
  const { data: pillarStats } = useQuery({
    queryKey: ['pillar-analytics', pillar.id],
    queryFn: async () => {
      const { data: videos, error } = await supabase
        .from('videos')
        .select('views, like_count, comment_count, published_at')
        .eq('pillar_id', pillar.id)
        .eq('user_id', user?.id)
        .not('youtube_video_id', 'is', null)
        .not('youtube_thumbnail_url', 'is', null)
        .not('published_at', 'is', null);

      if (error) throw error;

      const totalViews = videos?.reduce((sum, video) => sum + (video.views || 0), 0) || 0;
      const totalLikes = videos?.reduce((sum, video) => sum + (video.like_count || 0), 0) || 0;
      const totalComments = videos?.reduce((sum, video) => sum + (video.comment_count || 0), 0) || 0;
      const videoCount = videos?.length || 0;
      const topVideoViews = Math.max(...(videos?.map(v => v.views || 0) || [0]));

      const engagementRate = totalViews > 0 ? ((totalLikes + totalComments) / totalViews) * 100 : 0;

      return {
        totalViews,
        videoCount,
        topVideoViews,
        engagementRate
      };
    },
    enabled: !!pillar.id && !!user?.id
  });

  const totalViews = pillarStats?.totalViews || 0;
  const videoCount = pillarStats?.videoCount || 0;
  const topVideoViews = pillarStats?.topVideoViews || 0;
  const engagementRate = pillarStats?.engagementRate || 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <MetricCard
        title="Total Views"
        value={totalViews > 0 ? formatNumber(totalViews) : "0"}
        icon={Eye}
        iconColor="text-teal"
        gradient="from-teal/20 to-teal/5"
        change={totalViews > 0 ? "Real data" : "No data yet"}
        changeType={totalViews > 0 ? "positive" : "neutral"}
      />

      <MetricCard
        title="Videos Published"
        value={videoCount.toString()}
        icon={Video}
        iconColor="text-orange"
        gradient="from-orange/20 to-orange/5"
        change={videoCount > 0 ? `${videoCount} videos` : "No videos yet"}
        changeType="neutral"
      />

      <MetricCard
        title="Top Video"
        value={topVideoViews > 0 ? `${formatNumber(topVideoViews)} views` : "No data"}
        icon={TrendingUp}
        iconColor="text-green-400"
        gradient="from-green/20 to-green/5"
        change={topVideoViews > 0 ? "Best performer" : "Publish videos first"}
        changeType={topVideoViews > 0 ? "positive" : "neutral"}
      />

      <MetricCard
        title="Engagement Rate"
        value={engagementRate > 0 ? `${engagementRate.toFixed(1)}%` : "0%"}
        icon={Heart}
        iconColor="text-terracotta"
        gradient="from-terracotta/20 to-terracotta/5"
        change={engagementRate > 0 ? "Likes + comments" : "No engagement data"}
        changeType="neutral"
      />

      <MetricCard
        title="Average Watch Time"
        value="Not available"
        icon={Clock}
        iconColor="text-teal"
        gradient="from-teal/20 to-teal/5"
        change="Requires YouTube Analytics API"
        changeType="neutral"
      />

      <MetricCard
        title="Best Publishing Day"
        value="Analyzing..."
        icon={Clock}
        iconColor="text-yellow"
        gradient="from-yellow/20 to-yellow/5"
        change="Need more video data"
        changeType="neutral"
      />
    </div>
  );
};

export default AnalyticsOverviewCards;
