
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import OptimalScheduleTab from '@/components/OptimalSchedule/OptimalScheduleTab';
import StrategyTabContent from './StrategyTabContent';
import PublishedContentTab from './PublishedContentTab';
import NeedsAssignmentTab from './NeedsAssignmentTab';
import ErrorBoundary from '@/components/ui/error-boundary';
import { ContentPillar } from '@/types/pillar';
import CompleteMockDataRemoval from '@/components/Admin/CompleteMockDataRemoval';

interface PillarsMainTabsProps {
  pillars: ContentPillar[];
  onDeletePillar: (pillarId: string, pillarName: string) => void;
  onEditPillar: (pillar: ContentPillar) => void;
  addPillar: (pillar: any) => Promise<boolean>;
  canAddMorePillars: () => boolean;
  userTier: string;
  pillarLimit: number;
}

const PillarsMainTabs = ({ 
  pillars, 
  onDeletePillar, 
  onEditPillar,
  addPillar,
  canAddMorePillars,
  userTier,
  pillarLimit
}: PillarsMainTabsProps) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState('strategy');
  const [selectedPillarId, setSelectedPillarId] = useState<string | null>(null);

  // Handle URL parameters for direct navigation
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    const pillarParam = searchParams.get('pillar');
    
    if (tabParam) {
      setActiveTab(tabParam);
    }
    
    if (pillarParam) {
      setSelectedPillarId(pillarParam);
    }
  }, [searchParams]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Update URL without the pillar filter when switching tabs
    const newParams = new URLSearchParams();
    newParams.set('tab', value);
    setSearchParams(newParams);
  };

  const handleViewVideos = (pillarId: string) => {
    setSelectedPillarId(pillarId);
    setActiveTab('published');
    // Update URL with both tab and pillar
    const newParams = new URLSearchParams();
    newParams.set('tab', 'published');
    newParams.set('pillar', pillarId);
    setSearchParams(newParams);
  };

  const handleClearPillarFilter = () => {
    setSelectedPillarId(null);
    // Update URL to remove pillar filter
    const newParams = new URLSearchParams();
    newParams.set('tab', 'published');
    setSearchParams(newParams);
  };

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
      <TabsList className="w-full grid grid-cols-5 overflow-hidden glass-effect border border-gray-700">
        <TabsTrigger value="strategy" className="text-xs sm:text-sm whitespace-nowrap data-[state=active]:!bg-blue-600 data-[state=active]:!text-white">
          Strategy Overview
        </TabsTrigger>
        <TabsTrigger value="published" className="text-xs sm:text-sm whitespace-nowrap data-[state=active]:!bg-blue-600 data-[state=active]:!text-white">
          Published Content
        </TabsTrigger>
        <TabsTrigger value="needs-assignment" className="text-xs sm:text-sm whitespace-nowrap data-[state=active]:!bg-blue-600 data-[state=active]:!text-white">
          Needs Assignment
        </TabsTrigger>
        <TabsTrigger value="schedule" className="text-xs sm:text-sm whitespace-nowrap data-[state=active]:!bg-blue-600 data-[state=active]:!text-white">
          Optimal Schedule
        </TabsTrigger>
        <TabsTrigger value="admin" className="text-xs sm:text-sm whitespace-nowrap data-[state=active]:!bg-red-600 data-[state=active]:!text-white">
          🔧 Admin
        </TabsTrigger>
      </TabsList>

      <TabsContent value="strategy" className="space-y-8 mt-8">
        <StrategyTabContent
          pillars={pillars}
          onDeletePillar={onDeletePillar}
          onEditPillar={onEditPillar}
          onViewVideos={handleViewVideos}
          addPillar={addPillar}
          canAddMorePillars={canAddMorePillars}
          userTier={userTier}
          pillarLimit={pillarLimit}
        />
      </TabsContent>

      <TabsContent value="published" className="mt-8">
        <ErrorBoundary fallback={
          <div className="text-center py-8">
            <p className="text-gray-400">Unable to load published content. Please try refreshing.</p>
          </div>
        }>
          <PublishedContentTab 
            pillars={pillars}
            initialPillarFilter={selectedPillarId}
            onClearPillarFilter={handleClearPillarFilter}
          />
        </ErrorBoundary>
      </TabsContent>

      <TabsContent value="needs-assignment" className="mt-8">
        <ErrorBoundary fallback={
          <div className="text-center py-8">
            <p className="text-gray-400">Unable to load unassigned content. Please try refreshing.</p>
          </div>
        }>
          <NeedsAssignmentTab pillars={pillars} />
        </ErrorBoundary>
      </TabsContent>

      <TabsContent value="schedule" className="mt-8">
        <ErrorBoundary fallback={
          <div className="text-center py-8">
            <p className="text-gray-400">Unable to load schedule optimization. Please try refreshing.</p>
          </div>
        }>
          <OptimalScheduleTab pillars={pillars} />
        </ErrorBoundary>
      </TabsContent>

      <TabsContent value="admin" className="mt-8">
        <div className="space-y-6">
          <div className="bg-red-900/20 border border-red-700 rounded-lg p-4">
            <h3 className="text-red-400 font-medium mb-2">⚠️ Admin Tools</h3>
            <p className="text-gray-300 text-sm">
              These tools are for cleaning up mock data. Use with caution.
            </p>
          </div>
          <CompleteMockDataRemoval />
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default PillarsMainTabs;
