
import React from 'react';
import { ContentPillar } from '@/types/pillar';
import { COLOR_PALETTE } from '@/constants/pillarColors';
import PillarDialog from './PillarDialog';
import PillarEditDialog from './PillarEditDialog';

interface PillarsHeaderProps {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  newPillar: { name: string; target_percentage: number; color: string };
  setNewPillar: (pillar: { name: string; target_percentage: number; color: string }) => void;
  addPillar: () => Promise<boolean>;
  canAddMore: boolean;
  userTier: string;
  editingPillar: ContentPillar | null;
  setEditingPillar: (pillar: ContentPillar | null) => void;
  updatePillar: (pillar: ContentPillar) => Promise<boolean>;
  pillarLimit: number;
  currentCount: number;
  isSubmitting: boolean;
  getFieldError: (name: string) => string;
  touchField: (name: string) => void;
}

const PillarsHeader = ({
  isDialogOpen,
  setIsDialogOpen,
  newPillar,
  setNewPillar,
  addPillar,
  canAddMore,
  userTier,
  editingPillar,
  setEditingPillar,
  updatePillar,
  pillarLimit,
  currentCount,
  isSubmitting,
  getFieldError,
  touchField
}: PillarsHeaderProps) => {
  const handleAddPillar = async (): Promise<boolean> => {
    const success = await addPillar();
    if (success) {
      setNewPillar({ name: '', target_percentage: 25, color: COLOR_PALETTE[0] });
      setIsDialogOpen(false);
    }
    return success;
  };

  const handleUpdatePillar = async (): Promise<boolean> => {
    if (!editingPillar) return false;
    const success = await updatePillar(editingPillar);
    if (success) {
      setEditingPillar(null);
    }
    return success;
  };

  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">Content Pillars</h1>
        <p className="text-gray-300">
          Organize your content strategy around key themes • {currentCount}/{pillarLimit === 999 ? 'unlimited' : pillarLimit} pillars ({userTier})
        </p>
      </div>

      <PillarDialog
        isOpen={isDialogOpen}
        setIsOpen={setIsDialogOpen}
        newPillar={newPillar}
        setNewPillar={setNewPillar}
        onAddPillar={handleAddPillar}
        canAddMore={canAddMore}
        isSubmitting={isSubmitting}
        getFieldError={getFieldError}
        touchField={touchField}
      />

      <PillarEditDialog
        editingPillar={editingPillar}
        setEditingPillar={setEditingPillar}
        onUpdatePillar={handleUpdatePillar}
        isSubmitting={isSubmitting}
        getFieldError={getFieldError}
        touchField={touchField}
      />
    </div>
  );
};

export default PillarsHeader;
