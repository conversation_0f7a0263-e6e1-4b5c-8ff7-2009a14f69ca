
import React from 'react';
import { TrendingUp } from 'lucide-react';

interface EmptyStateProps {
  searchTerm: string;
  dateFilter: string;
}

const EmptyState = ({ searchTerm, dateFilter }: EmptyStateProps) => {
  if (searchTerm || dateFilter !== 'all') {
    return (
      <div className="text-center py-12">
        <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-300">No videos found</h3>
        <p className="text-gray-400 mt-1">
          Try adjusting your search or filters
        </p>
      </div>
    );
  }

  return (
    <div className="text-center py-12">
      <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <p className="text-gray-400">No published videos yet</p>
      <p className="text-sm text-gray-500 mt-2">Your YouTube videos will appear here once connected</p>
    </div>
  );
};

export default EmptyState;
