
import { PublishedVideo } from './usePublishedVideos';

export const getDateFilteredVideos = (videos: PublishedVideo[], dateFilter: string) => {
  if (dateFilter === 'all') return videos;
  
  const now = new Date();
  const filterDate = new Date();
  
  switch (dateFilter) {
    case '30':
      filterDate.setDate(now.getDate() - 30);
      break;
    case '90':
      filterDate.setDate(now.getDate() - 90);
      break;
    case 'year':
      filterDate.setFullYear(now.getFullYear(), 0, 1);
      break;
    default:
      return videos;
  }
  
  return videos.filter(video => 
    video.published_at && new Date(video.published_at) >= filterDate
  );
};

export const getSortedVideos = (videos: PublishedVideo[], sortBy: string) => {
  return [...videos].sort((a, b) => {
    switch (sortBy) {
      case 'recent':
        return new Date(b.published_at || 0).getTime() - new Date(a.published_at || 0).getTime();
      case 'views':
        return (b.views || 0) - (a.views || 0);
      case 'engagement':
        const aEngagement = (a.like_count || 0) + (a.comment_count || 0);
        const bEngagement = (b.like_count || 0) + (b.comment_count || 0);
        return bEngagement - aEngagement;
      default:
        return 0;
    }
  });
};

export const filterVideosBySearch = (videos: PublishedVideo[], searchTerm: string) => {
  return videos.filter(video => 
    video.title.toLowerCase().includes(searchTerm.toLowerCase())
  );
};
