
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { dataService } from '@/services/dataService';

export interface PublishedVideo {
  id: string;
  title: string;
  published_at: string;
  views: number;
  like_count: number;
  comment_count: number;
  pillar_id: string | null;
  youtube_video_id: string | null;
  youtube_thumbnail_url: string | null;
}

export const usePublishedVideos = () => {
  const { user } = useAuth();
  const [videos, setVideos] = useState<PublishedVideo[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchPublishedVideos();
    } else {
      setVideos([]);
      setIsLoading(false);
    }
  }, [user]);

  const fetchPublishedVideos = async () => {
    if (!user) {
      setVideos([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      console.log('Fetching published videos for user:', user.id);
      
      const data = await dataService.videos.getPublished(user.id);
      console.log('Data returned from service:', data);

      if (!data) {
        console.error('Error fetching videos from data service');
        setVideos([]);
        return;
      }

      // Extra safety filter to ensure we only show real YouTube videos
      const realVideos = (data || []).filter(video => 
        video.published_at && 
        video.youtube_video_id && 
        video.youtube_thumbnail_url &&
        video.title &&
        // Exclude any remaining mock titles
        !video.title.includes('React Server Components') &&
        !video.title.includes('Home Office Setup') &&
        !video.title.includes('AI Tools Revolutionizing') &&
        !video.title.includes('Boost Your Work From Home') &&
        !video.title.includes('How to Grow Your Channel') &&
        !video.title.includes('Beginner\'s Guide to YouTube') &&
        !video.title.includes('Top 10 Camera Tips') &&
        !video.title.includes('YouTube Algorithm Explained') &&
        !video.title.includes('Camera Settings for Beginners')
      );
      
      console.log('Filtered real videos:', realVideos.length);
      setVideos(realVideos);
    } catch (error) {
      console.error('Error fetching published videos:', error);
      setVideos([]);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    videos,
    isLoading,
    refetch: fetchPublishedVideos
  };
};
