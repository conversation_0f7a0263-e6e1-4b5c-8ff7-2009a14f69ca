
import React from 'react';
import { X } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ContentPillar } from '@/types/pillar';

interface PillarFilterAlertProps {
  pillarFilter: string | null;
  pillars: ContentPillar[];
  onClearFilter: () => void;
}

const PillarFilterAlert = ({ pillarFilter, pillars, onClearFilter }: PillarFilterAlertProps) => {
  if (!pillarFilter) return null;

  const pillar = pillars.find(p => p.id === pillarFilter);

  return (
    <Card className="bg-teal/10 border-teal/30">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: pillar?.color || '#37BEB0' }}
            />
            <span className="text-white font-medium">
              Showing videos for: {pillar?.name}
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilter}
            className="text-gray-300 hover:text-white hover:bg-white/10"
          >
            <X className="w-4 h-4 mr-1" />
            Show All Pillars
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default PillarFilterAlert;
