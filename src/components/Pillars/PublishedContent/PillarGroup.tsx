
import React from 'react';
import { Eye, TrendingUp, ChevronDown, ChevronRight } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ContentPillar } from '@/types/pillar';
import { PublishedVideo } from './usePublishedVideos';
import VideoItem from './VideoItem';

interface PillarGroupProps {
  pillar: ContentPillar;
  videos: PublishedVideo[];
  isExpanded: boolean;
  onToggleExpansion: () => void;
  onVideoClick: (video: PublishedVideo) => void;
}

const PillarGroup = ({ pillar, videos, isExpanded, onToggleExpansion, onVideoClick }: PillarGroupProps) => {
  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getEngagementRate = (video: PublishedVideo) => {
    if (!video.views || video.views === 0) return 0;
    return (((video.like_count || 0) + (video.comment_count || 0)) / video.views * 100);
  };

  const totalViews = videos.reduce((sum, video) => sum + (video.views || 0), 0);
  const avgEngagement = videos.length > 0 
    ? videos.reduce((sum, video) => sum + getEngagementRate(video), 0) / videos.length 
    : 0;

  if (videos.length === 0) return null;

  return (
    <Collapsible open={isExpanded} onOpenChange={onToggleExpansion}>
      <Card className="bg-gray-800 border-gray-600">
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-gray-700/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  {isExpanded ? (
                    <ChevronDown className="w-5 h-5 text-gray-400" />
                  ) : (
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  )}
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: pillar.color }}
                  />
                  <CardTitle className="text-white">{pillar.name}</CardTitle>
                </div>
                <Badge variant="outline" className="border-gray-500 text-gray-300">
                  {videos.length} videos
                </Badge>
              </div>
              <div className="flex items-center space-x-4 text-sm text-gray-400">
                <div className="flex items-center space-x-1">
                  <Eye className="w-4 h-4" />
                  <span>{formatNumber(totalViews)} views</span>
                </div>
                <div className="flex items-center space-x-1">
                  <TrendingUp className="w-4 h-4" />
                  <span>{avgEngagement.toFixed(1)}% engagement</span>
                </div>
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        
        <CollapsibleContent>
          <CardContent className="pt-0">
            <div className="space-y-3">
              {videos.map(video => (
                <VideoItem
                  key={video.id}
                  video={video}
                  onVideoClick={onVideoClick}
                />
              ))}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Card>
    </Collapsible>
  );
};

export default PillarGroup;
