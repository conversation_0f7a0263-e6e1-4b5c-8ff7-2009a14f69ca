
import React from 'react';
import { Search, Calendar, TrendingUp } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ContentFiltersProps {
  searchTerm: string;
  dateFilter: string;
  sortBy: string;
  onSearchChange: (value: string) => void;
  onDateFilterChange: (value: string) => void;
  onSortChange: (value: string) => void;
}

const ContentFilters = ({
  searchTerm,
  dateFilter,
  sortBy,
  onSearchChange,
  onDateFilterChange,
  onSortChange
}: ContentFiltersProps) => {
  return (
    <div className="flex flex-col md:flex-row gap-4">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          placeholder="Search videos..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10 bg-gray-800 border-gray-600 text-white"
        />
      </div>
      
      <Select value={dateFilter} onValueChange={onDateFilterChange}>
        <SelectTrigger className="w-full md:w-48 bg-gray-800 border-gray-600 text-white">
          <Calendar className="w-4 h-4 mr-2" />
          <SelectValue placeholder="Filter by date" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All time</SelectItem>
          <SelectItem value="30">Last 30 days</SelectItem>
          <SelectItem value="90">Last 90 days</SelectItem>
          <SelectItem value="year">This year</SelectItem>
        </SelectContent>
      </Select>

      <Select value={sortBy} onValueChange={onSortChange}>
        <SelectTrigger className="w-full md:w-48 bg-gray-800 border-gray-600 text-white">
          <TrendingUp className="w-4 h-4 mr-2" />
          <SelectValue placeholder="Sort by" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="recent">Most recent</SelectItem>
          <SelectItem value="views">Most views</SelectItem>
          <SelectItem value="engagement">Best engagement</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};

export default ContentFilters;
