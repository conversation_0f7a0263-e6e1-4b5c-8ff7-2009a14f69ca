
import React from 'react';
import { Eye, ThumbsUp, MessageCircle } from 'lucide-react';
import { PublishedVideo } from './usePublishedVideos';

interface VideoItemProps {
  video: PublishedVideo;
  onVideoClick: (video: PublishedVideo) => void;
}

const VideoItem = ({ video, onVideoClick }: VideoItemProps) => {
  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <div
      className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors cursor-pointer"
      onClick={() => onVideoClick(video)}
    >
      <div className="flex items-center space-x-3 flex-1">
        {video.youtube_thumbnail_url && (
          <img
            src={video.youtube_thumbnail_url}
            alt={video.title}
            className="w-16 h-9 object-cover rounded"
          />
        )}
        <div className="flex-1 min-w-0">
          <h4 className="text-white font-medium truncate">{video.title}</h4>
          <p className="text-sm text-gray-400">
            {video.published_at && new Date(video.published_at).toLocaleDateString()}
          </p>
        </div>
      </div>
      
      <div className="flex items-center space-x-4 text-sm text-gray-400">
        <div className="flex items-center space-x-1">
          <Eye className="w-4 h-4" />
          <span>{formatNumber(video.views || 0)}</span>
        </div>
        <div className="flex items-center space-x-1">
          <ThumbsUp className="w-4 h-4" />
          <span>{formatNumber(video.like_count || 0)}</span>
        </div>
        <div className="flex items-center space-x-1">
          <MessageCircle className="w-4 h-4" />
          <span>{formatNumber(video.comment_count || 0)}</span>
        </div>
      </div>
    </div>
  );
};

export default VideoItem;
