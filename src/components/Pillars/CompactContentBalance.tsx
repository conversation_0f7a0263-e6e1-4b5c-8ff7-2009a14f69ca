
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface PillarData {
  id: string;
  name: string;
  target_percentage: number;
  actual_percentage: number;
  color: string;
  video_count?: number;
  avg_views?: number;
}

interface CompactContentBalanceProps {
  pillars: PillarData[];
}

const CompactContentBalance = ({ pillars }: CompactContentBalanceProps) => {
  if (pillars.length === 0) return null;

  return (
    <Card className="bg-gray-800 border-gray-600 h-full flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="text-white text-lg">Content Balance</CardTitle>
      </CardHeader>
      <CardContent className="flex-1">
        <div className="space-y-2 h-full flex flex-col justify-start">
          {pillars.map(pillar => {
            const gap = pillar.target_percentage - pillar.actual_percentage;
            const status = Math.abs(gap) <= 5 ? 'balanced' : gap > 0 ? 'under' : 'over';
            const statusColor = status === 'balanced' ? 'text-green-400' : 
                               status === 'under' ? 'text-red-400' : 'text-orange';
            const statusText = status === 'balanced' ? 'On Target' :
                              status === 'under' ? `${Math.abs(gap)}% Under` : `${Math.abs(gap)}% Over`;

            return (
              <div key={pillar.id} className="flex items-center justify-between p-2 bg-gray-700/30 rounded text-sm">
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: pillar.color }}
                  />
                  <span className="text-white font-medium">{pillar.name}</span>
                  <span className="text-gray-400 text-xs">
                    {pillar.actual_percentage}% / {pillar.target_percentage}%
                  </span>
                </div>
                <span className={`text-xs font-medium ${statusColor}`}>
                  {statusText}
                </span>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default CompactContentBalance;
