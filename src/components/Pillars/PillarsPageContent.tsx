
import React, { useState } from 'react';
import { ContentPillar } from '@/types/pillar';
import PillarsGrid from './PillarsGrid';
import PillarModal from './PillarModal';
import PillarAnalyticsModal from './PillarAnalyticsModal';
import { useNavigate } from 'react-router-dom';
import PillarPerformanceChart from '@/components/Analytics/PillarPerformanceChart';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';

interface PillarsPageContentProps {
  pillars: ContentPillar[];
  userTier: string;
  canAddMorePillars: () => boolean;
  addPillar: (pillar: PillarInput) => Promise<boolean>;
  updatePillar: (pillar: ContentPillar) => Promise<boolean>;
  deletePillar: (pillarId: string, pillarName: string) => void;
  pillarLimit: number;
}

interface PillarInput {
  name: string;
  description?: string;
  target_percentage: number;
  color?: string;
}

const PillarsPageContent = ({
  pillars,
  userTier,
  canAddMorePillars,
  addPillar,
  updatePillar,
  deletePillar,
  pillarLimit
}: PillarsPageContentProps) => {
  const navigate = useNavigate();
  const [editingPillar, setEditingPillar] = useState<ContentPillar | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [analyticsModalPillar, setAnalyticsModalPillar] = useState<ContentPillar | null>(null);
  
  // Add date range state for analytics data
  const [dateRange] = useState({
    start: new Date(new Date().setDate(new Date().getDate() - 30)),
    end: new Date()
  });
  
  // Fetch analytics data for pillar performance chart
  const { data: analyticsData, isLoading: analyticsLoading, error: analyticsError } = useAnalyticsData('30d');

  const handleViewVideos = (pillarId: string) => {
    navigate(`/calendar?pillar=${pillarId}`);
  };

  const handleViewAnalytics = (pillar: ContentPillar) => {
    setAnalyticsModalPillar(pillar);
  };

  return (
    <>
      {/* Performance Overview Section */}
      <div className="mb-8">
        <div className="bg-gray-800/50 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Content Performance</h2>
          {analyticsError ? (
            <div className="p-4 bg-red-900/20 border border-red-700/50 rounded-md text-red-300">
              <p className="font-medium">Unable to load analytics data: {analyticsError}</p>
              <p className="text-sm mt-1">This could be due to a missing database table. Please check your database setup.</p>
            </div>
          ) : analyticsLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="w-8 h-8 border-4 border-teal border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : (
            <PillarPerformanceChart data={analyticsData} />
          )}
        </div>
      </div>

      <PillarsGrid
        pillars={pillars}
        onEdit={setEditingPillar}
        onDelete={deletePillar}
        onViewVideos={handleViewVideos}
        onViewAnalytics={handleViewAnalytics}
        canAddMore={canAddMorePillars()}
        onAddNew={() => setIsAddModalOpen(true)}
        userTier={userTier}
        pillarLimit={pillarLimit}
      />

      {/* Modals */}
      <PillarModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={addPillar}
        title="Create New Content Pillar"
        submitLabel="Create Pillar"
        mode="add"
      />

      <PillarModal
        isOpen={!!editingPillar}
        onClose={() => setEditingPillar(null)}
        onSubmit={updatePillar}
        pillar={editingPillar}
        title="Edit Content Pillar"
        submitLabel="Update Pillar"
        mode="edit"
      />

      <PillarAnalyticsModal
        pillar={analyticsModalPillar}
        isOpen={!!analyticsModalPillar}
        onClose={() => setAnalyticsModalPillar(null)}
      />
    </>
  );
};

export default PillarsPageContent;
