
import React from 'react';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AddPillarCardProps {
  onAddPillar: () => void;
  userTier: string;
  currentCount: number;
  limit: number;
}

const AddPillarCard = ({ onAddPillar, userTier, currentCount, limit }: AddPillarCardProps) => {
  const canAddMore = currentCount < limit;

  // Debug logging
  console.log('AddPillarCard Debug:', {
    userTier,
    currentCount,
    limit,
    canAddMore
  });
  
  return (
    <div className="bg-gray-700/50 border-2 border-dashed border-gray-600 rounded-xl p-6 flex flex-col items-center justify-center min-h-[200px] hover:border-teal/50 transition-colors">
      <div className="text-center space-y-4">
        <div className="w-12 h-12 rounded-full bg-teal/20 flex items-center justify-center mx-auto">
          <Plus className="w-6 h-6 text-teal" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-white mb-2">Add New Pillar</h3>
          <p className="text-sm text-gray-400 mb-4">
            {currentCount}/{limit === 999 ? 'unlimited' : limit} pillars ({userTier})
          </p>
        </div>
        <Button 
          onClick={onAddPillar}
          disabled={!canAddMore}
          className="bg-teal hover:bg-teal/90 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Pillar
        </Button>
        {!canAddMore && (
          <p className="text-xs text-red-400 mt-2">
            Upgrade your plan to add more pillars
          </p>
        )}
      </div>
    </div>
  );
};

export default AddPillarCard;
