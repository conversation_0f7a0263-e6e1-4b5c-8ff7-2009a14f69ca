
import React from 'react';
import { Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ContentPillar } from '@/types/pillar';
import EnhancedColorPicker from './EnhancedColorPicker';

interface PillarEditDialogProps {
  editingPillar: ContentPillar | null;
  setEditingPillar: (pillar: ContentPillar | null) => void;
  onUpdatePillar: () => Promise<boolean>;
  isSubmitting: boolean;
  getFieldError: (name: string) => string;
  touchField: (name: string) => void;
}

const PillarEditDialog = ({
  editingPillar,
  setEditingPillar,
  onUpdatePillar,
  isSubmitting,
  getFieldError,
  touchField
}: PillarEditDialogProps) => {
  const handleColorChange = (color: string) => {
    if (editingPillar) {
      setEditingPillar({ ...editingPillar, color });
    }
  };

  return (
    <Dialog open={!!editingPillar} onOpenChange={() => setEditingPillar(null)}>
      <DialogContent className="sm:max-w-md bg-gray-800 border-gray-600">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <Edit className="w-4 h-4 mr-2" />
            Edit Content Pillar
          </DialogTitle>
        </DialogHeader>
        {editingPillar && (
          <div className="space-y-6">
            <div>
              <label className="text-sm font-medium text-white">Pillar Name</label>
              <Input
                placeholder="e.g., Educational Tutorials"
                value={editingPillar.name}
                onChange={(e) => setEditingPillar({ ...editingPillar, name: e.target.value })}
                onBlur={() => touchField('name')}
                className="bg-gray-700 border-gray-600 text-white"
              />
              {getFieldError('name') && (
                <p className="text-red-400 text-sm mt-1">{getFieldError('name')}</p>
              )}
            </div>
            
            <div>
              <label className="text-sm font-medium text-white">Target Percentage</label>
              <Input
                type="number"
                placeholder="25"
                value={editingPillar.target_percentage}
                onChange={(e) => setEditingPillar({ ...editingPillar, target_percentage: parseInt(e.target.value) || 0 })}
                onBlur={() => touchField('target_percentage')}
                className="bg-gray-700 border-gray-600 text-white"
              />
              {getFieldError('target_percentage') && (
                <p className="text-red-400 text-sm mt-1">{getFieldError('target_percentage')}</p>
              )}
              <p className="text-xs text-gray-400 mt-1">
                What percentage of your content should be in this pillar?
              </p>
            </div>
            
            <EnhancedColorPicker 
              selectedColor={editingPillar.color} 
              onColorChange={handleColorChange}
            />
            
            <Button 
              onClick={onUpdatePillar} 
              disabled={isSubmitting}
              className="w-full bg-teal hover:bg-teal/90 text-white"
            >
              {isSubmitting ? 'Updating...' : 'Update Pillar'}
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default PillarEditDialog;
