
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Target, Video, TrendingUp, Youtube } from 'lucide-react';

interface SampleDataDisplayProps {
  onConnectYouTube?: () => void;
}

const SampleDataDisplay = ({ onConnectYouTube }: SampleDataDisplayProps) => {
  return (
    <div className="space-y-6">
      {/* Getting Started Guide */}
      <Card className="bg-gradient-to-r from-teal/10 to-terracotta/10 border-gray-600/30">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-teal/20 rounded-lg flex items-center justify-center">
              <Target className="w-6 h-6 text-teal" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Ready to Track Your Content Performance</h3>
              <p className="text-gray-300 text-sm mb-3">
                Create content pillars and connect your YouTube channel to see real analytics and insights. 
                Once you have videos assigned to pillars, this page will show comprehensive performance data.
              </p>
              <div className="flex items-center space-x-6 text-xs text-gray-400">
                <div className="flex items-center space-x-1">
                  <Video className="w-3 h-3" />
                  <span>Import your videos</span>
                </div>
                <div className="flex items-center space-x-1">
                  <TrendingUp className="w-3 h-3" />
                  <span>Track real performance</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Getting Started Steps */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gray-800 border-gray-600">
          <CardContent className="p-4 text-center">
            <div className="w-10 h-10 bg-teal/20 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-teal font-bold">1</span>
            </div>
            <h4 className="text-white font-medium mb-2">Create Content Pillars</h4>
            <p className="text-gray-400 text-sm">
              Define your content categories and set target percentages for each pillar
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-600">
          <CardContent className="p-4 text-center">
            <div className="w-10 h-10 bg-orange/20 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-orange font-bold">2</span>
            </div>
            <h4 className="text-white font-medium mb-2">Connect YouTube</h4>
            <p className="text-gray-400 text-sm">
              Link your YouTube channel to import video data and performance metrics
            </p>
            {onConnectYouTube && (
              <button
                onClick={onConnectYouTube}
                className="mt-3 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm transition-colors flex items-center mx-auto"
              >
                <Youtube className="w-4 h-4 mr-1" />
                Connect Now
              </button>
            )}
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-600">
          <CardContent className="p-4 text-center">
            <div className="w-10 h-10 bg-yellow/20 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-yellow font-bold">3</span>
            </div>
            <h4 className="text-white font-medium mb-2">Assign Videos</h4>
            <p className="text-gray-400 text-sm">
              Categorize your videos into pillars to see detailed analytics and insights
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SampleDataDisplay;
