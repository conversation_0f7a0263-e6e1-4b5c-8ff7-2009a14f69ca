
import React from 'react';
import { COLOR_PALETTE } from '@/constants/pillarColors';

interface ColorPickerProps {
  selectedColor: string;
  onColorChange: (color: string) => void;
}

const ColorPicker = ({ selectedColor, onColorChange }: ColorPickerProps) => {
  console.log('ColorPicker - selectedColor:', selectedColor);
  console.log('ColorPicker - COLOR_PALETTE:', COLOR_PALETTE);
  
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-white">Pillar Color</label>
      <div className="flex flex-wrap gap-3">
        {COLOR_PALETTE.map((color) => {
          const isSelected = selectedColor === color;
          console.log(`Color ${color} - isSelected:`, isSelected);
          
          return (
            <button
              key={color}
              type="button"
              className={`w-10 h-10 rounded-full transition-all hover:scale-110 relative ${
                isSelected
                  ? 'ring-2 ring-white scale-110 shadow-lg' 
                  : 'hover:ring-1 hover:ring-gray-300'
              }`}
              style={{ 
                backgroundColor: color,
                border: isSelected ? '3px solid white' : '2px solid #6B7280'
              }}
              onClick={() => {
                console.log('Color selected:', color);
                onColorChange(color);
              }}
              title={`Select ${color}`}
            >
              {isSelected && (
                <div 
                  className="absolute inset-1 rounded-full border-2"
                  style={{
                    borderColor: color === '#ffffff' ? '#000000' : 'rgba(255,255,255,0.8)'
                  }}
                />
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default ColorPicker;
