
import React, { useState, useEffect } from 'react';
import PillarsGrid from '@/components/Pillars/PillarsGrid';
import NextPillarFocus from '@/components/Pillars/NextPillarFocus';
import CompactContentBalance from '@/components/Pillars/CompactContentBalance';
import PillarAnalyticsModal from '@/components/Pillars/PillarAnalyticsModal';
import PillarModal from '@/components/Pillars/PillarModal';
import ErrorBoundary from '@/components/ui/error-boundary';
import { ContentPillar } from '@/types/pillar';
import { Button } from '@/components/ui/button';
import { PlusIcon } from '@radix-ui/react-icons';
import { dataService } from '@/services/dataService';
import { useAuth } from '@/hooks/useAuth';

interface StrategyTabContentProps {
  pillars: ContentPillar[];
  onDeletePillar: (pillarId: string, pillarName: string) => void;
  onEditPillar: (pillar: ContentPillar) => void;
  onViewVideos?: (pillarId: string) => void;
  addPillar: (pillar: any) => Promise<boolean>;
  canAddMorePillars: boolean; // Changed to boolean instead of function
  userTier: string;
  pillarLimit: number;
}

const StrategyTabContent = ({ 
  pillars, 
  onDeletePillar, 
  onEditPillar, 
  onViewVideos,
  addPillar,
  canAddMorePillars,
  userTier,
  pillarLimit
}: StrategyTabContentProps) => {
  const [analyticsModalPillar, setAnalyticsModalPillar] = useState<ContentPillar | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const handleViewAnalytics = (pillar: ContentPillar) => {
    setAnalyticsModalPillar(pillar);
  };

  const handleCloseAnalytics = () => {
    setAnalyticsModalPillar(null);
  };

  const handleAddPillar = () => {
    setIsAddModalOpen(true);
  };

  const handleAddPillarSubmit = async (pillarData: any) => {
    const success = await addPillar(pillarData);
    if (success) {
      setIsAddModalOpen(false);
    }
    return success;
  };

  return (
    <>
      <div className="space-y-8">
        {/* Strategy Insights Section - Only show if pillars exist */}
        {pillars.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 h-full">
              <ErrorBoundary fallback={
                <div className="text-center py-8 h-full flex items-center justify-center">
                  <p className="text-gray-400">Unable to load pillar focus suggestions.</p>
                </div>
              }>
                <div className="h-full">
                  <NextPillarFocus pillars={pillars} />
                </div>
              </ErrorBoundary>
            </div>
            <div className="lg:col-span-1 h-full">
              <ErrorBoundary fallback={
                <div className="text-center py-8 h-full flex items-center justify-center">
                  <p className="text-gray-400">Unable to load content balance.</p>
                </div>
              }>
                <div className="h-full">
                  <CompactContentBalance pillars={pillars} />
                </div>
              </ErrorBoundary>
            </div>
          </div>
        )}

        {/* Pillars Grid */}
        <ErrorBoundary fallback={
          <div className="text-center py-8">
            <p className="text-gray-400">Unable to load pillars. Please refresh the page.</p>
          </div>
        }>
          <PillarsGrid 
            pillars={pillars} 
            onDeletePillar={onDeletePillar}
            onEditPillar={onEditPillar}
            onViewVideos={onViewVideos}
            onViewAnalytics={handleViewAnalytics}
            canAddMore={canAddMorePillars} // Changed to use boolean directly
            onAddNew={handleAddPillar}
            userTier={userTier}
            pillarLimit={pillarLimit}
          />
        </ErrorBoundary>
      </div>

      {/* Analytics Modal */}
      <PillarAnalyticsModal
        pillar={analyticsModalPillar}
        isOpen={!!analyticsModalPillar}
        onClose={handleCloseAnalytics}
      />

      {/* Add Pillar Modal */}
      <PillarModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddPillarSubmit}
        title="Create New Content Pillar"
        submitLabel="Create Pillar"
        mode="add"
      />
    </>
  );
};

export default StrategyTabContent;
