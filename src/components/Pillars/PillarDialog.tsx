
import React from 'react';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import EnhancedColorPicker from './EnhancedColorPicker';

interface PillarDialogProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  newPillar: { name: string; target_percentage: number; color: string };
  setNewPillar: (pillar: { name: string; target_percentage: number; color: string }) => void;
  onAddPillar: () => Promise<boolean>;
  canAddMore: boolean;
  isSubmitting: boolean;
  getFieldError: (name: string) => string;
  touchField: (name: string) => void;
}

const PillarDialog = ({
  isOpen,
  setIsOpen,
  newPillar,
  setNewPillar,
  onAddPillar,
  canAddMore,
  isSubmitting,
  getFieldError,
  touchField
}: PillarDialogProps) => {
  const handleColorChange = (color: string) => {
    setNewPillar({ ...newPillar, color });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          className="bg-teal hover:bg-teal/90 text-white"
          disabled={!canAddMore}
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Pillar
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md bg-gray-800 border-gray-600">
        <DialogHeader>
          <DialogTitle className="text-white">Create New Content Pillar</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          <div>
            <label className="text-sm font-medium text-white">Pillar Name</label>
            <Input
              placeholder="e.g., Educational Tutorials"
              value={newPillar.name}
              onChange={(e) => setNewPillar({ ...newPillar, name: e.target.value })}
              onBlur={() => touchField('name')}
              className="bg-gray-700 border-gray-600 text-white"
            />
            {getFieldError('name') && (
              <p className="text-red-400 text-sm mt-1">{getFieldError('name')}</p>
            )}
          </div>
          
          <div>
            <label className="text-sm font-medium text-white">Target Percentage</label>
            <Input
              type="number"
              placeholder="25"
              value={newPillar.target_percentage}
              onChange={(e) => setNewPillar({ ...newPillar, target_percentage: parseInt(e.target.value) || 0 })}
              onBlur={() => touchField('target_percentage')}
              className="bg-gray-700 border-gray-600 text-white"
            />
            {getFieldError('target_percentage') && (
              <p className="text-red-400 text-sm mt-1">{getFieldError('target_percentage')}</p>
            )}
            <p className="text-xs text-gray-400 mt-1">
              What percentage of your content should be in this pillar?
            </p>
          </div>
          
          <EnhancedColorPicker 
            selectedColor={newPillar.color} 
            onColorChange={handleColorChange}
          />
          
          <Button 
            onClick={onAddPillar} 
            disabled={isSubmitting}
            className="w-full bg-teal hover:bg-teal/90 text-white"
          >
            {isSubmitting ? 'Creating...' : 'Create Pillar'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PillarDialog;
