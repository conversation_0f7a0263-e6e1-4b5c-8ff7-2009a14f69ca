
import React from 'react';
import { Skeleton, SkeletonCard } from '@/components/ui/loading-skeleton';
import PillarsBackground from './PillarsBackground';

const PillarsLoadingState = () => {
  return (
    <div className="relative min-h-screen">
      <PillarsBackground />
      <div className="relative z-10 space-y-8">
        {/* Header Loading */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>

        {/* Tabs Loading */}
        <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg w-fit">
          <Skeleton className="h-8 w-24" />
          <Skeleton className="h-8 w-32" />
        </div>

        {/* Content Loading */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="dashboard-card p-6">
              <SkeletonCard />
            </div>
          ))}
        </div>

        {/* Charts Loading */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
          <div className="dashboard-card p-6">
            <Skeleton className="h-6 w-48 mb-4" />
            <Skeleton className="h-64 w-full" />
          </div>
          <div className="dashboard-card p-6">
            <Skeleton className="h-6 w-48 mb-4" />
            <Skeleton className="h-64 w-full" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PillarsLoadingState;
