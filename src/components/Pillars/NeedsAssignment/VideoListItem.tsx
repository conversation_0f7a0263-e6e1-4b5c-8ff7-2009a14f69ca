
import React from 'react';
import { Calendar, Eye, MessageCircle, ThumbsUp } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ContentPillar } from '@/types/pillar';
import { UnassignedVideo } from './useNeedsAssignment';
import { formatNumber } from '@/utils/statsUtils';

interface VideoListItemProps {
  video: UnassignedVideo;
  isSelected: boolean;
  pillars: ContentPillar[];
  onToggleSelection: (videoId: string) => void;
  onAssignPillar: (videoId: string, pillarId: string) => void;
}

const VideoListItem = ({
  video,
  isSelected,
  pillars,
  onToggleSelection,
  onAssignPillar
}: VideoListItemProps) => {
  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardContent className="p-4">
        <div className="flex items-center space-x-4">
          <Checkbox
            checked={isSelected}
            onCheckedChange={() => onToggleSelection(video.id)}
          />
          
          {video.youtube_thumbnail_url && (
            <img
              src={video.youtube_thumbnail_url}
              alt={video.title}
              className="w-20 h-11 object-cover rounded"
            />
          )}
          
          <div className="flex-1 min-w-0">
            <h4 className="text-white font-medium mb-1">{video.title}</h4>
            <div className="flex items-center space-x-4 text-sm text-gray-400">
              <div className="flex items-center space-x-1">
                <Calendar className="w-4 h-4" />
                <span>{new Date(video.published_at).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Eye className="w-4 h-4" />
                <span>{formatNumber(video.views || 0)}</span>
              </div>
              <div className="flex items-center space-x-1">
                <ThumbsUp className="w-4 h-4" />
                <span>{formatNumber(video.like_count || 0)}</span>
              </div>
              <div className="flex items-center space-x-1">
                <MessageCircle className="w-4 h-4" />
                <span>{formatNumber(video.comment_count || 0)}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Select
              onValueChange={(pillarId) => onAssignPillar(video.id, pillarId)}
            >
              <SelectTrigger className="w-48 bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Assign to pillar" />
              </SelectTrigger>
              <SelectContent>
                {pillars.map(pillar => (
                  <SelectItem key={pillar.id} value={pillar.id}>
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: pillar.color }}
                      />
                      <span>{pillar.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default VideoListItem;
