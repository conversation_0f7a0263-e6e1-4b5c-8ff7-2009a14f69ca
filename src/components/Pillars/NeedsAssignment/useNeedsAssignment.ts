
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';

interface UnassignedVideo {
  id: string;
  title: string;
  published_at: string;
  views: number;
  like_count: number;
  comment_count: number;
  youtube_video_id: string | null;
  youtube_thumbnail_url: string | null;
}

export const useNeedsAssignment = () => {
  const { user } = useAuth();
  const [videos, setVideos] = useState<UnassignedVideo[]>([]);
  const [selectedVideos, setSelectedVideos] = useState<Set<string>>(new Set());
  const [bulkPillar, setBulkPillar] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isAssigning, setIsAssigning] = useState(false);
  const [userProfile, setUserProfile] = useState<any>(null);

  useEffect(() => {
    if (user) {
      fetchUserProfile();
    }
  }, [user]);

  useEffect(() => {
    if (userProfile) {
      fetchUnassignedVideos();
    }
  }, [userProfile]);

  const fetchUserProfile = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('users')
        .select('youtube_channel_id, youtube_channel_name, youtube_access_token, youtube_refresh_token')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      setUserProfile(data);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      setUserProfile(null);
    }
  };

  const fetchUnassignedVideos = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('videos')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'published')
        .is('pillar_id', null)
        .not('youtube_video_id', 'is', null)
        .not('youtube_thumbnail_url', 'is', null)
        .not('published_at', 'is', null)
        .order('published_at', { ascending: false });

      if (!error && data) {
        const realVideos = data.filter(video => 
          video.youtube_video_id && 
          video.youtube_thumbnail_url && 
          video.published_at &&
          video.youtube_video_id.length >= 10 &&
          video.youtube_thumbnail_url.includes('ytimg.com')
        );
        setVideos(realVideos);
      }
    } catch (error) {
      console.error('Error fetching unassigned videos:', error);
      setVideos([]);
    } finally {
      setIsLoading(false);
    }
  };

  const assignPillar = async (videoId: string, pillarId: string) => {
    if (!user) return;

    const { error } = await supabase
      .from('videos')
      .update({ pillar_id: pillarId })
      .eq('id', videoId)
      .eq('user_id', user.id);

    if (error) {
      toast.error('Failed to assign pillar');
    } else {
      toast.success('Pillar assigned successfully');
      setVideos(videos.filter(v => v.id !== videoId));
      setSelectedVideos(prev => {
        const newSet = new Set(prev);
        newSet.delete(videoId);
        return newSet;
      });
    }
  };

  const bulkAssignPillars = async () => {
    if (!user || !bulkPillar || selectedVideos.size === 0) return;

    setIsAssigning(true);
    const videoIds = Array.from(selectedVideos);
    
    const { error } = await supabase
      .from('videos')
      .update({ pillar_id: bulkPillar })
      .in('id', videoIds)
      .eq('user_id', user.id);

    if (error) {
      toast.error('Failed to assign pillars');
    } else {
      toast.success(`Assigned ${videoIds.length} videos to pillar`);
      setVideos(videos.filter(v => !selectedVideos.has(v.id)));
      setSelectedVideos(new Set());
      setBulkPillar('');
    }
    setIsAssigning(false);
  };

  const toggleVideoSelection = (videoId: string) => {
    const newSelection = new Set(selectedVideos);
    if (newSelection.has(videoId)) {
      newSelection.delete(videoId);
    } else {
      newSelection.add(videoId);
    }
    setSelectedVideos(newSelection);
  };

  const selectAll = () => {
    if (selectedVideos.size === videos.length) {
      setSelectedVideos(new Set());
    } else {
      setSelectedVideos(new Set(videos.map(v => v.id)));
    }
  };

  return {
    videos,
    selectedVideos,
    bulkPillar,
    setBulkPillar,
    isLoading,
    isAssigning,
    userProfile,
    assignPillar,
    bulkAssignPillars,
    toggleVideoSelection,
    selectAll
  };
};

export type { UnassignedVideo };
