
import React from 'react';
import { AlertCircle, Check } from 'lucide-react';

export const YouTubeNotConnectedState = () => (
  <div className="text-center py-12">
    <AlertCircle className="w-16 h-16 text-orange mx-auto mb-4" />
    <h3 className="text-lg font-medium text-gray-300 mb-2">YouTube Not Connected</h3>
    <p className="text-gray-400">
      Connect your YouTube channel to see videos that need pillar assignment.
    </p>
  </div>
);

export const AllVideosAssignedState = () => (
  <div className="text-center py-12">
    <Check className="w-16 h-16 text-green-400 mx-auto mb-4" />
    <h3 className="text-lg font-medium text-gray-300">All videos assigned!</h3>
    <p className="text-gray-400 mt-1">
      All your published videos have been assigned to content pillars.
    </p>
  </div>
);

export const LoadingState = () => (
  <div className="flex justify-center items-center py-12">
    <div className="w-8 h-8 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
  </div>
);
