
import React from 'react';
import { AlertCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { ContentPillar } from '@/types/pillar';

interface BulkActionsCardProps {
  videosCount: number;
  selectedCount: number;
  bulkPillar: string;
  setBulkPillar: (value: string) => void;
  isAssigning: boolean;
  pillars: ContentPillar[];
  onSelectAll: () => void;
  onBulkAssign: () => void;
}

const BulkActionsCard = ({
  videosCount,
  selectedCount,
  bulkPillar,
  setBulkPillar,
  isAssigning,
  pillars,
  onSelectAll,
  onBulkAssign
}: BulkActionsCardProps) => {
  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white flex items-center space-x-2">
          <AlertCircle className="w-5 h-5 text-orange" />
          <span>Bulk Assignment</span>
          <Badge variant="outline" className="border-orange text-orange">
            {videosCount} videos need assignment
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-end">
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={selectedCount === videosCount}
              onCheckedChange={onSelectAll}
            />
            <span className="text-gray-300">
              Select all ({selectedCount} selected)
            </span>
          </div>
          
          <div className="flex-1 max-w-md">
            <Select value={bulkPillar} onValueChange={setBulkPillar}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Choose pillar for selected videos" />
              </SelectTrigger>
              <SelectContent>
                {pillars.map(pillar => (
                  <SelectItem key={pillar.id} value={pillar.id}>
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: pillar.color }}
                      />
                      <span>{pillar.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <Button
            onClick={onBulkAssign}
            disabled={selectedCount === 0 || !bulkPillar || isAssigning}
            className="bg-teal hover:bg-teal/90 text-white"
          >
            {isAssigning ? 'Assigning...' : `Assign ${selectedCount} videos`}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default BulkActionsCard;
