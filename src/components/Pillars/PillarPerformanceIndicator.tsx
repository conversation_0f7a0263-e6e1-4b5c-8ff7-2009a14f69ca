
import React from 'react';
import { TrendingUp, TrendingDown, Minus, Target } from 'lucide-react';

interface PillarPerformanceIndicatorProps {
  actual: number;
  target: number;
  videoCount: number;
  avgViews: number;
}

const PillarPerformanceIndicator = ({ actual, target, videoCount, avgViews }: PillarPerformanceIndicatorProps) => {
  const difference = actual - target;
  const tolerance = 5; // 5% tolerance for "on target"
  
  const getStatus = () => {
    if (Math.abs(difference) <= tolerance) return 'on-target';
    return difference > 0 ? 'over-target' : 'under-target';
  };

  const getStatusColor = () => {
    const status = getStatus();
    switch (status) {
      case 'on-target': return 'text-green-400';
      case 'over-target': return 'text-orange';
      case 'under-target': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = () => {
    const status = getStatus();
    const iconClass = `w-4 h-4 ${getStatusColor()}`;
    
    switch (status) {
      case 'on-target': return <Target className={iconClass} />;
      case 'over-target': return <TrendingUp className={iconClass} />;
      case 'under-target': return <TrendingDown className={iconClass} />;
      default: return <Minus className={iconClass} />;
    }
  };

  const getStatusText = () => {
    const status = getStatus();
    switch (status) {
      case 'on-target': return 'On Target';
      case 'over-target': return `${Math.abs(difference).toFixed(1)}% Over`;
      case 'under-target': return `${Math.abs(difference).toFixed(1)}% Under`;
      default: return 'Unknown';
    }
  };

  const getRecommendation = () => {
    const status = getStatus();
    switch (status) {
      case 'on-target': return 'Great balance! Keep it up.';
      case 'over-target': return 'Consider diversifying content.';
      case 'under-target': return 'Create more content in this pillar.';
      default: return '';
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span className={`text-sm font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
        <div className="text-xs text-gray-400">
          {videoCount} videos
        </div>
      </div>
      
      <div className="text-xs text-gray-400">
        {getRecommendation()}
      </div>
      
      {avgViews > 0 && (
        <div className="text-xs text-gray-400">
          Avg views: {avgViews.toLocaleString()}
        </div>
      )}
    </div>
  );
};

export default PillarPerformanceIndicator;
