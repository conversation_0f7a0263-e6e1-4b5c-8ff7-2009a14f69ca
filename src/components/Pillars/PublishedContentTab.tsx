
import React, { useState, useEffect } from 'react';
import { ContentPillar } from '@/types/pillar';
import VideoAnalysisModal from '@/components/Dashboard/VideoAnalysisModal';
import { usePublishedVideos, PublishedVideo } from './PublishedContent/usePublishedVideos';
import PillarFilterAlert from './PublishedContent/PillarFilterAlert';
import ContentFilters from './PublishedContent/ContentFilters';
import PillarGroup from './PublishedContent/PillarGroup';
import EmptyState from './PublishedContent/EmptyState';
import { getDateFilteredVideos, getSortedVideos, filterVideosBySearch } from './PublishedContent/utils';

interface PublishedContentTabProps {
  pillars: ContentPillar[];
  initialPillarFilter?: string | null;
  onClearPillarFilter?: () => void;
}

const PublishedContentTab = ({ pillars, initialPillarFilter, onClearPillarFilter }: PublishedContentTabProps) => {
  const { videos, isLoading } = usePublishedVideos();
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('all');
  const [sortBy, setSortBy] = useState('recent');
  const [expandedPillars, setExpandedPillars] = useState<Set<string>>(new Set());
  const [pillarFilter, setPillarFilter] = useState<string | null>(initialPillarFilter || null);
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (initialPillarFilter) {
      setPillarFilter(initialPillarFilter);
      setExpandedPillars(new Set([initialPillarFilter]));
    }
  }, [initialPillarFilter]);

  useEffect(() => {
    // Only expand pillars that actually have videos
    if (videos.length > 0) {
      const pillarsWithVideos = new Set(videos.map(v => v.pillar_id).filter(Boolean));
      if (!initialPillarFilter) {
        setExpandedPillars(pillarsWithVideos);
      }
    }
  }, [videos, initialPillarFilter]);

  // Apply filters to the videos
  const filteredVideos = getSortedVideos(
    getDateFilteredVideos(
      filterVideosBySearch(videos, searchTerm),
      dateFilter
    ),
    sortBy
  );

  const displayPillars = pillarFilter 
    ? pillars.filter(p => p.id === pillarFilter)
    : pillars;

  const groupedVideos = displayPillars.reduce((acc, pillar) => {
    acc[pillar.id] = filteredVideos.filter(video => video.pillar_id === pillar.id);
    return acc;
  }, {} as Record<string, PublishedVideo[]>);

  // Add videos without pillar associations to a special "Uncategorized" group
  const videosWithoutPillars = filteredVideos.filter(video => !video.pillar_id);
  if (videosWithoutPillars.length > 0) {
    groupedVideos['uncategorized'] = videosWithoutPillars;
  }

  const togglePillarExpansion = (pillarId: string) => {
    const newExpanded = new Set(expandedPillars);
    if (newExpanded.has(pillarId)) {
      newExpanded.delete(pillarId);
    } else {
      newExpanded.add(pillarId);
    }
    setExpandedPillars(newExpanded);
  };

  const clearPillarFilter = () => {
    setPillarFilter(null);
    onClearPillarFilter?.();
    // Re-expand all pillars with videos
    if (videos.length > 0) {
      const pillarsWithVideos = new Set(videos.map(v => v.pillar_id).filter(Boolean));
      setExpandedPillars(pillarsWithVideos);
    }
  };

  const handleVideoClick = (video: PublishedVideo) => {
    // Transform the video data to match VideoAnalysisModal expectations
    const modalVideo = {
      id: video.id,
      title: video.title,
      views: video.views || 0,
      published_at: video.published_at,
      pillar: video.pillar_id ? {
        name: pillars.find(p => p.id === video.pillar_id)?.name || 'Unknown',
        color: pillars.find(p => p.id === video.pillar_id)?.color || '#37BEB0'
      } : undefined
    };
    
    setSelectedVideo(modalVideo);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedVideo(null);
  };

  // Calculate channel average views for performance comparison
  const channelAverage = filteredVideos.length > 0 
    ? Math.round(filteredVideos.reduce((sum, video) => sum + (video.views || 0), 0) / filteredVideos.length)
    : 0;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="w-8 h-8 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PillarFilterAlert
        pillarFilter={pillarFilter}
        pillars={pillars}
        onClearFilter={clearPillarFilter}
      />

      <ContentFilters
        searchTerm={searchTerm}
        dateFilter={dateFilter}
        sortBy={sortBy}
        onSearchChange={setSearchTerm}
        onDateFilterChange={setDateFilter}
        onSortChange={setSortBy}
      />

      {filteredVideos.length > 0 ? (
        <div className="space-y-4">
          {displayPillars.map(pillar => (
            <PillarGroup
              key={pillar.id}
              pillar={pillar}
              videos={groupedVideos[pillar.id] || []}
              isExpanded={expandedPillars.has(pillar.id)}
              onToggleExpansion={() => togglePillarExpansion(pillar.id)}
              onVideoClick={handleVideoClick}
            />
          ))}
        </div>
      ) : (
        <EmptyState
          searchTerm={searchTerm}
          dateFilter={dateFilter}
        />
      )}

      <VideoAnalysisModal
        video={selectedVideo}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        channelAverage={channelAverage}
      />
    </div>
  );
};

export default PublishedContentTab;
