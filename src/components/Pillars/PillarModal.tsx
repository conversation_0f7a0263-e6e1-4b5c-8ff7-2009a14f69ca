
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ContentPillar, NewPillar } from '@/types/pillar';
import { COLOR_PALETTE } from '@/constants/pillarColors';
import EnhancedColorPicker from './EnhancedColorPicker';

interface PillarModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (pillar: NewPillar | ContentPillar) => Promise<boolean>;
  pillar?: ContentPillar | null;
  title: string;
  submitLabel: string;
  mode: 'add' | 'edit';
}

const PillarModal = ({ isOpen, onClose, onSubmit, pillar, title, submitLabel, mode }: PillarModalProps) => {
  const [name, setName] = useState(pillar?.name || '');
  const [targetPercentage, setTargetPercentage] = useState(pillar?.target_percentage || 25);
  const [color, setColor] = useState(pillar?.color || COLOR_PALETTE[0]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  React.useEffect(() => {
    if (pillar) {
      setName(pillar.name);
      setTargetPercentage(pillar.target_percentage);
      setColor(pillar.color);
    } else {
      setName('');
      setTargetPercentage(25);
      setColor(COLOR_PALETTE[0]);
    }
  }, [pillar, isOpen]);

  const handleSubmit = async () => {
    if (!name.trim()) return;
    
    setIsSubmitting(true);
    
    const pillarData = mode === 'edit' && pillar 
      ? { ...pillar, name, target_percentage: targetPercentage, color }
      : { name, target_percentage: targetPercentage, color };
    
    const success = await onSubmit(pillarData);
    
    if (success) {
      onClose();
      setName('');
      setTargetPercentage(25);
      setColor(COLOR_PALETTE[0]);
    }
    
    setIsSubmitting(false);
  };

  return (
    <Dialog 
      open={isOpen} 
      onOpenChange={onClose}
      aria-labelledby="pillar-modal-title"
    >
      <DialogContent className="glass-modal sm:max-w-md">
        <DialogHeader>
          <DialogTitle id="pillar-modal-title" className="text-white">{title}</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          <div>
            <label className="text-sm font-medium text-white">Pillar Name</label>
            <Input
              placeholder="e.g., Educational Tutorials"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white"
            />
          </div>
          
          <div>
            <label className="text-sm font-medium text-white">Target Percentage</label>
            <Input
              type="number"
              placeholder="25"
              value={targetPercentage}
              onChange={(e) => setTargetPercentage(parseInt(e.target.value) || 0)}
              className="bg-gray-700 border-gray-600 text-white"
            />
            <p className="text-xs text-gray-400 mt-1">
              What percentage of your content should be in this pillar?
            </p>
          </div>
          
          <EnhancedColorPicker 
            selectedColor={color} 
            onColorChange={setColor}
          />
          
          <Button 
            onClick={handleSubmit} 
            disabled={isSubmitting || !name.trim()}
            className="w-full bg-teal hover:bg-teal/90 text-white"
          >
            {isSubmitting ? 'Saving...' : submitLabel}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PillarModal;
