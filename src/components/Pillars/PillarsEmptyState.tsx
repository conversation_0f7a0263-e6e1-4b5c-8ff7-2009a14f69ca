
import React from 'react';
import { Plus, Target } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

interface PillarsEmptyStateProps {
  onAddPillar: () => void;
}

const PillarsEmptyState = ({ onAddPillar }: PillarsEmptyStateProps) => {
  return (
    <div className="text-center py-16">
      <div className="max-w-md mx-auto">
        <div className="w-16 h-16 rounded-full bg-teal/20 flex items-center justify-center mx-auto mb-6">
          <Target className="w-8 h-8 text-teal" />
        </div>
        <h3 className="text-xl font-semibold text-white mb-3">No Content Pillars Yet</h3>
        <p className="text-gray-400 mb-6">
          Create your first content pillar to start organizing your content strategy around key themes.
        </p>
        <Button 
          onClick={onAddPillar}
          className="bg-teal hover:bg-teal/90 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Your First Pillar
        </Button>
      </div>
    </div>
  );
};

export default PillarsEmptyState;
