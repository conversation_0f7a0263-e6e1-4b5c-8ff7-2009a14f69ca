import { Badge } from '@/components/ui/badge';

interface UserProfileBadgeProps {
  tier: string;
}

const getBadgeColor = (tier: string) => {
  switch(tier) {
    case 'analytics_only': return 'bg-teal-500';
    case 'ai_lite': return 'bg-orange';
    case 'ai_pro': return 'bg-green-500';
    default: return 'bg-gray-500';
  }
};

const getTierDisplayName = (tier: string) => {
  switch(tier) {
    case 'analytics_only': return 'MCH STARTER';
    case 'ai_lite': return 'MCH LITE';
    case 'ai_pro': return 'MCH PRO';
    default: return 'FREE';
  }
};

const UserProfileBadge = ({ tier }: UserProfileBadgeProps) => {
  return (
    <Badge className={`${getBadgeColor(tier)} text-white font-medium`}>
      {getTierDisplayName(tier)}
    </Badge>
  );
};

export default UserProfileBadge;