import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';

const MockDataChecker = () => {
  const { user } = useAuth();
  const [mockData, setMockData] = useState<any>(null);
  const [isChecking, setIsChecking] = useState(false);
  
  const checkForMockData = async () => {
    if (!user) return;
    
    setIsChecking(true);
    try {
      // Check for mock pillars - only obvious mock indicators
      const { data: mockPillars, error: pillarsError } = await supabase
        .from('content_pillars')
        .select('*')
        .eq('user_id', user.id)
        .or('name.ilike.%mock%,name.ilike.%demo%,name.ilike.%test%,name.ilike.%sample%');
        
      if (pillarsError) throw pillarsError;
      
      // Check for mock videos - only very specific mock indicators
      const mockTitles = [
        'React Server Components: Complete Guide',
        'React Hooks Complete Guide',
        'Building a Full Stack React App',
        'React State Management Deep Dive',
        'Home Office Setup Tour',
        'AI Tools Revolutionizing Content Creation',
        'Boost Your Work From Home Productivity',
        'How to Grow Your Channel Fast',
        'Beginner\'s Guide to YouTube Success',
        'Top 10 Camera Tips for Creators',
        'YouTube Algorithm Explained',
        'Camera Settings for Beginners'
      ];

      const { data: mockVideos, error: videosError } = await supabase
        .from('videos')
        .select('*')
        .eq('user_id', user.id)
        .or(`title.ilike.%mock%,title.ilike.%demo%,title.ilike.%test%,title.ilike.%sample%,title.in.(${mockTitles.map(t => `"${t}"`).join(',')})`);
        
      if (videosError) throw videosError;
      
      setMockData({
        pillars: mockPillars || [],
        videos: mockVideos || []
      });
    } catch (error) {
      console.error('Error checking for mock data:', error);
    } finally {
      setIsChecking(false);
    }
  };
  
  const deleteMockData = async () => {
    if (!user || !mockData) return;
    
    try {
      // Delete mock pillars
      if (mockData.pillars.length > 0) {
        const pillarIds = mockData.pillars.map(p => p.id);
        const { error: deletePillarsError } = await supabase
          .from('content_pillars')
          .delete()
          .in('id', pillarIds);
          
        if (deletePillarsError) throw deletePillarsError;
      }
      
      // Delete mock videos
      if (mockData.videos.length > 0) {
        const videoIds = mockData.videos.map(v => v.id);
        const { error: deleteVideosError } = await supabase
          .from('videos')
          .delete()
          .in('id', videoIds);
          
        if (deleteVideosError) throw deleteVideosError;
      }
      
      // Refresh the check
      await checkForMockData();
    } catch (error) {
      console.error('Error deleting mock data:', error);
    }
  };
  
  return (
    <div className="p-4 border rounded-md">
      <h3 className="font-medium mb-2">Mock Data Checker</h3>
      <Button 
        variant="outline" 
        onClick={checkForMockData}
        disabled={isChecking}
        className="mb-4"
      >
        {isChecking ? 'Checking...' : 'Check for Mock Data'}
      </Button>
      
      {mockData && (
        <div className="space-y-4">
          <div>
            <h4 className="font-medium">Mock Pillars ({mockData.pillars.length})</h4>
            {mockData.pillars.length > 0 ? (
              <ul className="text-sm">
                {mockData.pillars.map(pillar => (
                  <li key={pillar.id}>{pillar.name}</li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-green-500">No mock pillars found</p>
            )}
          </div>
          
          <div>
            <h4 className="font-medium">Mock Videos ({mockData.videos.length})</h4>
            {mockData.videos.length > 0 ? (
              <ul className="text-sm">
                {mockData.videos.map(video => (
                  <li key={video.id}>{video.title}</li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-green-500">No mock videos found</p>
            )}
          </div>
          
          {(mockData.pillars.length > 0 || mockData.videos.length > 0) && (
            <Button 
              variant="destructive" 
              onClick={deleteMockData}
            >
              Delete All Mock Data
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default MockDataChecker;