import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useUserProfile } from '@/hooks/useUserProfile';
import { useUserProfile as useSettingsUserProfile } from '@/hooks/settings/useUserProfile';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const ProfileDiagnostic = () => {
  const { user, loading: authLoading } = useAuth();
  const { profile: profile1, isLoading: loading1 } = useUserProfile();
  const { profile: profile2, isLoading: loading2 } = useSettingsUserProfile();
  
  const [manualProfile, setManualProfile] = useState(null);
  const [manualError, setManualError] = useState(null);
  const [usersTableData, setUsersTableData] = useState(null);
  const [profilesTableData, setProfilesTableData] = useState(null);

  const fetchManualProfile = async () => {
    if (!user) return;
    
    try {
      setManualError(null);
      
      // Try users table
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();
      
      setUsersTableData({ data: userData, error: userError });
      
      // Try profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();
      
      setProfilesTableData({ data: profileData, error: profileError });
      
      if (userData) {
        setManualProfile(userData);
      } else if (profileData) {
        setManualProfile(profileData);
      } else {
        setManualError('No profile found in either table');
      }
    } catch (error) {
      setManualError(error.message);
    }
  };

  useEffect(() => {
    if (user) {
      fetchManualProfile();
    }
  }, [user]);

  return (
    <div className="p-4 space-y-4">
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white">Profile Loading Diagnostic</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold text-white">Auth State:</h3>
            <p className="text-gray-300">Loading: {authLoading ? 'Yes' : 'No'}</p>
            <p className="text-gray-300">User: {user ? 'Authenticated' : 'Not authenticated'}</p>
            <p className="text-gray-300">User ID: {user?.id || 'None'}</p>
            <p className="text-gray-300">Email: {user?.email || 'None'}</p>
          </div>

          <div>
            <h3 className="font-semibold text-white">useUserProfile Hook:</h3>
            <p className="text-gray-300">Loading: {loading1 ? 'Yes' : 'No'}</p>
            <p className="text-gray-300">Profile: {profile1 ? 'Loaded' : 'None'}</p>
            {profile1 && (
              <pre className="text-xs bg-gray-800 text-green-400 p-2 rounded border border-gray-600">
                {JSON.stringify(profile1, null, 2)}
              </pre>
            )}
          </div>

          <div>
            <h3 className="font-semibold text-white">useSettingsUserProfile Hook:</h3>
            <p className="text-gray-300">Loading: {loading2 ? 'Yes' : 'No'}</p>
            <p className="text-gray-300">Profile: {profile2 ? 'Loaded' : 'None'}</p>
            {profile2 && (
              <pre className="text-xs bg-gray-800 text-blue-400 p-2 rounded border border-gray-600">
                {JSON.stringify(profile2, null, 2)}
              </pre>
            )}
          </div>

          <div>
            <h3 className="font-semibold text-white">Manual Database Check:</h3>
            <Button onClick={fetchManualProfile} className="mb-2">
              Refresh Manual Check
            </Button>

            {manualError && (
              <p className="text-red-400">Error: {manualError}</p>
            )}

            <div className="space-y-2">
              <div>
                <h4 className="font-medium text-white">Users Table:</h4>
                {usersTableData?.error ? (
                  <p className="text-red-400">Error: {usersTableData.error.message}</p>
                ) : usersTableData?.data ? (
                  <pre className="text-xs bg-gray-800 text-green-400 p-2 rounded border border-gray-600">
                    {JSON.stringify(usersTableData.data, null, 2)}
                  </pre>
                ) : (
                  <p className="text-gray-300">No data</p>
                )}
              </div>

              <div>
                <h4 className="font-medium text-white">Profiles Table:</h4>
                {profilesTableData?.error ? (
                  <p className="text-red-400">Error: {profilesTableData.error.message}</p>
                ) : profilesTableData?.data ? (
                  <pre className="text-xs bg-gray-800 text-blue-400 p-2 rounded border border-gray-600">
                    {JSON.stringify(profilesTableData.data, null, 2)}
                  </pre>
                ) : (
                  <p className="text-gray-300">No data</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileDiagnostic;
