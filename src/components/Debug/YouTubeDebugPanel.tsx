import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { useYouTubeConnection } from '@/hooks/useYouTubeConnection';
import { useYouTubeRefresh } from '@/hooks/useYouTubeRefresh';
import { RefreshCw, Bug } from 'lucide-react';

/**
 * Debug panel to track YouTube connection status in real-time
 * This helps identify timing and caching issues during OAuth flow
 */
const YouTubeDebugPanel = () => {
  const { 
    youtubeData, 
    isConnected, 
    needsReconnection, 
    tokenStatus, 
    isLoading, 
    checkTokenStatus,
    refreshConnection 
  } = useYouTubeConnection();
  
  const { refreshYouTubeConnection } = useYouTubeRefresh();
  const [lastUpdate, setLastUpdate] = useState(new Date().toISOString());

  // Track when data changes
  useEffect(() => {
    setLastUpdate(new Date().toISOString());
    console.log('🐛 DEBUG: YouTube data changed at', new Date().toISOString());
  }, [youtubeData, tokenStatus, isConnected]);

  const handleForceRefresh = async () => {
    console.log('🐛 DEBUG: Force refresh triggered');
    await refreshYouTubeConnection();
  };

  const handleForceTokenCheck = async () => {
    console.log('🐛 DEBUG: Force token check triggered');
    await checkTokenStatus();
  };

  return (
    <Card className="dashboard-card border-yellow-500/50">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-yellow-400">
          <Bug className="w-5 h-5" />
          YouTube Debug Panel
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-xs text-gray-400">
          Last Update: {new Date(lastUpdate).toLocaleTimeString()}
        </div>
        
        {/* Connection Status */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Is Connected:</span>{' '}
            <span className={isConnected ? 'text-green-400' : 'text-red-400'}>
              {isConnected ? 'Yes' : 'No'}
            </span>
          </div>
          <div>
            <span className="text-gray-400">Needs Reconnection:</span>{' '}
            <span className={needsReconnection ? 'text-orange-400' : 'text-gray-400'}>
              {needsReconnection ? 'Yes' : 'No'}
            </span>
          </div>
        </div>

        {/* Token Status */}
        <div className="space-y-2">
          <h4 className="text-white font-medium">Token Status</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-gray-400">Valid:</span>{' '}
              <span className={tokenStatus.isValid ? 'text-green-400' : 'text-red-400'}>
                {tokenStatus.isValid ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="text-gray-400">Needs Refresh:</span>{' '}
              <span className={tokenStatus.needsRefresh ? 'text-orange-400' : 'text-gray-400'}>
                {tokenStatus.needsRefresh ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
          {tokenStatus.error && (
            <div className="text-red-400 text-xs">
              Error: {tokenStatus.error}
            </div>
          )}
        </div>

        {/* YouTube Data */}
        <div className="space-y-2">
          <h4 className="text-white font-medium">YouTube Data</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-gray-400">Channel ID:</span>{' '}
              <span className="text-white">
                {youtubeData?.youtube_channel_id ? 'Present' : 'None'}
              </span>
            </div>
            <div>
              <span className="text-gray-400">Access Token:</span>{' '}
              <span className="text-white">
                {youtubeData?.youtube_access_token ? 
                  `${youtubeData.youtube_access_token.length} chars` : 'None'}
              </span>
            </div>
            <div>
              <span className="text-gray-400">Refresh Token:</span>{' '}
              <span className="text-white">
                {youtubeData?.youtube_refresh_token ? 
                  `${youtubeData.youtube_refresh_token.length} chars` : 'None'}
              </span>
            </div>
            <div>
              <span className="text-gray-400">Channel Name:</span>{' '}
              <span className="text-white">
                {youtubeData?.youtube_channel_name || 'None'}
              </span>
            </div>
          </div>
        </div>

        {/* Debug Actions */}
        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="outline" 
            onClick={handleForceRefresh}
            className="text-xs"
          >
            <RefreshCw className="w-3 h-3 mr-1" />
            Force Cache Refresh
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={handleForceTokenCheck}
            className="text-xs"
          >
            Force Token Check
          </Button>
        </div>

        {/* Loading State */}
        {(isLoading) && (
          <div className="text-yellow-400 text-xs flex items-center gap-2">
            <div className="w-3 h-3 border border-yellow-400 border-t-transparent rounded-full animate-spin"></div>
            Loading...
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default YouTubeDebugPanel;
