import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, CheckCircle, ExternalLink, Copy } from 'lucide-react';
import { YOUTUBE_CONFIG } from '@/config/youtube';
import { toast } from '@/hooks/use-toast';

const YouTubeOAuthDiagnostic = () => {
  const [testResults, setTestResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const currentOrigin = window.location.origin;
  const redirectUri = `${currentOrigin}/auth/youtube/callback`;

  const runDiagnostics = async () => {
    setIsLoading(true);
    const results: any = {
      timestamp: new Date().toISOString(),
      config: {},
      tests: {}
    };

    // Test 1: Configuration Check
    results.config = {
      clientId: YOUTUBE_CONFIG.CLIENT_ID,
      hasClientSecret: !!YOUTUBE_CONFIG.CLIENT_SECRET,
      clientSecretFormat: YOUTUBE_CONFIG.CLIENT_SECRET?.startsWith('GOCSPX-'),
      redirectUri: redirectUri,
      scopes: YOUTUBE_CONFIG.SCOPES,
      endpoints: YOUTUBE_CONFIG.ENDPOINTS
    };

    // Test 2: OAuth URL Generation
    try {
      const params = new URLSearchParams({
        client_id: YOUTUBE_CONFIG.CLIENT_ID,
        redirect_uri: redirectUri,
        scope: YOUTUBE_CONFIG.SCOPES.join(' '),
        response_type: 'code',
        access_type: 'offline',
        prompt: 'consent',
        include_granted_scopes: 'true'
      });
      
      const oauthUrl = `${YOUTUBE_CONFIG.ENDPOINTS.OAUTH_AUTHORIZE}?${params.toString()}`;
      results.tests.oauthUrlGeneration = {
        success: true,
        url: oauthUrl,
        urlLength: oauthUrl.length
      };
    } catch (error) {
      results.tests.oauthUrlGeneration = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    // Test 3: Client ID Validation (basic format check)
    const clientIdPattern = /^\d+-[a-z0-9]+\.apps\.googleusercontent\.com$/;
    results.tests.clientIdFormat = {
      success: clientIdPattern.test(YOUTUBE_CONFIG.CLIENT_ID),
      pattern: clientIdPattern.toString(),
      actual: YOUTUBE_CONFIG.CLIENT_ID
    };

    // Test 4: Redirect URI Accessibility
    try {
      const response = await fetch(redirectUri, { method: 'HEAD' });
      results.tests.redirectUriAccessible = {
        success: response.status !== 404,
        status: response.status,
        statusText: response.statusText
      };
    } catch (error) {
      results.tests.redirectUriAccessible = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    // Test 5: Google OAuth Endpoint Accessibility
    try {
      const response = await fetch(YOUTUBE_CONFIG.ENDPOINTS.OAUTH_AUTHORIZE, { method: 'HEAD' });
      results.tests.googleOAuthEndpoint = {
        success: response.status < 400,
        status: response.status,
        statusText: response.statusText
      };
    } catch (error) {
      results.tests.googleOAuthEndpoint = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    // Test 6: YouTube API Accessibility (basic check)
    try {
      const apiUrl = `https://www.googleapis.com/youtube/v3/channels?part=snippet&mine=true&key=test`;
      const response = await fetch(apiUrl, { method: 'HEAD' });
      results.tests.youtubeApiEndpoint = {
        success: response.status === 400 || response.status === 401, // These are expected without proper auth
        status: response.status,
        statusText: response.statusText,
        note: 'Status 400/401 is expected without proper authentication'
      };
    } catch (error) {
      results.tests.youtubeApiEndpoint = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    setTestResults(results);
    setIsLoading(false);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({ title: "Copied to clipboard" });
  };

  const TestResult = ({ test, name }: { test: any, name: string }) => (
    <div className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
      <div className="flex items-center space-x-2">
        {test.success ? (
          <CheckCircle className="w-4 h-4 text-green-400" />
        ) : (
          <AlertCircle className="w-4 h-4 text-red-400" />
        )}
        <span className="text-sm font-medium text-white">{name}</span>
      </div>
      <div className="text-xs text-gray-400">
        {test.success ? 'PASS' : 'FAIL'}
      </div>
    </div>
  );

  return (
    <Card className="dashboard-card">
      <CardHeader>
        <CardTitle className="text-white flex items-center space-x-2">
          <AlertCircle className="w-5 h-5" />
          <span>YouTube OAuth Diagnostics</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex space-x-2">
          <Button 
            onClick={runDiagnostics}
            disabled={isLoading}
            className="bg-teal hover:bg-teal/90"
          >
            {isLoading ? 'Running Tests...' : 'Run Diagnostics'}
          </Button>
        </div>

        {testResults && (
          <div className="space-y-4">
            <div>
              <h3 className="text-white font-medium mb-2">Configuration</h3>
              <div className="space-y-2 text-xs">
                <div className="bg-gray-800/50 p-3 rounded-lg">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <span className="text-gray-400">Client ID:</span>
                      <div className="text-white font-mono text-xs break-all">
                        {testResults.config.clientId}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-400">Redirect URI:</span>
                      <div className="text-white font-mono text-xs break-all flex items-center">
                        {testResults.config.redirectUri}
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(testResults.config.redirectUri)}
                          className="ml-1 h-4 w-4 p-0"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-white font-medium mb-2">Test Results</h3>
              <div className="space-y-2">
                <TestResult test={testResults.tests.clientIdFormat} name="Client ID Format" />
                <TestResult test={testResults.tests.oauthUrlGeneration} name="OAuth URL Generation" />
                <TestResult test={testResults.tests.redirectUriAccessible} name="Redirect URI Accessible" />
                <TestResult test={testResults.tests.googleOAuthEndpoint} name="Google OAuth Endpoint" />
                <TestResult test={testResults.tests.youtubeApiEndpoint} name="YouTube API Endpoint" />
              </div>
            </div>

            <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-3">
              <h4 className="text-blue-400 font-medium mb-2">🔍 Find Your Google Account</h4>
              <div className="text-xs text-gray-300 space-y-2">
                <p><strong>Your Client ID:</strong></p>
                <div className="bg-gray-800 p-2 rounded font-mono text-xs flex items-center justify-between">
                  <span>{YOUTUBE_CONFIG.CLIENT_ID}</span>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => copyToClipboard(YOUTUBE_CONFIG.CLIENT_ID)}
                    className="h-4 w-4 p-0"
                  >
                    <Copy className="w-3 h-3" />
                  </Button>
                </div>
                <p><strong>Steps to find the right Google account:</strong></p>
                <ol className="list-decimal list-inside space-y-1 ml-2">
                  <li>Go to <a href="https://console.cloud.google.com" target="_blank" rel="noopener noreferrer" className="text-teal underline">Google Cloud Console</a></li>
                  <li>Switch between your Google accounts (click profile picture)</li>
                  <li>For each account: APIs & Services → Credentials</li>
                  <li>Look for the Client ID above</li>
                </ol>
              </div>
            </div>

            <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-3">
              <h4 className="text-yellow-400 font-medium mb-2">Google Cloud Console Setup Required</h4>
              <div className="text-xs text-gray-300 space-y-1">
                <p>Once you find the right account:</p>
                <p>1. Enable YouTube Data API v3</p>
                <p>2. Edit your OAuth 2.0 Web Application credentials</p>
                <p>3. Add this redirect URI to authorized redirect URIs:</p>
                <div className="bg-gray-800 p-2 rounded font-mono text-xs flex items-center justify-between">
                  <span>{redirectUri}</span>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => copyToClipboard(redirectUri)}
                    className="h-4 w-4 p-0"
                  >
                    <Copy className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </div>

            {testResults.tests.oauthUrlGeneration?.success && (
              <div>
                <h4 className="text-white font-medium mb-2">Test OAuth Flow</h4>
                <Button
                  onClick={() => window.open(testResults.tests.oauthUrlGeneration.url, '_blank')}
                  className="bg-red-600 hover:bg-red-700 text-white flex items-center space-x-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  <span>Test OAuth in New Tab</span>
                </Button>
                <p className="text-xs text-gray-400 mt-1">
                  This will open the OAuth flow in a new tab. Check for any errors.
                </p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default YouTubeOAuthDiagnostic;
