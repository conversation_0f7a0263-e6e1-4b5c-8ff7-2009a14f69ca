import React from 'react';
import { COLOR_PALETTE } from '@/constants/pillarColors';

const CreatorStudioBackground = () => {
  // Using the brand colors from your color palette
  const teal = COLOR_PALETTE[0]; // #37BEB0
  
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Enhanced teal gradient background */}
      <div 
        className="absolute inset-0 w-full h-full"
        style={{
          background: `
            linear-gradient(135deg, 
              rgba(55, 190, 176, 0.5) 0%, 
              rgba(12, 192, 223, 0.4) 30%, 
              rgba(5, 150, 140, 0.35) 60%, 
              rgba(0, 120, 130, 0.3) 100%)
          `,
          opacity: 0.9
        }}
      />
      
      {/* Enhanced overlay with teal-focused mesh gradient effect */}
      <div
        className="absolute inset-0 w-full h-full opacity-50"
        style={{
          background: `
            radial-gradient(circle at 20% 20%, rgba(12, 192, 223, 0.6) 0%, transparent 40%),
            radial-gradient(circle at 80% 30%, rgba(55, 190, 176, 0.5) 0%, transparent 40%),
            radial-gradient(circle at 40% 80%, rgba(0, 150, 160, 0.5) 0%, transparent 40%),
            radial-gradient(circle at 70% 70%, rgba(0, 130, 140, 0.5) 0%, transparent 40%)
          `
        }}
      />
      
      {/* Add a subtle noise texture overlay */}
      <div 
        className="absolute inset-0 w-full h-full opacity-10"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
          backgroundSize: '200px 200px'
        }}
      />
    </div>
  );
};

export default CreatorStudioBackground;


