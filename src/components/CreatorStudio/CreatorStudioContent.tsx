import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import OptimizeTitleDialog from '@/components/Ideas/OptimizeTitleDialog';
import DescriptionGeneratorDialog from '@/components/Ideas/DescriptionGeneratorDialog';
import { Sparkles, Search, Hash, Lightbulb } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import KeywordResearcherDialog from '@/components/Ideas/KeywordResearcherDialog';
import HashtagGeneratorDialog from '../Ideas/HashtagGeneratorDialog';
import AIGeneratedIdeasTab from '@/components/Ideas/AIGeneratedIdeasTab';

interface CreatorStudioContentProps {
  [key: string]: any;
}

const CreatorStudioContent: React.FC<CreatorStudioContentProps> = (props) => {
  // State for core dialogs
  const [showTitleOptimizer, setShowTitleOptimizer] = useState(false);
  const [showKeywordResearcher, setShowKeywordResearcher] = useState(false);
  const [showDescriptionGenerator, setShowDescriptionGenerator] = useState(false);
  const [showHashtagGenerator, setShowHashtagGenerator] = useState(false);
  const [showAIGenerator, setShowAIGenerator] = useState(false);
  
  // Current content state
  const [currentTitle, setCurrentTitle] = useState('');
  const [currentTopic, setCurrentTopic] = useState('');
  
  // State for pillars and user
  const [pillars, setPillars] = useState([]);
  const [user, setUser] = useState(null);
  
  // Fetch user and pillars when component mounts
  useEffect(() => {
    const fetchUserAndPillars = async () => {
      try {
        // Get current user
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          setUser(session.user);
          
          // Fetch pillars for this user
          const { data } = await supabase
            .from('content_pillars')
            .select('*')
            .eq('user_id', session.user.id);
            
          if (data) setPillars(data);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        // Continue with offline functionality
      }
    };
    
    fetchUserAndPillars();
    
    // Set up auth state listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          setUser(session.user);
        } else {
          setUser(null);
          setPillars([]);
        }
      }
    );
    
    // Clean up listener
    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, []);
  
  // Simple function to add generated idea
  const addGeneratedIdea = async (ideaData) => {
    if (!user) {
      // Store offline if no user
      try {
        const offlineIdeas = JSON.parse(localStorage.getItem('offlineIdeas') || '[]');
        offlineIdeas.push({
          ...ideaData,
          createdAt: new Date().toISOString()
        });
        localStorage.setItem('offlineIdeas', JSON.stringify(offlineIdeas));
        return true;
      } catch (error) {
        console.error('Error storing offline idea:', error);
        return false;
      }
    }
    
    try {
      await supabase.from('videos').insert({
        user_id: user.id,
        title: ideaData.title,
        description: ideaData.description || '',
        pillar_id: ideaData.pillarId,
        status: 'idea',
        script: ideaData.script || '',
        hook: ideaData.hook || '',
        tags: ideaData.tags || []
      });
      return true;
    } catch (error) {
      console.error('Error adding idea:', error);
      return false;
    }
  };

  // Tool card component for consistent layout
  const ToolCard = ({ 
    title, 
    description, 
    icon: Icon, 
    buttonText, 
    onClick,
    color = "bg-teal-600 hover:bg-teal-700"
  }: { 
    title: string; 
    description: string; 
    icon: any; 
    buttonText: string; 
    onClick: () => void;
    color?: string;
  }) => (
    <Card className="hover:bg-gray-800/50 transition-colors cursor-pointer flex flex-col h-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Icon className="w-5 h-5 mr-2 text-teal-500" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col flex-1">
        <p className="text-sm text-gray-400 mb-4 flex-grow">
          {description}
        </p>
        <Button 
          onClick={onClick}
          className={`w-full ${color} mt-auto`}
        >
          {buttonText}
        </Button>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <div className="glass-effect p-6 mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">AI Content Tools</h2>
        <p className="text-gray-400">Use AI to enhance your content creation workflow</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Title Optimizer */}
        <ToolCard
          title="Title Optimizer"
          description="Get AI-powered suggestions to improve your video titles for better CTR."
          icon={Sparkles}
          buttonText="Optimize Title"
          onClick={() => setShowTitleOptimizer(true)}
        />
        
        {/* NEW TOOL: Keyword Researcher */}
        <ToolCard
          title="Keyword Researcher"
          description="Find trending keywords and topics in your niche to optimize content discoverability."
          icon={Search}
          buttonText="Research Keywords"
          color="bg-blue-600 hover:bg-blue-700"
          onClick={() => setShowKeywordResearcher(true)}
        />
        
        {/* NEW TOOL: Hashtag Generator */}
        <ToolCard
          title="Hashtag Generator"
          description="Generate optimized hashtags for your videos to increase discoverability across platforms."
          icon={Hash}
          buttonText="Generate Hashtags"
          color="bg-purple-600 hover:bg-purple-700"
          onClick={() => setShowHashtagGenerator(true)}
        />

        {/* NEW TOOL: Script Generator */}
        <ToolCard
          title="Script Generator"
          description="Complete AI-powered video creation workflow from topic to script, with SEO optimization."
          icon={Lightbulb}
          buttonText="Generate Script"
          color="bg-orange-600 hover:bg-orange-700"
          onClick={() => setShowAIGenerator(true)}
        />
      </div>
      
      {/* Core Dialog Components */}
      <OptimizeTitleDialog
        isOpen={showTitleOptimizer}
        onClose={() => setShowTitleOptimizer(false)}
        currentTitle={currentTitle}
        onSelectTitle={(newTitle) => {
          setCurrentTitle(newTitle);
          setShowTitleOptimizer(false);
        }}
      />
      
      <KeywordResearcherDialog
        isOpen={showKeywordResearcher}
        onClose={() => setShowKeywordResearcher(false)}
      />
      
      <HashtagGeneratorDialog
        isOpen={showHashtagGenerator}
        onClose={() => setShowHashtagGenerator(false)}
      />
      
      <DescriptionGeneratorDialog
        isOpen={showDescriptionGenerator}
        onClose={() => setShowDescriptionGenerator(false)}
      />

      {/* Script Generator Dialog */}
      <Dialog open={showAIGenerator} onOpenChange={(open) => !open && setShowAIGenerator(false)}>
        <DialogContent className="max-w-6xl bg-gray-900 border-gray-700 text-white max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-white">Script Generator</DialogTitle>
          </DialogHeader>
          <AIGeneratedIdeasTab
            pillars={pillars}
            addGeneratedIdea={addGeneratedIdea}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CreatorStudioContent;
