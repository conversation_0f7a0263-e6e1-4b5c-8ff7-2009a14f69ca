
import React, { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useUserProfile } from '@/hooks/settings/useUserProfile';
import { useBillingManagement } from '@/hooks/settings/useBillingManagement';
import ProfileAvatarUpload from './Profile/ProfileAvatarUpload';
import ProfileBasicInfo from './Profile/ProfileBasicInfo';
import ProfileAccountInfo from './Profile/ProfileAccountInfo';

const ProfileSettings = () => {
  const { user: authUser } = useAuth();
  const { profile, isLoading, updateProfile } = useUserProfile();
  const { getTierInfo } = useBillingManagement(profile);
  const [isSaving, setIsSaving] = useState(false);

  const handleNameUpdate = async (newName: string) => {
    setIsSaving(true);
    await updateProfile({ name: newName });
    setIsSaving(false);
  };

  const handleAvatarUpdate = async (avatarUrl: string) => {
    await updateProfile({ avatar_url: avatarUrl });
  };

  if (isLoading || !profile) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-6 h-6 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  const tierInfo = getTierInfo(profile.subscription_tier);

  return (
    <div className="space-y-6">
      <ProfileAvatarUpload 
        profile={profile}
        onAvatarUpdate={handleAvatarUpdate}
        tierInfo={tierInfo}
      />

      <ProfileBasicInfo 
        profile={profile}
        authUser={authUser}
        isSaving={isSaving}
        onNameUpdate={handleNameUpdate}
      />

      <ProfileAccountInfo 
        profile={profile}
        authUser={authUser}
      />
    </div>
  );
};

export default ProfileSettings;
