
import React from 'react';
import { Info, Calendar, Code, Globe } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { getBuildInfo, getFullVersionString } from '@/utils/version';
import { APP_NAME, APP_DESCRIPTION } from '@/constants/version';

const AboutSettings = () => {
  const buildInfo = getBuildInfo();

  const formatBuildDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* App Information */}
      <Card className="bg-gray-800/50 border-gray-600">
        <CardContent className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Info className="w-5 h-5 text-teal" />
            <h3 className="text-white font-semibold">Application Information</h3>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <img 
                src="/MCH-Logo-Transparent2.png" 
                alt="MCH Logo - MyContentHub" 
                className="w-12 h-12 object-contain rounded transition-opacity hover:opacity-80"
                loading="lazy"
              />
              <div>
                <h4 className="text-white font-medium">{APP_NAME}</h4>
                <p className="text-gray-400 text-sm">{APP_DESCRIPTION}</p>
              </div>
            </div>

            <Separator className="bg-gray-600" />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Code className="w-4 h-4 text-teal" />
                  <span className="text-white font-medium">Version</span>
                </div>
                <p className="text-gray-300 text-sm font-mono">
                  {buildInfo.version}
                  {buildInfo.isPrerelease && (
                    <span className="ml-2 px-2 py-1 bg-orange-600 text-white text-xs rounded">
                      Pre-release
                    </span>
                  )}
                </p>
              </div>

              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Calendar className="w-4 h-4 text-teal" />
                  <span className="text-white font-medium">Build Date</span>
                </div>
                <p className="text-gray-300 text-sm">
                  {formatBuildDate(buildInfo.buildDate)}
                </p>
              </div>

              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Globe className="w-4 h-4 text-teal" />
                  <span className="text-white font-medium">Environment</span>
                </div>
                <p className="text-gray-300 text-sm capitalize">
                  {buildInfo.environment}
                </p>
              </div>

              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Info className="w-4 h-4 text-teal" />
                  <span className="text-white font-medium">Release Type</span>
                </div>
                <p className="text-gray-300 text-sm">
                  {buildInfo.isPrerelease ? 'Pre-release' : 'Stable Release'}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Information */}
      <Card className="bg-gray-800/50 border-gray-600">
        <CardContent className="p-6">
          <h3 className="text-white font-semibold mb-4">System Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Browser:</span>
              <span className="text-white ml-2">{navigator.userAgent.split(' ').slice(-1)[0]}</span>
            </div>
            <div>
              <span className="text-gray-400">Platform:</span>
              <span className="text-white ml-2">{navigator.platform}</span>
            </div>
            <div>
              <span className="text-gray-400">Language:</span>
              <span className="text-white ml-2">{navigator.language}</span>
            </div>
            <div>
              <span className="text-gray-400">Timezone:</span>
              <span className="text-white ml-2">{Intl.DateTimeFormat().resolvedOptions().timeZone}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Legal Information */}
      <Card className="bg-gray-800/30 border-gray-600">
        <CardContent className="p-4">
          <h4 className="text-white font-medium mb-2">Legal & Support</h4>
          <div className="text-sm text-gray-300 space-y-1">
            <p>• © 2025 MyContentHub. All rights reserved.</p>
            <p>• For support, visit our help documentation or contact support.</p>
            <p>• Built with modern web technologies for the best user experience.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AboutSettings;
