
import React from 'react';
import { Palette } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { SettingsPreferences } from '@/hooks/settings/useSettingsPreferences';

interface ThemeSettingsProps {
  preferences: SettingsPreferences;
  updatePreference: (key: keyof SettingsPreferences, value: any) => void;
}

const ThemeSettings = ({ preferences, updatePreference }: ThemeSettingsProps) => {
  return (
    <Card className="bg-gray-800/50 border-gray-600">
      <CardContent className="p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Palette className="w-5 h-5 text-teal" />
          <h3 className="text-white font-semibold">Appearance</h3>
        </div>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="theme" className="text-white mb-2 block">Theme</Label>
            <Select value={preferences.theme} onValueChange={(value) => updatePreference('theme', value)}>
              <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                <SelectItem value="dark" className="text-white hover:bg-gray-700">Dark (Current)</SelectItem>
                <SelectItem value="light" className="text-white hover:bg-gray-700" disabled>Light (Coming Soon)</SelectItem>
                <SelectItem value="auto" className="text-white hover:bg-gray-700" disabled>Auto (Coming Soon)</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-gray-400 text-sm mt-1">
              Light theme and auto-switching coming soon!
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ThemeSettings;
