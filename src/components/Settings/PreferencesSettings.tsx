
import React from 'react';
import { useSettingsPreferences } from '@/hooks/settings/useSettingsPreferences';
import TimeSettings from './TimeSettings';
import NotificationSettings from './NotificationSettings';
import ThemeSettings from './ThemeSettings';
import PrivacyInfo from './PrivacyInfo';

const PreferencesSettings = () => {
  const { preferences, isLoading, updatePreference } = useSettingsPreferences();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-6 h-6 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <TimeSettings preferences={preferences} updatePreference={updatePreference} />
      <NotificationSettings preferences={preferences} updatePreference={updatePreference} />
      <ThemeSettings preferences={preferences} updatePreference={updatePreference} />
      <PrivacyInfo />
    </div>
  );
};

export default PreferencesSettings;
