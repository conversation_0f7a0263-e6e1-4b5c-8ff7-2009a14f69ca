
import React from 'react';
import { Clock } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { SettingsPreferences } from '@/hooks/settings/useSettingsPreferences';

interface TimeSettingsProps {
  preferences: SettingsPreferences;
  updatePreference: (key: keyof SettingsPreferences, value: any) => void;
}

const timezones = [
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
  { value: 'Europe/Paris', label: 'Central European Time (CET)' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
  { value: 'Australia/Brisbane', label: 'Australian Eastern Standard Time (Brisbane)' },
  { value: 'Australia/Sydney', label: 'Australian Eastern Time (Sydney/Melbourne)' },
  { value: 'Australia/Perth', label: 'Australian Western Time (Perth)' },
  { value: 'Australia/Adelaide', label: 'Australian Central Time (Adelaide)' }
];

const TimeSettings = ({ preferences, updatePreference }: TimeSettingsProps) => {
  return (
    <Card className="bg-gray-800/50 border-gray-600">
      <CardContent className="p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Clock className="w-5 h-5 text-teal" />
          <h3 className="text-white font-semibold">Time & Schedule</h3>
        </div>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="timezone" className="text-white mb-2 block">Timezone</Label>
            <Select value={preferences.timezone} onValueChange={(value) => updatePreference('timezone', value)}>
              <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                {timezones.map((tz) => (
                  <SelectItem key={tz.value} value={tz.value} className="text-white hover:bg-gray-700">
                    {tz.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-gray-400 text-sm mt-1">
              Used for scheduling videos and displaying analytics
            </p>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-white">Auto-sync YouTube data</Label>
              <p className="text-gray-400 text-sm">Automatically sync your YouTube data every 24 hours</p>
            </div>
            <Switch
              checked={preferences.auto_sync}
              onCheckedChange={(checked) => updatePreference('auto_sync', checked)}
              className="data-[state=checked]:bg-teal"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TimeSettings;
