import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import { removeAllMockData } from '@/utils/cleanupUtils';
import { toast } from '@/components/ui/use-toast';

const AdminSettings = () => {
  const { user } = useAuth();
  const [isCleaning, setIsCleaning] = useState(false);
  
  const handleCleanupMockData = async () => {
    if (!user) return;
    
    setIsCleaning(true);
    try {
      await removeAllMockData(user.id);
      toast.success('All mock data has been removed successfully');
    } catch (error) {
      console.error('Error removing mock data:', error);
      toast.error('Failed to remove mock data');
    } finally {
      setIsCleaning(false);
    }
  };
  
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold">Admin Settings</h2>
      
      <div>
        <Button 
          variant="destructive"
          onClick={handleCleanupMockData}
          disabled={isCleaning}
        >
          {isCleaning ? 'Removing...' : 'Remove All Mock Data'}
        </Button>
      </div>
    </div>
  );
};

export default AdminSettings;
