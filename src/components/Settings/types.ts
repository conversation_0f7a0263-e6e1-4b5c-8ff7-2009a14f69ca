
export interface UserPreferences {
  timezone: string;
  email_notifications: boolean;
  marketing_emails: boolean;
  theme: 'light' | 'dark' | 'auto';
  auto_sync: boolean;
}

export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  subscription_tier: string;
  subscription_status: string;
  billing_period?: string;
  trial_end_date?: string;
  subscription_end_date?: string;
  additional_seats?: number;
  youtube_channel_id?: string;
  youtube_channel_name?: string;
  youtube_thumbnail_url?: string;
  youtube_subscriber_baseline?: number;
  last_youtube_sync?: string;
  preferences?: UserPreferences;
  created_at: string;
  stripe_customer_id?: string;
  current_period_end?: string;
}
