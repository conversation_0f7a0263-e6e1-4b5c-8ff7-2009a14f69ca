
import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { CreditCard, Crown, ExternalLink, RefreshCw } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useUserProfile } from '@/hooks/settings/useUserProfile';
import { useBillingManagement } from '@/hooks/settings/useBillingManagement';
import BillingCurrentPlan from './Billing/BillingCurrentPlan';
import BillingTrialBanner from './Billing/BillingTrialBanner';
import BillingActions from './Billing/BillingActions';
import BillingDebugInfo from './Billing/BillingDebugInfo';
import BillingInfoCard from './Billing/BillingInfoCard';

const BillingSettings = () => {
  const navigate = useNavigate();
  const { profile, isLoading, refetchProfile } = useUserProfile();
  const { 
    isLoading: billingLoading, 
    isRefreshing, 
    getTierInfo, 
    getTrialDaysLeft, 
    handleManageSubscription, 
    refreshSubscription 
  } = useBillingManagement(profile);

  if (isLoading || !profile) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-6 h-6 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  const tierInfo = getTierInfo(profile.subscription_tier);
  const trialDaysLeft = getTrialDaysLeft();
  const isTrialing = profile.subscription_status === 'trialing';
  const hasActiveSubscription = profile.stripe_customer_id && profile.subscription_status === 'active';

  return (
    <div className="space-y-6">
      <BillingDebugInfo profile={profile} />

      <BillingCurrentPlan 
        profile={profile}
        tierInfo={tierInfo}
        trialDaysLeft={trialDaysLeft}
        isTrialing={isTrialing}
        hasActiveSubscription={hasActiveSubscription}
        isRefreshing={isRefreshing}
        onRefresh={() => refreshSubscription(refetchProfile)}
      />

      <BillingTrialBanner 
        isTrialing={isTrialing}
        trialDaysLeft={trialDaysLeft}
        onViewPlans={() => navigate('/pricing')}
      />

      <BillingActions 
        profile={profile}
        isLoading={billingLoading}
        isRefreshing={isRefreshing}
        onManageSubscription={handleManageSubscription}
        onViewPlans={() => navigate('/pricing')}
        onRefresh={() => refreshSubscription(refetchProfile)}
      />

      <BillingInfoCard />
    </div>
  );
};

export default BillingSettings;
