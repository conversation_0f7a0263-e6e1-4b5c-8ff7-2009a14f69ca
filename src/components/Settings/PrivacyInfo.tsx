
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

const PrivacyInfo = () => {
  return (
    <Card className="bg-gray-800/30 border-gray-600">
      <CardContent className="p-4">
        <h4 className="text-white font-medium mb-2">Data & Privacy</h4>
        <div className="text-sm text-gray-300 space-y-1">
          <p>• Your YouTube data is only used to provide analytics and recommendations</p>
          <p>• We never share your personal information with third parties</p>
          <p>• You can disconnect your YouTube account at any time</p>
          <p>• All data is securely stored and encrypted</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default PrivacyInfo;
