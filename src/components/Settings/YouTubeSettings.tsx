
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import YouTubeConnect from '@/components/YouTube/YouTubeConnect';
import YouTubeSyncInfo from './YouTube/YouTubeSyncInfo';
import YouTubeOnboardingAlert from './YouTube/YouTubeOnboardingAlert';
import YouTubeConnectionDiagnostics from './YouTube/YouTubeConnectionDiagnostics';
import { useYouTubeConnection } from '@/hooks/useYouTubeConnection';
import { testYouTubeConfiguration } from '@/utils/testYouTubeConfig';
import { YOUTUBE_CONFIG } from '@/config/youtube';

const YouTubeSettings = () => {
  const [searchParams] = useSearchParams();
  const [showOnboardingHighlight, setShowOnboardingHighlight] = useState(false);
  const [configTestResult, setConfigTestResult] = useState<boolean | null>(null);
  const { youtubeData, isConnected, isLoading, refetch } = useYouTubeConnection();

  useEffect(() => {
    const fromOnboarding = searchParams.get('tab') === 'youtube';
    if (fromOnboarding) {
      setShowOnboardingHighlight(true);
      setTimeout(() => setShowOnboardingHighlight(false), 3000);
    }

    // Test YouTube configuration when component mounts
    const testConfig = async () => {
      try {
        const result = testYouTubeConfiguration();
        setConfigTestResult(result);
      } catch (error) {
        console.error('Configuration test failed:', error);
        setConfigTestResult(false);
      }
    };

    testConfig();
  }, [searchParams]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-6 h-6 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  const hasChannel = !!youtubeData?.youtube_channel_id;

  return (
    <div className="space-y-6">
      {/* Configuration Test Status */}
      {configTestResult !== null && (
        <div className={`p-4 rounded-lg border ${
          configTestResult
            ? 'bg-green-500/10 border-green-500/30 text-green-400'
            : 'bg-red-500/10 border-red-500/30 text-red-400'
        }`}>
          <div className="flex items-center space-x-2">
            <span className="text-lg">
              {configTestResult ? '✅' : '❌'}
            </span>
            <div>
              <p className="font-medium">
                YouTube Configuration {configTestResult ? 'Fixed' : 'Issue Detected'}
              </p>
              <p className="text-sm opacity-80">
                {configTestResult
                  ? `Client secret consistency verified. Token refresh should work correctly.`
                  : 'Configuration test failed. Check browser console for details.'
                }
              </p>
              {configTestResult && (
                <p className="text-xs mt-1 opacity-60">
                  Using client secret: {YOUTUBE_CONFIG.CLIENT_SECRET.substring(0, 10)}...
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      <YouTubeOnboardingAlert
        showOnboardingHighlight={showOnboardingHighlight}
        isConnected={isConnected}
      />

      <YouTubeConnect
        onConnectionUpdate={refetch}
      />

      {/* Only show sync info if there's a channel (regardless of token status) */}
      {hasChannel && <YouTubeSyncInfo />}

      {/* Diagnostic tool for debugging connection issues */}
      <YouTubeConnectionDiagnostics />
    </div>
  );
};

export default YouTubeSettings;
