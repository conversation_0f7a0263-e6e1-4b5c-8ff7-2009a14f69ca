import React from 'react';
import { Youtube, CheckCircle, AlertCircle, Unlink, Zap } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface YouTubeConnectionHeaderProps {
  isConnected: boolean;
  isConnecting: boolean;
  isDisconnecting: boolean;
  showOnboardingHighlight: boolean;
  onConnect: () => void;
  onDisconnect: () => void;
  shouldShowReconnect?: boolean;
  tokenError?: string;
}

const YouTubeConnectionHeader = ({
  isConnected,
  isConnecting,
  isDisconnecting,
  showOnboardingHighlight,
  onConnect,
  onDisconnect,
  shouldShowReconnect = false,
  tokenError
}: YouTubeConnectionHeaderProps) => {
  // Determine the correct status to display
  const getStatus = () => {
    if (shouldShowReconnect && tokenError) {
      return {
        icon: <AlertCircle className="w-5 h-5 text-orange animate-pulse" />,
        badge: <Badge className="bg-orange text-white">Token Expired</Badge>,
        text: 'Connection Issue'
      };
    } else if (isConnected) {
      return {
        icon: <CheckCircle className="w-5 h-5 text-green-400" />,
        badge: <Badge className="bg-green-500 text-white">Connected</Badge>,
        text: 'Connected'
      };
    } else {
      return {
        icon: <AlertCircle className="w-5 h-5 text-orange animate-pulse" />,
        badge: <Badge className="bg-gray-600 text-white">Not Connected</Badge>,
        text: 'Not Connected'
      };
    }
  };

  const status = getStatus();

  return (
    <div className="space-y-4">
      {/* Header Section */}
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <Youtube className="w-6 h-6 text-red-500 flex-shrink-0" />
          <div>
            <h3 className="text-white font-semibold text-lg">YouTube Connection</h3>
            <p className="text-gray-400 text-sm mt-1">
              Connect your channel to unlock powerful analytics and insights
            </p>
          </div>
        </div>
      </div>

      {/* Status and Action Section */}
      <div className="space-y-3">
        {/* Status Badge */}
        <div className="flex items-center space-x-3">
          {status.icon}
          {status.badge}
        </div>
        
        {/* Action Button - Only show reconnect if there's a token error */}
        <div>
          {shouldShowReconnect && tokenError ? (
            <Button
              onClick={onConnect}
              disabled={isConnecting}
              className="bg-teal hover:bg-teal/90 text-white"
              size="sm"
            >
              <Youtube className="w-4 h-4 mr-2" />
              {isConnecting ? 'Connecting...' : 'Reconnect YouTube'}
            </Button>
          ) : isConnected ? (
            <Button
              onClick={onDisconnect}
              disabled={isDisconnecting}
              className="bg-terracotta hover:bg-terracotta/90 text-white"
              size="sm"
            >
              <Unlink className="w-4 h-4 mr-2" />
              {isDisconnecting ? 'Disconnecting...' : 'Disconnect'}
            </Button>
          ) : (
            <Button
              onClick={onConnect}
              disabled={isConnecting}
              className={`bg-teal hover:bg-teal/90 text-white font-medium px-6 ${
                showOnboardingHighlight ? 'animate-pulse' : ''
              }`}
              size="sm"
            >
              <Youtube className="w-4 h-4 mr-2" />
              {isConnecting ? 'Connecting...' : 'Connect'}
              <Zap className="w-4 h-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default YouTubeConnectionHeader;
