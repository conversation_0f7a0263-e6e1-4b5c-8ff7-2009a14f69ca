
import React from 'react';

interface YouTubeChannelDetailsProps {
  userProfile: any;
}

const YouTubeChannelDetails = ({ userProfile }: YouTubeChannelDetailsProps) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-600">
      <div className="flex items-center space-x-4">
        {userProfile.youtube_thumbnail_url && (
          <img 
            src={userProfile.youtube_thumbnail_url} 
            alt="Channel thumbnail"
            className="w-12 h-12 rounded-full"
          />
        )}
        <div className="flex-1">
          <h4 className="text-white font-medium">{userProfile.youtube_channel_name}</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2 text-sm text-gray-300">
            <div>
              <span className="text-gray-400">Subscribers:</span> {userProfile.youtube_subscriber_baseline?.toLocaleString() || 'Syncing...'}
            </div>
            <div>
              <span className="text-gray-400">Last Sync:</span> {userProfile.last_youtube_sync ? formatDate(userProfile.last_youtube_sync) : 'Never'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default YouTubeChannelDetails;
