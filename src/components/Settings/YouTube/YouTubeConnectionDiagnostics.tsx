import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, CheckCircle, XCircle, AlertTriangle, Eye, EyeOff } from 'lucide-react';
import { useYouTubeConnection } from '@/hooks/useYouTubeConnection';
import { useYouTubeTokenValidation } from '@/hooks/useYouTubeTokenValidation';
import { useAuth } from '@/hooks/useAuth';
import { YOUTUBE_CONFIG } from '@/config/youtube';

const YouTubeConnectionDiagnostics = () => {
  const { user } = useAuth();
  const { youtubeData, isConnected, tokenStatus, isValidatingToken, checkTokenStatus } = useYouTubeConnection();
  const { tokenStatus: validationTokenStatus, isValidating: isValidatingWithRefresh, checkTokenStatus: checkWithRefresh } = useYouTubeTokenValidation(user);
  
  const [showTokens, setShowTokens] = useState(false);
  const [lastRefreshAttempt, setLastRefreshAttempt] = useState<string | null>(null);

  const formatToken = (token: string | undefined) => {
    if (!token) return 'Not available';
    if (!showTokens) return `${token.substring(0, 10)}...${token.substring(token.length - 10)}`;
    return token;
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  const testTokenRefresh = async () => {
    setLastRefreshAttempt(new Date().toISOString());
    await checkWithRefresh();
  };

  const getStatusIcon = (isValid: boolean, needsRefresh: boolean) => {
    if (isValid) return <CheckCircle className="w-4 h-4 text-green-400" />;
    if (needsRefresh) return <AlertTriangle className="w-4 h-4 text-orange" />;
    return <XCircle className="w-4 h-4 text-red-400" />;
  };

  const getStatusBadge = (isValid: boolean, needsRefresh: boolean, error?: string) => {
    if (isValid) return <Badge className="bg-green-500 text-white">Valid</Badge>;
    if (needsRefresh) return <Badge className="bg-orange text-white">Needs Refresh</Badge>;
    return <Badge className="bg-red-500 text-white">Invalid</Badge>;
  };

  return (
    <Card className="dashboard-card">
      <CardHeader>
        <CardTitle className="text-white text-lg flex items-center space-x-2">
          <AlertTriangle className="w-5 h-5 text-orange" />
          <span>YouTube Connection Diagnostics</span>
        </CardTitle>
        <p className="text-sm text-gray-400">
          Detailed analysis of YouTube connection status and token validation
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Configuration Status */}
        <div className="space-y-3">
          <h3 className="text-white font-medium">Configuration Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-400">Client ID</p>
              <p className="text-white font-mono">{YOUTUBE_CONFIG.CLIENT_ID}</p>
            </div>
            <div>
              <p className="text-gray-400">Client Secret</p>
              <p className="text-white font-mono">{formatToken(YOUTUBE_CONFIG.CLIENT_SECRET)}</p>
            </div>
          </div>
        </div>

        {/* Connection Data */}
        <div className="space-y-3">
          <h3 className="text-white font-medium">Connection Data</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-400">Channel ID</p>
              <p className="text-white">{youtubeData?.youtube_channel_id || 'Not connected'}</p>
            </div>
            <div>
              <p className="text-gray-400">Channel Name</p>
              <p className="text-white">{youtubeData?.youtube_channel_name || 'Not available'}</p>
            </div>
            <div>
              <p className="text-gray-400">Last Sync</p>
              <p className="text-white">{formatDate(youtubeData?.last_youtube_sync)}</p>
            </div>
            <div>
              <p className="text-gray-400">Subscriber Baseline</p>
              <p className="text-white">{youtubeData?.youtube_subscriber_baseline?.toLocaleString() || 'Not set'}</p>
            </div>
          </div>
        </div>

        {/* Token Information */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-white font-medium">Token Information</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowTokens(!showTokens)}
              className="text-gray-400 hover:text-white"
            >
              {showTokens ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              {showTokens ? 'Hide' : 'Show'} Tokens
            </Button>
          </div>
          <div className="space-y-3">
            <div>
              <p className="text-gray-400 text-sm">Access Token</p>
              <p className="text-white font-mono text-xs break-all">
                {formatToken(youtubeData?.youtube_access_token)}
              </p>
            </div>
            <div>
              <p className="text-gray-400 text-sm">Refresh Token</p>
              <p className="text-white font-mono text-xs break-all">
                {formatToken(youtubeData?.youtube_refresh_token)}
              </p>
            </div>
          </div>
        </div>

        {/* Token Validation Comparison */}
        <div className="space-y-3">
          <h3 className="text-white font-medium">Token Validation Status</h3>
          
          {/* Basic Validation (useYouTubeConnection) */}
          <div className="p-3 bg-gray-800/50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <p className="text-white font-medium">Basic Validation (Settings Page)</p>
              {getStatusIcon(tokenStatus.isValid, tokenStatus.needsRefresh)}
            </div>
            <div className="flex items-center space-x-2 mb-2">
              {getStatusBadge(tokenStatus.isValid, tokenStatus.needsRefresh, tokenStatus.error)}
              <span className="text-sm text-gray-400">
                Connected: {isConnected ? 'Yes' : 'No'}
              </span>
            </div>
            {tokenStatus.error && (
              <p className="text-red-400 text-sm">{tokenStatus.error}</p>
            )}
          </div>

          {/* Advanced Validation with Refresh (useYouTubeTokenValidation) */}
          <div className="p-3 bg-gray-800/50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <p className="text-white font-medium">Advanced Validation (With Refresh)</p>
              {getStatusIcon(validationTokenStatus.isValid, validationTokenStatus.needsRefresh)}
            </div>
            <div className="flex items-center space-x-2 mb-2">
              {getStatusBadge(validationTokenStatus.isValid, validationTokenStatus.needsRefresh, validationTokenStatus.error)}
              {isValidatingWithRefresh && (
                <RefreshCw className="w-4 h-4 animate-spin text-teal" />
              )}
            </div>
            {validationTokenStatus.error && (
              <p className="text-red-400 text-sm">{validationTokenStatus.error}</p>
            )}
            {lastRefreshAttempt && (
              <p className="text-gray-400 text-xs">
                Last refresh attempt: {formatDate(lastRefreshAttempt)}
              </p>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-wrap gap-3">
          <Button
            onClick={checkTokenStatus}
            disabled={isValidatingToken}
            className="bg-teal hover:bg-teal/90 text-white"
          >
            {isValidatingToken && <RefreshCw className="w-4 h-4 mr-2 animate-spin" />}
            Test Basic Validation
          </Button>
          
          <Button
            onClick={testTokenRefresh}
            disabled={isValidatingWithRefresh}
            className="bg-orange hover:bg-orange/90 text-white"
          >
            {isValidatingWithRefresh && <RefreshCw className="w-4 h-4 mr-2 animate-spin" />}
            Test Token Refresh
          </Button>
        </div>

        {/* Status Summary */}
        <div className="p-4 bg-gray-900/50 rounded-lg border border-gray-700">
          <h4 className="text-white font-medium mb-2">Status Summary</h4>
          <div className="space-y-1 text-sm">
            <p className="text-gray-300">
              <span className="text-gray-400">Settings shows connected:</span> {isConnected ? 'Yes' : 'No'}
            </p>
            <p className="text-gray-300">
              <span className="text-gray-400">Basic token valid:</span> {tokenStatus.isValid ? 'Yes' : 'No'}
            </p>
            <p className="text-gray-300">
              <span className="text-gray-400">Advanced token valid:</span> {validationTokenStatus.isValid ? 'Yes' : 'No'}
            </p>
            <p className="text-gray-300">
              <span className="text-gray-400">Has channel data:</span> {youtubeData?.youtube_channel_id ? 'Yes' : 'No'}
            </p>
            <p className="text-gray-300">
              <span className="text-gray-400">Has access token:</span> {youtubeData?.youtube_access_token ? 'Yes' : 'No'}
            </p>
            <p className="text-gray-300">
              <span className="text-gray-400">Has refresh token:</span> {youtubeData?.youtube_refresh_token ? 'Yes' : 'No'}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default YouTubeConnectionDiagnostics;
