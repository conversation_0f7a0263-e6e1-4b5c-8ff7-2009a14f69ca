
import React from 'react';
import { Youtube } from 'lucide-react';

interface YouTubeOnboardingAlertProps {
  showOnboardingHighlight: boolean;
  isConnected: boolean;
}

const YouTubeOnboardingAlert = ({ showOnboardingHighlight, isConnected }: YouTubeOnboardingAlertProps) => {
  if (!showOnboardingHighlight || isConnected) {
    return null;
  }

  return (
    <div className="bg-gradient-to-r from-red-500/20 to-orange/20 border border-red-500/30 rounded-lg p-4 animate-fade-in">
      <div className="flex items-center space-x-2">
        <Youtube className="w-5 h-5 text-red-500 animate-pulse" />
        <p className="text-white font-medium">🚀 Ready to supercharge your YouTube strategy?</p>
      </div>
      <p className="text-gray-300 text-sm mt-1">
        Connect now to unlock powerful insights and automated content analysis!
      </p>
    </div>
  );
};

export default YouTubeOnboardingAlert;
