
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { useYouTubeConnection } from '@/hooks/useYouTubeConnection';
import { CheckCircle, AlertTriangle, Info, Clock, XCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

const YouTubeSyncInfo = () => {
  const { youtubeData, tokenStatus, isConnected, isLoading } = useYouTubeConnection();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <Card className="dashboard-card">
        <CardContent className="flex items-center justify-center p-8">
          <div className="w-6 h-6 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
        </CardContent>
      </Card>
    );
  }

  if (!youtubeData?.youtube_channel_id) {
    return null;
  }

  const hasTokenError = !tokenStatus.isValid && youtubeData?.youtube_channel_id;
  const isValidLastSync = youtubeData.last_youtube_sync && new Date(youtubeData.last_youtube_sync) <= new Date();

  return (
    <Card className="dashboard-card">
      <CardHeader>
        <CardTitle className="text-white text-lg flex items-center space-x-2">
          <Info className="w-5 h-5 text-teal" />
          <span>Sync Status & Analytics</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Connection Status with Helper Text */}
        {hasTokenError ? (
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-orange/20 border border-orange/30 rounded-lg">
              <div className="flex items-center space-x-3">
                <XCircle className="w-5 h-5 text-orange" />
                <div>
                  <p className="text-white font-medium">Connection Expired</p>
                  <p className="text-sm text-gray-400">
                    Your YouTube data won't update until you reconnect
                  </p>
                </div>
              </div>
              <Badge className="bg-orange text-white">
                Expired
              </Badge>
            </div>
          </div>
        ) : isConnected ? (
          <div className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
            <div className="flex items-center space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <div>
                <p className="text-white font-medium">Sync Active</p>
                <p className="text-sm text-gray-400">
                  Data syncing automatically
                </p>
              </div>
            </div>
            <Badge className="bg-green-500 text-white">
              Active
            </Badge>
          </div>
        ) : null}

        {/* Sync Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="p-3 bg-gray-800/30 rounded-lg">
            <div className="flex items-center space-x-2 mb-1">
              <Clock className="w-4 h-4 text-teal" />
              <span className="text-gray-400">Last Sync</span>
            </div>
            <p className="text-white font-medium">
              {isValidLastSync 
                ? formatDate(youtubeData.last_youtube_sync) 
                : 'Never synced'
              }
            </p>
          </div>
          
          <div className="p-3 bg-gray-800/30 rounded-lg">
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-gray-400">Subscribers</span>
            </div>
            <p className="text-white font-medium">
              {youtubeData.youtube_subscriber_baseline?.toLocaleString() || 'Not available'}
            </p>
          </div>

          <div className="p-3 bg-gray-800/30 rounded-lg">
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-gray-400">Sync Type</span>
            </div>
            <p className="text-white font-medium">
              {hasTokenError ? 'Disabled' : (isValidLastSync ? 'Enhanced' : 'Basic only')}
            </p>
          </div>

          <div className="p-3 bg-gray-800/30 rounded-lg">
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-gray-400">Status</span>
            </div>
            <p className="text-white font-medium">
              {hasTokenError ? 'Paused' : (isConnected ? 'Active' : 'Paused')}
            </p>
          </div>
        </div>

        {/* Status-based Information */}
        {isConnected && !hasTokenError && (
          <div className="bg-teal/10 border border-teal/30 rounded-lg p-3">
            <p className="text-teal text-sm font-medium mb-1">✨ Auto-Sync Enabled</p>
            <p className="text-gray-300 text-xs">
              Your YouTube data syncs automatically. New videos and analytics are processed within minutes of publishing.
            </p>
          </div>
        )}

        {hasTokenError && (
          <div className="bg-orange/10 border border-orange/30 rounded-lg p-3">
            <p className="text-orange text-sm font-medium mb-1">⚠️ Sync Disabled</p>
            <p className="text-gray-300 text-xs">
              Your connection has expired. Use the "Reconnect YouTube" button above to restore automatic syncing and import your latest data.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default YouTubeSyncInfo;
