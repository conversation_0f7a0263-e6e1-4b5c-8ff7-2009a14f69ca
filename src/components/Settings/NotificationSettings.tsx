
import React from 'react';
import { Bell } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { SettingsPreferences } from '@/hooks/settings/useSettingsPreferences';

interface NotificationSettingsProps {
  preferences: SettingsPreferences;
  updatePreference: (key: keyof SettingsPreferences, value: any) => void;
}

const NotificationSettings = ({ preferences, updatePreference }: NotificationSettingsProps) => {
  return (
    <Card className="bg-gray-800/50 border-gray-600">
      <CardContent className="p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Bell className="w-5 h-5 text-teal" />
          <h3 className="text-white font-semibold">Email Notifications</h3>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-white">System notifications</Label>
              <p className="text-gray-400 text-sm">Important updates and security alerts</p>
            </div>
            <Switch
              checked={preferences.email_notifications}
              onCheckedChange={(checked) => updatePreference('email_notifications', checked)}
              className="data-[state=checked]:bg-teal"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-white">Marketing emails</Label>
              <p className="text-gray-400 text-sm">Tips, feature updates, and promotional content</p>
            </div>
            <Switch
              checked={preferences.marketing_emails}
              onCheckedChange={(checked) => updatePreference('marketing_emails', checked)}
              className="data-[state=checked]:bg-teal"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default NotificationSettings;
