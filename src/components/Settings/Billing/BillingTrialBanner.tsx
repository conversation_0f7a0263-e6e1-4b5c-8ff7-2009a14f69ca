import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Crown } from 'lucide-react';

interface BillingTrialBannerProps {
  isTrialing: boolean;
  trialDaysLeft: number | null;
  onViewPlans: () => void;
}

const BillingTrialBanner = ({ isTrialing, trialDaysLeft, onViewPlans }: BillingTrialBannerProps) => {
  if (!isTrialing || !trialDaysLeft || trialDaysLeft <= 0) return null;

  return (
    <Card className="border-orange bg-orange/10">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Crown className="w-5 h-5 text-orange" />
            <span className="text-white font-semibold">
              {trialDaysLeft} days left in trial - Upgrade now to continue enjoying all features!
            </span>
          </div>
          <Button
            onClick={onViewPlans}
            className="bg-teal hover:bg-teal/90 text-white"
          >
            View Plans
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default BillingTrialBanner;
