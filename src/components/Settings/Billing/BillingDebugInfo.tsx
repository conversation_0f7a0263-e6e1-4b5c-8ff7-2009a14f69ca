import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { UserProfile } from '@/hooks/settings/useUserProfile';

interface BillingDebugInfoProps {
  profile: UserProfile;
}

const BillingDebugInfo = ({ profile }: BillingDebugInfoProps) => {
  if (!import.meta.env.DEV) return null;

  return (
    <Card className="bg-gray-800/30 border-yellow-500/50">
      <CardContent className="p-4">
        <h4 className="text-yellow-400 font-medium mb-2">Debug Info (Dev Only)</h4>
        <div className="text-sm text-gray-300 space-y-1 font-mono">
          <p>• User ID: {profile.id}</p>
          <p>• Tier: {profile.subscription_tier}</p>
          <p>• Status: {profile.subscription_status}</p>
          <p>• Stripe Customer: {profile.stripe_customer_id || 'None'}</p>
          <p>• Period End: {profile.current_period_end || 'None'}</p>
          <p>• Last Updated: {new Date().toLocaleTimeString()}</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default BillingDebugInfo;
