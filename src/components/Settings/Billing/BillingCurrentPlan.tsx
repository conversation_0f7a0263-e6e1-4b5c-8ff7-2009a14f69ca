
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CreditCard, RefreshCw } from 'lucide-react';
import { UserProfile } from '@/hooks/settings/useUserProfile';

interface BillingCurrentPlanProps {
  profile: UserProfile;
  tierInfo: { color: string; price: string; features: string[] };
  trialDaysLeft: number | null;
  isTrialing: boolean;
  hasActiveSubscription: boolean;
  isRefreshing: boolean;
  onRefresh: () => void;
}

const BillingCurrentPlan = ({ 
  profile, 
  tierInfo, 
  trialDaysLeft, 
  isTrialing, 
  hasActiveSubscription, 
  isRefreshing, 
  onRefresh 
}: BillingCurrentPlanProps) => {
  return (
    <Card className="bg-gray-800/50 border-gray-600">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <CreditCard className="w-6 h-6 text-teal" />
            <h3 className="text-white font-semibold text-lg">Current Plan</h3>
          </div>
          <div className="flex items-center space-x-2">
            <Badge className={`${tierInfo.color} text-white`}>
              {profile.subscription_tier?.toUpperCase() || 'STARTER'}
            </Badge>
            <span className="text-gray-400 text-sm">
              {tierInfo.price}/month
            </span>
            <Button
              onClick={onRefresh}
              disabled={isRefreshing}
              variant="ghost"
              size="sm"
              className="text-gray-400 hover:text-white"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <div className="space-y-3">
              <div>
                <span className="text-gray-400 text-sm">Status:</span>
                <p className="text-white font-medium capitalize">{profile.subscription_status || 'Free'}</p>
              </div>
              {isTrialing && trialDaysLeft !== null && (
                <div>
                  <span className="text-gray-400 text-sm">Trial Days Left:</span>
                  <p className="text-orange font-medium">{trialDaysLeft} days</p>
                </div>
              )}
              {hasActiveSubscription && profile.current_period_end && (
                <div>
                  <span className="text-gray-400 text-sm">Next Billing:</span>
                  <p className="text-white font-medium">
                    {new Date(profile.current_period_end).toLocaleDateString()}
                  </p>
                </div>
              )}
            </div>
          </div>

          <div>
            <span className="text-gray-400 text-sm">Plan Features:</span>
            <ul className="mt-2 space-y-1">
              {tierInfo.features.map((feature, index) => (
                <li key={index} className="text-white text-sm flex items-center">
                  <span className="text-teal mr-2">•</span>
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BillingCurrentPlan;
