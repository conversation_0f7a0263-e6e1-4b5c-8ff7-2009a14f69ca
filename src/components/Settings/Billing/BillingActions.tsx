import React from 'react';
// import { Button } from '@/components/ui/button';
// import { ExternalLink, RefreshCw } from 'lucide-react';
import { UserProfile } from '@/hooks/settings/useUserProfile';

interface BillingActionsProps {
  profile: UserProfile;
  isLoading: boolean;
  isRefreshing: boolean;
  onManageSubscription: () => void;
  onViewPlans: () => void;
  onRefresh: () => void;
}

const BillingActions = ({ 
  profile, 
  isLoading, 
  isRefreshing, 
  onManageSubscription, 
  onViewPlans, 
  onRefresh 
}: BillingActionsProps) => {
  return null;
};

export default BillingActions;
