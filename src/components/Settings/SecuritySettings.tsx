
import React, { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Shield, Key, Smartphone, AlertTriangle } from 'lucide-react';

const SecuritySettings = () => {
  const { user: authUser } = useAuth();
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  const handlePasswordChange = async () => {
    if (!newPassword || !confirmPassword) {
      toast.error('Please fill in all fields');
      return;
    }

    if (newPassword !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (newPassword.length < 6) {
      toast.error('Password must be at least 6 characters long');
      return;
    }

    setIsChangingPassword(true);
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) throw error;

      toast.success('Password updated successfully');
      setNewPassword('');
      setConfirmPassword('');
    } catch (error: any) {
      console.error('Error updating password:', error);
      toast.error(error.message || 'Failed to update password');
    } finally {
      setIsChangingPassword(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Password Change */}
      <Card className="bg-gray-800/50 border-gray-600">
        <CardContent className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Key className="w-5 h-5 text-teal" />
            <h3 className="text-white font-semibold">Change Password</h3>
          </div>
          
          <div className="space-y-4 max-w-md">
            <div>
              <Label htmlFor="newPassword" className="text-white">New Password</Label>
              <Input
                id="newPassword"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="Enter new password"
                className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="confirmPassword" className="text-white">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm new password"
                className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 mt-1"
              />
            </div>

            <Button
              onClick={handlePasswordChange}
              disabled={isChangingPassword || !newPassword || !confirmPassword}
              className="bg-teal hover:bg-teal/90 text-white"
            >
              {isChangingPassword ? 'Updating...' : 'Update Password'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Two-Factor Authentication */}
      <Card className="bg-gray-800/50 border-gray-600">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Smartphone className="w-5 h-5 text-teal" />
              <h3 className="text-white font-semibold">Two-Factor Authentication</h3>
            </div>
            <Badge className="bg-gray-600 text-white">Coming Soon</Badge>
          </div>
          
          <div className="space-y-3">
            <p className="text-gray-300 text-sm">
              Add an extra layer of security to your account with two-factor authentication.
            </p>
            <Button 
              disabled 
              variant="outline" 
              className="border-gray-600 text-gray-400 cursor-not-allowed"
            >
              Enable 2FA (Coming Soon)
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Session Management */}
      <Card className="bg-gray-800/50 border-gray-600">
        <CardContent className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Shield className="w-5 h-5 text-teal" />
            <h3 className="text-white font-semibold">Active Sessions</h3>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg border border-gray-600">
              <div>
                <p className="text-white font-medium">Current Session</p>
                <p className="text-gray-400 text-sm">
                  {authUser?.email} • Last activity: Now
                </p>
              </div>
              <Badge className="bg-green-500 text-white">Active</Badge>
            </div>
            
            <p className="text-gray-400 text-sm">
              Session management features coming soon. You can always sign out using the sidebar menu.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Account Security */}
      <Card className="bg-gray-800/30 border-gray-600">
        <CardContent className="p-4">
          <div className="flex items-center space-x-2 mb-3">
            <AlertTriangle className="w-5 h-5 text-orange" />
            <h4 className="text-white font-medium">Security Recommendations</h4>
          </div>
          <div className="text-sm text-gray-300 space-y-1">
            <p>• Use a strong, unique password for your MyContentHub account</p>
            <p>• Never share your login credentials with anyone</p>
            <p>• Sign out from shared or public computers</p>
            <p>• Keep your email account secure as it's used for password resets</p>
            <p>• Report any suspicious activity to our support team immediately</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SecuritySettings;
