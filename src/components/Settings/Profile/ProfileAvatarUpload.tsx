
import React, { useState } from 'react';
import { supabase } from '@/lib/supabase';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Upload, User } from 'lucide-react';
import { toast } from 'sonner';
import { UserProfile } from '@/hooks/settings/useUserProfile';

interface ProfileAvatarUploadProps {
  profile: UserProfile;
  onAvatarUpdate: (avatarUrl: string) => void;
  tierInfo: { color: string; price: string };
}

const ProfileAvatarUpload = ({ profile, onAvatarUpdate, tierInfo }: ProfileAvatarUploadProps) => {
  const [isUploading, setIsUploading] = useState(false);

  const handleAvatarUpload = async (file: File) => {
    setIsUploading(true);
    try {
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size must be less than 5MB');
        return;
      }

      // Check file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please upload an image file');
        return;
      }

      const fileExt = file.name.split('.').pop();
      const fileName = `${profile.id}-${Date.now()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      // Upload the file directly to the avatars bucket
      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) {
        console.error('Upload error:', uploadError);
        toast.error('Failed to upload avatar. Please try again.');
        return;
      }

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      // Update the profile with the new avatar URL
      await onAvatarUpdate(publicUrl);
      toast.success('Avatar updated successfully');
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast.error('Failed to upload avatar');
    } finally {
      setIsUploading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="flex items-center space-x-6">
      <div className="relative">
        <Avatar className="w-20 h-20">
          <AvatarImage src={profile.avatar_url} alt="Profile picture" />
          <AvatarFallback className="text-xl bg-gray-700 text-white">
            <User className="w-8 h-8" />
          </AvatarFallback>
        </Avatar>
        <label className={`absolute -bottom-2 -right-2 bg-teal hover:bg-teal/90 text-white p-2 rounded-full cursor-pointer transition-colors ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}>
          <Upload className="w-4 h-4" />
          <input
            type="file"
            accept="image/*"
            className="hidden"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file && !isUploading) handleAvatarUpload(file);
            }}
            disabled={isUploading}
          />
        </label>
        {isUploading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full">
            <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>
      <div>
        <h3 className="text-white font-semibold text-lg">{profile.name || 'No name set'}</h3>
        <p className="text-gray-400">{profile.email}</p>
        <div className="flex items-center space-x-2 mt-2">
          <Badge className={`${tierInfo.color} text-white`}>
            {profile.subscription_tier === 'analytics_only' ? 'MCH STARTER' : 
             profile.subscription_tier === 'ai_lite' ? 'MCH LITE' : 'MCH PRO'}
          </Badge>
          <span className="text-gray-400 text-sm">
            Member since {formatDate(profile.created_at)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ProfileAvatarUpload;
