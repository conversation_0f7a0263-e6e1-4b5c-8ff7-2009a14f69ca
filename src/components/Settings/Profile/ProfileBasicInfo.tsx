
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { UserProfile } from '@/hooks/settings/useUserProfile';

interface ProfileBasicInfoProps {
  profile: UserProfile;
  authUser: any;
  isSaving: boolean;
  onNameUpdate: (name: string) => void;
}

const ProfileBasicInfo = ({ profile, authUser, isSaving, onNameUpdate }: ProfileBasicInfoProps) => {
  const [name, setName] = useState(profile.name || '');

  const handleSave = () => {
    onNameUpdate(name);
  };

  return (
    <div className="space-y-2">
      <Label htmlFor="displayName" className="text-white">Display Name</Label>
      <div className="flex space-x-2">
        <Input
          id="displayName"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Enter your display name"
          className="bg-gray-800 border-gray-600 text-white placeholder-gray-400"
        />
        <Button
          onClick={handleSave}
          disabled={isSaving || name === (profile.name || '')}
          className="bg-teal hover:bg-teal/90 text-white"
        >
          {isSaving ? 'Saving...' : 'Save'}
        </Button>
      </div>
    </div>
  );
};

export default ProfileBasicInfo;
