import { Badge } from '@/components/ui/badge';
import { UserProfile } from '@/hooks/settings/useUserProfile';

interface ProfileComponentProps {
  profile: UserProfile;
}

const ProfileComponent = ({ profile }: ProfileComponentProps) => {
  return (
    <Badge className={`
      ${profile.subscription_tier === 'analytics_only' ? 'bg-teal-500' :
        profile.subscription_tier === 'ai_lite' ? 'bg-orange' : 'bg-green-500'}
      text-white font-medium`}
    >
      {profile.subscription_tier === 'analytics_only' ? 'MCH STARTER' :
       profile.subscription_tier === 'ai_lite' ? 'MCH LITE' : 'MCH PRO'}
    </Badge>
  );
};

export default ProfileComponent;