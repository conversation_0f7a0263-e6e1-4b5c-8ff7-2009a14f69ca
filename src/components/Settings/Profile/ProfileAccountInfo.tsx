
import React from 'react';
import { Label } from '@/components/ui/label';
import { UserProfile } from '@/hooks/settings/useUserProfile';

interface ProfileAccountInfoProps {
  profile: UserProfile;
  authUser: any;
}

const ProfileAccountInfo = ({ profile, authUser }: ProfileAccountInfoProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-600">
      <div>
        <Label className="text-gray-400">Email Address</Label>
        <p className="text-white mt-1">{authUser?.email}</p>
      </div>
      <div>
        <Label className="text-gray-400">Account Status</Label>
        <p className="text-white mt-1 capitalize">{profile.subscription_status || 'Trialing'}</p>
      </div>
    </div>
  );
};

export default ProfileAccountInfo;
