import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useYouTubeSyncEnhanced } from '@/hooks/useYouTubeSyncEnhanced';
import { useYouTubeTokenValidation } from '@/hooks/useYouTubeTokenValidation';
import { supabase } from '@/lib/supabase';
import { RefreshCw, AlertTriangle, CheckCircle, Clock } from 'lucide-react';

interface SidebarYouTubeSyncProps {
  user: any;
  userProfile: any;
}

const SidebarYouTubeSync = ({ user, userProfile }: SidebarYouTubeSyncProps) => {
  const [fullUserProfile, setFullUserProfile] = useState<any>(null);
  const { performFullSync, isSyncing, syncProgress } = useYouTubeSyncEnhanced();
  const { tokenStatus, isValidating, checkTokenStatus } = useYouTubeTokenValidation(fullUserProfile);

  // Fetch complete user profile with all YouTube data
  useEffect(() => {
    if (user?.id) {
      const fetchFullProfile = async () => {
        try {
          const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', user.id)
            .single();

          if (error) throw error;
          setFullUserProfile(data);
        } catch (error) {
          console.error('Error fetching full user profile:', error);
        }
      };

      fetchFullProfile();
    }
  }, [user?.id]);

  const handleYouTubeSync = async () => {
    if (!user) {
      toast.error('Please log in to sync YouTube data');
      return;
    }

    if (!fullUserProfile?.youtube_channel_id) {
      toast.error('Please connect your YouTube channel first');
      return;
    }

    if (!fullUserProfile?.youtube_access_token) {
      toast.error('YouTube access token missing. Please reconnect your channel.');
      return;
    }

    console.log('=== SYNC BUTTON CLICKED ===');
    console.log('User profile:', {
      channelId: fullUserProfile.youtube_channel_id,
      hasToken: !!fullUserProfile.youtube_access_token,
      tokenStatus: tokenStatus
    });

    // Check token validity before syncing
    if (!tokenStatus.isValid) {
      if (tokenStatus.needsRefresh) {
        toast.error('YouTube token expired. Please try reconnecting your channel.');
      } else {
        toast.error(tokenStatus.error || 'YouTube connection invalid. Please reconnect.');
      }
      return;
    }

    try {
      console.log('Starting YouTube sync from sidebar...');
      const syncStats = await performFullSync(fullUserProfile);
      if (syncStats) {
        toast.success(
          `Data synced! Updated ${syncStats.videosProcessed} videos with real-time metrics`
        );
        
        // Force refresh the page to show updated data
        setTimeout(() => {
          window.location.reload();
        }, 1000);
        
        // Refresh user profile to get latest data
        const { data: updatedProfile, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();
          
        if (!error && updatedProfile) {
          setFullUserProfile(updatedProfile);
        }
      } else {
        toast.error('Sync failed. Check console for details.');
      }
    } catch (error) {
      console.error('Sync failed:', error);
      toast.error('Failed to sync YouTube data: ' + (error as Error).message);
    }
  };

  const handleReconnect = () => {
    window.location.href = '/settings?tab=youtube';
  };

  // Don't show if no YouTube channel connected
  if (!fullUserProfile?.youtube_channel_id) {
    return null;
  }

  const getStatusIcon = () => {
    if (isValidating || isSyncing) return <RefreshCw className="w-4 h-4 animate-spin" />;
    if (tokenStatus.isValid) return <CheckCircle className="w-4 h-4 text-green-400" />;
    return <AlertTriangle className="w-4 h-4 text-orange" />;
  };

  const getStatusText = () => {
    if (isSyncing) return 'Syncing...';
    if (isValidating) return 'Checking connection...';
    if (tokenStatus.isValid) return 'Connected';
    if (tokenStatus.needsRefresh) return 'Refreshing token...';
    return 'Connection issue';
  };

  const getLastSyncInfo = () => {
    const preferences = fullUserProfile?.preferences || {};
    const lastSync = preferences.last_full_sync || fullUserProfile?.last_youtube_sync;
    
    if (!lastSync) return 'Never synced';
    
    const syncDate = new Date(lastSync);
    const now = new Date();
    const hoursSince = Math.round((now.getTime() - syncDate.getTime()) / (1000 * 60 * 60));
    
    if (hoursSince < 1) return 'Just synced';
    if (hoursSince < 24) return `${hoursSince}h ago`;
    return syncDate.toLocaleDateString();
  };

  return (
    <div className="px-4 py-3 border-b border-gray-700 space-y-3">
      {/* Connection Status */}
      <div className="flex items-center space-x-2 text-sm">
        {getStatusIcon()}
        <span className={`${tokenStatus.isValid ? 'text-green-400' : 'text-orange'}`}>
          {getStatusText()}
        </span>
      </div>

      {/* Last Sync Info */}
      <div className="flex items-center space-x-2 text-xs text-gray-400">
        <Clock className="w-3 h-3" />
        <span>Last sync: {getLastSyncInfo()}</span>
      </div>

      {/* Error Message */}
      {tokenStatus.error && !tokenStatus.isValid && (
        <p className="text-xs text-red-400">{tokenStatus.error}</p>
      )}

      {/* Action Buttons */}
      <div className="space-y-2">
        {tokenStatus.isValid ? (
          <Button
            onClick={handleYouTubeSync}
            disabled={isSyncing || isValidating}
            className="w-full bg-teal hover:bg-teal/90 text-white"
            size="sm"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
            {isSyncing ? 'Syncing...' : 'Sync YouTube'}
          </Button>
        ) : (
          <Button
            onClick={handleReconnect}
            className="w-full bg-teal hover:bg-teal/90 text-white"
            size="sm"
          >
            <AlertTriangle className="w-4 h-4 mr-2" />
            Reconnect YouTube
          </Button>
        )}
      </div>

      {/* Sync Progress */}
      {syncProgress && (
        <div className="text-xs text-gray-400 text-center bg-gray-800/50 rounded px-2 py-1">
          {syncProgress}
        </div>
      )}
    </div>
  );
};

export default SidebarYouTubeSync;
