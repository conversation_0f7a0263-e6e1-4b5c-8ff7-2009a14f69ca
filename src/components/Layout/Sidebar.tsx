
import React from 'react';
import { TooltipProvider } from '@/components/ui/tooltip';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import <PERSON>barLogo from './SidebarLogo';
import SidebarNavigation from './SidebarNavigation';
import SidebarYouTubeSync from './SidebarYouTubeSync';
import SidebarYouTubeStatus from './SidebarYouTubeStatus';
import SidebarUserSection from './SidebarUserSection';
import SidebarBackground from './SidebarBackground';

const Sidebar = () => {
  const { user } = useAuth();
  const [userProfile, setUserProfile] = React.useState<any>(null);

  React.useEffect(() => {
    if (user) {
      const fetchProfile = async () => {
        try {
          const { data, error } = await supabase
            .from('users')
            .select('youtube_channel_id, youtube_channel_name')
            .eq('id', user.id)
            .single();
          
          if (error) throw error;
          setUserProfile(data);
        } catch (error) {
          console.error('Error fetching user profile:', error);
          setUserProfile(null);
        }
      };
      fetchProfile();
    }
  }, [user]);

  return (
    <TooltipProvider>
      <div className="relative flex h-screen w-64 flex-col bg-gray-900 border-r border-gray-700 overflow-hidden">
        {/* Sidebar Background */}
        <SidebarBackground />

        {/* Content with higher z-index */}
        <div className="relative z-10 flex h-full flex-col">
          {/* Logo */}
          <SidebarLogo />

          {/* YouTube Connection Status */}
          <SidebarYouTubeStatus userProfile={userProfile} />

          {/* YouTube Sync Button (only shown if connected) */}
          <SidebarYouTubeSync user={user} userProfile={userProfile} />

          {/* Navigation */}
          <SidebarNavigation />

          {/* User Profile & Settings */}
          <SidebarUserSection user={user} />
        </div>
      </div>
    </TooltipProvider>
  );
};

export default Sidebar;
