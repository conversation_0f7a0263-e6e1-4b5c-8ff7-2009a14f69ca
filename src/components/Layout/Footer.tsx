
import React from 'react';
import { Link } from 'react-router-dom';
import { Twitter, Youtube, Mail } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-800 border-t border-gray-700 mt-auto">
      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-8 flex-shrink-0">
                <img
                  src="/MCH_White_Vertical1.svg"
                  alt="MCH Logo - MyContentHub"
                  className="w-full h-full object-contain transition-opacity hover:opacity-80"
                  loading="lazy"
                />
              </div>
              <div>
                <h3 className="text-lg font-bold text-white">MyContentHub</h3>
                <p className="text-xs text-gray-400">YouTube Strategy Platform</p>
              </div>
            </div>
            <p className="text-gray-400 text-sm max-w-md">
              Transform your YouTube content strategy with data-driven insights. 
              Track content pillars, analyze performance, and grow your channel strategically.
            </p>
          </div>

          {/* Legal Links */}
          <div>
            <h4 className="text-white font-semibold mb-4">Legal</h4>
            <ul className="space-y-2">
              <li>
                <Link to="/privacy" className="text-gray-400 hover:text-teal transition-colors text-sm">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link to="/terms" className="text-gray-400 hover:text-teal transition-colors text-sm">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link to="/support" className="text-gray-400 hover:text-teal transition-colors text-sm">
                  Support
                </Link>
              </li>
            </ul>
          </div>

          {/* Social & Contact */}
          <div>
            <h4 className="text-white font-semibold mb-4">Connect</h4>
            <div className="flex space-x-4 mb-4">
              <a
                href="https://twitter.com/mycontenthub"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-teal transition-colors"
              >
                <Twitter className="w-5 h-5" />
              </a>
              <a
                href="https://youtube.com/@mycontenthub"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-teal transition-colors"
              >
                <Youtube className="w-5 h-5" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-gray-400 hover:text-teal transition-colors"
              >
                <Mail className="w-5 h-5" />
              </a>
            </div>
            <p className="text-gray-400 text-xs">
              <EMAIL>
            </p>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-8 pt-6 border-t border-gray-700">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 MyContentHub. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 sm:mt-0">
              <Link to="/privacy" className="text-gray-400 hover:text-teal transition-colors text-xs">
                Privacy
              </Link>
              <Link to="/terms" className="text-gray-400 hover:text-teal transition-colors text-xs">
                Terms
              </Link>
              <Link to="/support" className="text-gray-400 hover:text-teal transition-colors text-xs">
                Support
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
