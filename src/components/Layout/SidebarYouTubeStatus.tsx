import React, { useState, useEffect } from 'react';
import { useYouTubeConnection } from '@/hooks/useYouTubeConnection';
import { useYouTubeSyncEnhanced } from '@/hooks/useYouTubeSyncEnhanced';
import { useYouTubeTokenValidation } from '@/hooks/useYouTubeTokenValidation';
import { useAuth } from '@/hooks/useAuth';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Youtube, CheckCircle, Unlink, RefreshCw, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/lib/supabase';

const SidebarYouTubeStatus = () => {
  const { user } = useAuth();
  const { youtubeData, isConnected, tokenStatus, refreshConnection } = useYouTubeConnection();
  const { performFullSync, isSyncing, syncProgress } = useYouTubeSyncEnhanced();
  const [fullUserProfile, setFullUserProfile] = useState<any>(null);
  const { tokenStatus: validationStatus, isValidating, checkTokenStatus } = useYouTubeTokenValidation(fullUserProfile);

  const hasChannel = !!youtubeData?.youtube_channel_id;
  const hasValidToken = tokenStatus.isValid;
  const hasRecentSync = youtubeData?.last_youtube_sync &&
    new Date(youtubeData.last_youtube_sync) > new Date(Date.now() - 24 * 60 * 60 * 1000); // Within 24 hours
  const navigate = useNavigate();

  // Fetch complete user profile with all YouTube data
  useEffect(() => {
    if (user?.id) {
      const fetchFullProfile = async () => {
        try {
          const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', user.id)
            .single();

          if (error) throw error;
          setFullUserProfile(data);
        } catch (error) {
          console.error('Error fetching full user profile:', error);
        }
      };

      fetchFullProfile();
    }
  }, [user?.id]);

  // Use the same logic as the main connection hook for consistency
  const showConnected = isConnected; // This already includes proper logic
  // Show warning if channel exists but not connected (which means token issues)
  const showTokenWarning = hasChannel && !isConnected;

  const handleYouTubeSync = async () => {
    if (!user) {
      toast.error('Please log in to sync YouTube data');
      return;
    }

    if (!fullUserProfile?.youtube_channel_id) {
      toast.error('Please connect your YouTube channel first');
      return;
    }

    if (!fullUserProfile?.youtube_access_token) {
      toast.error('YouTube access token missing. Please reconnect your channel.');
      return;
    }

    // Check token validity before syncing
    if (!validationStatus.isValid) {
      if (validationStatus.needsRefresh) {
        toast.error('YouTube token expired. Please try reconnecting your channel.');
      } else {
        toast.error(validationStatus.error || 'YouTube connection invalid. Please reconnect.');
      }
      return;
    }

    try {
      console.log('Starting YouTube sync from sidebar...');
      const syncStats = await performFullSync(fullUserProfile);
      if (syncStats) {
        toast.success(
          `Data synced! Updated ${syncStats.videosProcessed} videos with real-time metrics`
        );

        // Force refresh the page to show updated data
        setTimeout(() => {
          window.location.reload();
        }, 1000);

        // Refresh user profile to get latest data
        const { data: updatedProfile, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();

        if (!error && updatedProfile) {
          setFullUserProfile(updatedProfile);
        }
      } else {
        toast.error('Sync failed. Check console for details.');
      }
    } catch (error) {
      console.error('Sync failed:', error);
      toast.error('Failed to sync YouTube data: ' + (error as Error).message);
    }
  };

  const getLastSyncInfo = () => {
    const preferences = fullUserProfile?.preferences || {};
    const lastSync = preferences.last_full_sync || fullUserProfile?.last_youtube_sync;

    if (!lastSync) return 'Never synced';

    const syncDate = new Date(lastSync);
    const now = new Date();
    const hoursSince = Math.round((now.getTime() - syncDate.getTime()) / (1000 * 60 * 60));

    if (hoursSince < 1) return 'Just synced';
    if (hoursSince < 24) return `${hoursSince}h ago`;
    return syncDate.toLocaleDateString();
  };

  const handleDisconnect = async () => {
    if (!youtubeData?.id) {
      toast.error('User not found');
      return;
    }
    
    try {
      const { error } = await supabase
        .from('users')
        .update({
          youtube_channel_id: null,
          youtube_access_token: null,
          youtube_refresh_token: null,
          youtube_channel_name: null,
          youtube_subscriber_baseline: null,
          last_youtube_sync: null,
          youtube_thumbnail_url: null
        })
        .eq('id', youtubeData.id);

      if (error) throw error;

      toast.success('YouTube channel disconnected successfully');
      
      // Force a refresh of the connection data
      refreshConnection();
      
      // Add a small delay and then navigate to refresh the UI
      setTimeout(() => {
        navigate('/');
        // Force a page reload after navigation
        setTimeout(() => window.location.reload(), 100);
      }, 500);
    } catch (error) {
      console.error('Error disconnecting YouTube:', error);
      toast.error('Failed to disconnect YouTube channel');
    }
  };

  return (
    <div className="px-4 py-3 border-b border-gray-700">
      {/* YouTube Integration Header */}
      <div className="flex items-center text-white mb-2">
        <Youtube className="w-5 h-5 mr-2 text-red-500" />
        YouTube Integration
      </div>

      {/* Channel Name with Connected Badge */}
      {youtubeData?.youtube_channel_name && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-400 truncate">
            {youtubeData.youtube_channel_name}
          </p>
          {showConnected && (
            <Badge className="bg-green-500 text-white ml-2 flex-shrink-0">
              <CheckCircle className="w-3 h-3 mr-1" />
              Connected
            </Badge>
          )}
        </div>
      )}

      {/* Last Sync Info - Show when connected */}
      {showConnected && fullUserProfile && (
        <div className="flex items-center space-x-2 text-xs text-gray-400 mt-2">
          <Clock className="w-3 h-3" />
          <span>Last sync: {getLastSyncInfo()}</span>
        </div>
      )}

      {/* Sync Button - Show when connected and valid token */}
      {showConnected && validationStatus.isValid && (
        <div className="mt-3">
          <Button
            onClick={handleYouTubeSync}
            disabled={isSyncing || isValidating}
            className="w-full bg-teal hover:bg-teal/90 text-white"
            size="sm"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
            {isSyncing ? 'Syncing...' : 'Sync YouTube'}
          </Button>

          {/* Sync Progress */}
          {syncProgress && (
            <div className="text-xs text-gray-400 text-center bg-gray-800/50 rounded px-2 py-1 mt-2">
              {syncProgress}
            </div>
          )}
        </div>
      )}

      {showTokenWarning && (
        <div className="mt-2 p-2 bg-orange/20 border border-orange/40 rounded flex flex-col items-start">
          <div className="flex items-center text-orange text-xs mb-2">
            <AlertTriangle className="w-4 h-4 mr-1" />
            Token expired – reconnect needed
          </div>
          <div className="flex flex-col w-full gap-2">
            <Button
              size="sm"
              className="bg-teal hover:bg-teal/90 text-white w-full"
              onClick={() => navigate('/settings?tab=youtube')}
            >
              Reconnect YouTube
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="border-orange hover:bg-orange/10 text-orange w-full"
              onClick={() => navigate('/settings?tab=youtube')}
            >
              Manage Connection
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-gray-400 hover:text-white hover:bg-gray-800 w-full"
              onClick={handleDisconnect}
            >
              <Unlink className="w-3 h-3 mr-1" />
              Disconnect YouTube
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SidebarYouTubeStatus;
