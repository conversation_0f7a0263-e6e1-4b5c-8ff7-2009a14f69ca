import React from 'react';

interface PageContainerProps {
  children: React.ReactNode;
  className?: string;
  fullWidth?: boolean;
}

/**
 * Standard page container with consistent width constraints
 */
const PageContainer: React.FC<PageContainerProps> = ({ 
  children, 
  className = '',
  fullWidth = false
}) => {
  return (
    <div className={`mx-auto px-4 sm:px-6 lg:px-8 ${fullWidth ? 'w-full' : 'max-w-7xl'} ${className}`}>
      {children}
    </div>
  );
};

export default PageContainer;