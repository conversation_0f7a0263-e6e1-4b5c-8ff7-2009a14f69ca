
import React from 'react';
import { LucideIcon } from 'lucide-react';

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  icon?: LucideIcon;
  children?: React.ReactNode;
}

const PageHeader = ({ title, subtitle, icon: Icon, children }: PageHeaderProps) => {
  return (
    <div className="py-8">
      <div className="relative">
        <div className="relative glass-effect p-8 shadow-2xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-1 h-16 bg-gradient-to-b from-teal via-orange to-terracotta rounded-full" />
                <div className="flex items-center space-x-3">
                  {Icon && (
                    <div className="p-3 bg-orange/20 rounded-lg">
                      <Icon className="w-8 h-8 text-orange" />
                    </div>
                  )}
                  <div>
                    <h1 className="text-4xl font-bold text-white mb-2">
                      {title}
                    </h1>
                    {subtitle && (
                      <p className="text-gray-300 text-lg">
                        {subtitle}
                      </p>
                    )}
                  </div>
                </div>
              </div>
              
              {children && (
                <div>
                  {children}
                </div>
              )}
            </div>
          </div>
        </div>
    </div>
  );
};

export default PageHeader;
