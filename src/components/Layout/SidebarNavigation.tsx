import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import {
  BarChart3,
  Calendar,
  Target,
  Lightbulb,
  TrendingUp,
  Wand2,
} from 'lucide-react';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: BarChart3 },
  { name: 'Content Pillars', href: '/pillars', icon: Target },
  { name: 'Ideas Bank', href: '/ideas', icon: Lightbulb },
  { name: 'Creator Studio', href: '/creator-studio', icon: Wand2 },
  { name: 'Calendar', href: '/calendar', icon: Calendar },
  // Goals removed - now integrated into Dashboard
  // Analytics removed
];

const SidebarNavigation = () => {
  const location = useLocation();

  return (
    <SidebarMenu className="px-2 py-4 space-y-2">
      {navigation.map((item) => {
        const isActive = location.pathname === item.href;
        return (
          <SidebarMenuItem key={item.name}>
            <SidebarMenuButton asChild size="lg">
              <Link
                to={item.href}
                className={cn(
                  'sidebar-nav-link group flex items-center !text-sm font-medium w-full',
                  isActive
                    ? 'active text-white'
                    : 'text-gray-300 hover:text-white'
                )}
              >
                <item.icon
                  className={cn(
                    'mr-3 h-5 w-5 flex-shrink-0',
                    isActive ? 'text-white' : 'text-gray-400 group-hover:text-white'
                  )}
                />
                {item.name}
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        );
      })}
    </SidebarMenu>
  );
};

export default SidebarNavigation;
