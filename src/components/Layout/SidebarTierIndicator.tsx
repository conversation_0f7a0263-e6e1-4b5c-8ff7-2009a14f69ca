import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Crown, Star, Zap } from 'lucide-react';
import { useUserProfile } from '@/hooks/settings/useUserProfile';
import { getTierById } from '@/config/subscriptionTiers';

const SidebarTierIndicator = () => {
  const { profile, isLoading } = useUserProfile();

  if (isLoading || !profile) {
    return null;
  }

  const userTier = profile.subscription_tier || 'analytics_only';
  const tierInfo = getTierById(userTier);

  const getTierIcon = () => {
    switch (userTier) {
      case 'ai_pro':
        return <Crown className="w-4 h-4" />;
      case 'ai_lite':
        return <Star className="w-4 h-4" />;
      case 'analytics_only':
        return <Zap className="w-4 h-4" />;
      default:
        return <Zap className="w-4 h-4" />;
    }
  };

  const getTierColor = () => {
    switch (userTier) {
      case 'ai_pro':
        return 'bg-green-500 text-white';
      case 'ai_lite':
        return 'bg-orange text-white';
      case 'analytics_only':
        return 'bg-teal-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  return (
    <div className="px-4 py-3 border-b border-gray-700">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-gray-400 text-sm">Current Plan</span>
        </div>
        <Badge className={`${getTierColor()} flex items-center space-x-1`}>
          {getTierIcon()}
          <span className="font-medium">{tierInfo.name}</span>
        </Badge>
      </div>
      
      {/* Optional: Show subscription status */}
      {profile.subscription_status && (
        <div className="mt-2 text-xs text-gray-500 capitalize">
          Status: {profile.subscription_status}
        </div>
      )}
    </div>
  );
};

export default SidebarTierIndicator;
