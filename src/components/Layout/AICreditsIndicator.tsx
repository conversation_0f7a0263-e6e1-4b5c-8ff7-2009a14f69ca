
import React from 'react';
import { Sparkles, Crown, Zap, AlertTriangle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useNavigate } from 'react-router-dom';
import { useAICredits } from '@/hooks/useAICredits';

const AICreditsIndicator = () => {
  const navigate = useNavigate();
  const { 
    creditsUsed, 
    maxCredits, 
    userTier, 
    getRemainingCredits 
  } = useAICredits();

  const remainingCredits = getRemainingCredits();
  const isLowCredits = userTier !== 'studio' && (typeof remainingCredits === 'number' && remainingCredits <= 10);
  const isCriticalCredits = userTier !== 'studio' && (typeof remainingCredits === 'number' && remainingCredits <= 3);
  const isOutOfCredits = userTier !== 'studio' && (typeof remainingCredits === 'number' && remainingCredits === 0);

  // Calculate remaining credits and percentage for color coding
  const actualRemaining = typeof remainingCredits === 'number' ? remainingCredits : maxCredits - creditsUsed;
  const percentage = maxCredits > 0 ? (actualRemaining / maxCredits) * 100 : 100;

  const getIcon = () => {
    if (userTier === 'studio') return Zap;
    if (isOutOfCredits || isCriticalCredits) return AlertTriangle;
    return Sparkles;
  };

  const getColor = () => {
    if (userTier === 'studio') return 'text-blue-400';
    if (isOutOfCredits) return 'text-red-400';
    if (isCriticalCredits) return 'text-orange';
    if (isLowCredits) return 'text-yellow';
    return 'text-blue-300';
  };

  const getBadgeColor = () => {
    if (userTier === 'studio') return 'bg-blue-500 text-white';
    if (isOutOfCredits) return 'bg-red-600 text-white';
    if (isCriticalCredits) return 'bg-orange text-white';
    if (isLowCredits) return 'bg-yellow text-black';
    return 'bg-blue-500 text-white';
  };

  // Get text color based on percentage for the fraction display
  const getFractionTextColor = () => {
    if (percentage > 50) return 'text-blue-300';
    if (percentage >= 20) return 'text-yellow-400';
    return 'text-red-400';
  };

  const Icon = getIcon();

  const tooltipContent = userTier === 'studio' 
    ? 'Unlimited AI Credits'
    : `${actualRemaining}/${maxCredits} AI Credits remaining`;

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div
          onClick={() => navigate('/settings?tab=billing')}
          className={`flex items-center justify-between p-2 rounded-md cursor-pointer transition-colors hover:bg-blue-500/30 ${
            (isLowCredits || isOutOfCredits) ? 'animate-pulse' : ''
          }`}
        >
          <div className="flex items-center space-x-2">
            <Icon className={`w-4 h-4 ${getColor()}`} />
            <span className="text-base text-blue-300">AI Credits</span>
          </div>
          <div className="flex items-center space-x-1">
            {userTier === 'studio' ? (
              <Badge className={`${getBadgeColor()} text-xs font-medium`}>
                Unlimited
              </Badge>
            ) : (
              <span className={`text-base font-medium ${getFractionTextColor()}`}>
                {actualRemaining}/{maxCredits}
              </span>
            )}
            {(isCriticalCredits || isOutOfCredits) && (
              <Crown className="w-3 h-3 text-orange animate-bounce" />
            )}
          </div>
        </div>
      </TooltipTrigger>
      <TooltipContent>
        <div className="text-center">
          <p className="font-medium">{tooltipContent}</p>
          {userTier !== 'studio' && (
            <p className="text-xs text-gray-400 mt-1">
              {userTier.charAt(0).toUpperCase() + userTier.slice(1)} Plan
            </p>
          )}
          {(isLowCredits || isOutOfCredits) && (
            <p className="text-xs text-orange mt-1">Click to upgrade</p>
          )}
        </div>
      </TooltipContent>
    </Tooltip>
  );
};

export default AICreditsIndicator;
