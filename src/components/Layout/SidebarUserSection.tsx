import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import AICreditsIndicator from './AICreditsIndicator';
import { getVersionString } from '@/utils/version';
import {
  Settings,
  LogOut,
  CreditCard,
} from 'lucide-react';

interface SidebarUserSectionProps {
  user: any;
}

const SidebarUserSection = ({ user }: SidebarUserSectionProps) => {
  const location = useLocation();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      toast.success('Signed out successfully');
      navigate('/auth');
    } catch (error) {
      console.error('Error signing out:', error);
      toast.error('Failed to sign out');
    }
  };

  return (
    <div className="border-t border-gray-700 p-4 space-y-2">
      {/* AI Credits Indicator */}
      <div className="px-3 py-2">
        <AICreditsIndicator />
      </div>

      <Link
        to="/settings"
        className={cn(
          'group flex items-center px-3 py-2 text-base font-medium rounded-md transition-colors w-full',
          location.pathname === '/settings'
            ? 'bg-blue-600 text-white'
            : 'text-blue-300 hover:bg-blue-500/30 hover:text-white'
        )}
      >
        <Settings className={cn(
          'mr-3 h-5 w-5 flex-shrink-0',
          location.pathname === '/settings' ? 'text-white' : 'text-blue-300 group-hover:text-white'
        )} />
        Settings
      </Link>

      <Link
        to="/billing"
        className={cn(
          'group flex items-center px-3 py-2 text-base font-medium rounded-md transition-colors w-full',
          location.pathname === '/billing'
            ? 'bg-blue-600 text-white'
            : 'text-blue-300 hover:bg-blue-500/30 hover:text-white'
        )}
      >
        <CreditCard className={cn(
          'mr-3 h-5 w-5 flex-shrink-0',
          location.pathname === '/billing' ? 'text-white' : 'text-blue-300 group-hover:text-white'
        )} />
        Billing
      </Link>

      <button
        onClick={handleSignOut}
        className="group flex items-center px-3 py-2 text-base font-medium rounded-md transition-colors w-full text-blue-300 hover:bg-blue-500/30 hover:text-white"
      >
        <LogOut className="mr-3 h-5 w-5 flex-shrink-0 text-blue-300 group-hover:text-white" />
        Sign Out
      </button>

      <div className="px-3 py-2 text-xs truncate">
        <a 
          href="mailto:<EMAIL>" 
          className="text-blue-300 hover:text-blue-200 hover:underline transition-colors"
          title="Send <NAME_EMAIL>"
        >
          <EMAIL>
        </a>
      </div>

      <div className="px-3 py-1 text-xs text-gray-500 text-center border-t border-gray-700 pt-2">
        v{getVersionString()}
      </div>
    </div>
  );
};

export default SidebarUserSection;
