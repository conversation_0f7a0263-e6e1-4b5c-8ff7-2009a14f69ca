import React from 'react';

const PageBackground = () => {
  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none">
      {/* Dark overlay to improve text readability */}
      <div 
        className="absolute inset-0 w-full h-full bg-black opacity-40"
      />
      
      {/* Darker purple gradient with logo color accents */}
      <div 
        className="absolute inset-0 w-full h-full"
        style={{
          background: 'linear-gradient(135deg, #2e1065, #5b21b6)',
          backgroundImage: `
            linear-gradient(135deg, #2e1065, #5b21b6),
            radial-gradient(circle at 15% 15%, rgba(48, 145, 249, 0.4), transparent 40%),
            radial-gradient(circle at 85% 15%, rgba(229, 57, 181, 0.3), transparent 40%),
            radial-gradient(circle at 75% 75%, rgba(17, 193, 76, 0.25), transparent 40%),
            radial-gradient(circle at 25% 75%, rgba(249, 115, 22, 0.25), transparent 40%)
          `,
          opacity: 0.95
        }}
      />
      
      {/* Keep the noise texture overlay */}
      <div 
        className="absolute inset-0 w-full h-full opacity-10"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
          backgroundSize: '200px 200px'
        }}
      />
    </div>
  );
};

export default PageBackground;
