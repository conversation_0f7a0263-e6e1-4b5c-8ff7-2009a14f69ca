import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { Trash2, Search, AlertTriangle } from 'lucide-react';

interface MockDataItem {
  id: string;
  name?: string;
  title?: string;
  type: 'pillar' | 'video' | 'goal';
  user_id?: string;
}

const CompleteMockDataRemoval = () => {
  const { user } = useAuth();
  const [isScanning, setIsScanning] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const [mockData, setMockData] = useState<MockDataItem[]>([]);
  const [scanComplete, setScanComplete] = useState(false);

  const scanForMockData = async () => {
    if (!user) {
      toast.error('Please sign in to scan for mock data');
      return;
    }

    setIsScanning(true);
    setMockData([]);
    setScanComplete(false);

    try {
      const foundMockData: MockDataItem[] = [];

      // Scan for mock pillars - only obvious mock indicators, not user-created content
      const { data: mockPillars, error: pillarsError } = await supabase
        .from('content_pillars')
        .select('id, name, user_id, created_at')
        .eq('user_id', user.id)
        .or('name.ilike.%mock%,name.ilike.%demo%,name.ilike.%test%,name.ilike.%sample%');

      if (pillarsError) throw pillarsError;

      if (mockPillars && mockPillars.length > 0) {
        mockPillars.forEach(pillar => {
          foundMockData.push({
            id: pillar.id,
            name: pillar.name,
            type: 'pillar',
            user_id: pillar.user_id
          });
        });
      }

      // Scan for mock videos - only very specific mock indicators
      const { data: allVideos, error: videosError } = await supabase
        .from('videos')
        .select('id, title, user_id')
        .eq('user_id', user.id);

      if (videosError) throw videosError;

      // Filter videos client-side to avoid SQL complexity
      const mockTitles = [
        'React Server Components: Complete Guide',
        'React Hooks Complete Guide',
        'Building a Full Stack React App',
        'React State Management Deep Dive',
        'Home Office Setup Tour',
        'AI Tools Revolutionizing Content Creation',
        'Boost Your Work From Home Productivity',
        'How to Grow Your Channel Fast',
        'Beginner\'s Guide to YouTube Success',
        'Top 10 Camera Tips for Creators',
        'YouTube Algorithm Explained',
        'Camera Settings for Beginners'
      ];

      const mockVideos = allVideos?.filter(video => {
        const title = video.title?.toLowerCase() || '';
        return (
          title.includes('mock') ||
          title.includes('demo') ||
          title.includes('test') ||
          title.includes('sample') ||
          mockTitles.includes(video.title)
        );
      }) || [];



      if (mockVideos && mockVideos.length > 0) {
        mockVideos.forEach(video => {
          foundMockData.push({
            id: video.id,
            title: video.title,
            type: 'video',
            user_id: video.user_id
          });
        });
      }

      // Scan for mock goals
      const { data: mockGoals, error: goalsError } = await supabase
        .from('goals')
        .select('id, type, user_id')
        .eq('user_id', user.id)
        .or('type.ilike.%mock%,type.ilike.%demo%,type.ilike.%test%,type.ilike.%sample%');

      if (goalsError) throw goalsError;

      if (mockGoals && mockGoals.length > 0) {
        mockGoals.forEach(goal => {
          foundMockData.push({
            id: goal.id,
            name: goal.type,
            type: 'goal',
            user_id: goal.user_id
          });
        });
      }

      setMockData(foundMockData);
      setScanComplete(true);

      if (foundMockData.length === 0) {
        toast.success('No mock data found! Your database is clean.');
      } else {
        toast.warning(`Found ${foundMockData.length} mock data items that need removal.`);
      }

    } catch (error) {
      console.error('Error scanning for mock data:', error);
      toast.error('Failed to scan for mock data');
    } finally {
      setIsScanning(false);
    }
  };

  const removeAllMockData = async () => {
    if (!user || mockData.length === 0) {
      toast.error('No mock data to remove');
      return;
    }

    setIsRemoving(true);

    try {
      let removedCount = 0;

      // Remove mock videos first (to avoid foreign key constraints)
      const mockVideoIds = mockData.filter(item => item.type === 'video').map(item => item.id);
      if (mockVideoIds.length > 0) {
        const { error: videosError } = await supabase
          .from('videos')
          .delete()
          .in('id', mockVideoIds)
          .eq('user_id', user.id);

        if (videosError) throw videosError;
        removedCount += mockVideoIds.length;
      }

      // Remove mock pillars
      const mockPillarIds = mockData.filter(item => item.type === 'pillar').map(item => item.id);
      if (mockPillarIds.length > 0) {
        const { error: pillarsError } = await supabase
          .from('content_pillars')
          .delete()
          .in('id', mockPillarIds)
          .eq('user_id', user.id);

        if (pillarsError) throw pillarsError;
        removedCount += mockPillarIds.length;
      }

      // Remove mock goals
      const mockGoalIds = mockData.filter(item => item.type === 'goal').map(item => item.id);
      if (mockGoalIds.length > 0) {
        const { error: goalsError } = await supabase
          .from('goals')
          .delete()
          .in('id', mockGoalIds)
          .eq('user_id', user.id);

        if (goalsError) throw goalsError;
        removedCount += mockGoalIds.length;
      }

      // Clear the mock data list
      setMockData([]);
      setScanComplete(false);

      toast.success(`Successfully removed ${removedCount} mock data items!`);

      // Refresh the page to show clean data
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error) {
      console.error('Error removing mock data:', error);
      toast.error('Failed to remove some mock data items');
    } finally {
      setIsRemoving(false);
    }
  };

  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-yellow-500" />
          Complete Mock Data Removal
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-gray-300 text-sm">
          This tool will scan for and remove mock data including pillars with "mock", "demo", "test",
          or "sample" in their names, plus specific demo videos and goals. It only targets obvious mock content,
          not your real videos.
        </p>

        <div className="flex gap-3">
          <Button
            onClick={scanForMockData}
            disabled={isScanning || isRemoving}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Search className="w-4 h-4 mr-2" />
            {isScanning ? 'Scanning...' : 'Scan for Mock Data'}
          </Button>

          {mockData.length > 0 && (
            <Button
              onClick={removeAllMockData}
              disabled={isRemoving || isScanning}
              variant="destructive"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {isRemoving ? 'Removing...' : `Remove ${mockData.length} Items`}
            </Button>
          )}
        </div>

        {scanComplete && mockData.length > 0 && (
          <div className="mt-4 p-4 bg-gray-900 rounded-lg">
            <h4 className="text-white font-medium mb-2">Found Mock Data:</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {mockData.map((item, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <span className="text-gray-300">
                    {item.type === 'pillar' ? '📊' : item.type === 'video' ? '🎥' : '🎯'} 
                    {item.name || item.title}
                  </span>
                  <span className="text-gray-500 capitalize">{item.type}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {scanComplete && mockData.length === 0 && (
          <div className="mt-4 p-4 bg-green-900/20 border border-green-700 rounded-lg">
            <p className="text-green-400 text-sm">✅ No mock data found! Your database is clean.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CompleteMockDataRemoval;
