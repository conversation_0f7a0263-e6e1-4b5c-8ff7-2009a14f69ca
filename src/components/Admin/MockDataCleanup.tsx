import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Trash2, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Loader2 } from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface CleanupResults {
  deletedVideos: number;
  deletedPillars: number;
  deletedGoals: number;
  deletedAILogs: number;
  deletedWhiteboardProjects: number;
}

const MockDataCleanup = () => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isCleaning, setIsCleaning] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [cleanupResults, setCleanupResults] = useState<CleanupResults | null>(null);

  const analyzeMockData = async () => {
    setIsAnalyzing(true);
    try {
      // Check for mock videos - only very specific mock indicators
      const mockTitles = [
        'React Server Components: Complete Guide',
        'React Hooks Complete Guide',
        'Building a Full Stack React App',
        'React State Management Deep Dive',
        'Home Office Setup Tour',
        'AI Tools Revolutionizing Content Creation',
        'Boost Your Work From Home Productivity',
        'How to Grow Your Channel Fast',
        'Beginner\'s Guide to YouTube Success',
        'Top 10 Camera Tips for Creators',
        'YouTube Algorithm Explained',
        'Camera Settings for Beginners'
      ];

      const { data: mockVideos, error: videosError } = await supabase
        .from('videos')
        .select('id, title, youtube_video_id')
        .or(`title.ilike.%mock%,title.ilike.%demo%,title.ilike.%test%,title.ilike.%sample%,title.in.(${mockTitles.map(t => `"${t}"`).join(',')})`);

      if (videosError) throw videosError;

      // Check for mock pillars - only obvious mock indicators
      const { data: mockPillars, error: pillarsError } = await supabase
        .from('content_pillars')
        .select('id, name, description')
        .or('name.ilike.%mock%,name.ilike.%demo%,name.ilike.%test%,name.ilike.%sample%,description.ilike.%mock%,description.ilike.%demo%,description.ilike.%example%,description.ilike.%sample%');

      if (pillarsError) throw pillarsError;

      // Check for mock goals
      const { data: mockGoals, error: goalsError } = await supabase
        .from('goals')
        .select('id, type')
        .or('type.ilike.%mock%,type.ilike.%demo%,type.ilike.%test%');

      if (goalsError) throw goalsError;

      // Check for orphaned pillars
      const { data: orphanedPillars, error: orphanedError } = await supabase
        .from('content_pillars')
        .select(`
          id, 
          name,
          videos!left(id)
        `)
        .is('videos.id', null);

      if (orphanedError) throw orphanedError;

      setAnalysisResults({
        mockVideos: mockVideos || [],
        mockPillars: mockPillars || [],
        mockGoals: mockGoals || [],
        orphanedPillars: orphanedPillars || []
      });

      toast.success('Analysis complete!');
    } catch (error) {
      console.error('Error analyzing mock data:', error);
      toast.error('Failed to analyze mock data');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const cleanupMockData = async () => {
    if (!analysisResults) {
      toast.error('Please run analysis first');
      return;
    }

    setIsCleaning(true);
    try {
      let deletedVideos = 0;
      let deletedPillars = 0;
      let deletedGoals = 0;
      let deletedAILogs = 0;
      let deletedWhiteboardProjects = 0;

      // Delete mock videos - only very specific mock indicators
      if (analysisResults.mockVideos.length > 0) {
        const mockTitles = [
          'React Server Components: Complete Guide',
          'React Hooks Complete Guide',
          'Building a Full Stack React App',
          'React State Management Deep Dive',
          'Home Office Setup Tour',
          'AI Tools Revolutionizing Content Creation',
          'Boost Your Work From Home Productivity',
          'How to Grow Your Channel Fast',
          'Beginner\'s Guide to YouTube Success',
          'Top 10 Camera Tips for Creators',
          'YouTube Algorithm Explained',
          'Camera Settings for Beginners'
        ];

        const { error: deleteVideosError } = await supabase
          .from('videos')
          .delete()
          .or(`title.ilike.%mock%,title.ilike.%demo%,title.ilike.%test%,title.ilike.%sample%,title.in.(${mockTitles.map(t => `"${t}"`).join(',')})`);

        if (deleteVideosError) throw deleteVideosError;
        deletedVideos = analysisResults.mockVideos.length;
      }

      // Delete mock pillars
      if (analysisResults.mockPillars.length > 0) {
        const { error: deletePillarsError } = await supabase
          .from('content_pillars')
          .delete()
          .or('name.ilike.%mock%,name.ilike.%demo%,name.ilike.%test%,name.ilike.%sample%,description.ilike.%mock%,description.ilike.%demo%,description.ilike.%example%,description.ilike.%sample%');

        if (deletePillarsError) throw deletePillarsError;
        deletedPillars = analysisResults.mockPillars.length;
      }

      // Delete mock goals
      if (analysisResults.mockGoals.length > 0) {
        const { error: deleteGoalsError } = await supabase
          .from('goals')
          .delete()
          .or('type.ilike.%mock%,type.ilike.%demo%,type.ilike.%test%');

        if (deleteGoalsError) throw deleteGoalsError;
        deletedGoals = analysisResults.mockGoals.length;
      }

      // Clean up AI usage logs for mock content
      const { error: deleteAILogsError } = await supabase
        .from('ai_usage_logs')
        .delete()
        .or('metadata::text.ilike.%mock%,metadata::text.ilike.%demo%,metadata::text.ilike.%test%');

      if (!deleteAILogsError) {
        deletedAILogs = 1; // We don't know the exact count, but indicate cleanup happened
      }

      // Clean up mock whiteboard projects
      const { error: deleteWhiteboardError } = await supabase
        .from('whiteboard_projects')
        .delete()
        .or('name.ilike.%mock%,name.ilike.%demo%,name.ilike.%test%,description.ilike.%mock%,description.ilike.%demo%,description.ilike.%test%');

      if (!deleteWhiteboardError) {
        deletedWhiteboardProjects = 1; // We don't know the exact count, but indicate cleanup happened
      }

      setCleanupResults({
        deletedVideos,
        deletedPillars,
        deletedGoals,
        deletedAILogs,
        deletedWhiteboardProjects
      });

      toast.success('Mock data cleanup completed successfully!');
      
      // Clear analysis results to force re-analysis
      setAnalysisResults(null);
    } catch (error) {
      console.error('Error cleaning up mock data:', error);
      toast.error('Failed to cleanup mock data');
    } finally {
      setIsCleaning(false);
    }
  };

  const totalMockItems = analysisResults ? 
    analysisResults.mockVideos.length + 
    analysisResults.mockPillars.length + 
    analysisResults.mockGoals.length : 0;

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Trash2 className="w-5 h-5" />
          Mock Data Cleanup
        </CardTitle>
        <CardDescription>
          Analyze and remove mock/demo data from your database
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Analysis Section */}
        <div className="space-y-4">
          <Button 
            onClick={analyzeMockData}
            disabled={isAnalyzing}
            className="w-full"
          >
            {isAnalyzing ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              'Analyze Mock Data'
            )}
          </Button>

          {analysisResults && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p><strong>Analysis Results:</strong></p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Mock Videos: {analysisResults.mockVideos.length}</li>
                    <li>Mock Pillars: {analysisResults.mockPillars.length}</li>
                    <li>Mock Goals: {analysisResults.mockGoals.length}</li>
                    <li>Orphaned Pillars: {analysisResults.orphanedPillars.length}</li>
                  </ul>
                  <p className="mt-2">
                    <strong>Total items to clean: {totalMockItems}</strong>
                  </p>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Cleanup Section */}
        {analysisResults && totalMockItems > 0 && (
          <div className="space-y-4">
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                <strong>Warning:</strong> This action will permanently delete {totalMockItems} mock data items. 
                This cannot be undone.
              </AlertDescription>
            </Alert>

            <Button 
              onClick={cleanupMockData}
              disabled={isCleaning}
              variant="destructive"
              className="w-full"
            >
              {isCleaning ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Cleaning...
                </>
              ) : (
                <>
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete All Mock Data
                </>
              )}
            </Button>
          </div>
        )}

        {/* Results Section */}
        {cleanupResults && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              <div className="space-y-2">
                <p><strong>Cleanup Complete!</strong></p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Videos deleted: {cleanupResults.deletedVideos}</li>
                  <li>Pillars deleted: {cleanupResults.deletedPillars}</li>
                  <li>Goals deleted: {cleanupResults.deletedGoals}</li>
                  <li>AI logs cleaned: {cleanupResults.deletedAILogs > 0 ? 'Yes' : 'No'}</li>
                  <li>Whiteboard projects cleaned: {cleanupResults.deletedWhiteboardProjects > 0 ? 'Yes' : 'No'}</li>
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {analysisResults && totalMockItems === 0 && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              <strong>Great!</strong> No mock data found in your database.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default MockDataCleanup;
