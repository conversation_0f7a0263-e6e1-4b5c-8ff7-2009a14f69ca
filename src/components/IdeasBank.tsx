import React, { useState } from 'react';
import { Lightbulb, Plus, Search, Filter } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useIdeasData } from '@/hooks/useIdeasData';
import { useIdeasFilters } from '@/hooks/useIdeasFilters';
import IdeaCard from '@/components/Ideas/IdeaCard';
import EnhancedAIWizard from '@/components/Ideas/EnhancedAIWizard';

const IdeasBank = () => {
  const {
    ideas,
    pillars,
    isDialogOpen,
    setIsDialogOpen,
    editingIdea,
    setEditingIdea,
    newIdea,
    setNewIdea,
    addIdea,
    updateIdea,
    updateIdeaTitle,
    deleteIdea,
    moveToCalendar,
    addGeneratedIdea
  } = useIdeasData();

  const {
    searchTerm,
    setSearchTerm,
    filterPillar,
    setFilterPillar,
    filterPriority,
    setFilterPriority,
    filterStatus,
    setFilterStatus,
    filteredIdeas
  } = useIdeasFilters(ideas, pillars);

  // AI Wizard state
  const [isAIWizardOpen, setIsAIWizardOpen] = useState(false);

  // Helper function to get pillar name by ID
  const getPillarName = (pillarId: string | null) => {
    if (!pillarId) return undefined;
    const pillar = pillars.find(p => p.id === pillarId);
    return pillar?.name;
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-white">Ideas Bank</h1>
          <p className="text-gray-300 mt-1">Organize and prioritize your video content ideas</p>
        </div>
        <div className="flex space-x-2">
          <Button 
            onClick={() => setIsAIWizardOpen(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            <Lightbulb className="w-4 h-4 mr-2" />
            AI Generate
          </Button>
          <Button 
            onClick={() => setIsDialogOpen(true)}
            className="bg-blue hover:bg-blue-dark text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Idea
          </Button>
        </div>
      </div>

      {/* AI Wizard Dialog */}
      <EnhancedAIWizard
        isOpen={isAIWizardOpen}
        onClose={() => setIsAIWizardOpen(false)}
        pillars={pillars}
        onIdeaGenerated={addGeneratedIdea}
        title="AI Video Idea Generator"
      />

      {/* Search and Filters */}
      <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search ideas by title or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={filterPillar} onValueChange={setFilterPillar}>
          <SelectTrigger className="w-full md:w-48">
            <Filter className="w-4 h-4 mr-2" />
            <SelectValue placeholder="Filter by pillar" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Pillars</SelectItem>
            {pillars.map(pillar => (
              <SelectItem key={pillar.id} value={pillar.id}>{pillar.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={filterPriority} onValueChange={setFilterPriority}>
          <SelectTrigger className="w-full md:w-32">
            <SelectValue placeholder="Priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="High">High</SelectItem>
            <SelectItem value="Medium">Medium</SelectItem>
            <SelectItem value="Low">Low</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-full md:w-32">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="idea">Idea</SelectItem>
            <SelectItem value="planned">Planned</SelectItem>
            <SelectItem value="in_progress">In Progress</SelectItem>
            <SelectItem value="published">Published</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Ideas Grid */}
      {filteredIdeas.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredIdeas.map((idea) => (
            <IdeaCard
              key={idea.id}
              idea={idea}
              pillarName={getPillarName(idea.pillar_id)}
              onEdit={setEditingIdea}
              onDelete={deleteIdea}
              onMoveToCalendar={moveToCalendar}
              onTitleUpdate={updateIdeaTitle}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Lightbulb className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300">
            {searchTerm || filterPillar !== 'all' || filterPriority !== 'all' || filterStatus !== 'all'
              ? 'No ideas found'
              : 'No ideas yet'
            }
          </h3>
          <p className="text-gray-400 mt-1">
            {searchTerm || filterPillar !== 'all' || filterPriority !== 'all' || filterStatus !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Start building your content strategy by adding your first video idea'
            }
          </p>
          {!searchTerm && filterPillar === 'all' && filterPriority === 'all' && filterStatus === 'all' && (
            <Button 
              onClick={() => setIsDialogOpen(true)}
              className="mt-4 bg-blue hover:bg-blue-dark text-white"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Your First Idea
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default IdeasBank;
