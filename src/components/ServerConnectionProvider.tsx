import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { checkApiConnection } from '@/lib/connection-utils';
import ServerConnectionError from './ServerConnectionError';

interface ServerConnectionContextType {
  isConnected: boolean;
  isChecking: boolean;
  checkConnection: () => Promise<void>;
  lastChecked: Date | null;
}

const ServerConnectionContext = createContext<ServerConnectionContextType>({
  isConnected: true,
  isChecking: true,
  checkConnection: async () => {},
  lastChecked: null
});

export const useServerConnection = () => useContext(ServerConnectionContext);

interface ServerConnectionProviderProps {
  children: ReactNode;
  checkInterval?: number; // in milliseconds
}

export const ServerConnectionProvider = ({ 
  children, 
  checkInterval = 60000 // Default: check every minute
}: ServerConnectionProviderProps) => {
  const [isConnected, setIsConnected] = useState(true);
  const [isChecking, setIsChecking] = useState(true);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkConnection = async () => {
    setIsChecking(true);
    try {
      const result = await checkApiConnection('/health');
      setIsConnected(result.ok);
      setLastChecked(new Date());
    } catch (error) {
      console.error('Server connection check failed:', error);
      setIsConnected(false);
      setLastChecked(new Date());
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    // Initial check
    checkConnection();
    
    // Set up periodic checks
    const intervalId = setInterval(() => {
      checkConnection();
    }, checkInterval);
    
    return () => clearInterval(intervalId);
  }, [checkInterval]);

  if (!isConnected && !isChecking) {
    return <ServerConnectionError onRetry={checkConnection} />;
  }

  return (
    <ServerConnectionContext.Provider value={{ isConnected, isChecking, checkConnection, lastChecked }}>
      {children}
    </ServerConnectionContext.Provider>
  );
};