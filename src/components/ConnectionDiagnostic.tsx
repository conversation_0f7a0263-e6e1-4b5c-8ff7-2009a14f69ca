import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON><PERSON><PERSON>, XCircle, Alert<PERSON>riangle, RefreshCw } from 'lucide-react';

interface DiagnosticResult {
  test: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: any;
}

export const ConnectionDiagnostic: React.FC = () => {
  const [results, setResults] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (result: DiagnosticResult) => {
    setResults(prev => [...prev, result]);
  };

  const runDiagnostics = async () => {
    setIsRunning(true);
    setResults([]);

    // Test 1: Environment Variables
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      addResult({
        test: 'Environment Variables',
        status: 'error',
        message: 'Missing Supabase environment variables',
        details: { urlExists: !!supabaseUrl, keyExists: !!supabaseKey }
      });
      setIsRunning(false);
      return;
    }

    addResult({
      test: 'Environment Variables',
      status: 'success',
      message: 'Environment variables found',
      details: { url: supabaseUrl.substring(0, 30) + '...', keyLength: supabaseKey.length }
    });

    // Test 2: DNS Resolution
    try {
      const hostname = new URL(supabaseUrl).hostname;
      addResult({
        test: 'URL Parsing',
        status: 'success',
        message: `Hostname extracted: ${hostname}`
      });

      // Test 3: Basic HTTP Connectivity
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      try {
        const response = await fetch(`${supabaseUrl}/rest/v1/`, {
          method: 'GET',
          headers: {
            'apikey': supabaseKey,
            'Authorization': `Bearer ${supabaseKey}`
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          addResult({
            test: 'HTTP Connectivity',
            status: 'success',
            message: `Connected successfully (${response.status})`,
            details: { status: response.status, statusText: response.statusText }
          });
        } else {
          addResult({
            test: 'HTTP Connectivity',
            status: 'error',
            message: `HTTP Error: ${response.status} ${response.statusText}`,
            details: { status: response.status, statusText: response.statusText }
          });
        }
      } catch (fetchError: any) {
        clearTimeout(timeoutId);
        
        if (fetchError.name === 'AbortError') {
          addResult({
            test: 'HTTP Connectivity',
            status: 'error',
            message: 'Connection timeout (10s)',
            details: { error: 'Timeout' }
          });
        } else {
          addResult({
            test: 'HTTP Connectivity',
            status: 'error',
            message: `Connection failed: ${fetchError.message}`,
            details: { error: fetchError.message, type: fetchError.name }
          });
        }
      }

      // Test 4: Supabase Client Test
      try {
        const { createClient } = await import('@supabase/supabase-js');
        const testClient = createClient(supabaseUrl, supabaseKey);
        
        addResult({
          test: 'Supabase Client',
          status: 'success',
          message: 'Supabase client created successfully'
        });

        // Test 5: Database Query
        try {
          const { data, error } = await testClient.from('users').select('count', { count: 'exact', head: true });
          
          if (error) {
            addResult({
              test: 'Database Query',
              status: 'error',
              message: `Database error: ${error.message}`,
              details: { error: error.message, code: error.code }
            });
          } else {
            addResult({
              test: 'Database Query',
              status: 'success',
              message: 'Database query successful',
              details: { count: data }
            });
          }
        } catch (dbError: any) {
          addResult({
            test: 'Database Query',
            status: 'error',
            message: `Database query failed: ${dbError.message}`,
            details: { error: dbError.message }
          });
        }

      } catch (clientError: any) {
        addResult({
          test: 'Supabase Client',
          status: 'error',
          message: `Failed to create Supabase client: ${clientError.message}`,
          details: { error: clientError.message }
        });
      }

    } catch (urlError: any) {
      addResult({
        test: 'URL Parsing',
        status: 'error',
        message: `Invalid URL: ${urlError.message}`,
        details: { error: urlError.message }
      });
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-500 bg-green-500/10';
      case 'error':
        return 'border-red-500 bg-red-500/10';
      case 'warning':
        return 'border-yellow-500 bg-yellow-500/10';
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Database Connection Diagnostic
          {isRunning && <RefreshCw className="w-4 h-4 animate-spin" />}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={runDiagnostics} 
          disabled={isRunning}
          className="w-full"
        >
          {isRunning ? 'Running Diagnostics...' : 'Run Connection Diagnostic'}
        </Button>

        {results.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold">Diagnostic Results:</h3>
            {results.map((result, index) => (
              <Alert key={index} className={getStatusColor(result.status)}>
                <div className="flex items-start gap-3">
                  {getStatusIcon(result.status)}
                  <div className="flex-1">
                    <div className="font-medium">{result.test}</div>
                    <AlertDescription className="mt-1">
                      {result.message}
                      {result.details && (
                        <details className="mt-2">
                          <summary className="cursor-pointer text-sm opacity-70">
                            Show details
                          </summary>
                          <pre className="mt-1 text-xs bg-black/20 p-2 rounded overflow-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </AlertDescription>
                  </div>
                </div>
              </Alert>
            ))}
          </div>
        )}

        {results.length > 0 && (
          <Alert className="border-blue-500 bg-blue-500/10">
            <AlertTriangle className="w-4 h-4 text-blue-500" />
            <AlertDescription>
              <strong>Next Steps:</strong>
              <ul className="mt-2 space-y-1 text-sm">
                <li>• If DNS/connectivity fails: Check if your Supabase project still exists</li>
                <li>• If HTTP fails: Verify your project URL and API keys</li>
                <li>• If database query fails: Check your database schema and RLS policies</li>
                <li>• Create a new Supabase project if the current one is deleted</li>
              </ul>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default ConnectionDiagnostic;
