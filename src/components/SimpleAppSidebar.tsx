import React from 'react';
import { Link, useLocation } from 'react-router-dom';

export function SimpleAppSidebar() {
  const location = useLocation();
  
  const navigation = [
    { name: 'Dashboard', href: '/dashboard' },
    { name: 'Content Pillars', href: '/pillars' },
    { name: 'Ideas Bank', href: '/ideas' },
    { name: 'Calendar', href: '/calendar' },
    { name: 'Test Page', href: '/test' },
  ];

  return (
    <aside className="w-64 h-screen bg-gray-900 text-white p-4 fixed left-0 top-0 overflow-y-auto">
      <div className="mb-6">
        <h1 className="text-xl font-bold">MyContentHub</h1>
      </div>
      <nav className="space-y-2">
        {navigation.map((item) => {
          const isActive = location.pathname === item.href;
          return (
            <Link
              key={item.name}
              to={item.href}
              className={`block py-2 px-4 rounded ${
                isActive 
                  ? 'bg-gray-800 text-white' 
                  : 'text-gray-300 hover:bg-gray-800 hover:text-white'
              }`}
            >
              {item.name}
            </Link>
          );
        })}
      </nav>
    </aside>
  );
}
