import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Calendar, Eye } from 'lucide-react';
import { formatDate } from '../../utils/dateUtils';
import { VideoIdea } from '@/components/Ideas/types';
import { ContentPillar } from '@/types/pillar';

interface VideoCardProps {
  video: VideoIdea;
  pillars: ContentPillar[];
  isCompact?: boolean;
  onVideoClick?: (video: VideoIdea) => void;
  onDragStart?: (e: React.DragEvent, video: VideoIdea) => void;
  onStatusChanged?: (video: VideoIdea, newStatus: string) => void;
  onVideoUpdated?: () => void;
}

const VideoCard: React.FC<VideoCardProps> = ({
  video,
  pillars,
  isCompact = false,
  onVideoClick,
  onDragStart,
  onStatusChanged,
  onVideoUpdated
}) => {
  const pillar = pillars.find(p => p.id === video.pillar_id);
  const pillarColor = pillar?.color || '#888888';
  
  const handleClick = () => {
    if (onVideoClick) {
      onVideoClick(video);
    }
  };

  const handleDragStart = (e: React.DragEvent) => {
    if (onDragStart) {
      onDragStart(e, video);
    }
  };

  if (isCompact) {
    return (
      <div
        className="calendar-video-card text-xs p-1 rounded bg-gray-800/60 border border-gray-700 text-white truncate mb-1 cursor-pointer"
        style={{ borderLeftColor: pillarColor, borderLeftWidth: '3px' }}
        onClick={handleClick}
        draggable
        onDragStart={handleDragStart}
      >
        <div className="truncate">{video.title}</div>
      </div>
    );
  }

  return (
    <div
      className="video-card p-3 rounded-lg bg-gray-800 border border-gray-700 hover:border-gray-500 transition-all cursor-pointer"
      style={{ borderLeftColor: pillarColor, borderLeftWidth: '4px' }}
      onClick={handleClick}
      draggable
      onDragStart={handleDragStart}
    >
      <h3 className="font-medium text-white truncate">{video.title}</h3>
      
      <div className="flex items-center mt-2 text-xs text-gray-400">
        {video.scheduled_date && (
          <div className="flex items-center mr-3">
            <Calendar className="w-3 h-3 mr-1" />
            <span>{formatDate(new Date(video.scheduled_date))}</span>
          </div>
        )}
        
        {video.views !== undefined && (
          <div className="flex items-center mr-3">
            <Eye className="w-3 h-3 mr-1" />
            <span>{video.views.toLocaleString()} views</span>
          </div>
        )}
        
        <Badge 
          variant="outline" 
          className="ml-auto text-xs"
          style={{ 
            backgroundColor: pillarColor + '20', 
            color: pillarColor,
            borderColor: pillarColor + '40'
          }}
        >
          {pillar?.name || 'Uncategorized'}
        </Badge>
      </div>
    </div>
  );
};

export default VideoCard;
