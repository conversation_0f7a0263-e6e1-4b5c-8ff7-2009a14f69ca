import React, { useState, useEffect } from 'react';
import { Video } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { useVideoEditForm } from '@/hooks/useVideoEditForm';
import BasicInfoFields from '../Calendar/VideoEditModal/BasicInfoFields';
import StatusSelect from '../Calendar/VideoEditModal/StatusSelect';
import PillarSelect from '../Calendar/VideoEditModal/PillarSelect';
import SchedulingFields from '../Calendar/VideoEditModal/SchedulingFields';
import ActionButtons from '../Calendar/VideoEditModal/ActionButtons';
import { VideoIdea } from '@/components/Ideas/types';
import { ContentPillar } from '@/types/pillar';

interface VideoEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  video: VideoIdea | null;
  pillars: ContentPillar[];
  onVideoUpdated: () => void;
}

const VideoEditModal = ({ isOpen, onClose, video, pillars, onVideoUpdated }: VideoEditModalProps) => {
  const { formData, isLoading, updateFormField, handleSubmit, handleSave } = useVideoEditForm(
    video,
    onVideoUpdated,
    onClose
  );

  if (!video) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="calendar-modal sm:max-w-[600px] p-0 overflow-hidden">
        <div className="p-6">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-white flex items-center">
              <Video className="w-5 h-5 mr-2 text-teal" />
              Edit Video Details
            </DialogTitle>
            <DialogDescription className="text-gray-400">
              Update your video information below.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6 mt-4">
            <BasicInfoFields
              title={formData.title}
              description={formData.description}
              onTitleChange={(value) => updateFormField('title', value)}
              onDescriptionChange={(value) => updateFormField('description', value)}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <StatusSelect
                value={formData.status}
                onChange={(value) => updateFormField('status', value)}
              />

              <PillarSelect
                value={formData.pillar_id}
                onChange={(value) => updateFormField('pillar_id', value)}
                pillars={pillars}
              />
            </div>

            <SchedulingFields
              scheduledDate={formData.scheduled_date}
              calendarNotes={formData.calendar_notes}
              onScheduledDateChange={(value) => updateFormField('scheduled_date', value)}
              onCalendarNotesChange={(value) => updateFormField('calendar_notes', value)}
            />

            <ActionButtons
              onCancel={onClose}
              onSave={handleSave}
              isLoading={isLoading}
            />
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default VideoEditModal;
