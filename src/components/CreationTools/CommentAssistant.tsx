
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MessageCircle, Copy, Check } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { useAICredits } from '@/hooks/useAICredits';

interface Reply {
  text: string;
  tone: string;
}

const CommentAssistant = () => {
  const { hasCreditsAvailable, useCredits, getRemainingCredits } = useAICredits();
  const [comment, setComment] = useState('');
  const [tone, setTone] = useState('friendly');
  const [replies, setReplies] = useState<Reply[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const generateReplies = async () => {
    if (!comment.trim()) return;

    const creditsNeeded = 2; // 2 credits for comment reply generation
    
    if (!hasCreditsAvailable(creditsNeeded)) {
      toast.error(`Not enough AI credits. You need ${creditsNeeded} credits but only have ${getRemainingCredits()} remaining.`);
      return;
    }

    setIsGenerating(true);
    try {
      // First, deduct the credits
      const creditsDeducted = await useCredits(creditsNeeded, 'comment_reply_generation', comment, '');
      if (!creditsDeducted) {
        setIsGenerating(false);
        return;
      }

      const { data, error } = await supabase.functions.invoke('generate-comment-replies', {
        body: { 
          comment: comment.trim(),
          tone
        }
      });

      if (error) throw error;

      setReplies(data.replies);
      toast.success('Replies generated successfully!');
    } catch (error) {
      console.error('Error generating replies:', error);
      toast.error('Failed to generate replies');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyReply = async (reply: string, index: number) => {
    try {
      await navigator.clipboard.writeText(reply);
      setCopiedIndex(index);
      toast.success('Reply copied to clipboard!');
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      toast.error('Failed to copy reply');
    }
  };

  const resetAssistant = () => {
    setComment('');
    setReplies([]);
    setCopiedIndex(null);
  };

  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <MessageCircle className="w-5 h-5" />
          Comment Reply Assistant
        </CardTitle>
        <CardDescription className="text-gray-300">
          Generate engaging replies to build your community (2 credits)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Viewer Comment
            </label>
            <Textarea
              placeholder="Paste viewer comment here..."
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 min-h-[100px]"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Reply Tone
            </label>
            <Select value={tone} onValueChange={setTone}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-700 border-gray-600">
                <SelectItem value="friendly">Friendly</SelectItem>
                <SelectItem value="professional">Professional</SelectItem>
                <SelectItem value="casual">Casual</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            onClick={generateReplies}
            disabled={!comment.trim() || isGenerating || !hasCreditsAvailable(2)}
            className="w-full bg-teal hover:bg-teal/90 disabled:opacity-50"
          >
            {isGenerating ? 'Generating Replies...' : 'Generate Replies (2 credits)'}
          </Button>
          
          {!hasCreditsAvailable(2) && (
            <p className="text-sm text-orange text-center">
              Not enough credits remaining: {getRemainingCredits()}
            </p>
          )}
        </div>

        {replies.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-white font-medium">Generated Replies:</h4>
              <Button
                onClick={resetAssistant}
                variant="outline"
                size="sm"
                className="border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                New Comment
              </Button>
            </div>

            <div className="space-y-3">
              {replies.map((reply, index) => (
                <div
                  key={index}
                  className="bg-gray-700 rounded-lg p-4 border border-gray-600"
                >
                  <div className="flex items-start justify-between mb-2">
                    <span className="text-xs text-gray-400 uppercase tracking-wide">
                      {reply.tone} Reply {index + 1}
                    </span>
                    <Button
                      onClick={() => copyReply(reply.text, index)}
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 hover:bg-gray-600"
                    >
                      {copiedIndex === index ? (
                        <Check className="w-4 h-4 text-green-500" />
                      ) : (
                        <Copy className="w-4 h-4 text-gray-400" />
                      )}
                    </Button>
                  </div>
                  <p className="text-gray-200 text-sm leading-relaxed">
                    {reply.text}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CommentAssistant;
