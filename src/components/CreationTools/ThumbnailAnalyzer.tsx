
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Upload, Image as ImageIcon, RotateCcw } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';

interface AnalysisResult {
  clickabilityScore: number;
  readabilityScore: number;
  contrastScore: number;
  visualImpactScore: number;
  improvementTips: string[];
}

const ThumbnailAnalyzer = () => {
  const { user } = useAuth();
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [analysis, setAnalysis] = useState<AnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type.startsWith('image/')) {
        setSelectedImage(file);
        const reader = new FileReader();
        reader.onload = () => {
          setImagePreview(reader.result as string);
        };
        reader.readAsDataURL(file);
        setAnalysis(null);
      } else {
        toast.error('Please select an image file');
      }
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
      setAnalysis(null);
    }
  };

  const analyzeThumbnail = async () => {
    if (!selectedImage || !user) return;

    setIsAnalyzing(true);
    try {
      // Convert image to base64
      const reader = new FileReader();
      reader.onload = async () => {
        const base64Image = reader.result as string;
        
        const { data, error } = await supabase.functions.invoke('analyze-thumbnail', {
          body: { 
            image: base64Image,
            user_id: user.id 
          }
        });

        if (error) throw error;

        setAnalysis(data.analysis);
        toast.success('Thumbnail analyzed successfully!');
      };
      reader.readAsDataURL(selectedImage);
    } catch (error) {
      console.error('Error analyzing thumbnail:', error);
      toast.error('Failed to analyze thumbnail');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const resetAnalyzer = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setAnalysis(null);
  };

  const ScoreGauge = ({ score, label }: { score: number; label: string }) => (
    <div className="flex flex-col items-center">
      <div className="relative w-16 h-16 mb-2">
        <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
          <path
            d="M18 2.0845
              a 15.9155 15.9155 0 0 1 0 31.831
              a 15.9155 15.9155 0 0 1 0 -31.831"
            fill="none"
            stroke="#374151"
            strokeWidth="2"
          />
          <path
            d="M18 2.0845
              a 15.9155 15.9155 0 0 1 0 31.831
              a 15.9155 15.9155 0 0 1 0 -31.831"
            fill="none"
            stroke={score >= 7 ? "#37BEB0" : score >= 4 ? "#F59F0A" : "#E76F51"}
            strokeWidth="2"
            strokeDasharray={`${score * 10}, 100`}
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-lg font-bold text-white">{score}</span>
        </div>
      </div>
      <span className="text-sm text-gray-300 text-center">{label}</span>
    </div>
  );

  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <ImageIcon className="w-5 h-5" />
          AI Thumbnail Analyzer
        </CardTitle>
        <CardDescription className="text-gray-300">
          Get instant feedback to create clickable thumbnails
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {!imagePreview ? (
          <div
            className="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center hover:border-teal transition-colors cursor-pointer"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onClick={() => document.getElementById('thumbnail-upload')?.click()}
          >
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-300 mb-2">Drop your thumbnail here or click to upload</p>
            <p className="text-sm text-gray-500">PNG, JPG up to 10MB</p>
            <input
              id="thumbnail-upload"
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>
        ) : (
          <div className="space-y-4">
            <div className="relative">
              <img
                src={imagePreview}
                alt="Thumbnail preview"
                className="w-full h-48 object-cover rounded-lg"
              />
              <Button
                onClick={resetAnalyzer}
                variant="ghost"
                size="sm"
                className="absolute top-2 right-2 bg-black bg-opacity-50 hover:bg-opacity-70"
              >
                <RotateCcw className="w-4 h-4" />
              </Button>
            </div>

            {!analysis && (
              <Button
                onClick={analyzeThumbnail}
                disabled={isAnalyzing}
                className="w-full bg-teal hover:bg-teal/90"
              >
                {isAnalyzing ? 'Analyzing...' : 'Analyze Thumbnail'}
              </Button>
            )}

            {analysis && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <ScoreGauge score={analysis.clickabilityScore} label="Clickability" />
                  <ScoreGauge score={analysis.readabilityScore} label="Readability" />
                  <ScoreGauge score={analysis.contrastScore} label="Contrast" />
                  <ScoreGauge score={analysis.visualImpactScore} label="Visual Impact" />
                </div>

                <div>
                  <h4 className="text-white font-medium mb-3">Improvement Tips:</h4>
                  <ul className="space-y-2">
                    {analysis.improvementTips.map((tip, index) => (
                      <li key={index} className="text-gray-300 text-sm flex items-start">
                        <span className="text-teal mr-2">•</span>
                        {tip}
                      </li>
                    ))}
                  </ul>
                </div>

                <Button
                  onClick={resetAnalyzer}
                  variant="outline"
                  className="w-full border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  Upload New Thumbnail
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ThumbnailAnalyzer;
