
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TrendingUp, Zap, Sparkles, Eye, ThumbsUp, Clock, Target } from 'lucide-react';
import { useAICredits } from '@/hooks/useAICredits';
import { ContentPillar } from '@/components/Ideas/types';

interface PredictionResult {
  views: number;
  engagement: number;
  confidence: number;
  suggestions: string[];
  breakdown: {
    titleScore: number;
    lengthScore: number;
    pillarScore: number;
    keywordScore: number;
  };
}

interface PerformancePredictorCardProps {
  pillars: ContentPillar[];
}

const PerformancePredictorCard: React.FC<PerformancePredictorCardProps> = ({ pillars }) => {
  const [title, setTitle] = useState('');
  const [selectedPillar, setSelectedPillar] = useState('');
  const [prediction, setPrediction] = useState<PredictionResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [enhancedMode, setEnhancedMode] = useState(false);
  const { hasCreditsAvailable, useCredits } = useAICredits();

  const calculateLocalScore = (videoTitle: string, pillarId: string) => {
    // Title length optimization (55-60 characters ideal)
    const titleLength = videoTitle.length;
    const lengthScore = titleLength >= 40 && titleLength <= 70 ? 85 : 
                      titleLength >= 30 && titleLength <= 80 ? 70 : 50;

    // Keyword analysis (basic)
    const powerWords = ['ultimate', 'best', 'top', 'secret', 'proven', 'amazing', 'incredible', 'shocking'];
    const hasNumbers = /\d/.test(videoTitle);
    const hasPowerWords = powerWords.some(word => 
      videoTitle.toLowerCase().includes(word)
    );
    const keywordScore = (hasNumbers ? 20 : 0) + (hasPowerWords ? 25 : 0) + 55;

    // Pillar matching
    const pillar = pillars.find(p => p.id === pillarId);
    const pillarScore = pillar ? 80 : 60;

    // Title structure analysis
    const hasQuestion = videoTitle.includes('?');
    const hasEmoji = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/u.test(videoTitle);
    const titleScore = 60 + (hasQuestion ? 15 : 0) + (hasEmoji ? 10 : 0);

    const averageScore = (lengthScore + keywordScore + pillarScore + titleScore) / 4;

    // Predict views based on score
    const baseViews = Math.round(averageScore * 100 + Math.random() * 2000);
    const engagement = Math.round(averageScore * 0.8 + Math.random() * 20);

    return {
      views: baseViews,
      engagement,
      confidence: Math.min(averageScore, 85), // Local analysis max 85% confidence
      suggestions: generateLocalSuggestions(videoTitle, lengthScore, keywordScore),
      breakdown: {
        titleScore,
        lengthScore,
        pillarScore,
        keywordScore
      }
    };
  };

  const generateLocalSuggestions = (title: string, lengthScore: number, keywordScore: number) => {
    const suggestions = [];
    
    if (lengthScore < 70) {
      if (title.length < 40) suggestions.push('Consider making your title longer for better SEO');
      if (title.length > 80) suggestions.push('Shorten your title for better visibility');
    }
    
    if (keywordScore < 75) {
      suggestions.push('Add numbers or power words to increase click-through rate');
    }
    
    if (!title.includes('?') && !title.includes('!')) {
      suggestions.push('Consider adding a question or exclamation for engagement');
    }

    return suggestions;
  };

  const performQuickPredict = () => {
    if (!title || !selectedPillar) return;
    
    setLoading(true);
    setTimeout(() => {
      const result = calculateLocalScore(title, selectedPillar);
      setPrediction(result);
      setLoading(false);
    }, 500);
  };

  const performEnhancedPredict = async () => {
    if (!title || !selectedPillar) return;
    
    if (!hasCreditsAvailable(1)) {
      return;
    }

    setLoading(true);
    try {
      // Start with local score
      const localResult = calculateLocalScore(title, selectedPillar);
      
      // Use AI credits for enhanced analysis
      const canUseCredits = await useCredits(1, 'performance-prediction', title);
      if (!canUseCredits) {
        setLoading(false);
        return;
      }

      // Simulate AI enhancement (in real implementation, call your AI endpoint)
      setTimeout(() => {
        const enhancedResult = {
          ...localResult,
          views: Math.round(localResult.views * (1.2 + Math.random() * 0.4)), // AI boost
          confidence: Math.min(localResult.confidence + 10, 95), // Higher confidence
          suggestions: [
            ...localResult.suggestions,
            'AI suggests: Consider emotional triggers in your title',
            'AI recommends: Test variations with A/B testing'
          ]
        };
        
        setPrediction(enhancedResult);
        setLoading(false);
      }, 1500);
    } catch (error) {
      console.error('Enhanced prediction error:', error);
      setLoading(false);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-500';
    if (confidence >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const formatViews = (views: number) => {
    if (views >= 1000000) return `${(views / 1000000).toFixed(1)}M`;
    if (views >= 1000) return `${(views / 1000).toFixed(1)}K`;
    return views.toString();
  };

  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <div className="p-2 rounded-lg bg-purple-500 mr-3">
            <TrendingUp className="w-5 h-5 text-white" />
          </div>
          Performance Predictor
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <Input
            placeholder="Enter your video title..."
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="bg-gray-700 border-gray-600 text-white"
          />
          
          <Select value={selectedPillar} onValueChange={setSelectedPillar}>
            <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
              <SelectValue placeholder="Select content pillar" />
            </SelectTrigger>
            <SelectContent>
              {pillars.map((pillar) => (
                <SelectItem key={pillar.id} value={pillar.id}>
                  {pillar.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={performQuickPredict}
            disabled={!title || !selectedPillar || loading}
            className="flex-1 bg-teal hover:bg-teal/90 text-white"
          >
            <Zap className="w-4 h-4 mr-2" />
            Quick Predict (Free)
          </Button>
          
          <Button
            onClick={performEnhancedPredict}
            disabled={!title || !selectedPillar || loading || !hasCreditsAvailable(1)}
            variant="outline"
            className="flex-1 border-purple-500 text-purple-400 hover:bg-purple-500/10"
          >
            <Sparkles className="w-4 h-4 mr-2" />
            AI Enhanced (1 credit)
          </Button>
        </div>

        {loading && (
          <div className="text-center py-4">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-teal"></div>
            <p className="text-gray-400 mt-2">
              {enhancedMode ? 'Running AI analysis...' : 'Calculating prediction...'}
            </p>
          </div>
        )}

        {prediction && !loading && (
          <div className="space-y-4 p-4 bg-gray-700/50 rounded-lg">
            <div className="flex items-center justify-between">
              <h4 className="text-white font-medium">Prediction Results</h4>
              <Badge 
                className={`${getConfidenceColor(prediction.confidence)} font-bold`}
                variant="outline"
              >
                {prediction.confidence}% confidence
              </Badge>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gray-800 rounded">
                <Eye className="w-5 h-5 text-teal mx-auto mb-1" />
                <div className="text-2xl font-bold text-white">{formatViews(prediction.views)}</div>
                <div className="text-xs text-gray-400">Predicted Views</div>
              </div>
              <div className="text-center p-3 bg-gray-800 rounded">
                <ThumbsUp className="w-5 h-5 text-green-500 mx-auto mb-1" />
                <div className="text-2xl font-bold text-white">{prediction.engagement}%</div>
                <div className="text-xs text-gray-400">Engagement Rate</div>
              </div>
            </div>

            <div className="space-y-2">
              <h5 className="text-white font-medium">Score Breakdown</h5>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Title Structure</span>
                  <span className="text-white">{prediction.breakdown.titleScore}/100</span>
                </div>
                <Progress value={prediction.breakdown.titleScore} className="h-2" />
                
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Length Optimization</span>
                  <span className="text-white">{prediction.breakdown.lengthScore}/100</span>
                </div>
                <Progress value={prediction.breakdown.lengthScore} className="h-2" />
                
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Keyword Power</span>
                  <span className="text-white">{prediction.breakdown.keywordScore}/100</span>
                </div>
                <Progress value={prediction.breakdown.keywordScore} className="h-2" />
              </div>
            </div>

            {prediction.suggestions.length > 0 && (
              <div className="space-y-2">
                <h5 className="text-white font-medium flex items-center">
                  <Target className="w-4 h-4 mr-2 text-orange" />
                  Optimization Tips
                </h5>
                <ul className="space-y-1">
                  {prediction.suggestions.map((suggestion, index) => (
                    <li key={index} className="text-sm text-gray-300 flex items-start">
                      <span className="text-orange mr-2">•</span>
                      {suggestion}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        <div className="text-xs text-gray-400 text-center">
          Quick predictions use pattern analysis. AI Enhanced uses GPT for deeper insights.
        </div>
      </CardContent>
    </Card>
  );
};

export default PerformancePredictorCard;
