import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Plus } from 'lucide-react';

interface PillarCardProps {
  pillar: {
    id: string;
    name: string;
    color: string;
    description?: string;
    targetPercentage: number;
    actualPercentage: number;
    videoCount: number;
  };
  onViewVideos?: (pillarId: string) => void;
}

const PillarCard: React.FC<PillarCardProps> = ({ pillar, onViewVideos }) => {
  const variance = pillar.actualPercentage - pillar.targetPercentage;
  const isUnder = variance < 0;
  
  return (
    <Card className="glass-effect hover:border-white/30 transition-all">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: pillar.color }}></div>
          <CardTitle className="text-white text-lg">{pillar.name}</CardTitle>
        </div>
        <p className="text-sm text-gray-400">Target: {pillar.targetPercentage}% of content</p>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="space-y-1">
          <p className="text-sm text-gray-400">Progress</p>
          <Progress 
            value={pillar.actualPercentage} 
            max={pillar.targetPercentage} 
            className="h-2 bg-gray-700" 
            indicatorClassName="bg-teal" 
          />
          <div className="flex justify-between text-sm">
            <span className="text-orange-400">
              {isUnder ? Math.abs(variance).toFixed(1) + '% Under' : variance.toFixed(1) + '% Over'}
            </span>
            <span className="text-gray-400">{pillar.actualPercentage}% / {pillar.targetPercentage}%</span>
          </div>
        </div>
        
        <div className="flex justify-between items-center text-sm">
          <span className="text-gray-400">{pillar.videoCount} videos</span>
        </div>
        
        <div className="flex justify-between pt-2">
          <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white hover:bg-white/10">
            View analytics
          </Button>
          <Button 
            className="glass-button bg-blue-600/80 hover:bg-blue-600 text-white"
            onClick={() => onViewVideos && onViewVideos(pillar.id)}
          >
            View Videos
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default PillarCard;