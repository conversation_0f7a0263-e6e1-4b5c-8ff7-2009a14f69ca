
import React from 'react';
import { Bar<PERSON>hart3, Target, Lightbulb, TrendingUp, <PERSON>u, User, Crown } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface NavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const Navigation = ({ activeTab, onTabChange }: NavigationProps) => {
  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'pillars', label: 'Content Pillars', icon: Target },
    { id: 'ideas', label: 'Ideas Bank', icon: Lightbulb },
    { id: 'analytics', label: 'Analytics', icon: TrendingUp }
  ];

  return (
    <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-8 flex-shrink-0">
              <img
                src="/MCH_White_Vertical1.svg"
                alt="MCH Logo - MyContentHub"
                className="w-full h-full object-contain transition-opacity hover:opacity-80"
                loading="lazy"
              />
            </div>
            <div>
              <h1 className="text-xl font-bold text-terracotta-dark">MyContentHub</h1>
              <p className="text-xs text-gray-500">YouTube Strategy Platform</p>
            </div>
          </div>

          {/* Navigation Items */}
          <div className="hidden md:flex space-x-8">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => onTabChange(item.id)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === item.id
                      ? 'bg-blue-600 text-white'
                      : 'text-terracotta-dark hover:bg-blue-100 hover:text-blue-600'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.label}</span>
                </button>
              );
            })}
          </div>

          {/* User Section */}
          <div className="flex items-center space-x-4">
            <div className="hidden sm:flex items-center space-x-2 bg-yellow/10 px-3 py-1 rounded-full">
              <Crown className="w-4 h-4 text-yellow" />
              <span className="text-sm font-medium text-terracotta-dark">Creator Plan</span>
            </div>
            <Button variant="outline" size="sm" className="border-teal text-teal hover:bg-teal hover:text-white">
              <User className="w-4 h-4 mr-2" />
              Profile
            </Button>
            <Button className="md:hidden" variant="ghost" size="sm">
              <Menu className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
