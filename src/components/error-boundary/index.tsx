import React, { Component, ReactNode } from 'react';
import { Button } from '../ui/button';
import { logger } from '../../lib/logger';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

// Default fallback component
const DefaultFallback = ({ error, onReset }: { error?: Error; onReset: () => void }) => (
  <div className="p-6 rounded-lg bg-gray-900 border border-gray-700">
    <h3 className="text-lg font-medium text-white mb-2">Something went wrong</h3>
    <p className="text-sm text-gray-400 mb-4">
      {import.meta.env.DEV && error ? error.message : 'An unexpected error occurred'}
    </p>
    <Button onClick={onReset} variant="outline" size="sm" className="border-gray-600 text-gray-300 hover:bg-gray-800">
      Try again
    </Button>
  </div>
);

// Main error boundary component
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    logger.error('Error boundary caught error:', { error, errorInfo });
    this.props.onError?.(error, errorInfo);
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }
      return <DefaultFallback error={this.state.error} onReset={this.handleReset} />;
    }

    return this.props.children;
  }
}

// Specialized error boundaries
export const APIErrorBoundary = (props: Omit<ErrorBoundaryProps, 'fallback'>) => (
  <ErrorBoundary
    fallback={
      <div className="p-4 bg-gray-900 border border-gray-700 rounded-md">
        <p className="text-gray-300">Unable to load data. Please try again later.</p>
        <Button
          onClick={() => window.location.reload()}
          variant="outline"
          size="sm"
          className="mt-2 border-gray-600 text-gray-300 hover:bg-gray-800"
        >
          Refresh
        </Button>
      </div>
    }
    {...props}
  />
);

export const FormErrorBoundary = (props: Omit<ErrorBoundaryProps, 'fallback'>) => (
  <ErrorBoundary
    fallback={
      <div className="p-4 bg-gray-900 border border-gray-700 rounded-md">
        <p className="text-gray-300">There was a problem with this form. Please try again.</p>
        <Button
          onClick={() => window.location.reload()}
          variant="outline"
          size="sm"
          className="mt-2 border-gray-600 text-gray-300 hover:bg-gray-800"
        >
          Reset Form
        </Button>
      </div>
    }
    {...props}
  />
);