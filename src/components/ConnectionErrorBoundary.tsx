import React, { useEffect, useState } from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { checkApiConnection } from '@/lib/connection-utils';

interface ConnectionErrorBoundaryProps {
  children: React.ReactNode;
}

const ConnectionErrorBoundary: React.FC<ConnectionErrorBoundaryProps> = ({ children }) => {
  const [connectionError, setConnectionError] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  const checkConnection = async () => {
    setIsChecking(true);
    const result = await checkApiConnection();
    setConnectionError(!result.ok);
    setIsChecking(false);
  };

  useEffect(() => {
    checkConnection();
  }, []);

  if (isChecking) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-white mb-2">Connecting to server...</h2>
          <p className="text-gray-400">Please wait while we establish a connection.</p>
        </div>
      </div>
    );
  }

  if (connectionError) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="max-w-md w-full p-6 bg-gray-800 rounded-lg shadow-lg">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-white text-center mb-4">Connection Error</h2>
          <p className="text-gray-300 text-center mb-6">
            Unable to connect to the server. Please check if the backend server is running.
          </p>
          <div className="bg-gray-700 p-4 rounded-md mb-6">
            <h3 className="text-sm font-medium text-white mb-2">Troubleshooting Steps:</h3>
            <ul className="text-sm text-gray-300 space-y-1 list-disc pl-5">
              <li>Make sure the backend server is running with <code className="bg-gray-600 px-1 rounded">npm run backend</code></li>
              <li>Check that the backend is running on port 3000</li>
              <li>Verify that the proxy settings in vite.config.ts are correct</li>
              <li>Restart both frontend and backend servers</li>
            </ul>
          </div>
          <Button 
            onClick={checkConnection} 
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isChecking ? 'animate-spin' : ''}`} />
            Retry Connection
          </Button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default ConnectionErrorBoundary;
