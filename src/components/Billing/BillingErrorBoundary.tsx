
import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

class BillingErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Billing page error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-dark-gray flex items-center justify-center p-8">
          <div className="bg-red-900/20 border border-red-600 rounded-lg p-6 max-w-lg">
            <h2 className="text-red-400 text-xl font-bold mb-4">Billing Page Error</h2>
            <p className="text-gray-300 mb-4">Something went wrong on the billing page:</p>
            <pre className="text-red-300 text-sm bg-gray-800 p-3 rounded overflow-auto">
              {this.state.error?.message || 'Unknown error'}
            </pre>
            <button 
              onClick={() => this.setState({ hasError: false, error: null })}
              className="mt-4 bg-teal hover:bg-teal/90 text-white px-4 py-2 rounded"
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default BillingErrorBoundary;
