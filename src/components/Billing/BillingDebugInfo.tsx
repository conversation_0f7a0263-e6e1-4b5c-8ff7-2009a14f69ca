
import React from 'react';

interface BillingDebugInfoProps {
  user: any;
  loading: boolean;
  checkingOut: string | null;
  userProfile: any;
  isAnnual: boolean;
  plansCount: number;
}

const BillingDebugInfo = ({ 
  user, 
  loading, 
  checkingOut, 
  userProfile, 
  isAnnual, 
  plansCount 
}: BillingDebugInfoProps) => {
  return (
    <div className="mt-8 p-6 bg-gray-800 rounded-lg">
      <h2 className="text-2xl text-white mb-2">Debug Info:</h2>
      <p className="text-gray-300">• User: {user ? 'Loaded' : 'Not loaded'}</p>
      <p className="text-gray-300">• Loading: {loading ? 'Yes' : 'No'}</p>
      <p className="text-gray-300">• Processing: {checkingOut ? `Yes (${checkingOut})` : 'No'}</p>
      <p className="text-gray-300">• Plans available: {plansCount}</p>
      <p className="text-gray-300">• User Profile: {userProfile ? 'Loaded' : 'Not loaded'}</p>
      <p className="text-gray-300">• Billing Mode: {isAnnual ? 'Annual' : 'Monthly'}</p>
      <p className="text-gray-300">• Environment: {window.location.hostname}</p>
      <p className="text-gray-300">• User Agent: {navigator.userAgent.substring(0, 50)}...</p>
      {userProfile && (
        <p className="text-gray-300">• Subscription: {userProfile.subscription_tier} ({userProfile.subscription_status})</p>
      )}
      <div className="mt-4 p-4 bg-gray-700 rounded">
        <h3 className="text-white text-lg mb-2">Console Debugging</h3>
        <p className="text-gray-300 text-sm">Check browser console for detailed checkout logs when clicking upgrade buttons.</p>
        <p className="text-gray-300 text-sm">Look for logs starting with "=== CHECKOUT" for step-by-step debugging.</p>
      </div>
    </div>
  );
};

export default BillingDebugInfo;
