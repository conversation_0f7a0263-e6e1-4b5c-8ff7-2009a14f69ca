
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Download } from 'lucide-react';

const BillingHistoryCard = () => {
  const billingHistory = [
    { date: '2024-12-01', description: 'Starter Plan - Monthly', amount: '$6.99', status: 'paid' },
    { date: '2024-11-01', description: 'Starter Plan - Monthly', amount: '$6.99', status: 'paid' },
    { date: '2024-10-01', description: 'Starter Plan - Monthly', amount: '$6.99', status: 'paid' },
  ];

  return (
    <Card className="bg-gray-800/50 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white">Billing History</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {billingHistory.map((bill, index) => (
            <div key={index} className="flex items-center justify-between py-3 border-b border-gray-700 last:border-b-0">
              <div>
                <p className="text-white font-medium">{bill.description}</p>
                <p className="text-gray-400 text-sm">{bill.date}</p>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-white font-medium">{bill.amount}</span>
                <Badge variant="outline" className="border-green-500 text-green-500">
                  Paid
                </Badge>
                <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                  <Download className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default BillingHistoryCard;
