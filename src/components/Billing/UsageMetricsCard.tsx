
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { AlertCircle } from 'lucide-react';

interface UsageMetricsCardProps {
  aiUsage: {
    scriptWriter: number;
    titleOptimizer: number;
    performancePredictor: number;
    total: number;
    limit: number;
  };
}

const UsageMetricsCard = ({ aiUsage }: UsageMetricsCardProps) => {
  const usagePercentage = (aiUsage.total / aiUsage.limit) * 100;

  return (
    <Card className="bg-gray-800/50 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white">AI Tools Usage This Month</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between mb-4">
          <span className="text-gray-300">Total Usage</span>
          <span className="text-white font-medium">{aiUsage.total}/{aiUsage.limit} uses ({Math.round(usagePercentage)}%)</span>
        </div>
        <Progress value={usagePercentage} className="h-2" />
        
        {usagePercentage >= 80 && (
          <div className="flex items-center p-3 bg-orange/10 border border-orange/20 rounded-lg">
            <AlertCircle className="w-5 h-5 text-orange mr-2" />
            <span className="text-orange text-sm">
              You've used {Math.round(usagePercentage)}% of your AI credits. Consider upgrading for more usage.
            </span>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="bg-gray-700/30 p-4 rounded-lg">
            <h4 className="text-white font-medium">Script Writer</h4>
            <p className="text-gray-400 text-sm">{aiUsage.scriptWriter} uses</p>
          </div>
          <div className="bg-gray-700/30 p-4 rounded-lg">
            <h4 className="text-white font-medium">Title Optimizer</h4>
            <p className="text-gray-400 text-sm">{aiUsage.titleOptimizer} uses</p>
          </div>
          <div className="bg-gray-700/30 p-4 rounded-lg">
            <h4 className="text-white font-medium">Performance Predictor</h4>
            <p className="text-gray-400 text-sm">{aiUsage.performancePredictor} uses</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default UsageMetricsCard;
