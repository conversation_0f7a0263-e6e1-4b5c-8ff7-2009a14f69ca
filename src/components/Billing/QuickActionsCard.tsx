import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ExternalLink, RefreshCw } from 'lucide-react';
import { useBillingManagement } from '@/hooks/settings/useBillingManagement';
import { useUserProfile } from '@/hooks/settings/useUserProfile';

const QuickActionsCard = () => {
  const { profile } = useUserProfile();
  const { handleManageSubscription } = useBillingManagement(profile);

  return (
    <Card className="bg-gray-800/50 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <Button
          onClick={handleManageSubscription}
          className="w-full bg-deep-red hover:bg-deep-red-dark text-white"
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          Manage Subscription
        </Button>
        <Button variant="outline" className="w-full border-gray-600 text-gray-300">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh Subscription Status
        </Button>
      </CardContent>
    </Card>
  );
};

export default QuickActionsCard;
