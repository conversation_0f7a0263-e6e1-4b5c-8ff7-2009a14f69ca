import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, UserPlus, Minus, Plus } from 'lucide-react';
import { UserProfile } from '@/hooks/settings/useUserProfile';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

interface TeamSeatsCardProps {
  profile: UserProfile | null;
  isLoading: boolean;
  onProfileUpdate: () => Promise<void>;
}

const TeamSeatsCard = ({ profile, isLoading, onProfileUpdate }: TeamSeatsCardProps) => {
  const [additionalSeats, setAdditionalSeats] = useState(0);
  const [isPurchasing, setIsPurchasing] = useState(false);

  // Get current seats info
  const baseSeats = 1; // All plans include 1 seat
  const currentAdditionalSeats = profile?.additional_seats || 0;
  const totalCurrentSeats = baseSeats + currentAdditionalSeats;
  
  // Calculate pricing
  const seatPrice = 5; // $5 per additional seat per month
  const totalAdditionalCost = additionalSeats * seatPrice;
  const newTotalSeats = totalCurrentSeats + additionalSeats;

  // Check if user has an active subscription
  const hasActiveSubscription = profile?.subscription_status === 'active' && profile?.stripe_customer_id;
  const isProPlan = profile?.subscription_tier === 'ai_pro';

  const handleSeatChange = (change: number) => {
    const newValue = Math.max(0, additionalSeats + change);
    setAdditionalSeats(newValue);
  };

  const handlePurchaseSeats = async () => {
    if (!profile?.stripe_customer_id || additionalSeats === 0) return;

    setIsPurchasing(true);
    try {
      // Create checkout session for additional seats
      const { data, error } = await supabase.functions.invoke('create-seats-checkout', {
        body: {
          additional_seats: additionalSeats,
          customer_id: profile.stripe_customer_id
        }
      });

      if (error) throw error;

      if (data?.url) {
        window.location.href = data.url;
      } else {
        throw new Error('No checkout URL returned');
      }
    } catch (error) {
      console.error('Error creating seats checkout:', error);
      toast.error('Failed to start checkout process');
    } finally {
      setIsPurchasing(false);
    }
  };

  if (isLoading) {
    return (
      <Card className="bg-gray-800/50 border-gray-600">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="w-6 h-6 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gray-800/50 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <Users className="w-5 h-5 mr-2" />
          Team Seats
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Current Seats Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-gray-400 text-base">Included Seats</p>
              <p className="text-white text-3xl font-semibold">{baseSeats}</p>
              <p className="text-gray-400 text-sm">Included in your plan</p>
            </div>
            <div>
              <p className="text-gray-400 text-base">Additional Seats</p>
              <p className="text-white text-3xl font-semibold">{currentAdditionalSeats}</p>
              <p className="text-gray-400 text-sm">
                ${currentAdditionalSeats * seatPrice}/month
              </p>
            </div>
          </div>

          {/* Total Seats */}
          <div className="bg-gray-700/50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-base">Total Team Seats</p>
                <p className="text-teal text-2xl font-semibold">{totalCurrentSeats}</p>
              </div>
              <Badge className="bg-teal text-white text-base">
                Active
              </Badge>
            </div>
          </div>

          {/* Add More Seats Section */}
          {hasActiveSubscription && isProPlan ? (
            <div className="border-t border-gray-600 pt-6">
              <div className="flex items-center mb-4">
                <UserPlus className="w-5 h-5 mr-2 text-teal" />
                <h4 className="text-white font-semibold">Add More Team Seats</h4>
              </div>
              
              <div className="space-y-4">
                {/* Seat Counter */}
                <div className="flex items-center justify-between bg-gray-700/50 rounded-lg p-4">
                  <div>
                    <p className="text-white font-medium text-lg">Additional Seats</p>
                    <p className="text-gray-400 text-base">${seatPrice}/seat/month</p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSeatChange(-1)}
                      disabled={additionalSeats === 0}
                      className="border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      <Minus className="w-4 h-4" />
                    </Button>
                    <span className="text-white font-semibold text-xl min-w-[2rem] text-center">
                      {additionalSeats}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSeatChange(1)}
                      className="border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Cost Calculation */}
                {additionalSeats > 0 && (
                  <div className="bg-teal/10 border border-teal/20 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-300 text-base">Additional seats ({additionalSeats})</span>
                      <span className="text-white text-base">${totalAdditionalCost}/month</span>
                    </div>
                    <div className="flex justify-between items-center font-semibold">
                      <span className="text-white text-base">New total seats</span>
                      <span className="text-teal text-base">{newTotalSeats}</span>
                    </div>
                  </div>
                )}

                {/* Purchase Button */}
                <Button
                  onClick={handlePurchaseSeats}
                  disabled={additionalSeats === 0 || isPurchasing}
                  className="w-full bg-teal hover:bg-teal/90 text-white"
                >
                  {isPurchasing ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <UserPlus className="w-4 h-4 mr-2" />
                      Add {additionalSeats} Seat{additionalSeats !== 1 ? 's' : ''} - ${totalAdditionalCost}/month
                    </>
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <div className="border-t border-gray-600 pt-6">
              <div className="bg-orange/10 border border-orange/20 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <UserPlus className="w-5 h-5 mr-2 text-orange" />
                  <h4 className="text-white font-semibold">Upgrade for Team Features</h4>
                </div>
                <p className="text-gray-300 text-base mb-3">
                  {!hasActiveSubscription
                    ? "Team seats are available with an active subscription."
                    : "Team collaboration is available with MCH Pro plan."
                  }
                </p>
                <Button
                  onClick={() => window.location.href = '/pricing'}
                  className="bg-orange hover:bg-orange/90 text-white"
                >
                  {!hasActiveSubscription ? 'View Plans' : 'Upgrade to Pro'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default TeamSeatsCard;
