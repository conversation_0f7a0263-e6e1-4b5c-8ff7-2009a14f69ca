
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface CurrentSubscriptionProps {
  userProfile: any;
  onManageSubscription: () => void;
}

const CurrentSubscription = ({ userProfile, onManageSubscription }: CurrentSubscriptionProps) => {
  if (!userProfile) return null;

  const currentTier = userProfile.subscription_tier || 'starter';

  return (
    <Card className="mb-8 bg-gray-800 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white">Current Subscription</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-gray-400 text-sm">Current Plan</p>
            <p className="text-white font-semibold capitalize">{currentTier}</p>
          </div>
          {userProfile.subscription_status === 'active' && (
            <>
              <div>
                <p className="text-gray-400 text-sm">Billing</p>
                <p className="text-white font-semibold capitalize">{userProfile.billing_period || 'monthly'}</p>
              </div>
              <div>
                <p className="text-gray-400 text-sm">Next Billing Date</p>
                <p className="text-white font-semibold">
                  {userProfile.subscription_end_date 
                    ? new Date(userProfile.subscription_end_date).toLocaleDateString()
                    : 'N/A'
                  }
                </p>
              </div>
            </>
          )}
        </div>
        {userProfile.subscription_status === 'active' && (
          <div className="mt-4">
            <Button 
              onClick={onManageSubscription}
              variant="outline" 
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Manage Subscription
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CurrentSubscription;
