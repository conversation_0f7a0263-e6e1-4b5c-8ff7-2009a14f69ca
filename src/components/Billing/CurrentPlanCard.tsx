
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CreditCard, CheckCircle } from 'lucide-react';
import { getTierById, subscriptionTiers } from '@/config/subscriptionTiers';

interface CurrentPlanCardProps {
  userProfile: any;
}

const CurrentPlanCard = ({ userProfile }: CurrentPlanCardProps) => {
  const currentTier = getTierById(userProfile?.subscription_tier || 'analytics_only');

  return (
    <Card className="bg-gray-800/50 border-gray-600">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <CreditCard className="w-5 h-5 mr-2 text-teal" />
          Current Plan
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Badge className={`${currentTier.color} text-white`}>
              {currentTier.name}
            </Badge>
            <span className="text-white font-medium">{currentTier.price}/month</span>
            {userProfile?.subscription_status === 'trialing' && (
              <Badge variant="outline" className="border-orange text-orange">
                Free Trial
              </Badge>
            )}
          </div>
          <div className="flex space-x-2">
            <Button
              onClick={() => window.location.href = '/pricing'}
              variant="outline"
              size="sm"
              className="border-gray-600 text-gray-300"
            >
              Upgrade Plan
            </Button>
            {userProfile?.subscription_status === 'active' && (
              <Button
                variant="outline"
                size="sm"
                className="border-red-600 text-red-400 hover:bg-red-600/10"
              >
                Cancel
              </Button>
            )}
          </div>
        </div>

        {userProfile?.current_period_end && (
          <p className="text-gray-400 text-sm">
            Next billing: {new Date(userProfile.current_period_end).toLocaleDateString()}
          </p>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div>
            <h4 className="text-white font-medium mb-2">Plan Features:</h4>
            <ul className="space-y-1">
              {currentTier.features.map((feature, index) => (
                <li key={index} className="text-gray-300 text-sm flex items-center">
                  <CheckCircle className="w-3 h-3 mr-2 text-teal" />
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CurrentPlanCard;
