
import React from 'react';

interface BillingLoadingStateProps {
  billingLoading: boolean;
  productsLoading: boolean;
}

const BillingLoadingState = ({ billingLoading, productsLoading }: BillingLoadingStateProps) => {
  return (
    <div className="min-h-screen bg-dark-gray flex items-center justify-center">
      <div className="text-center">
        <div className="w-8 h-8 border-4 border-teal border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-white">Loading billing information...</p>
        {productsLoading && (
          <p className="text-gray-400 text-sm mt-2">Fetching latest pricing from Stripe...</p>
        )}
        <p className="text-gray-500 text-xs mt-4">
          Debug: billingLoading={billingLoading.toString()}, productsLoading={productsLoading.toString()}
        </p>
      </div>
    </div>
  );
};

export default BillingLoadingState;
