import { prefetchDNS, preconnect, preload, preinit } from 'react-dom';

// Preload critical resources
export function preloadCriticalResources() {
  // Preconnect to API domains
  preconnect('https://xpemuejeohbpsxoqvdry.supabase.co');
  preconnect('https://api.openai.com');
  preconnect('https://js.stripe.com');
  
  // Preload critical fonts
  preload('/fonts/Montserrat-Regular.woff2', { as: 'font', type: 'font/woff2' });
  
  // Preinit critical scripts
  preinit('https://js.stripe.com/v3/', { as: 'script' });
}

// Preload resources for specific routes
export function preloadRoute(route: string) {
  switch(route) {
    case 'analytics':
      preload('/assets/chart-library.js', { as: 'script' });
      break;
    case 'ai-tools':
      preconnect('https://api.openai.com');
      break;
  }
}

// Add specific route preloading for dashboard
export function preloadDashboardResources() {
  preload('/assets/dashboard-widgets.js', { as: 'script' });
  preload('/assets/chart-data.json', { as: 'fetch' });
}
