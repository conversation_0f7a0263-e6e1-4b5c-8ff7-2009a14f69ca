/**
 * Validates required environment variables on application startup
 */
export function validateEnvironment() {
  const requiredVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ];
  
  const missing = requiredVars.filter(
    varName => !import.meta.env[varName]
  );
  
  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(', ')}. ` +
      `Please check your .env file and make sure all required variables are defined.`
    );
  }
  
  // Validate URL format
  try {
    new URL(import.meta.env.VITE_SUPABASE_URL);
  } catch (e) {
    throw new Error(`VITE_SUPABASE_URL is not a valid URL: ${import.meta.env.VITE_SUPABASE_URL}`);
  }
  
  return true;
}