import { supabase } from './supabase';

/**
 * Calls a Supabase Edge Function with retry logic
 */
export async function callEdgeFunction<T = any>(
  functionName: string, 
  payload: any, 
  options = { retries: 3, backoff: 300 }
): Promise<T> {
  let lastError: any;
  
  for (let attempt = 0; attempt < options.retries; attempt++) {
    try {
      const { data, error } = await supabase.functions.invoke<T>(
        functionName,
        { body: payload }
      );
      
      if (error) throw error;
      return data;
    } catch (err: any) {
      lastError = err;
      console.warn(`Edge function ${functionName} failed (attempt ${attempt + 1}/${options.retries}):`, err);
      
      // Don't wait on the last attempt
      if (attempt < options.retries - 1) {
        // Exponential backoff
        const delay = options.backoff * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError;
}