import { supabase } from './supabase';

// Authentication helper functions
export const auth = {
  // Get current session with auto-refresh
  getCurrentSession: async () => {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Session error:', error);
      return null;
    }
    
    if (session && isTokenExpiringSoon(session.expires_at)) {
      await auth.refreshSession();
    }
    
    return session;
  },
  
  // Refresh token
  refreshSession: async () => {
    const { data, error } = await supabase.auth.refreshSession();
    if (error) {
      console.error('Error refreshing session:', error);
      return null;
    }
    return data.session;
  }
};

// Helper to check if token expires in less than 5 minutes
function isTokenExpiringSoon(expiresAt) {
  if (!expiresAt) return true;
  
  const expirationTime = new Date(expiresAt).getTime();
  const currentTime = new Date().getTime();
  const fiveMinutesInMs = 5 * 60 * 1000;
  
  return expirationTime - currentTime < fiveMinutesInMs;
}
