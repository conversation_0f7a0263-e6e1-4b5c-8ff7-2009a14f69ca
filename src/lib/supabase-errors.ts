// Create a centralized error handler for Supabase
export function handleSupabaseError(error: any, context: string = 'operation') {
  if (!error) return null;
  
  // Log the error with context
  console.error(`Supabase ${context} error:`, error);
  
  // Map common error codes to user-friendly messages
  const errorMap: Record<string, string> = {
    '42P01': 'Database table not found',
    '23505': 'This record already exists',
    'PGRST116': 'Resource not found',
    'P0001': 'Database constraint violation',
    'auth/invalid-email': 'Invalid email address',
    'auth/user-not-found': 'User not found',
  };
  
  // Return a user-friendly message when possible
  const message = errorMap[error.code] || error.message || 'An unknown error occurred';
  return { message, code: error.code, details: error.details };
}