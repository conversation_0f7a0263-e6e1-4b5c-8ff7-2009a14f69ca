import { useEffect, useState } from 'react';
import { supabase } from './supabase';
import { logger } from './logger';

// Feature flag definitions
export type FeatureFlag = 
  | 'new_dashboard'
  | 'advanced_analytics'
  | 'ai_assistant_v2'
  | 'stripe_billing_v2';

// Local cache of feature flags
let featureFlags: Record<string, boolean> = {};

// Initialize flags from localStorage to prevent flicker
try {
  const cached = localStorage.getItem('feature_flags');
  if (cached) {
    featureFlags = JSON.parse(cached);
  }
} catch (e) {
  logger.error('Failed to parse cached feature flags', e);
}

// Fetch flags from backend
export async function fetchFeatureFlags(userId: string): Promise<Record<string, boolean>> {
  try {
    const { data, error } = await supabase
      .from('feature_flags')
      .select('flag, enabled')
      .eq('user_id', userId);
    
    if (error) throw error;
    
    // Convert to object
    const flags: Record<string, boolean> = {};
    data.forEach(item => {
      flags[item.flag] = item.enabled;
    });
    
    // Cache flags
    featureFlags = flags;
    localStorage.setItem('feature_flags', JSON.stringify(flags));
    
    return flags;
  } catch (e) {
    logger.error('Failed to fetch feature flags', e);
    return featureFlags;
  }
}

// Hook for components
export function useFeatureFlag(flag: FeatureFlag): boolean {
  const [enabled, setEnabled] = useState(featureFlags[flag] || false);
  
  useEffect(() => {
    // Update if flag changes
    const handleFlagChange = () => {
      setEnabled(featureFlags[flag] || false);
    };
    
    // Listen for flag changes
    window.addEventListener('feature_flags_updated', handleFlagChange);
    return () => {
      window.removeEventListener('feature_flags_updated', handleFlagChange);
    };
  }, [flag]);
  
  return enabled;
}

// Component for conditional rendering
export function FeatureFlag({ 
  flag, 
  children, 
  fallback = null 
}: { 
  flag: FeatureFlag; 
  children: React.ReactNode; 
  fallback?: React.ReactNode;
}): JSX.Element | null {
  const enabled = useFeatureFlag(flag);
  return enabled ? <>{children}</> : <>{fallback}</>;
}