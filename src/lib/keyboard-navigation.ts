import { useEffect, useCallback } from 'react';

type KeyHandler = (event: KeyboardEvent) => void;
type KeyMap = Record<string, KeyHandler>;

export function useKeyboardNavigation(keyMap: KeyMap, isEnabled = true) {
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // Don't trigger shortcuts when user is typing in form fields
      if (
        ['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName) ||
        (event.target as HTMLElement).isContentEditable
      ) {
        return;
      }

      const key = event.key.toLowerCase();
      
      // Handle keyboard shortcuts (e.g., 'ctrl+s', 'shift+/')
      const isCtrl = event.ctrlKey || event.metaKey;
      const isShift = event.shiftKey;
      const isAlt = event.altKey;
      
      const keyCombo = [
        isCtrl ? 'ctrl' : '',
        isShift ? 'shift' : '',
        isAlt ? 'alt' : '',
        key
      ].filter(Boolean).join('+');
      
      const handler = keyMap[key] || keyMap[keyCombo];
      
      if (handler) {
        event.preventDefault();
        handler(event);
      }
    },
    [keyMap]
  );

  useEffect(() => {
    if (isEnabled) {
      window.addEventListener('keydown', handleKeyDown);
    }
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, isEnabled]);
}

// Usage example:
// useKeyboardNavigation({
//   'j': () => navigateToNextItem(),
//   'k': () => navigateToPreviousItem(),
//   'ctrl+n': () => createNewItem(),
//   'shift+/': () => setShowHelpModal(true),
// });