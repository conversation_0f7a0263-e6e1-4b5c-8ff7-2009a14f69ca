/**
 * Utility functions for checking connections to backend services
 */

// Check if the backend API is reachable
export async function checkApiConnection(endpoint = '/health') {
  try {
    const response = await fetch(endpoint, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
      // Short timeout to avoid long waits
      signal: AbortSignal.timeout(5000)
    });
    
    return {
      ok: response.ok,
      status: response.status,
      message: response.ok ? 'Connected' : `HTTP ${response.status}`
    };
  } catch (error) {
    console.error('API connection check failed:', error);
    return {
      ok: false,
      status: 0,
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Check if Supabase is reachable
export async function checkSupabaseConnection() {
  try {
    // Simple health check to Supabase
    const response = await fetch('/api/supabase-health', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
      signal: AbortSignal.timeout(5000)
    });
    
    return {
      ok: response.ok,
      message: response.ok ? 'Connected' : `Failed to connect to Supabase`
    };
  } catch (error) {
    console.error('Supabase connection check failed:', error);
    return {
      ok: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
