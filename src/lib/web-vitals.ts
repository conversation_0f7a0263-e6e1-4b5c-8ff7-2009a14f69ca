import { onCLS, onFID, onLCP, onFCP, onTTFB } from 'web-vitals';
import { logger } from './logger';

export function reportWebVitals() {
  // Only in production
  if (!import.meta.env.PROD) return;
  
  onCLS(metric => sendToAnalytics('CLS', metric));
  onFID(metric => sendToAnalytics('FID', metric));
  onLCP(metric => sendToAnalytics('LCP', metric));
  onFCP(metric => sendToAnalytics('FCP', metric));
  onTTFB(metric => sendToAnalytics('TTFB', metric));
}

function sendToAnalytics(name: string, metric: any) {
  // Log locally
  logger.info(`Web Vital: ${name}`, metric);
  
  // Send to your analytics service
  const body = JSON.stringify({
    name,
    value: metric.value,
    id: metric.id,
    page: window.location.pathname,
  });
  
  // Use Navigator.sendBeacon for non-blocking analytics
  if (navigator.sendBeacon) {
    navigator.sendBeacon('/api/analytics/vitals', body);
  } else {
    fetch('/api/analytics/vitals', {
      body,
      method: 'POST',
      keepalive: true,
    });
  }
}