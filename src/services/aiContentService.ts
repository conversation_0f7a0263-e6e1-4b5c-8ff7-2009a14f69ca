
import { supabase } from '@/lib/supabase';
import { ContentPillar } from '@/components/Ideas/types';

interface AIContentRequest {
  type: string;
  prompt: string;
  creditsUsed: number;
  userId: string;
}

interface GenerateTopicsRequest {
  pillarId: string;
  pillars: ContentPillar[];
}

interface GenerateSEORequest {
  title: string;
  topic: string;
  pillarId: string;
  pillars: ContentPillar[];
}

interface GenerateScriptRequest {
  title: string;
  description: string;
  pillar: string;
  videoLength: string;
  tone: string;
}

export class AIContentService {
  private static async logAIUsage(userId: string, type: string, creditsUsed: number) {
    try {
      await supabase.functions.invoke('log-ai-usage', {
        body: { user_id: userId, usage_type: type, credits_used: creditsUsed }
      });
    } catch (error) {
      console.error('Failed to log AI usage:', error);
    }
  }

  static async generateContent({ type, prompt, creditsUsed, userId }: AIContentRequest): Promise<string[]> {
    try {
      const { data, error } = await supabase.functions.invoke('generate-ai-content', {
        body: { prompt, type, user_id: userId }
      });

      if (error) throw error;

      await this.logAIUsage(userId, type, creditsUsed);
      
      return Array.isArray(data.content) ? data.content : [data.content];
    } catch (error) {
      console.error(`Failed to generate ${type}:`, error);
      throw error;
    }
  }

  static async generateTopics({ pillarId, pillars }: GenerateTopicsRequest, userId: string): Promise<any[]> {
    const pillar = pillars.find(p => p.id === pillarId);
    if (!pillar) throw new Error('Pillar not found');

    const prompt = `Generate 5 specific video topic ideas for the "${pillar.name}" content pillar. Return as JSON array with objects containing: title, description, reason.`;
    
    try {
      const { data, error } = await supabase.functions.invoke('generate-ai-content', {
        body: { prompt, type: 'topic-generation', user_id: userId }
      });

      if (error) throw error;

      await this.logAIUsage(userId, 'topic-generation', 3);
      
      return data.content || [];
    } catch (error) {
      console.error('Failed to generate topics:', error);
      throw error;
    }
  }

  static async generateScript({ title, description, pillar, videoLength, tone }: GenerateScriptRequest, userId: string): Promise<{ script: string; estimatedReadTime: string; wordCount: number }> {
    try {
      const { data, error } = await supabase.functions.invoke('generate-script', {
        body: { title, description, pillar, videoLength, tone }
      });

      if (error) throw error;
      
      return data;
    } catch (error) {
      console.error('Failed to generate script:', error);
      throw error;
    }
  }

  static async generateSEOTags({ title, topic, pillarId, pillars }: GenerateSEORequest, userId: string): Promise<{ tags: string[], hashtags: string }> {
    const pillar = pillars.find(p => p.id === pillarId);
    
    const prompt = `Generate SEO tags and hashtags for YouTube video titled "${title}" about "${topic}" in the "${pillar?.name || 'general'}" category.

Please return a JSON object with:
- "tags": array of 15-20 relevant YouTube tags (single words or short phrases)
- "hashtags": string of 10-15 hashtags with # symbols separated by spaces

Example format:
{
  "tags": ["video editing", "tutorial", "beginner", "youtube", "content creation"],
  "hashtags": "#videoediting #tutorial #youtube #contentcreator #beginners"
}

Focus on:
- Relevant keywords for the topic
- Popular search terms
- Category-specific tags
- Trending hashtags in this niche`;

    try {
      const { data, error } = await supabase.functions.invoke('generate-ai-content', {
        body: { prompt, type: 'seo-generation', user_id: userId }
      });

      if (error) throw error;

      await this.logAIUsage(userId, 'seo-generation', 1);
      
      // Handle the response properly
      const seoData = data.content;
      
      return {
        tags: seoData.tags || [],
        hashtags: seoData.hashtags || ''
      };
    } catch (error) {
      console.error('Failed to generate SEO tags:', error);
      return { tags: [], hashtags: '' };
    }
  }
}
