
import { supabase } from '@/lib/supabase';
import { DatabasePillar, NewPillar, ContentPillar } from '@/types/pillar';
import { toast } from 'sonner';

// Input validation and sanitization functions
const sanitizeText = (text: string): string => {
  return text.trim().replace(/<[^>]*>/g, '').substring(0, 255);
};

const validatePillarName = (name: string): boolean => {
  const sanitized = sanitizeText(name);
  return sanitized.length >= 1 && sanitized.length <= 100;
};

const validateTargetPercentage = (percentage: number): boolean => {
  return percentage >= 0 && percentage <= 100 && Number.isInteger(percentage);
};

const validateColor = (color: string): boolean => {
  const colorRegex = /^#[0-9A-Fa-f]{6}$/;
  return colorRegex.test(color);
};

export const fetchPillars = async (userId: string): Promise<DatabasePillar[]> => {
  if (!userId || typeof userId !== 'string') {
    toast.error('Invalid user session');
    return [];
  }

  const { data, error } = await supabase
    .from('content_pillars')
    .select('*')
    .eq('user_id', userId);

  if (error) {
    console.error('Error fetching pillars:', error);
    toast.error('Failed to fetch pillars');
    return [];
  }

  return data || [];
};

export const createPillar = async (userId: string, newPillar: NewPillar): Promise<boolean> => {
  if (!userId || typeof userId !== 'string') {
    toast.error('Invalid user session');
    return false;
  }

  // Validate and sanitize input
  const sanitizedName = sanitizeText(newPillar.name);
  
  if (!validatePillarName(sanitizedName)) {
    toast.error('Pillar name must be between 1 and 100 characters');
    return false;
  }

  if (!validateTargetPercentage(newPillar.target_percentage)) {
    toast.error('Target percentage must be between 0 and 100');
    return false;
  }

  if (!validateColor(newPillar.color)) {
    toast.error('Invalid color format');
    return false;
  }

  const { error } = await supabase
    .from('content_pillars')
    .insert({
      user_id: userId,
      name: sanitizedName,
      target_percentage: newPillar.target_percentage,
      color: newPillar.color
    });

  if (error) {
    console.error('Error creating pillar:', error);
    toast.error('Failed to create pillar');
    return false;
  } else {
    toast.success('Pillar created successfully!');
    return true;
  }
};

export const updatePillar = async (userId: string, pillar: ContentPillar): Promise<boolean> => {
  if (!userId || typeof userId !== 'string') {
    toast.error('Invalid user session');
    return false;
  }

  // Validate and sanitize input
  const sanitizedName = sanitizeText(pillar.name);
  
  if (!validatePillarName(sanitizedName)) {
    toast.error('Pillar name must be between 1 and 100 characters');
    return false;
  }

  if (!validateTargetPercentage(pillar.target_percentage)) {
    toast.error('Target percentage must be between 0 and 100');
    return false;
  }

  if (!validateColor(pillar.color)) {
    toast.error('Invalid color format');
    return false;
  }

  const { error } = await supabase
    .from('content_pillars')
    .update({
      name: sanitizedName,
      target_percentage: pillar.target_percentage,
      color: pillar.color
    })
    .eq('id', pillar.id)
    .eq('user_id', userId);

  if (error) {
    console.error('Error updating pillar:', error);
    toast.error('Failed to update pillar');
    return false;
  } else {
    toast.success('Pillar updated successfully!');
    return true;
  }
};

export const deletePillar = async (userId: string, pillarId: string, pillarName: string): Promise<void> => {
  if (!userId || typeof userId !== 'string') {
    toast.error('Invalid user session');
    return;
  }

  if (!pillarId || typeof pillarId !== 'string') {
    toast.error('Invalid pillar ID');
    return;
  }

  const sanitizedPillarName = sanitizeText(pillarName);

  const { error } = await supabase
    .from('content_pillars')
    .delete()
    .eq('id', pillarId)
    .eq('user_id', userId);

  if (error) {
    console.error('Error deleting pillar:', error);
    toast.error('Failed to delete pillar');
  } else {
    toast.success(`"${sanitizedPillarName}" pillar deleted successfully!`);
  }
};
