import { supabase } from '@/lib/supabase';

interface YouTubeVideoInfo {
  title: string;
  thumbnail_url: string;
  author_name: string;
  width: number;
  height: number;
  video_id: string;
}

export const extractVideoId = (url: string): string | null => {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /youtube\.com\/watch\?v=([^&\n?#]+)/,
    /youtu\.be\/([^&\n?#]+)/,
    /youtube\.com\/embed\/([^&\n?#]+)/
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
};

export const fetchVideoInfo = async (url: string): Promise<YouTubeVideoInfo> => {
  const videoId = extractVideoId(url);
  if (!videoId) {
    throw new Error('Invalid YouTube URL');
  }

  try {
    const response = await fetch(
      `https://www.youtube.com/oembed?url=${encodeURIComponent(url)}&format=json`
    );

    if (!response.ok) {
      throw new Error('Failed to fetch video information');
    }

    const data = await response.json();
    return {
      ...data,
      video_id: videoId
    };
  } catch (error) {
    console.error('Error fetching video info:', error);
    throw new Error('Failed to fetch video information');
  }
};

export const fetchTranscript = async (videoId: string): Promise<string> => {
  try {
    const { data, error } = await supabase.functions.invoke('youtube-transcript', {
      body: { videoId }
    });

    if (error) {
      throw new Error(error.message);
    }

    if (!data?.transcript) {
      throw new Error('No transcript available for this video');
    }

    return data.transcript;
  } catch (error) {
    console.error('Error fetching transcript:', error);
    throw new Error('Failed to fetch video transcript');
  }
};

export const analyzeVideo = async (
  transcript: string,
  metadata: { title: string; author: string }
): Promise<any> => {
  try {
    const { data, error } = await supabase.functions.invoke('analyze-youtube', {
      body: {
        transcript,
        metadata
      }
    });

    if (error) {
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error analyzing video:', error);
    throw new Error('Failed to analyze video');
  }
}; 