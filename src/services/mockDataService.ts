// Mock Data Service for Demo Videos
// Set ENABLE_MOCK_DATA to false to use real data
const ENABLE_MOCK_DATA = false;

// Utility functions to check if mock data should be used
export const shouldUseMockData = () => false; // Always return false

// Export empty mock data for compatibility
export const mockVideos = [];
export const mockPillars = [];
export const mockUserProfile = null; // Set to null instead of empty object

// Add a console log to confirm mock data is disabled
console.log('🚫 Mock data service is completely disabled');
