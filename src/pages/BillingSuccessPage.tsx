
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle2, ArrowRight } from 'lucide-react';
import { supabase } from '@/lib/supabase';

const BillingSuccessPage = () => {
  const navigate = useNavigate();
  const [isCheckingSubscription, setIsCheckingSubscription] = useState(true);

  useEffect(() => {
    const checkSubscription = async () => {
      try {
        // Make a call to check-subscription to update our local state
        await supabase.functions.invoke('check-subscription');
        setIsCheckingSubscription(false);
      } catch (error) {
        console.error('Error checking subscription:', error);
        setIsCheckingSubscription(false);
      }
    };

    checkSubscription();
  }, []);

  return (
    <div className="min-h-screen bg-dark-gray flex flex-col items-center justify-center p-4">
      <Card className="max-w-md w-full bg-gray-800 border-gray-700">
        <CardHeader className="text-center pb-2">
          <div className="flex justify-center mb-4">
            <div className="rounded-full bg-green-500/20 p-3">
              <CheckCircle2 className="w-12 h-12 text-green-500" />
            </div>
          </div>
          <CardTitle className="text-2xl text-white">Subscription Successful!</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-gray-300 mb-6">
            Thank you for subscribing to MyContentHub. Your account has been upgraded and you now have access to all the features included in your subscription.
          </p>
          
          {isCheckingSubscription ? (
            <div className="flex justify-center my-4">
              <div className="w-6 h-6 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : (
            <p className="text-teal font-medium">Your subscription has been activated!</p>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button 
            onClick={() => navigate('/dashboard')}
            className="bg-teal hover:bg-teal/90 text-white"
          >
            Go to Dashboard
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default BillingSuccessPage;
