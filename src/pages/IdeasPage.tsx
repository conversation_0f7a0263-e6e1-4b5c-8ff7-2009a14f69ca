
import React from 'react';
import IdeasPageBackground from '@/components/Ideas/IdeasPageBackground';
import IdeasPageTabs from '@/components/Ideas/IdeasPageTabs';
import IdeasPageDialogs from '@/components/Ideas/IdeasPageDialogs';
import PageHeader from '@/components/Layout/PageHeader';
import { useIdeasPageData } from '@/hooks/ideas/useIdeasPageData';

const IdeasPage = () => {
  const {
    activeTab,
    setActiveTab,
    pillars,
    isDialogOpen,
    setIsDialogOpen,
    editingIdea,
    setEditingIdea,
    newIdea,
    setNewIdea,
    addIdea,
    updateIdea,
    updateIdeaTitle,
    deleteIdea,
    moveToCalendar,
    searchTerm,
    setSearchTerm,
    filterPillar,
    setFilterPillar,
    filterPriority,
    setFilterPriority,
    filterStatus,
    setFilterStatus,
    groupedIdeas,
    handleStatusChange
  } = useIdeasPageData();

  return (
    <div className="relative min-h-screen">
      <IdeasPageBackground />

      <div className="relative z-10">
        <PageHeader
          title="Ideas Bank"
          subtitle="Capture, organize, and develop your YouTube video ideas"
        />

        <div className="pb-12">
          <IdeasPageTabs
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            groupedIdeas={groupedIdeas}
            pillars={pillars}
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            filterPillar={filterPillar}
            setFilterPillar={setFilterPillar}
            filterPriority={filterPriority}
            setFilterPriority={setFilterPriority}
            filterStatus={filterStatus}
            setFilterStatus={setFilterStatus}
            onEditIdea={setEditingIdea}
            onDeleteIdea={deleteIdea}
            onMoveToCalendar={moveToCalendar}
            onTitleUpdate={updateIdeaTitle}
            onStatusChange={handleStatusChange}
            onAddIdea={() => setIsDialogOpen(true)}
          />
        </div>
      </div>

      <IdeasPageDialogs
        isDialogOpen={isDialogOpen}
        setIsDialogOpen={setIsDialogOpen}
        editingIdea={editingIdea}
        setEditingIdea={setEditingIdea}
        newIdea={newIdea}
        setNewIdea={setNewIdea}
        addIdea={addIdea}
        updateIdea={updateIdea}
        pillars={pillars}
      />
    </div>
  );
};

export default IdeasPage;
