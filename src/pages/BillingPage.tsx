import React, { useState } from 'react';
import BillingPageBackground from '@/components/Billing/BillingPageBackground';
import BillingHeader from '@/components/Billing/BillingHeader';
import BillingHistoryCard from '@/components/Billing/BillingHistoryCard';
import BillingErrorBoundary from '@/components/Billing/BillingErrorBoundary';
import BillingDebugInfo from '@/components/Billing/BillingDebugInfo';
import TeamSeatsCard from '@/components/Billing/TeamSeatsCard';
import PricingTierCard from '@/components/Pricing/PricingTierCard';
import { useUserProfile } from '@/hooks/settings/useUserProfile';
import { useBillingManagement } from '@/hooks/settings/useBillingManagement';
import { usePricingActions } from '@/hooks/usePricingActions';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Crown, RefreshCw, ArrowUpCircle } from 'lucide-react';
import { subscriptionTiers } from '@/config/subscriptionTiers';

const BillingPage = () => {
  const { profile, isLoading, refetchProfile } = useUserProfile();
  const {
    isRefreshing,
    getTrialDaysLeft,
    refreshSubscription
  } = useBillingManagement(profile);
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');
  const { loadingTier, handleSubscribe, isCurrentPlan } = usePricingActions();

  // Mock data for UI display
  const user = { id: '123' };
  const checkingOut = null;
  const isAnnual = billingPeriod === 'yearly';
  const plansCount = 3;

  // Plan name mapping function to ensure consistency
  const getPlanDisplayName = (tierKey: string) => {
    const planNames = {
      'analytics_only': 'MCH Starter',
      'ai_lite': 'MCH Lite',
      'ai_pro': 'MCH Pro'
    };
    return planNames[tierKey as keyof typeof planNames] || 'Free';
  };

  // Function to smoothly scroll to subscription plans section
  const scrollToPlans = () => {
    const plansSection = document.getElementById('subscription-plans-section');
    if (plansSection) {
      plansSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    }
  };

  // Pricing tiers data
  const tiers = subscriptionTiers;

  return (
    <div className="relative min-h-screen app-background">
      <BillingPageBackground />
      <BillingErrorBoundary>
        <div className="relative z-10 py-8">
          <BillingHeader />
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-8">
            {/* Current Subscription Card */}
            <Card className="lg:col-span-2 bg-gray-800/50 border-gray-600">
              <CardHeader>
                <CardTitle className="text-white">Current Subscription</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center p-8">
                    <div className="w-6 h-6 border-2 border-teal border-t-transparent rounded-full animate-spin"></div>
                  </div>
                ) : profile ? (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-gray-400 text-base">Current Plan</p>
                      <p className="text-white font-semibold text-lg">{getPlanDisplayName(profile.subscription_tier || '')}</p>
                    </div>
                    {profile.subscription_status === 'active' && (
                      <>
                        <div>
                          <p className="text-gray-400 text-base">Billing</p>
                          <p className="text-white font-semibold text-lg capitalize">{profile.billing_period || 'monthly'}</p>
                        </div>
                        <div>
                          <p className="text-gray-400 text-base">Next Billing Date</p>
                          <p className="text-white font-semibold text-lg">
                            {profile.subscription_end_date
                              ? new Date(profile.subscription_end_date).toLocaleDateString()
                              : 'N/A'}
                          </p>
                        </div>
                      </>
                    )}
                    
                    {profile.subscription_status === 'trialing' && (
                      <div className="md:col-span-2">
                        <p className="text-gray-400 text-base">Trial Status</p>
                        <div className="flex items-center">
                          <Badge className="bg-orange text-white text-base">Trial</Badge>
                          <span className="text-white ml-2 text-lg">
                            {getTrialDaysLeft()} days remaining
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-300 text-base">Unable to load subscription information</p>
                )}
                
                <div className="flex flex-wrap gap-3 mt-6">
                  {/* Change Plan button for active subscribers */}
                  {profile?.subscription_status === 'active' && profile?.stripe_customer_id && (
                    <Button
                      onClick={scrollToPlans}
                      className="bg-teal hover:bg-cyan-400 text-white shadow-lg text-base"
                    >
                      <ArrowUpCircle className="w-4 h-4 mr-2" />
                      Change Plan
                    </Button>
                  )}

                  {/* View Plans button for users without active subscription */}
                  {!profile?.stripe_customer_id && (
                    <Button
                      onClick={() => window.location.href = '/pricing'}
                      className="bg-orange hover:bg-orange-400 text-white shadow-lg text-base"
                    >
                      <Crown className="w-4 h-4 mr-2" />
                      View Plans
                    </Button>
                  )}

                  <Button
                    onClick={() => refreshSubscription(refetchProfile)}
                    disabled={isRefreshing}
                    className="bg-blue-600 hover:bg-blue-500 text-white border-0 shadow-lg text-base"
                  >
                    <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                    Refresh Status
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            {/* Billing Info Card */}
            <Card className="bg-gray-800/50 border-gray-600">
              <CardHeader>
                <CardTitle className="text-white">AI Credits</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <p className="text-gray-400 text-base">Monthly Allocation</p>
                    <p className="text-white text-3xl font-semibold">
                      {profile?.subscription_credits || 50} credits
                    </p>
                  </div>

                  <div>
                    <p className="text-gray-400 text-base">Used This Month</p>
                    <p className="text-white text-3xl font-semibold">
                      {profile?.ai_credits_used || 0} credits
                    </p>
                  </div>

                  <div>
                    <p className="text-gray-400 text-base">Remaining</p>
                    <p className="text-teal text-3xl font-semibold">
                      {profile ? ((profile.subscription_credits || 0) - (profile.ai_credits_used || 0)) : 0} credits
                    </p>
                  </div>
                  
                  <Button
                    onClick={() => window.location.href = '/pricing'}
                    className="w-full mt-2 bg-teal hover:bg-cyan-400 text-white text-base shadow-lg border-0"
                  >
                    Need More Credits?
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Team Seats Section */}
          <div className="mt-8">
            <TeamSeatsCard
              profile={profile}
              isLoading={isLoading}
              onProfileUpdate={refetchProfile}
            />
          </div>

          {/* Pricing Plans Section */}
          <div id="subscription-plans-section" className="mt-12">
            <Card className="bg-gray-800/50 border-gray-600">
              <CardHeader>
                <CardTitle className="text-white">Subscription Plans</CardTitle>
              </CardHeader>
              <CardContent>
                {/* Billing Period Toggle */}
                <div className="flex justify-center mb-8">
                  <div className="bg-gray-800 p-1 rounded-full inline-flex">
                    <button
                      className={`px-4 py-2 rounded-full transition-colors text-base ${
                        billingPeriod === 'monthly' ? 'bg-teal text-white' : 'text-gray-400 hover:text-white'
                      }`}
                      onClick={() => setBillingPeriod('monthly')}
                    >
                      Monthly
                    </button>
                    <button
                      className={`px-4 py-2 rounded-full transition-colors flex items-center text-base ${
                        billingPeriod === 'yearly' ? 'bg-teal text-white' : 'text-gray-400 hover:text-white'
                      }`}
                      onClick={() => setBillingPeriod('yearly')}
                    >
                      Yearly
                      <span className="ml-1 text-sm bg-green-500 text-white px-2 py-0.5 rounded-full">
                        Save 2 months!
                      </span>
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
                  {tiers.map((tier) => (
                    <PricingTierCard
                      key={tier.id}
                      tier={tier}
                      isAnnual={isAnnual}
                      isCurrentPlan={isCurrentPlan(tier.id)}
                      isLoading={loadingTier === tier.id}
                      onSubscribe={(tierId) => handleSubscribe(tierId, isAnnual)}
                    />
                  ))}
                </div>

                <div className="text-center mt-8">
                  <p className="text-gray-400 text-base font-medium">
                    All plans include a 7-day free trial • Cancel anytime
                  </p>
                  {isAnnual && (
                    <p className="text-teal text-base font-medium mt-2">
                      Yearly plans save you 2 months compared to monthly billing
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Billing History */}
          <div className="mt-8">
            <BillingHistoryCard />
          </div>
          
          {/* Debug Info (only in development) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-8">
              <BillingDebugInfo
                user={user}
                loading={isLoading}
                checkingOut={checkingOut}
                userProfile={profile}
                isAnnual={isAnnual}
                plansCount={plansCount}
              />
            </div>
          )}
        </div>
      </BillingErrorBoundary>
    </div>
  );
};

export default BillingPage;
