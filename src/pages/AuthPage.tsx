
import React, { useState } from 'react';
import AuthForm from '@/components/Auth/AuthForm';
import PageBackground from '@/components/Layout/PageBackground';

const AuthPage = () => {
  const [mode, setMode] = useState<'signin' | 'signup'>('signin');

  return (
    <div className="relative min-h-screen flex flex-col items-center justify-center p-4">
      {/* Background */}
      <PageBackground />
      
      {/* Content container */}
      <div className="relative z-10 flex flex-col items-center max-w-md w-full">
        {/* Logo container - moved up with negative margin */}
        <div className="flex flex-row justify-center mb-24 relative" style={{ marginTop: "-100px" }}>
          {/* Icon logo */}
          <img 
            src="/MCH_Icon_FullColor.svg" 
            alt="MCH Icon Logo" 
            style={{ width: '600px', height: '600px', maxWidth: 'none', objectFit: 'contain' }}
          />
          
          {/* Text logo - positioned with custom margin to align tagline */}
          <img 
            src="/MCH_White_Logotype.svg" 
            alt="MCH Text Logo" 
            style={{ 
              width: '800px', 
              height: 'auto', 
              maxWidth: 'none', 
              objectFit: 'contain',
              marginLeft: '-50px',
              marginTop: '20px'
            }}
          />
        </div>
        
        {/* Form - increased width by 50% and kept the same positioning */}
        <div className="w-full mt-24" style={{ maxWidth: "900px" }}>
          <AuthForm 
            mode={mode} 
            onToggleMode={() => setMode(mode === 'signin' ? 'signup' : 'signin')} 
          />
        </div>
      </div>
    </div>
  );
};

export default AuthPage;
