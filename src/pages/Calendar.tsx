import React from 'react';

export default function Calendar() {
  // Generate days for the current month
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();
  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  
  const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);
  
  // Sample events
  const events = [
    { day: 5, title: 'How to Edit Videos Faster', pillar: 'Technical' },
    { day: 12, title: 'Growing Your Channel in 2023', pillar: 'Growth' },
    { day: 18, title: 'Camera Settings for Perfect Videos', pillar: 'Technical' },
    { day: 25, title: 'Monetization Strategies', pillar: 'Business' },
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Content Calendar</h1>
        <div className="flex space-x-2">
          <button className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
            Previous
          </button>
          <button className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
            Next
          </button>
          <button className="px-4 py-2 bg-teal-500 text-white rounded hover:bg-teal-600 ml-2">
            Schedule Video
          </button>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="p-4 bg-gray-50 border-b">
          <h2 className="text-xl font-semibold">
            {new Date(currentYear, currentMonth).toLocaleString('default', { month: 'long' })} {currentYear}
          </h2>
        </div>
        
        <div className="grid grid-cols-7 gap-px bg-gray-200">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <div key={day} className="bg-gray-50 p-2 text-center text-sm font-medium text-gray-500">
              {day}
            </div>
          ))}
          
          {/* Empty cells for days before the 1st of the month */}
          {Array.from({ length: new Date(currentYear, currentMonth, 1).getDay() }, (_, i) => (
            <div key={`empty-${i}`} className="bg-white p-2 h-32"></div>
          ))}
          
          {/* Calendar days */}
          {days.map((day) => {
            const dayEvents = events.filter(event => event.day === day);
            const isToday = day === currentDate.getDate();
            
            return (
              <div 
                key={day} 
                className={`bg-white p-2 h-32 border-t ${isToday ? 'bg-blue-50' : ''}`}
              >
                <div className={`text-right ${isToday ? 'font-bold text-blue-600' : ''}`}>
                  {day}
                </div>
                
                {dayEvents.map((event, index) => (
                  <div 
                    key={index}
                    className="mt-1 p-1 text-xs rounded bg-teal-100 text-teal-800 cursor-pointer hover:bg-teal-200"
                  >
                    {event.title}
                  </div>
                ))}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}