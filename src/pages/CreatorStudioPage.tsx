
import React from 'react';
import PageBackground from '@/components/Layout/PageBackground';
import CreatorStudioContent from '@/components/CreatorStudio/CreatorStudioContent';
import PageHeader from '@/components/Layout/PageHeader';
import useCreatorStudioData from '@/hooks/useCreatorStudioData';

const CreatorStudioPage = () => {
  const { data, loading, error } = useCreatorStudioData();
  
  return (
    <div className="relative min-h-screen">
      <PageBackground />
      
      <div className="relative z-10">
        <PageHeader
          title="Creator Studio"
          subtitle="Record, upload, and manage your video content"
        />
        
        <div className="pb-12">
          {loading ? (
            <div className="flex justify-center py-12">
              <div className="w-8 h-8 border-4 border-teal border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-400">Error loading creator studio data</p>
            </div>
          ) : (
            <CreatorStudioContent {...data} />
          )}
        </div>
      </div>
    </div>
  );
};

export default CreatorStudioPage;
