
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Youtube, AlertCircle, CheckCircle, RefreshCw, ExternalLink } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { useYouTubeRefresh } from '@/hooks/useYouTubeRefresh';

const YouTubeCallbackPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState('');
  const [retryCount, setRetryCount] = useState(0);
  const [showSetupInstructions, setShowSetupInstructions] = useState(false);
  const { refreshYouTubeConnection } = useYouTubeRefresh();

  const handleCallback = async () => {
    try {
      const code = searchParams.get('code');
      const error = searchParams.get('error');
      const state = searchParams.get('state');

      console.log('YouTube OAuth callback params:', { 
        hasCode: !!code, 
        error, 
        hasState: !!state 
      });

      if (error) {
        if (error === 'access_denied') {
          throw new Error('Access was denied. Please try connecting again and grant the necessary permissions.');
        }
        throw new Error(`OAuth error: ${error}`);
      }

      if (!code) {
        throw new Error('No authorization code received from YouTube');
      }

      if (!state) {
        throw new Error('No state parameter received from YouTube OAuth');
      }

      console.log('Processing YouTube OAuth callback...');

      // Get current user session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError || !session) {
        throw new Error('User not authenticated. Please log in and try again.');
      }

      const redirectUri = `${window.location.origin}/auth/youtube/callback`;

      console.log('Calling YouTube OAuth edge function...');

      // Call the Supabase Edge Function
      const { data, error: functionError } = await supabase.functions.invoke('youtube-oauth', {
        body: {
          code: code,
          redirectUri: redirectUri,
          state: state
        },
      });

      if (functionError) {
        console.error('Edge function error:', functionError);
        
        if (functionError.message?.includes('403') || functionError.message?.includes('access_not_configured')) {
          setShowSetupInstructions(true);
          throw new Error('YouTube API access is not properly configured. This requires setup in Google Cloud Console.');
        }
        
        if (functionError.message?.includes('invalid_client')) {
          setShowSetupInstructions(true);
          throw new Error('Invalid client credentials. Please verify Google Cloud Console configuration.');
        }

        if (functionError.message?.includes('redirect_uri_mismatch')) {
          setShowSetupInstructions(true);
          throw new Error(`Redirect URI mismatch. Please ensure your Google Cloud Console has: ${redirectUri}`);
        }
        
        throw new Error(`Connection failed: ${functionError.message}`);
      }

      if (!data?.success) {
        console.error('Function returned error:', data);
        throw new Error(data?.error || 'Unknown error occurred during YouTube connection');
      }

      console.log('🎉 YouTube connection successful:', data.channelData);
      console.log('📊 Channel data received:', {
        channelId: data.channelData?.youtube_channel_id,
        channelName: data.channelData?.youtube_channel_name,
        hasAccessToken: !!data.channelData?.youtube_access_token,
        hasRefreshToken: !!data.channelData?.youtube_refresh_token,
        subscriberBaseline: data.channelData?.youtube_subscriber_baseline
      });

      setStatus('success');

      // Set flag to indicate OAuth completion
      localStorage.setItem('youtube_oauth_completed', new Date().toISOString());

      // Refresh YouTube connection data to get fresh tokens
      console.log('🔄 Starting cache refresh...');
      await refreshYouTubeConnection();
      console.log('✅ Cache refresh completed');

      // Verify the data was stored correctly by checking the database
      console.log('🔍 Verifying data storage...');
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          const { data: userData, error: fetchError } = await supabase
            .from('users')
            .select('youtube_channel_id, youtube_access_token, youtube_refresh_token, youtube_channel_name')
            .eq('id', session.user.id)
            .single();

          if (fetchError) {
            console.error('❌ Error verifying data storage:', fetchError);
          } else {
            console.log('✅ Data verification successful:', {
              hasChannelId: !!userData?.youtube_channel_id,
              hasAccessToken: !!userData?.youtube_access_token,
              hasRefreshToken: !!userData?.youtube_refresh_token,
              channelName: userData?.youtube_channel_name,
              accessTokenLength: userData?.youtube_access_token?.length || 0
            });
          }
        }
      } catch (verifyError) {
        console.error('❌ Data verification failed:', verifyError);
      }

      toast.success('🎉 YouTube channel connected successfully!');

      // Give a bit more time for cache refresh to complete
      console.log('⏱️ Waiting 3 seconds before navigation to ensure data sync...');
      setTimeout(() => {
        console.log('🧭 Navigating to settings to verify connection...');
        navigate('/settings?tab=youtube');
      }, 3000);

    } catch (error) {
      console.error('OAuth callback error:', error);
      const message = error instanceof Error ? error.message : 'Unknown error occurred';
      setErrorMessage(message);
      setStatus('error');
      
      toast.error('Failed to connect YouTube channel');
      
      if (!showSetupInstructions) {
        setTimeout(() => {
          navigate('/settings?tab=youtube');
        }, 5000);
      }
    }
  };

  const handleRetry = () => {
    if (retryCount < 2) {
      setRetryCount(prev => prev + 1);
      setStatus('loading');
      setErrorMessage('');
      setShowSetupInstructions(false);
      handleCallback();
    } else {
      navigate('/settings?tab=youtube');
    }
  };

  const handleGoToDashboard = () => {
    navigate('/settings?tab=youtube');
  };

  useEffect(() => {
    handleCallback();
  }, []);

  const SetupInstructions = () => (
    <div className="mt-6 text-left bg-gray-700 p-4 rounded-lg">
      <h3 className="font-semibold text-white mb-3 flex items-center">
        <ExternalLink className="w-4 h-4 mr-2" />
        Required Google Cloud Console Setup:
      </h3>
      <ol className="text-sm text-gray-300 space-y-2 list-decimal list-inside">
        <li>Go to <a href="https://console.cloud.google.com" target="_blank" rel="noopener noreferrer" className="text-teal underline">Google Cloud Console</a></li>
        <li>Enable the YouTube Data API v3</li>
        <li>Create OAuth 2.0 credentials (Web application type)</li>
        <li><strong>Add authorized redirect URIs:</strong>
          <div className="bg-gray-800 p-2 mt-1 rounded text-xs font-mono">
            {window.location.origin}/auth/youtube/callback
          </div>
        </li>
        <li>Update the client credentials in Supabase Edge Function secrets</li>
      </ol>
      <p className="text-xs text-gray-400 mt-3">
        Authentication errors usually mean the OAuth client isn't properly configured.
      </p>
    </div>
  );

  return (
    <div className="min-h-screen bg-dark-gray flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-gray-800 rounded-lg p-8 text-center">
        <div className="mb-6">
          <Youtube className="w-16 h-16 text-red-500 mx-auto mb-4" />
          
          {status === 'loading' && (
            <>
              <div className="w-8 h-8 border-4 border-teal border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <h2 className="text-2xl font-bold text-white mb-2">Connecting YouTube Channel</h2>
              <p className="text-gray-300">Processing your authorization...</p>
              {retryCount > 0 && (
                <p className="text-sm text-gray-400 mt-2">Retry attempt {retryCount}/2</p>
              )}
            </>
          )}

          {status === 'success' && (
            <>
              <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-white mb-2">Connection Successful!</h2>
              <p className="text-gray-300">Your YouTube channel has been connected. Redirecting to dashboard...</p>
            </>
          )}

          {status === 'error' && (
            <>
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-white mb-2">Connection Failed</h2>
              <div className="text-gray-300 mb-4">
                <p className="mb-2">{errorMessage}</p>
              </div>

              {showSetupInstructions && <SetupInstructions />}

              <div className="flex flex-col space-y-3 mt-6">
                {retryCount < 2 && !showSetupInstructions && (
                  <Button 
                    onClick={handleRetry}
                    className="bg-teal hover:bg-teal/90 text-white flex items-center justify-center"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Try Again
                  </Button>
                )}
                <Button 
                  onClick={handleGoToDashboard}
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  Go to Dashboard
                </Button>
              </div>

              {!showSetupInstructions && (
                <p className="text-sm text-gray-400 mt-4">
                  {retryCount >= 2 ? 'Please contact support for assistance.' : 'Redirecting to dashboard in 5 seconds...'}
                </p>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default YouTubeCallbackPage;
