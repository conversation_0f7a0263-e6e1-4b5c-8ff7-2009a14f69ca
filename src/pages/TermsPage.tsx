
import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ard, <PERSON>f<PERSON><PERSON><PERSON>, <PERSON>ert<PERSON>riangle, Scale } from 'lucide-react';

const TermsPage = () => {
  return (
    <div className="min-h-screen bg-dark-gray text-white">
      <div className="py-12">
        <div className="text-center mb-12">
          <FileText className="w-16 h-16 text-teal mx-auto mb-4" />
          <h1 className="text-4xl font-bold text-white mb-4">Terms of Service</h1>
          <p className="text-gray-300 text-lg">
            Last updated: December 2024
          </p>
        </div>

        <div className="space-y-8">
          <section className="bg-gray-700 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Service Terms</h2>
            <div className="space-y-4 text-gray-300">
              <p>
                By using MyContentHub, you agree to these terms. Our service provides YouTube content strategy tools including:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>Content pillar management and tracking</li>
                <li>YouTube analytics integration and insights</li>
                <li>Video planning and scheduling tools</li>
                <li>Performance analytics and recommendations</li>
              </ul>
            </div>
          </section>

          <section className="bg-gray-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <CreditCard className="w-6 h-6 text-teal mr-3" />
              <h2 className="text-2xl font-semibold text-white">Subscription Terms</h2>
            </div>
            <div className="space-y-4 text-gray-300">
              <h3 className="text-lg font-semibold text-white">Billing</h3>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>Subscriptions are billed monthly or annually in advance</li>
                <li>Pricing is subject to change with 30 days notice</li>
                <li>Failed payments may result in service suspension</li>
                <li>You can upgrade or downgrade your plan at any time</li>
              </ul>
              
              <h3 className="text-lg font-semibold text-white mt-6">Cancellation</h3>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>You can cancel your subscription at any time</li>
                <li>Service continues until the end of your billing period</li>
                <li>No refunds for partial billing periods</li>
                <li>Data export available for 30 days after cancellation</li>
              </ul>
            </div>
          </section>

          <section className="bg-gray-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <RefreshCw className="w-6 h-6 text-teal mr-3" />
              <h2 className="text-2xl font-semibold text-white">Refund Policy</h2>
            </div>
            <div className="space-y-4 text-gray-300">
              <p>
                We want you to be satisfied with MyContentHub:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>7-day money-back guarantee for new subscriptions</li>
                <li>Refunds processed within 5-7 business days</li>
                <li>Refunds issued to original payment method</li>
                <li>Service violations void refund eligibility</li>
                <li>Contact support for refund requests</li>
              </ul>
            </div>
          </section>

          <section className="bg-gray-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <AlertTriangle className="w-6 h-6 text-teal mr-3" />
              <h2 className="text-2xl font-semibold text-white">User Responsibilities</h2>
            </div>
            <div className="space-y-4 text-gray-300">
              <p>
                As a user of MyContentHub, you agree to:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>Provide accurate account information</li>
                <li>Maintain the security of your account credentials</li>
                <li>Use the service in compliance with applicable laws</li>
                <li>Not attempt to reverse engineer or abuse our systems</li>
                <li>Respect intellectual property rights</li>
                <li>Not share account access with others</li>
              </ul>
            </div>
          </section>

          <section className="bg-gray-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <Scale className="w-6 h-6 text-teal mr-3" />
              <h2 className="text-2xl font-semibold text-white">Limitation of Liability</h2>
            </div>
            <div className="space-y-4 text-gray-300">
              <p>
                MyContentHub is provided "as is" without warranties. We limit our liability as follows:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>We are not liable for YouTube API changes or downtime</li>
                <li>Data accuracy depends on YouTube's provided information</li>
                <li>Service interruptions may occur for maintenance</li>
                <li>Total liability limited to amount paid in the last 12 months</li>
                <li>We are not responsible for content creation decisions</li>
              </ul>
            </div>
          </section>

          <section className="bg-gray-700 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Contact Us</h2>
            <p className="text-gray-300">
              Questions about these terms? Contact us at{' '}
              <a href="mailto:<EMAIL>" className="text-teal hover:underline">
                <EMAIL>
              </a>
            </p>
          </section>
        </div>
      </div>
    </div>
  );
};

export default TermsPage;
