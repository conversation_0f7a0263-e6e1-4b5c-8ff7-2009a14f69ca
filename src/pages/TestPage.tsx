import React from 'react';
import { Link } from 'react-router-dom';
import ProfileDiagnostic from '@/components/Debug/ProfileDiagnostic';
import YouTubeOAuthDiagnostic from '@/components/Debug/YouTubeOAuthDiagnostic';
import ConnectionDiagnostic from '@/components/ConnectionDiagnostic';

export default function TestPage() {
  return (
    <div className="p-8">
      <h1 className="text-3xl font-bold mb-4">Test Page</h1>
      <p className="mb-4">This is a test page to verify that the application is working correctly.</p>
      
      <div className="space-y-4">
        <button 
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={() => alert('Button clicked!')}
        >
          Click Me
        </button>
        
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-2">Test Components</h2>
          <Link
            to="/test-video-service"
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 inline-block"
          >
            Test Video Service
          </Link>
        </div>

        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Profile Diagnostic</h2>
          <ProfileDiagnostic />
        </div>

        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">YouTube OAuth Diagnostic</h2>
          <YouTubeOAuthDiagnostic />
        </div>

        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Database Connection Diagnostic</h2>
          <ConnectionDiagnostic />
        </div>
      </div>
    </div>
  );
}
