
import React, { useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import ProfileSettings from '@/components/Settings/ProfileSettings';
import YouTubeSettings from '@/components/Settings/YouTubeSettings';
import PreferencesSettings from '@/components/Settings/PreferencesSettings';
import SecuritySettings from '@/components/Settings/SecuritySettings';
import AboutSettings from '@/components/Settings/AboutSettings';
import SettingsPageBackground from '@/components/Settings/SettingsPageBackground';
import YouTubeDebugPanel from '@/components/Debug/YouTubeDebugPanel';
import { useYouTubeRefresh } from '@/hooks/useYouTubeRefresh';

const SettingsPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const activeTab = searchParams.get('tab') || 'profile';
  const { refreshYouTubeConnection } = useYouTubeRefresh();

  // Log when Settings page loads to track navigation from OAuth
  useEffect(() => {
    console.log('⚙️ Settings page loaded at:', new Date().toISOString());
    console.log('📍 Active tab:', activeTab);
    console.log('🔗 Current URL:', window.location.href);

    // Check if we might be returning from OAuth (look for recent localStorage activity)
    const recentOAuthActivity = localStorage.getItem('youtube_oauth_completed');
    if (recentOAuthActivity) {
      console.log('🔄 Detected recent OAuth activity on Settings page, forcing refresh');

      // Force aggressive refresh when returning from OAuth
      refreshYouTubeConnection().then(() => {
        console.log('✅ Settings page OAuth refresh completed');
      });

      localStorage.removeItem('youtube_oauth_completed');
    }
  }, [activeTab]);

  const handleTabChange = (value: string) => {
    navigate(`/settings?tab=${value}`);
  };

  return (
    <div className="relative min-h-screen app-background">
      <SettingsPageBackground />
      <div className="relative z-10 py-8">
        <h1 className="text-3xl font-bold text-white mb-6">Settings</h1>
        <p className="text-gray-300">Manage your account settings and preferences</p>
        <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
            <TabsTrigger value="profile">
              Profile
            </TabsTrigger>
            <TabsTrigger value="youtube">
              YouTube
            </TabsTrigger>
            <TabsTrigger value="preferences">
              Preferences
            </TabsTrigger>
            <TabsTrigger value="security">
              Security
            </TabsTrigger>
            <TabsTrigger value="about">
              About
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile">
            <Card className="dashboard-card">
              <CardHeader>
                <CardTitle className="text-white">Profile Information</CardTitle>
              </CardHeader>
              <CardContent>
                <ProfileSettings />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="youtube" className="space-y-6">
            <Card className="dashboard-card">
              <CardHeader>
                <CardTitle className="text-white">YouTube Integration</CardTitle>
              </CardHeader>
              <CardContent>
                <YouTubeSettings />
              </CardContent>
            </Card>

            {/* Temporary debug panel to track OAuth flow issues */}
            <YouTubeDebugPanel />
          </TabsContent>

          <TabsContent value="preferences">
            <Card className="dashboard-card">
              <CardHeader>
                <CardTitle className="text-white">Preferences</CardTitle>
              </CardHeader>
              <CardContent>
                <PreferencesSettings />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security">
            <Card className="dashboard-card">
              <CardHeader>
                <CardTitle className="text-white">Security</CardTitle>
              </CardHeader>
              <CardContent>
                <SecuritySettings />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="about">
            <Card className="dashboard-card">
              <CardHeader>
                <CardTitle className="text-white">About MyContentHub</CardTitle>
              </CardHeader>
              <CardContent>
                <AboutSettings />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SettingsPage;
