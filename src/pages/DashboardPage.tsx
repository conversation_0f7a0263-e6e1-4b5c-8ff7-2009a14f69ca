import { useAuth } from '@/hooks/useAuth';
import { useDashboardData } from '@/hooks/useDashboardData';
import { TooltipProvider } from '@/components/ui/tooltip';
import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import DashboardLoading from '@/components/Dashboard/DashboardLoading';
import DashboardBackground from '@/components/Dashboard/DashboardBackground';
import DashboardWelcomeHeader from '@/components/Dashboard/DashboardWelcomeHeader';
import DashboardContent from '@/components/Dashboard/DashboardContent';


const DashboardPage = () => {
  const { user: authUser, loading: authLoading } = useAuth();
  const navigate = useNavigate();

  
  const {
    userProfile,
    pillars,
    videoCount,
    isLoading,
    error,
    fetchUserProfile,
    fetchPillarsAndVideos
  } = useDashboardData({ authUser });

  useEffect(() => {
    if (!authLoading && !authUser) {
      navigate('/auth');
      return;
    }
  }, [authUser, authLoading, navigate]);

  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);


  if (authLoading || !authUser) {
    return <DashboardLoading />;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-teal border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Loading your dashboard...</p>
          {error && (
            <p className="text-red-400 text-sm mt-2">
              Error: {typeof error === 'string' ? error : 'Failed to load dashboard data. Please refresh.'}
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <>
      <DashboardBackground />

      <TooltipProvider>
        <div className="relative min-h-screen">
          <div className="relative z-10 space-y-8">
            {/* Welcome Header */}
            <div className="py-8">
              <DashboardWelcomeHeader userProfile={userProfile} />
            </div>

            {/* Dashboard Content */}
            <div className="mb-6">
              <DashboardContent
                userProfile={userProfile}
                pillars={pillars}
                videoCount={videoCount}
                onConnectionUpdate={() => {
                  fetchUserProfile(true);
                  fetchPillarsAndVideos();
                }}
              />
            </div>
          </div>
        </div>
      </TooltipProvider>
    </>
  );
};

export default DashboardPage;
