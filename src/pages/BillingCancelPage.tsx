
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>Circle, ArrowRight } from 'lucide-react';

const BillingCancelPage = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-dark-gray flex flex-col items-center justify-center p-4">
      <Card className="max-w-md w-full bg-gray-800 border-gray-700">
        <CardHeader className="text-center pb-2">
          <div className="flex justify-center mb-4">
            <div className="rounded-full bg-red-500/20 p-3">
              <XCircle className="w-12 h-12 text-red-500" />
            </div>
          </div>
          <CardTitle className="text-2xl text-white">Payment Cancelled</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-gray-300 mb-6">
            You've cancelled the subscription process. No changes have been made to your account.
          </p>
          <p className="text-gray-300">
            You can still continue using MyContentHub with your current plan.
          </p>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button 
            onClick={() => navigate('/pricing')}
            className="bg-teal hover:bg-teal/90 text-white"
          >
            View Plans
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default BillingCancelPage;
