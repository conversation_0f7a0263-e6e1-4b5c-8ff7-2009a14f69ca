import React, { useState, useEffect } from 'react';
import { supabase } from '../utils/supabaseClient';
import { useUser } from '@supabase/auth-helpers-react';

interface Pillar {
  id: number;
  name: string;
  description: string;
  videoCount: number;
  color: string;
}

export default function Pillars() {
  const { user } = useUser();
  const [pillars, setPillars] = useState<Pillar[]>([]);

  useEffect(() => {
    const fetchPillarsData = async () => {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from('content_pillars')
          .select('*')
          .eq('user_id', user.id);
          
        if (error) throw error;
        
        // Get video counts for each pillar
        const { data: videos, error: videosError } = await supabase
          .from('videos')
          .select('id, pillar_id, views, status')
          .eq('user_id', user.id)
          .eq('status', 'published')
          .not('youtube_video_id', 'is', null);
          
        if (videosError) throw videosError;
        
        // Add video counts to pillars
        const pillarsWithCounts = data.map(pillar => {
          const pillarVideos = videos.filter(v => v.pillar_id === pillar.id);
          const videoCount = pillarVideos.length;
          
          return {
            ...pillar,
            videoCount: videoCount
          };
        });
        
        setPillars(pillarsWithCounts);
      } catch (error) {
        console.error('Error fetching pillars:', error);
      }
    };
    
    fetchPillarsData();
  }, [user]);

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Content Pillars</h1>
        <button className="px-4 py-2 bg-teal-500 text-white rounded hover:bg-teal-600">
          Add Pillar
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {pillars.map((pillar) => (
          <div key={pillar.id} className="bg-white rounded-lg shadow overflow-hidden">
            <div className={`h-2 ${pillar.color}`}></div>
            <div className="p-6">
              <div className="flex justify-between items-start">
                <h3 className="text-lg font-semibold">{pillar.name}</h3>
                <span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">
                  {pillar.videoCount} videos
                </span>
              </div>
              <p className="mt-2 text-gray-600">{pillar.description}</p>
              <div className="mt-4 flex justify-end space-x-2">
                <button className="text-sm text-blue-600 hover:text-blue-800">
                  Edit
                </button>
                <button className="text-sm text-red-600 hover:text-red-800">
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
