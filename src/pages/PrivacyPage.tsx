
import React from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Trash2 } from 'lucide-react';

const PrivacyPage = () => {
  return (
    <div className="min-h-screen bg-dark-gray text-white">
      <div className="py-12">
        <div className="text-center mb-12">
          <Shield className="w-16 h-16 text-teal mx-auto mb-4" />
          <h1 className="text-4xl font-bold text-white mb-4">Privacy Policy</h1>
          <p className="text-gray-300 text-lg">
            Last updated: December 2024
          </p>
        </div>

        <div className="space-y-8">
          <section className="bg-gray-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <Database className="w-6 h-6 text-teal mr-3" />
              <h2 className="text-2xl font-semibold text-white">Data Collection</h2>
            </div>
            <div className="space-y-4 text-gray-300">
              <p>
                MyContentHub collects and processes the following types of information:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li><strong>Account Information:</strong> Email address, username, profile data</li>
                <li><strong>YouTube Data:</strong> Channel statistics, video metadata, performance metrics</li>
                <li><strong>Content Planning Data:</strong> Content pillars, video ideas, scheduling information</li>
                <li><strong>Usage Analytics:</strong> Feature usage, session data, performance metrics</li>
                <li><strong>Payment Information:</strong> Processed securely through Stripe (we don't store card details)</li>
              </ul>
            </div>
          </section>

          <section className="bg-gray-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <CreditCard className="w-6 h-6 text-teal mr-3" />
              <h2 className="text-2xl font-semibold text-white">YouTube API Data Usage</h2>
            </div>
            <div className="space-y-4 text-gray-300">
              <p>
                We use the YouTube Data API v3 to provide our core functionality:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>Read your channel statistics and video performance data</li>
                <li>Access video metadata to analyze content pillar performance</li>
                <li>We do NOT modify, delete, or upload content to your YouTube channel</li>
                <li>We comply with Google's API Services User Data Policy</li>
                <li>You can revoke access at any time through your Google Account settings</li>
              </ul>
            </div>
          </section>

          <section className="bg-gray-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <Cookie className="w-6 h-6 text-teal mr-3" />
              <h2 className="text-2xl font-semibold text-white">Cookies & Tracking</h2>
            </div>
            <div className="space-y-4 text-gray-300">
              <p>
                We use cookies and similar technologies for:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>Authentication and session management</li>
                <li>Remembering user preferences and settings</li>
                <li>Analytics to improve our service</li>
                <li>Essential functionality (cannot be disabled)</li>
              </ul>
            </div>
          </section>

          <section className="bg-gray-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <Trash2 className="w-6 h-6 text-teal mr-3" />
              <h2 className="text-2xl font-semibold text-white">Data Retention & Deletion</h2>
            </div>
            <div className="space-y-4 text-gray-300">
              <p>
                Your data rights and our retention policies:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>Account data is retained while your account is active</li>
                <li>You can request data deletion by contacting support</li>
                <li>Deleted accounts are permanently removed within 30 days</li>
                <li>Anonymous analytics data may be retained for service improvement</li>
                <li>Payment data is handled by Stripe according to their privacy policy</li>
              </ul>
            </div>
          </section>

          <section className="bg-gray-700 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Contact Us</h2>
            <p className="text-gray-300">
              If you have questions about this Privacy Policy, please contact us at{' '}
              <a href="mailto:<EMAIL>" className="text-teal hover:underline">
                <EMAIL>
              </a>
            </p>
          </section>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPage;
