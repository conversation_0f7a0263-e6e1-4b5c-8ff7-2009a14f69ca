
import React from 'react';
import { usePillarsData } from '@/hooks/usePillarsData';
import PillarsBackground from '@/components/Pillars/PillarsBackground';
import PillarsLoadingState from '@/components/Pillars/PillarsLoadingState';
import PillarsMainTabs from '@/components/Pillars/PillarsMainTabs';
import PageHeader from '@/components/Layout/PageHeader';

const PillarsPage = () => {
  const {
    pillars,
    userTier,
    isLoading,
    canAddMorePillars,
    addPillar,
    updatePillar,
    deletePillar,
    pillarLimit
  } = usePillarsData();

  if (isLoading) {
    return <PillarsLoadingState />;
  }

  return (
    <div className="relative min-h-screen app-background">
      <PillarsBackground />
      <div className="relative z-10">
        <PageHeader
          title="Content Pillars"
          subtitle="Organize your content strategy around key themes"
        />
        
        <div className="pb-12">
          {/* Remove the dashboard-card wrapper to allow individual cards */}
          <PillarsMainTabs
            pillars={pillars}
            onDeletePillar={deletePillar}
            onEditPillar={updatePillar}
            addPillar={addPillar}
            canAddMorePillars={canAddMorePillars}
            userTier={userTier}
            pillarLimit={pillarLimit}
          />
        </div>
      </div>
    </div>
  );
};

export default PillarsPage;
