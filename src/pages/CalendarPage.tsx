import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { calculateCalendarMetrics } from '@/utils/calendarMetrics';
import PageBackground from '@/components/Layout/PageBackground';
import PageLayout from '@/components/Layout/PageLayout';
import CalendarPageHeader from '@/components/Calendar/CalendarPageHeader';
import ModernCalendarHeader from '@/components/Calendar/ModernCalendarHeader';
import CalendarLayout from '@/components/Calendar/CalendarLayout';
import AddVideoModal from '@/components/Calendar/AddVideoModal';

const CalendarPage = () => {
  console.log('CalendarPage: Component starting...');

  // State
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<'month' | 'week' | 'timeline'>('month');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [videos, setVideos] = useState([]);
  const [pillars, setPillars] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const { user } = useAuth();

  console.log('Calendar state:', { user: !!user, currentDate, view, videosCount: videos.length, pillarsCount: pillars.length });

  // Create basic metrics for the header
  const metrics = calculateCalendarMetrics(videos, currentDate);

  // Navigation functions
  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const handleDayClick = (date: Date) => {
    setSelectedDate(date);
    setIsAddModalOpen(true);
  };

  const handleVideoAdded = () => {
    // Refresh data when video is added
    fetchData();
    setIsAddModalOpen(false);
  };

  const handleVideoUpdated = () => {
    // Refresh data when video is updated
    fetchData();
  };

  // Fetch data function
  const fetchData = async () => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      // Fetch pillars
      const { data: pillarsData, error: pillarsError } = await supabase
        .from('content_pillars')
        .select('*')
        .eq('user_id', user.id);

      if (pillarsError) {
        console.error('Error fetching pillars:', pillarsError);
        setError(pillarsError.message);
        return;
      }

      setPillars(pillarsData || []);

      // Fetch videos
      const { data: videosData, error: videosError } = await supabase
        .from('videos')
        .select('*')
        .eq('user_id', user.id)
        .order('scheduled_date', { ascending: true });

      if (videosError) {
        console.error('Error fetching videos:', videosError);
        setError(videosError.message);
        return;
      }

      setVideos(videosData || []);
      setError(null);
    } catch (err) {
      console.error('Exception in fetchData:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on mount
  useEffect(() => {
    fetchData();
  }, [user]);

  // Show loading state
  if (isLoading) {
    return (
      <PageLayout>
        <div className="relative min-h-screen">
          <PageBackground />
          <div className="relative z-10 flex items-center justify-center min-h-[60vh]">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 border-4 border-teal border-t-transparent rounded-full animate-spin mb-4"></div>
              <div className="text-white text-xl">Loading calendar data...</div>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <PageLayout>
        <div className="relative min-h-screen">
          <PageBackground />
          <div className="relative z-10 flex items-center justify-center min-h-[60vh]">
            <div className="content-card p-8 text-center">
              <h2 className="text-xl font-bold text-white mb-4">Unable to load calendar</h2>
              <p className="text-gray-400 mb-6">
                There was a problem loading your calendar data. Please try again.
              </p>
              <p className="text-red-400 text-sm mb-6">{error}</p>
              <button
                onClick={fetchData}
                className="bg-teal hover:bg-teal/90 text-white px-4 py-2 rounded"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="relative min-h-screen">
        <PageBackground />
        <div className="relative z-10 space-y-8 animate-fade-in">
          <CalendarPageHeader
            metrics={metrics}
            pillarsCount={pillars?.length || 0}
            onScheduleClick={() => setIsAddModalOpen(true)}
          />

          <ModernCalendarHeader
            currentDate={currentDate}
            view={view}
            onNavigateMonth={navigateMonth}
            onViewChange={setView}
            onGoToToday={goToToday}
          />

          <CalendarLayout
            currentDate={currentDate}
            videos={videos}
            pillars={pillars}
            view={view}
            onDayClick={handleDayClick}
            onVideoUpdated={handleVideoUpdated}
          />

          <AddVideoModal
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
            onVideoAdded={handleVideoAdded}
            selectedDate={selectedDate}
            pillars={pillars}
          />
        </div>
      </div>
    </PageLayout>
  );
};

export default CalendarPage;