
import React, { useState } from 'react';
import { HelpCircle, Mail, Clock, Book, Send, ChevronDown, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const SupportPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  const faqs = [
    {
      question: "How do I connect my YouTube channel?",
      answer: "Go to Settings > YouTube Integration and click 'Connect YouTube Channel'. You'll be redirected to Google to authorize access to your channel data."
    },
    {
      question: "What YouTube data does MyContentHub access?",
      answer: "We only access read-only data including channel statistics, video metadata, and performance metrics. We never modify or delete your content."
    },
    {
      question: "How often is my YouTube data updated?",
      answer: "Data is automatically synced every 24 hours. You can also manually refresh data anytime from your dashboard."
    },
    {
      question: "Can I change my subscription plan?",
      answer: "Yes, you can upgrade or downgrade your plan anytime. Changes take effect on your next billing cycle, and you'll have immediate access to new features when upgrading."
    },
    {
      question: "What happens to my data if I cancel?",
      answer: "Your data remains accessible for 30 days after cancellation, allowing you to export or reactivate. After 30 days, all data is permanently deleted."
    },
    {
      question: "How do content pillars work?",
      answer: "Content pillars are categories for your videos. Set target percentages for each pillar, and we'll track your actual performance to help maintain a balanced content strategy."
    },
    {
      question: "Is there a free trial?",
      answer: "Yes! All new accounts get a 7-day free trial with full access to all features. No credit card required to start."
    },
    {
      question: "How do I export my data?",
      answer: "Go to Settings > Data Export to download your content pillars, video ideas, and analytics data in CSV format."
    }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would send the form data to your backend
    console.log('Support form submitted:', formData);
    alert('Thank you for your message! We\'ll get back to you within 24 hours.');
    setFormData({ name: '', email: '', subject: '', message: '' });
  };

  const toggleFaq = (index: number) => {
    setExpandedFaq(expandedFaq === index ? null : index);
  };

  return (
    <div className="min-h-screen bg-dark-gray text-white">
      <div className="py-12">
        <div className="text-center mb-12">
          <HelpCircle className="w-16 h-16 text-teal mx-auto mb-4" />
          <h1 className="text-4xl font-bold text-white mb-4">Support Center</h1>
          <p className="text-gray-300 text-lg">
            Get help with MyContentHub or reach out to our team
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div className="bg-gray-700 rounded-lg p-8">
            <div className="flex items-center mb-6">
              <Mail className="w-6 h-6 text-teal mr-3" />
              <h2 className="text-2xl font-semibold text-white">Contact Us</h2>
            </div>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <Label htmlFor="name" className="text-white">Name</Label>
                <Input
                  id="name"
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="bg-gray-600 border-gray-500 text-white"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="email" className="text-white">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({...formData, email: e.target.value})}
                  className="bg-gray-600 border-gray-500 text-white"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="subject" className="text-white">Subject</Label>
                <Input
                  id="subject"
                  type="text"
                  value={formData.subject}
                  onChange={(e) => setFormData({...formData, subject: e.target.value})}
                  className="bg-gray-600 border-gray-500 text-white"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="message" className="text-white">Message</Label>
                <textarea
                  id="message"
                  value={formData.message}
                  onChange={(e) => setFormData({...formData, message: e.target.value})}
                  className="w-full min-h-[120px] rounded-md border border-gray-500 bg-gray-600 px-3 py-2 text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                  placeholder="Describe your question or issue..."
                  required
                />
              </div>
              
              <Button type="submit" className="w-full">
                <Send className="w-4 h-4 mr-2" />
                Send Message
              </Button>
            </form>

            <div className="mt-8 p-4 bg-gray-600 rounded-lg">
              <div className="flex items-center mb-2">
                <Clock className="w-5 h-5 text-teal mr-2" />
                <span className="font-semibold text-white">Response Time</span>
              </div>
              <p className="text-gray-300 text-sm">
                We typically respond within 24 hours during business days. 
                For urgent issues, please mark your subject as "URGENT".
              </p>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-gray-700 rounded-lg p-8">
            <div className="flex items-center mb-6">
              <Book className="w-6 h-6 text-teal mr-3" />
              <h2 className="text-2xl font-semibold text-white">Frequently Asked Questions</h2>
            </div>
            
            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <div key={index} className="border border-gray-600 rounded-lg">
                  <button
                    onClick={() => toggleFaq(index)}
                    className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-600 transition-colors"
                  >
                    <span className="font-medium text-white">{faq.question}</span>
                    {expandedFaq === index ? (
                      <ChevronDown className="w-5 h-5 text-teal" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-teal" />
                    )}
                  </button>
                  {expandedFaq === index && (
                    <div className="px-4 pb-4">
                      <p className="text-gray-300">{faq.answer}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className="mt-8 p-4 bg-teal/10 border border-teal/20 rounded-lg">
              <h3 className="font-semibold text-teal mb-2">Need More Help?</h3>
              <p className="text-gray-300 text-sm">
                Check our documentation for detailed guides and tutorials, or contact us directly using the form.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupportPage;
