import React, { useState } from 'react';
import { Modal } from '../components/ui/Modal';

export default function Ideas() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newIdea, setNewIdea] = useState({
    title: '',
    pillar: '',
    status: 'Draft',
    priority: 'Medium'
  });
  const [ideas, setIdeas] = useState([
    {
      id: 1,
      title: 'How to Create Engaging Content',
      pillar: 'Educational',
      status: 'Ready',
      priority: 'High'
    },
    {
      id: 2,
      title: 'Top 10 YouTube Growth Strategies',
      pillar: 'Growth',
      status: 'In Progress',
      priority: 'Medium'
    },
    {
      id: 3,
      title: 'Camera Settings for Perfect Videos',
      pillar: 'Technical',
      status: 'Draft',
      priority: 'Low'
    },
    {
      id: 4,
      title: 'Monetization Strategies for Creators',
      pillar: 'Business',
      status: 'Ready',
      priority: 'High'
    },
    {
      id: 5,
      title: 'Building Your Personal Brand',
      pillar: 'Branding',
      status: 'In Progress',
      priority: 'Medium'
    }
  ]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewIdea(prev => ({ ...prev, [name]: value }));
  };

  const handleAddIdea = () => {
    if (!newIdea.title.trim()) return;
    
    const newIdeaWithId = {
      ...newIdea,
      id: Date.now()
    };
    
    setIdeas(prev => [...prev, newIdeaWithId]);
    setNewIdea({
      title: '',
      pillar: '',
      status: 'Draft',
      priority: 'Medium'
    });
    setIsModalOpen(false);
  };

  const handleDeleteIdea = (id: number) => {
    setIdeas(prev => prev.filter(idea => idea.id !== id));
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Ideas Bank</h1>
        <button 
          className="px-4 py-2 bg-teal-500 text-white rounded hover:bg-teal-600"
          onClick={() => setIsModalOpen(true)}
        >
          Add Idea
        </button>
      </div>
      
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Title
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Pillar
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Priority
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {ideas.map((idea) => (
              <tr key={idea.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {idea.title}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500">{idea.pillar}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    idea.status === 'Ready' ? 'bg-green-100 text-green-800' :
                    idea.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {idea.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className={`text-sm ${
                    idea.priority === 'High' ? 'text-red-500' :
                    idea.priority === 'Medium' ? 'text-yellow-500' :
                    'text-green-500'
                  }`}>
                    {idea.priority}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button className="text-blue-600 hover:text-blue-900 mr-3">
                    Edit
                  </button>
                  <button 
                    className="text-red-600 hover:text-red-900"
                    onClick={() => handleDeleteIdea(idea.id)}
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Add New Idea"
      >
        <div className="space-y-4">
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700">
              Title
            </label>
            <input
              type="text"
              name="title"
              id="title"
              value={newIdea.title}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
              placeholder="Enter idea title"
            />
          </div>
          
          <div>
            <label htmlFor="pillar" className="block text-sm font-medium text-gray-700">
              Content Pillar
            </label>
            <input
              type="text"
              name="pillar"
              id="pillar"
              value={newIdea.pillar}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
              placeholder="e.g. Educational, Technical, etc."
            />
          </div>
          
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700">
              Status
            </label>
            <select
              name="status"
              id="status"
              value={newIdea.status}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
            >
              <option value="Draft">Draft</option>
              <option value="In Progress">In Progress</option>
              <option value="Ready">Ready</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
              Priority
            </label>
            <select
              name="priority"
              id="priority"
              value={newIdea.priority}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-teal-500 focus:border-teal-500"
            >
              <option value="Low">Low</option>
              <option value="Medium">Medium</option>
              <option value="High">High</option>
            </select>
          </div>
          
          <div className="pt-4">
            <button
              type="button"
              onClick={handleAddIdea}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-teal-600 text-base font-medium text-white hover:bg-teal-700 focus:outline-none"
            >
              Add Idea
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
