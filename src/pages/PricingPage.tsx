import React, { useState } from 'react';
import PricingHeader from '@/components/Pricing/PricingHeader';
import PricingTierCard from '@/components/Pricing/PricingTierCard';
import CreditCalculatorModal from '@/components/Pricing/CreditCalculatorModal';
import { usePricingActions } from '@/hooks/usePricingActions';
import { AppSidebar } from '@/components/AppSidebar';
import { SidebarInset } from '@/components/ui/sidebar';
import { subscriptionTiers } from '@/config/subscriptionTiers';

const PricingPage = () => {
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');
  const [showCalculator, setShowCalculator] = useState(false);
  const { loadingTier, userProfile, handleSubscribe, isCurrentPlan } = usePricingActions();

  const tiers = subscriptionTiers;

  const isAnnual = billingPeriod === 'yearly';

  return (
    <div className="min-h-screen flex bg-dark-gray w-full">
      <AppSidebar />
      <SidebarInset className="flex-1 bg-dark-gray">
        <main className="flex-1 overflow-auto bg-dark-gray">
          <div className="min-h-screen bg-dark-gray">
            <div className="py-8 md:py-12">
              <PricingHeader />
              
              {/* Billing Period Toggle */}
              <div className="flex justify-center mb-8">
                <div className="bg-gray-800 p-1 rounded-full inline-flex">
                  <button 
                    className={`px-4 py-2 rounded-full transition-colors ${
                      billingPeriod === 'monthly' ? 'bg-teal text-white' : 'text-gray-400 hover:text-white'
                    }`}
                    onClick={() => setBillingPeriod('monthly')}
                  >
                    Monthly
                  </button>
                  <button 
                    className={`px-4 py-2 rounded-full transition-colors flex items-center ${
                      billingPeriod === 'yearly' ? 'bg-teal text-white' : 'text-gray-400 hover:text-white'
                    }`}
                    onClick={() => setBillingPeriod('yearly')}
                  >
                    Yearly
                    <span className="ml-1 text-xs bg-green-500 text-white px-2 py-0.5 rounded-full">
                      Save 2 months!
                    </span>
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 pt-8">
                {tiers.map((tier) => (
                  <PricingTierCard
                    key={tier.id}
                    tier={tier}
                    isAnnual={isAnnual}
                    isCurrentPlan={isCurrentPlan(tier.id)}
                    isLoading={loadingTier === tier.id}
                    onSubscribe={(tierId) => handleSubscribe(tierId, isAnnual)}
                  />
                ))}
              </div>

              {/* Credit Packs Section */}
              <div className="mt-12 text-center">
                <h3 className="text-xl font-semibold mb-4 text-white">Need More Credits?</h3>
                <p className="text-gray-400 mb-4">One-time purchases for when you need extra</p>
                <div className="flex justify-center gap-4 flex-wrap">
                  <button className="bg-gray-700 hover:bg-gray-600 px-6 py-3 rounded-lg transition-all">
                    <div className="font-semibold text-white">50 credits</div>
                    <div className="text-sm text-gray-300">$4.99</div>
                  </button>
                  <button className="bg-gray-700 hover:bg-gray-600 px-6 py-3 rounded-lg border-2 border-teal-500 transition-all">
                    <div className="font-semibold text-white">150 credits</div>
                    <div className="text-sm text-teal-300">$12.99 (Save 13%)</div>
                  </button>
                  <button className="bg-gray-700 hover:bg-gray-600 px-6 py-3 rounded-lg transition-all">
                    <div className="font-semibold text-white">300 credits</div>
                    <div className="text-sm text-yellow">$19.99 (Save 33%)</div>
                  </button>
                </div>
              </div>

              {/* Team Seats Section (MCH Pro Exclusive) */}
              <div className="mt-12 p-6 bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg border border-teal-500/20">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Need Team Access?</h3>
                  <span className="text-xs bg-teal-500 text-white px-3 py-1 rounded-full font-medium">
                    MCH Pro Exclusive
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-white">Additional Team Members</p>
                    <p className="text-sm text-gray-300">Each member gets 100 AI credits/month</p>
                    <p className="text-xs text-gray-400 mt-1">Perfect for agencies and content teams</p>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-white">$5<span className="text-sm font-normal text-gray-300">/month</span></p>
                    <p className="text-xs text-gray-400">per seat</p>
                  </div>
                </div>
                
                <div className="mt-4 p-3 bg-teal-500/10 rounded border border-teal-500/30">
                  <p className="text-sm text-teal-300">
                    🚀 Upgrade to MCH Pro to add team members and unlock premium AI models
                  </p>
                </div>
              </div>

              {/* Credit Calculator */}
              <div className="mt-8 text-center">
                <button 
                  className="text-sm text-gray-400 hover:text-white underline"
                  onClick={() => setShowCalculator(true)}
                >
                  What can I create with credits? View pricing calculator →
                </button>
              </div>

              <div className="text-center mt-16">
                <p className="text-gray-400 text-sm font-medium">
                  All plans include a 7-day free trial • Cancel anytime
                </p>
                {isAnnual && (
                  <p className="text-teal text-sm font-medium mt-2">
                    Yearly plans save you 2 months compared to monthly billing
                  </p>
                )}
              </div>
            </div>

            <CreditCalculatorModal 
              isOpen={showCalculator} 
              onClose={() => setShowCalculator(false)} 
            />
          </div>
        </main>
      </SidebarInset>
    </div>
  );
};

export default PricingPage;
