// YouTube OAuth Configuration
// This file ensures consistency between OAuth flow and token refresh

export const YOUTUBE_CONFIG = {
  // Google OAuth Client Configuration
  CLIENT_ID: '343487815633-rtm8h3ptunici9q3f384e2q66clfohu3.apps.googleusercontent.com',
  
  // ⚠️ TEMPORARY HARDCODED SECRET DUE TO SUPABASE ENVIRONMENT VARIABLE BUG ⚠️
  // This should match the secret used in the Supabase Edge Function
  // TODO: Move to environment variables when Supabase bug is resolved
  CLIENT_SECRET: '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',
  
  // OAuth Scopes
  SCOPES: [
    'https://www.googleapis.com/auth/youtube.readonly',
    'https://www.googleapis.com/auth/yt-analytics.readonly',
    'https://www.googleapis.com/auth/userinfo.email',
    'https://www.googleapis.com/auth/userinfo.profile'
  ],
  
  // API Endpoints
  ENDPOINTS: {
    TOKEN_INFO: 'https://www.googleapis.com/oauth2/v1/tokeninfo',
    TOKEN_REFRESH: 'https://oauth2.googleapis.com/token',
    OAUTH_AUTHORIZE: 'https://accounts.google.com/o/oauth2/v2/auth'
  }
} as const;

// Validation function to ensure configuration is correct
export const validateYouTubeConfig = () => {
  const { CLIENT_ID, CLIENT_SECRET } = YOUTUBE_CONFIG;

  if (!CLIENT_ID || !CLIENT_ID.includes('.apps.googleusercontent.com')) {
    throw new Error('Invalid YouTube Client ID configuration');
  }

  if (!CLIENT_SECRET || !CLIENT_SECRET.startsWith('GOCSPX-')) {
    throw new Error('Invalid YouTube Client Secret configuration');
  }

  console.log('✅ YouTube configuration validated:', {
    clientId: CLIENT_ID,
    clientSecretFormat: CLIENT_SECRET.substring(0, 10) + '...',
    scopesCount: YOUTUBE_CONFIG.SCOPES.length
  });

  return true;
};

// Function to verify configuration consistency across the app
export const verifyConfigurationConsistency = () => {
  console.log('🔍 Verifying YouTube configuration consistency...');

  // This should match the secret used in Supabase Edge Function
  const expectedSecret = '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';

  if (YOUTUBE_CONFIG.CLIENT_SECRET !== expectedSecret) {
    console.error('❌ Configuration mismatch detected!');
    console.error('Expected secret:', expectedSecret);
    console.error('Current secret:', YOUTUBE_CONFIG.CLIENT_SECRET);
    throw new Error('YouTube configuration mismatch between frontend and backend');
  }

  console.log('✅ Configuration consistency verified');
  return true;
};

// Helper function to get OAuth URL
export const buildOAuthUrl = (redirectUri: string, state?: string) => {
  validateYouTubeConfig();
  
  const params = new URLSearchParams({
    client_id: YOUTUBE_CONFIG.CLIENT_ID,
    redirect_uri: redirectUri,
    scope: YOUTUBE_CONFIG.SCOPES.join(' '),
    response_type: 'code',
    access_type: 'offline',
    prompt: 'consent',
    include_granted_scopes: 'true'
  });
  
  if (state) {
    params.append('state', state);
  }
  
  return `${YOUTUBE_CONFIG.ENDPOINTS.OAUTH_AUTHORIZE}?${params.toString()}`;
};
