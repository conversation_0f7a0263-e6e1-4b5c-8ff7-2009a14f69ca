// Centralized subscription tier configuration
export type SubscriptionTierId = 'analytics_only' | 'ai_lite' | 'ai_pro';

export interface SubscriptionTier {
  id: SubscriptionTierId;
  name: string;
  monthlyPrice: number;
  annualPrice: number;
  description: string;
  aiCredits: number;
  features: string[];
  pillarLimit: number;
  stripePriceIds: {
    monthly: string;
    annual: string;
  };
}

// Legacy tier mapping (for backward compatibility)
export const legacyTierMapping: Record<string, SubscriptionTierId> = {
  'starter': 'analytics_only',
  'creator': 'ai_lite',
  'growth': 'ai_pro',
  'studio': 'ai_pro'
};

// New tier to legacy mapping (for backward compatibility)
export const newToLegacyTierMapping: Record<SubscriptionTierId, string> = {
  'analytics_only': 'starter',
  'ai_lite': 'creator',
  'ai_pro': 'studio'
};

// Environment-specific tier configurations
const devTiers: SubscriptionTier[] = [
  {
    id: 'analytics_only',
    name: 'MCH Starter',
    monthlyPrice: 4.99,
    annualPrice: 49,
    description: 'Perfect for getting started with YouTube analytics and basic AI assistance',
    aiCredits: 50,
    pillarLimit: 2,
    stripePriceIds: {
      monthly: 'price_1RUFOzPClzaBVxvzE319jwnX',
      annual: 'price_1RUFOzPClzaBVxvzd662V0E7'
    },
    features: [
      'YouTube analytics integration',
      '50 AI credits/month',
      'Create ~10 video scripts or 50 titles',
      'Basic AI models (GPT-3.5)',
      'Manual content planning',
      '7-day free trial'
    ]
  },
  {
    id: 'ai_lite',
    name: 'MCH Lite',
    monthlyPrice: 9.99,
    annualPrice: 99,
    description: 'Ideal for content creators who want more AI power and advanced features',
    aiCredits: 200,
    pillarLimit: 5,
    stripePriceIds: {
      monthly: 'price_1RUFWEPClzaBVxvzspZ3knL0',
      annual: 'price_1RUFWEPClzaBVxvzqM6n0B9b'
    },
    features: [
      'All analytics features',
      '200 AI credits/month',
      'Create ~40 scripts, 200 titles, or 66 hooks',
      'Standard AI models (GPT-3.5)',
      'Smart recommendations',
      'Content optimization tips',
      '7-day free trial'
    ]
  },
  {
    id: 'ai_pro',
    name: 'MCH Pro',
    monthlyPrice: 19.99,
    annualPrice: 199,
    description: 'The ultimate solution for professional creators and teams',
    aiCredits: 500,
    pillarLimit: 999,
    stripePriceIds: {
      monthly: 'price_1RUFZYPClzaBVxvzGg5UaS8g',
      annual: 'price_1RUFauPClzaBVxvz7jjcKz7t'
    },
    features: [
      'Everything in MCH Lite',
      '500 AI credits/month',
      'Create ~100 scripts with premium quality',
      'Premium AI models (GPT-4o)',
      'Advanced script, hook & title AI',
      'Priority support',
      'Team collaboration (add unlimited seats)',
      '7-day free trial'
    ]
  }
];

// Production tiers
export const subscriptionTiers: SubscriptionTier[] = 
  (import.meta.env?.MODE === 'development' || process.env.NODE_ENV === 'development') 
    ? devTiers 
    : [
        {
          id: 'analytics_only',
          name: 'MCH Starter',
          monthlyPrice: 4.99,
          annualPrice: 49,
          description: 'Perfect for getting started with YouTube analytics and basic AI content creation',
          aiCredits: 50,
          pillarLimit: 2,
          stripePriceIds: {
            monthly: 'price_1RUFOzPClzaBVxvzE319jwnX',
            annual: 'price_1RUFOzPClzaBVxvzd662V0E7'
          },
          features: [
            'YouTube analytics integration',
            '50 AI credits/month',
            'Create ~10 video scripts or 50 titles',
            'Basic AI models (GPT-3.5)',
            'Manual content planning',
            '7-day free trial'
          ]
        },
        {
          id: 'ai_lite',
          name: 'MCH Lite',
          monthlyPrice: 9.99,
          annualPrice: 99,
          description: 'Ideal for content creators who want more AI power and advanced features',
          aiCredits: 200,
          pillarLimit: 5,
          stripePriceIds: {
            monthly: 'price_1RUFWEPClzaBVxvzspZ3knL0',
            annual: 'price_1RUFWEPClzaBVxvzqM6n0B9b'
          },
          features: [
            'All analytics features',
            '200 AI credits/month',
            'Create ~40 scripts, 200 titles, or 66 hooks',
            'Standard AI models (GPT-3.5)',
            'Smart recommendations',
            'Content optimization tips',
            '7-day free trial'
          ]
        },
        {
          id: 'ai_pro',
          name: 'MCH Pro',
          monthlyPrice: 19.99,
          annualPrice: 199,
          description: 'The ultimate solution for professional content creators and teams',
          aiCredits: 500,
          pillarLimit: 999,
          stripePriceIds: {
            monthly: 'price_1RUFZYPClzaBVxvzGg5UaS8g',
            annual: 'price_1RUFauPClzaBVxvz7jjcKz7t'
          },
          features: [
            'Everything in MCH Lite',
            '500 AI credits/month',
            'Create ~100 scripts with premium quality',
            'Premium AI models (GPT-4o)',
            'Advanced script, hook & title AI',
            'Priority support',
            'Team collaboration (add unlimited seats)',
            '7-day free trial'
          ]
        }
      ];

// Helper functions
export const getTierById = (id: string): SubscriptionTier => {
  const tier = subscriptionTiers.find(tier => tier.id === id);
  return tier || subscriptionTiers[0]; // Default to first tier if not found
};

export const getTierByLegacyId = (legacyId: string): SubscriptionTier => {
  const newId = legacyTierMapping[legacyId] || 'analytics_only';
  return getTierById(newId);
};

export const getAICreditsForTier = (tierId: string): number => {
  const tier = getTierById(tierId);
  return tier.aiCredits;
};

export const getPillarLimitForTier = (tierId: string): number => {
  const tier = getTierById(tierId);
  return tier.pillarLimit;
};

export const getStripePriceId = (tierId: string, isAnnual: boolean): string => {
  const tier = getTierById(tierId);
  return isAnnual ? tier.stripePriceIds.annual : tier.stripePriceIds.monthly;
};
