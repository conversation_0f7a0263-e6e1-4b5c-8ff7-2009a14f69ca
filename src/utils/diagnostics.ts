import { supabase } from '@/lib/supabase';

export const runDataHealthCheck = async (userId: string) => {
  console.log('🏥 Starting Data Health Check...');
  const results: any = {
    pillars: { count: 0, data: null },
    videos: { count: 0, data: null },
    videosWithPillars: { count: 0, data: null },
    videosWithViews: { count: 0, data: null },
    videosPublished: { count: 0, data: null },
    youtubeConnection: { connected: false, data: null }
  };

  try {
    // Check Content Pillars
    const { data: pillars, error: pillarError } = await supabase
      .from('content_pillars')
      .select('*')
      .eq('user_id', userId);

    if (pillarError) throw new Error(`Pillar Error: ${pillarError.message}`);
    results.pillars = {
      count: pillars?.length || 0,
      data: pillars
    };

    // Check Videos
    const { data: allVideos, error: videoError } = await supabase
      .from('videos')
      .select('*, content_pillars(name, color)')
      .eq('user_id', userId);

    if (videoError) throw new Error(`Video Error: ${videoError.message}`);
    results.videos = {
      count: allVideos?.length || 0,
      data: allVideos
    };

    // Check Videos with Pillars
    const videosWithPillars = allVideos?.filter(v => v.content_pillars?.name) || [];
    results.videosWithPillars = {
      count: videosWithPillars.length,
      data: videosWithPillars
    };

    // Check Videos with Views
    const videosWithViews = allVideos?.filter(v => v.views > 0) || [];
    results.videosWithViews = {
      count: videosWithViews.length,
      data: videosWithViews
    };

    // Check Published Videos
    const publishedVideos = allVideos?.filter(v => v.status === 'published') || [];
    results.videosPublished = {
      count: publishedVideos.length,
      data: publishedVideos
    };

    // Check YouTube Connection
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('youtube_channel_id, youtube_access_token, last_youtube_sync')
      .eq('id', userId)
      .single();

    if (userError) throw new Error(`User Error: ${userError.message}`);
    results.youtubeConnection = {
      connected: !!userData?.youtube_channel_id && !!userData?.youtube_access_token,
      data: userData
    };

    // Print Health Check Results
    console.log('=== Data Health Check Results ===');
    console.log('Content Pillars:', results.pillars.count);
    console.log('Total Videos:', results.videos.count);
    console.log('Videos with Pillars:', results.videosWithPillars.count);
    console.log('Videos with Views:', results.videosWithViews.count);
    console.log('Published Videos:', results.videosPublished.count);
    console.log('YouTube Connected:', results.youtubeConnection.connected);
    if (results.youtubeConnection.connected) {
      console.log('Last YouTube Sync:', results.youtubeConnection.data.last_youtube_sync);
    }

    return results;
  } catch (error) {
    console.error('❌ Health Check Error:', error);
    throw error;
  }
}; 