
import { isSameMonth, isToday, isWeekend, isBefore } from 'date-fns';

export const isCurrentMonth = (date: Date, currentDate: Date): boolean => {
  return isSameMonth(date, currentDate);
};

export const isPastDate = (date: Date): boolean => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return isBefore(date, today);
};

export const isOverdue = (video: { status: string; scheduled_date: string }, date: Date): boolean => {
  if (video.status === 'published') return false;
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return date < today && Boolean(video.scheduled_date);
};

export const getActivityLevel = (videosCount: number): 'none' | 'low' | 'medium' | 'high' => {
  if (videosCount === 0) return 'none';
  if (videosCount === 1) return 'low';
  if (videosCount === 2) return 'medium';
  return 'high';
};

export const getPillarColor = (pillarId: string, pillars: Array<{ id: string; color: string }>): string => {
  const pillar = pillars.find(p => p.id === pillarId);
  return pillar?.color || '#37BEB0';
};
