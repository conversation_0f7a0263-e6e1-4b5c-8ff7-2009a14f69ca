import { supabase } from '@/lib/supabase';

export const cleanupMockDataForUser = async (userId: string) => {
  try {
    const { data, error } = await supabase.rpc('cleanup_mock_data', {
      user_id: userId
    });
    
    if (error) throw error;
    
    console.log('Mock data cleanup results:', data);
    return data;
  } catch (error) {
    console.error('Error cleaning up mock data:', error);
    return null;
  }
};