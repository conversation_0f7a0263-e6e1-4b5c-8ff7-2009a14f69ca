// Test utility to verify YouTube configuration consistency
import { YOUTUBE_CONFIG, validateYouTubeConfig, verifyConfigurationConsistency } from '@/config/youtube';

export const testYouTubeConfiguration = () => {
  console.log('🧪 Testing YouTube Configuration...');
  
  try {
    // Test 1: Basic configuration validation
    console.log('Test 1: Basic configuration validation');
    validateYouTubeConfig();
    console.log('✅ Basic validation passed');
    
    // Test 2: Configuration consistency check
    console.log('Test 2: Configuration consistency check');
    verifyConfigurationConsistency();
    console.log('✅ Consistency check passed');
    
    // Test 3: Display current configuration
    console.log('Test 3: Current configuration');
    console.log('Client ID:', YOUTUBE_CONFIG.CLIENT_ID);
    console.log('Client Secret (masked):', YOUTUBE_CONFIG.CLIENT_SECRET.substring(0, 10) + '...');
    console.log('Scopes:', YOUTUBE_CONFIG.SCOPES);
    console.log('Endpoints:', YOUTUBE_CONFIG.ENDPOINTS);
    
    // Test 4: Verify the fix
    console.log('Test 4: Verifying client secret fix');
    const expectedSecret = '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';
    const actualSecret = YOUTUBE_CONFIG.CLIENT_SECRET;
    
    if (actualSecret === expectedSecret) {
      console.log('✅ Client secret matches expected value');
      console.log('✅ Token refresh should now work correctly');
    } else {
      console.error('❌ Client secret mismatch!');
      console.error('Expected:', expectedSecret);
      console.error('Actual:', actualSecret);
    }
    
    // Test 5: Verify token refresh endpoint configuration
    console.log('Test 5: Token refresh endpoint configuration');
    console.log('Token refresh URL:', YOUTUBE_CONFIG.ENDPOINTS.TOKEN_REFRESH);
    console.log('Token info URL:', YOUTUBE_CONFIG.ENDPOINTS.TOKEN_INFO);

    console.log('🎉 All YouTube configuration tests passed!');
    console.log('✅ Our client secret consistency fix should resolve token refresh issues');
    console.log('✅ All components should now use the same configuration');
    return true;

  } catch (error) {
    console.error('❌ YouTube configuration test failed:', error);
    return false;
  }
};

// Auto-run test when this module is imported
if (typeof window !== 'undefined') {
  // Run test after a short delay to ensure console is ready
  setTimeout(() => {
    testYouTubeConfiguration();
  }, 1000);
}
