import { supabase } from '@/lib/supabase';

export const createDefaultPillarsForUser = async (userId: string) => {
  console.log('Creating default pillars for user:', userId);
  
  try {
    // Check if user already has pillars
    const { data: existingPillars, error: checkError } = await supabase
      .from('content_pillars')
      .select('id')
      .eq('user_id', userId);
      
    if (checkError) throw checkError;
    
    // If user already has pillars, don't create defaults
    if (existingPillars && existingPillars.length > 0) {
      console.log('User already has pillars, skipping default creation');
      return;
    }
    
    // Create default pillars
    const defaultPillars = [
      {
        user_id: userId,
        name: 'Tutorials',
        description: 'Educational content teaching specific skills',
        color: '#4F46E5',
        target_percentage: 50
      },
      {
        user_id: userId,
        name: 'Reviews',
        description: 'Product reviews and recommendations',
        color: '#10B981',
        target_percentage: 30
      },
      {
        user_id: userId,
        name: 'Vlogs',
        description: 'Personal content and behind-the-scenes',
        color: '#F59E0B',
        target_percentage: 20
      }
    ];
    
    const { data, error } = await supabase
      .from('content_pillars')
      .insert(defaultPillars)
      .select();
      
    if (error) throw error;
    
    console.log('Created default pillars for user:', data);
    return data;
  } catch (error) {
    console.error('Error creating default pillars:', error);
    return null;
  }
};