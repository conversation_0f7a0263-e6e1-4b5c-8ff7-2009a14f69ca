
import { YOUTUBE_CONFIG, validateYouTubeConfig, buildOAuthUrl } from '@/config/youtube';

// Utility functions for YouTube OAuth flow
export const getOAuthConfig = () => {
  // Validate configuration before proceeding
  validateYouTubeConfig();

  // Use the correct redirect URI paths
  const currentOrigin = window.location.origin;

  const config = {
    clientId: YOUTUBE_CONFIG.CLIENT_ID,
    redirectUri: `${currentOrigin}/auth/youtube/callback`,
    scopes: YOUTUBE_CONFIG.SCOPES.join(' ')
  };

  console.log('OAuth Config:', {
    clientId: config.clientId,
    redirectUri: config.redirectUri
  });

  return config;
};

export const generateOAuthUrl = () => {
  const config = getOAuthConfig();

  // Get current user from auth state
  const userFromStorage = localStorage.getItem('supabase.auth.token');
  let userId = null;

  if (userFromStorage) {
    try {
      const authData = JSON.parse(userFromStorage);
      userId = authData?.user?.id;
    } catch (error) {
      console.error('Error parsing auth data:', error);
    }
  }

  console.log('Generating OAuth URL with user ID:', userId);
  console.log('Using client ID:', config.clientId);
  console.log('Using redirect URI:', config.redirectUri);

  const state = JSON.stringify({ userId });

  // Use the shared configuration helper
  const url = buildOAuthUrl(config.redirectUri, state);
  console.log('Generated OAuth URL:', url);
  return url;
};

// Legacy localStorage utilities for backwards compatibility
export const setYouTubeConnection = (channelData: {
  channelName: string;
  channelId: string;
  thumbnailUrl?: string;
}) => {
  localStorage.setItem('youtube_connected', 'true');
  localStorage.setItem('youtube_channel_data', JSON.stringify(channelData));
};

export const getYouTubeConnection = () => {
  const isConnected = localStorage.getItem('youtube_connected') === 'true';
  const channelData = localStorage.getItem('youtube_channel_data');
  
  return {
    isConnected,
    channelData: channelData ? JSON.parse(channelData) : null
  };
};

export const removeYouTubeConnection = () => {
  localStorage.removeItem('youtube_connected');
  localStorage.removeItem('youtube_channel_data');
};
