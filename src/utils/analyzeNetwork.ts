import { NodeData } from '@/components/whiteboard/ContentNode';
import { Connection } from '@/components/whiteboard/types';

interface NetworkAnalysis {
  clusters: {
    nodes: NodeData[];
    connections: Connection[];
    theme: string;
  }[];
  insights: string[];
  recommendations: string[];
}

export async function analyzeNetwork(
  nodes: NodeData[],
  connections: Connection[]
): Promise<NetworkAnalysis> {
  // Group nodes into clusters based on connections
  const clusters: NetworkAnalysis['clusters'] = [];
  const visitedNodes = new Set<string>();

  // Helper function to find all connected nodes
  const findConnectedNodes = (nodeId: string, cluster: { nodes: NodeData[]; connections: Connection[] }) => {
    if (visitedNodes.has(nodeId)) return;
    visitedNodes.add(nodeId);

    const node = nodes.find(n => n.id === nodeId);
    if (!node) return;

    cluster.nodes.push(node);

    // Find all connections for this node
    const nodeConnections = connections.filter(
      c => c.fromNodeId === nodeId || c.toNodeId === nodeId
    );
    cluster.connections.push(...nodeConnections);

    // Recursively find connected nodes
    nodeConnections.forEach(connection => {
      const nextNodeId = connection.fromNodeId === nodeId ? connection.toNodeId : connection.fromNodeId;
      findConnectedNodes(nextNodeId, cluster);
    });
  };

  // Find all clusters
  nodes.forEach(node => {
    if (!visitedNodes.has(node.id)) {
      const cluster = { nodes: [], connections: [] };
      findConnectedNodes(node.id, cluster);
      if (cluster.nodes.length > 0) {
        clusters.push({
          ...cluster,
          theme: 'Cluster ' + (clusters.length + 1), // This would be replaced with actual theme analysis
        });
      }
    }
  });

  // Generate insights and recommendations
  const insights: string[] = [];
  const recommendations: string[] = [];

  // Analyze clusters
  clusters.forEach((cluster, index) => {
    const nodeTypes = new Set(cluster.nodes.map(n => n.type));
    const connectionTypes = new Set(cluster.connections.map(c => c.type));

    insights.push(
      `Cluster ${index + 1} contains ${cluster.nodes.length} nodes with ${cluster.connections.length} connections`,
      `Types of content: ${Array.from(nodeTypes).join(', ')}`,
      `Relationship types: ${Array.from(connectionTypes).join(', ')}`
    );

    if (cluster.nodes.length > 3) {
      recommendations.push(
        `Consider breaking down Cluster ${index + 1} into smaller, more focused groups`,
        `Look for patterns in the ${cluster.connections.length} connections to identify key themes`
      );
    }
  });

  // Analyze overall network
  const totalNodes = nodes.length;
  const totalConnections = connections.length;
  const avgConnectionsPerNode = totalConnections / totalNodes;

  insights.push(
    `Total network size: ${totalNodes} nodes with ${totalConnections} connections`,
    `Average connections per node: ${avgConnectionsPerNode.toFixed(1)}`
  );

  if (avgConnectionsPerNode < 1) {
    recommendations.push('Consider adding more connections to strengthen the network');
  }

  return {
    clusters,
    insights,
    recommendations,
  };
} 