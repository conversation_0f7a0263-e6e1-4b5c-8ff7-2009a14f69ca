import { format, isToday, isYesterday, isTomorrow, differenceInDays } from 'date-fns';

/**
 * Formats a date in a user-friendly way
 */
export function formatDate(date: Date | string): string {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isToday(dateObj)) {
    return 'Today';
  }
  
  if (isYesterday(dateObj)) {
    return 'Yesterday';
  }
  
  if (isTomorrow(dateObj)) {
    return 'Tomorrow';
  }
  
  const now = new Date();
  const diff = differenceInDays(dateObj, now);
  
  // If within the next 7 days, show day name
  if (diff > 0 && diff < 7) {
    return format(dateObj, 'EEEE'); // Monday, Tuesday, etc.
  }
  
  // Otherwise show date
  return format(dateObj, 'MMM d, yyyy'); // Jan 1, 2023
}

/**
 * Formats a date in ISO format (YYYY-MM-DD)
 */
export function formatISODate(date: Date | string): string {
  if (!date) return '';
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, 'yyyy-MM-dd');
}

/**
 * Formats a date and time
 */
export function formatDateTime(date: Date | string): string {
  if (!date) return '';
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, 'MMM d, yyyy h:mm a');
}
