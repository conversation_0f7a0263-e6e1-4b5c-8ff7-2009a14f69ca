
export const formatNumber = (num: number): string => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
  return num.toLocaleString();
};

export const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

export const getPerformanceStatus = (actual: number, target: number) => {
  const diff = actual - target;
  if (Math.abs(diff) <= 5) return { status: 'On Target', color: 'text-green-400', type: 'on-target' };
  return diff > 0 
    ? { status: 'Over Target', color: 'text-orange', type: 'over-target' }
    : { status: 'Under Target', color: 'text-red-400', type: 'under-target' };
};

export const getPerformanceIcon = (actual: number, target: number) => {
  const status = getPerformanceStatus(actual, target);
  return status.type;
};

export const getPerformanceColor = (actual: number, target: number) => {
  const status = getPerformanceStatus(actual, target);
  return status.color;
};

export const calculatePercentageDiff = (actual: number, target: number): string => {
  const diff = actual - target;
  return `${diff > 0 ? '+' : ''}${diff}%`;
};

export const getRecommendation = (actualPercentage: number, targetPercentage: number): string => {
  return actualPercentage < targetPercentage 
    ? 'creating more content in this pillar'
    : 'balancing with other content types';
};

export const formatPercentage = (value: number): string => {
  return `${value.toFixed(1)}%`;
};

export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

export const getChartColors = () => ({
  grid: '#374151',
  text: '#9CA3AF',
  tooltip: {
    background: '#374151',
    border: '#4B5563',
    color: '#fff'
  }
});
