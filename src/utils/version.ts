
import { VERSION, VERSION_STRING, APP_NAME } from '@/constants/version';

export const getVersionString = (): string => VERSION_STRING;

export const getFullVersionString = (): string => {
  const buildDate = VERSION.buildDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
  return `${APP_NAME} v${VERSION_STRING} (${buildDate})`;
};

export const getBuildInfo = () => ({
  version: VERSION_STRING,
  buildDate: VERSION.buildDate,
  environment: VERSION.environment,
  isPrerelease: !!VERSION.prerelease
});

export const isProductionBuild = (): boolean => VERSION.environment === 'production';

export const isDevelopmentBuild = (): boolean => VERSION.environment === 'development';
