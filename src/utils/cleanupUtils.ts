import { supabase } from '@/lib/supabase';

export const removeAllMockData = async (userId: string) => {
  console.log('🧹 Removing all mock data...');
  
  try {
    // Use the existing RPC function to clean up mock data
    const { data, error } = await supabase.rpc('cleanup_mock_data', {
      user_id: userId
    });
    
    if (error) throw error;
    
    console.log('Mock data cleanup results:', data);
    return data;
  } catch (error) {
    console.error('Error cleaning up mock data:', error);
    throw error;
  }
};
