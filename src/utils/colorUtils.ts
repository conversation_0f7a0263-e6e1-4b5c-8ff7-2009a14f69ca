export const PILLAR_COLORS = [
  '#4F46E5', // Indigo
  '#10B981', // Emerald
  '#F59E0B', // Amber
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#06B6D4', // <PERSON><PERSON>
];

export const getNextPillarColor = (existingPillars: any[]) => {
  if (existingPillars.length === 0) return PILLAR_COLORS[0];
  
  // Find a color that's not already used
  const usedColors = existingPillars.map(p => p.color);
  const availableColor = PILLAR_COLORS.find(color => !usedColors.includes(color));
  
  // If all colors are used, cycle through them
  return availableColor || PILLAR_COLORS[existingPillars.length % PILLAR_COLORS.length];
};