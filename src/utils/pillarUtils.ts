
import { COLOR_PALETTE } from '@/constants/pillarColors';
import { ContentPillar, DatabasePillar } from '@/types/pillar';
import { getPillarLimitForTier } from '@/config/subscriptionTiers';

export const getPillarLimit = (userTier: string): number => {
  // Use the tier directly without any legacy mapping
  return getPillarLimitForTier(userTier);
};

export const canAddMorePillars = (pillarCount: number, userTier: string): boolean => {
  const limit = getPillarLimit(userTier);
  return pillarCount < limit;
};

export const getNextAvailableColor = (existingPillars: ContentPillar[]): string => {
  const usedColors = existingPillars.map(p => p.color);
  const availableColor = COLOR_PALETTE.find(color => !usedColors.includes(color));
  return availableColor || COLOR_PALETTE[0];
};

export const convertDatabasePillarsToContentPillars = (pillars: DatabasePillar[]): ContentPillar[] => {
  return pillars.map((pillar) => ({
    ...pillar,
    actual_percentage: 0, // Will be calculated based on real video data
    video_count: 0, // Will be calculated based on real video data
    avg_views: 0, // Will be calculated based on real video data
    best_day: 'Monday', // Will be calculated based on real video data
    best_day_boost: 0 // Will be calculated based on real video data
  }));
};
