
import { VideoIdea } from '@/components/Ideas/types';

export const calculateCalendarMetrics = (videos: VideoIdea[], currentDate: Date) => {
  // Safety check for videos array
  if (!Array.isArray(videos)) {
    console.error('Invalid videos data in calculateCalendarMetrics:', videos);
    return {
      totalThisMonth: 0,
      plannedNextMonth: 0,
      averagePerWeek: 0
    };
  }

  try {
    const now = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    
    // Get first and last day of current month
    const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
    const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);
    
    // Get first and last day of next month
    const firstDayOfNextMonth = new Date(currentYear, currentMonth + 1, 1);
    const lastDayOfNextMonth = new Date(currentYear, currentMonth + 2, 0);
    
    // Count videos for this month
    const videosThisMonth = videos.filter(video => {
      if (!video.scheduled_date) return false;
      const videoDate = new Date(video.scheduled_date);
      return videoDate >= firstDayOfMonth && videoDate <= lastDayOfMonth;
    });
    
    // Count videos for next month
    const videosNextMonth = videos.filter(video => {
      if (!video.scheduled_date) return false;
      const videoDate = new Date(video.scheduled_date);
      return videoDate >= firstDayOfNextMonth && videoDate <= lastDayOfNextMonth;
    });
    
    // Calculate weekly average (based on last 4 weeks)
    const fourWeeksAgo = new Date(now);
    fourWeeksAgo.setDate(now.getDate() - 28);
    
    const videosLastFourWeeks = videos.filter(video => {
      if (!video.scheduled_date) return false;
      const videoDate = new Date(video.scheduled_date);
      return videoDate >= fourWeeksAgo && videoDate <= now;
    });
    
    const averagePerWeek = videosLastFourWeeks.length / 4;
    
    return {
      totalThisMonth: videosThisMonth.length,
      plannedNextMonth: videosNextMonth.length,
      averagePerWeek: averagePerWeek
    };
  } catch (error) {
    console.error('Error calculating calendar metrics:', error);
    return {
      totalThisMonth: 0,
      plannedNextMonth: 0,
      averagePerWeek: 0
    };
  }
};
