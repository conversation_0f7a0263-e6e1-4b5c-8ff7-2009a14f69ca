
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

export const getChangeText = (
  user: any, 
  isConnected: boolean, 
  statType: string, 
  currentValue: number
): string => {
  if (!isConnected) {
    return 'Connect YouTube to see data';
  }
  
  if (currentValue === 0) {
    switch (statType) {
      case 'subscribers':
        return 'No subscriber data synced';
      case 'monthlyViews':
        return 'No recent views data';
      case 'avgWatchTime':
        return 'No watch time data';
      case 'videosPublished':
        return 'No published videos imported';
      default:
        return 'No data available';
    }
  }
  
  // Show that this is real YouTube data
  const preferences = user?.preferences || {};
  const hasEnhancedSync = preferences.last_full_sync;
  
  if (hasEnhancedSync) {
    return 'From YouTube API';
  }
  
  return 'Current data';
};
