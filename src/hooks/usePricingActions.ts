
import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { getStripePriceId, newToLegacyTierMapping } from '@/config/subscriptionTiers';

export const usePricingActions = () => {
  const { user } = useAuth();
  const [loadingTier, setLoadingTier] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState<any>(null);

  useEffect(() => {
    if (user) {
      fetchUserProfile();
    }
  }, [user]);

  const fetchUserProfile = async () => {
    try {
      console.log('=== FETCHING USER PROFILE ===');
      console.log('User ID:', user?.id);
      
      const { data, error } = await supabase
        .from('users')
        .select('subscription_tier, subscription_status, stripe_customer_id')
        .eq('id', user?.id)
        .single();

      console.log('Profile fetch result:', { data, error });
      
      if (error) {
        console.error('Profile fetch error:', error);
        throw error;
      }
      
      setUserProfile(data);
      console.log('User profile set:', data);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      toast.error('Failed to load user profile');
    }
  };

  const handleSubscribe = async (tier: string, isAnnual: boolean) => {
    console.log('=== SUBSCRIBE BUTTON CLICKED ===');
    console.log('Tier:', tier);
    console.log('Is Annual:', isAnnual);
    console.log('User:', user ? { id: user.id, email: user.email } : 'Not logged in');

    if (!user) {
      console.error('ERROR: No user found');
      toast.error('Please sign in to subscribe');
      return;
    }

    // Map new tier names to the old ones for Stripe compatibility
    const tierMapping = newToLegacyTierMapping;
    const stripeTier = tierMapping[tier as keyof typeof tierMapping] || tier;
    
    // You can also pass the price ID directly if your backend supports it
    const stripePriceId = getStripePriceId(tier, isAnnual);
    
    setLoadingTier(tier);
    
    try {
      console.log('=== CALLING CREATE-CHECKOUT FUNCTION ===');
      const requestBody = { tier: stripeTier, isAnnual };
      console.log('Request body:', requestBody);
      
      const { data, error } = await supabase.functions.invoke('create-checkout', {
        body: requestBody
      });

      console.log('Function response:', { data, error });

      if (error) {
        console.error('=== SUPABASE FUNCTION ERROR ===');
        console.error('Error object:', error);
        throw error;
      }

      if (!data) {
        console.error('ERROR: No data returned from function');
        throw new Error('No response data from checkout function');
      }

      if (data.error) {
        console.error('=== CHECKOUT FUNCTION RETURNED ERROR ===');
        console.error('Error:', data.error);
        throw new Error(data.error);
      }

      if (!data.url) {
        console.error('ERROR: No checkout URL in response');
        console.error('Response data:', data);
        throw new Error('No checkout URL returned');
      }

      console.log('=== SUCCESS: REDIRECTING TO STRIPE ===');
      console.log('Checkout URL:', data.url);
      
      // Redirect to Stripe checkout
      window.location.href = data.url;
      
    } catch (error) {
      console.error('=== SUBSCRIPTION ERROR ===');
      console.error('Error type:', error.constructor.name);
      console.error('Error message:', error.message);
      console.error('Full error:', error);
      
      let errorMessage = 'Failed to start checkout process';
      
      if (error.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }
      
      toast.error(errorMessage);
    } finally {
      setLoadingTier(null);
      console.log('=== SUBSCRIBE FUNCTION COMPLETED ===');
    }
  };

  const isCurrentPlan = (tierId: string) => {
    return userProfile?.subscription_tier === tierId &&
           userProfile?.subscription_status === 'active' &&
           userProfile?.stripe_customer_id;
  };

  return {
    loadingTier,
    userProfile,
    handleSubscribe,
    isCurrentPlan
  };
};
