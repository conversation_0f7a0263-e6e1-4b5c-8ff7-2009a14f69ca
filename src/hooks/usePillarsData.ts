
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { useUserProfile } from '@/hooks/settings/useUserProfile';
import { getPillarLimit, canAddMorePillars as canAddMorePillarsUtil } from '@/utils/pillarUtils';
import { createPillar, updatePillar as updatePillarService, deletePillar as deletePillarService } from '@/services/pillarService';
import { ContentPillar } from '@/types/pillar';
import { toast } from 'sonner';

export const usePillarsData = () => {
  const { user } = useAuth();
  const { profile } = useUserProfile();
  const [pillars, setPillars] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Add this function to calculate actual percentages
  const calculateActualPercentages = (pillarsWithCounts, totalVideos) => {
    return pillarsWithCounts.map(pillar => {
      const actualPercentage = totalVideos > 0 
        ? Math.round((pillar.video_count / totalVideos) * 100) 
        : 0;
      
      return {
        ...pillar,
        actual_percentage: actualPercentage
      };
    });
  };

  useEffect(() => {
    if (user) {
      fetchPillars();
    } else {
      setPillars([]);
      setIsLoading(false);
    }
  }, [user]);

  const fetchPillars = async () => {
    if (!user) {
      setPillars([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      // Simplified: just fetch directly from database
      console.log('Fetching pillars for user:', user.id);
      const { data, error } = await supabase
        .from('content_pillars')
        .select('*')
        .eq('user_id', user.id);

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching pillars:', error);
        setPillars([]);
        setIsLoading(false);
        return;
      }
      
      // Get video counts for each pillar
      const { data: videos, error: videosError } = await supabase
        .from('videos')
        .select('id, pillar_id, views, status')
        .eq('user_id', user.id)
        .eq('status', 'published')
        .not('youtube_video_id', 'is', null);
        
      if (videosError) throw videosError;
      
      // Add video counts to pillars
      const pillarsWithCounts = data.map(pillar => {
        const pillarVideos = videos.filter(v => v.pillar_id === pillar.id);
        const videoCount = pillarVideos.length;
        const totalViews = pillarVideos.reduce((sum, v) => sum + (v.views || 0), 0);
        const avgViews = videoCount > 0 ? totalViews / videoCount : 0;
        
        return {
          ...pillar,
          video_count: videoCount,
          total_views: totalViews,
          avg_views: avgViews
        };
      });
      
      // Calculate actual percentages
      const totalVideos = videos.length;
      const pillarsWithPercentages = calculateActualPercentages(pillarsWithCounts, totalVideos);
      
      console.log('Pillars with percentages:', pillarsWithPercentages);
      setPillars(pillarsWithPercentages);
    } catch (error) {
      console.error('Error fetching pillars:', error);
      // Don't show error toast, just set empty array and continue
      setPillars([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Get user tier and pillar limit
  const userTier = profile?.subscription_tier || 'analytics_only';
  const pillarLimit = getPillarLimit(userTier);

  // Check if user can add more pillars
  const canAddMorePillars = () => {
    return canAddMorePillarsUtil(pillars.length, userTier);
  };

  // Add pillar function
  const addPillar = async (pillarData: Partial<ContentPillar>): Promise<boolean> => {
    if (!user) {
      toast.error('You must be logged in to create a pillar');
      return false;
    }
    
    if (!pillarData.name) {
      toast.error('Pillar name is required');
      return false;
    }
    
    try {
      const { data, error } = await supabase
        .from('content_pillars')
        .insert({
          user_id: user.id,
          name: pillarData.name,
          target_percentage: pillarData.target_percentage || 25,
          color: pillarData.color || '#37BEB0'
        })
        .select();
        
      if (error) {
        console.error('Error creating pillar:', error);
        toast.error('Failed to create content pillar');
        return false;
      }
      
      toast.success(`Content pillar "${pillarData.name}" created successfully!`);
      fetchPillars(); // Refresh pillars data
      return true;
    } catch (err) {
      console.error('Exception creating pillar:', err);
      toast.error('Failed to create content pillar');
      return false;
    }
  };

  // Update pillar function
  const updatePillar = async (pillar: ContentPillar): Promise<boolean> => {
    if (!user) {
      toast.error('Please sign in to update pillars');
      return false;
    }

    const success = await updatePillarService(user.id, pillar);
    if (success) {
      await fetchPillars(); // Refresh the pillars list
    }
    return success;
  };

  // Delete pillar function
  const deletePillar = async (pillarId: string, pillarName: string): Promise<void> => {
    if (!user) {
      toast.error('Please sign in to delete pillars');
      return;
    }

    await deletePillarService(user.id, pillarId, pillarName);
    await fetchPillars(); // Refresh the pillars list
  };

  return {
    pillars,
    userTier,
    isLoading,
    canAddMorePillars,
    addPillar,
    updatePillar,
    deletePillar,
    pillarLimit,
    refetch: fetchPillars
  };
};
