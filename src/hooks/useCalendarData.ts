
import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { VideoIdea } from '@/components/Ideas/types';
import { ContentPillar } from '@/types/pillar';

export const useCalendarData = (currentDate: Date) => {
  const [videos, setVideos] = useState<VideoIdea[]>([]);
  const [pillars, setPillars] = useState<ContentPillar[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchData = useCallback(async () => {
    if (!user) {
      console.log('No user found, returning empty data');
      return;
    }

    console.log('Starting calendar data fetch...');
    setIsLoading(true);
    setError(null);

    try {
      // Fetch pillars first
      console.log('Fetching pillars for user:', user.id);
      const { data: pillarsData, error: pillarsError } = await supabase
        .from('content_pillars')
        .select('*')
        .eq('user_id', user.id);
      
      if (pillarsError) {
        console.error('Error fetching pillars:', pillarsError);
        setError('Failed to load pillars: ' + pillarsError.message);
        return;
      }
      
      console.log(`Successfully loaded ${pillarsData?.length || 0} pillars`);
      setPillars(pillarsData || []);
      
      // Then fetch videos
      console.log('Fetching videos for user:', user.id);
      const { data: videosData, error: videosError } = await supabase
        .from('videos')
        .select('*')
        .eq('user_id', user.id)
        .order('scheduled_date', { ascending: true });
      
      if (videosError) {
        console.error('Error fetching videos:', videosError);
        setError('Failed to load videos: ' + videosError.message);
        return;
      }
      
      console.log(`Successfully loaded ${videosData?.length || 0} videos`);
      setVideos(videosData || []);
      
    } catch (err: any) {
      console.error('Exception in fetchData:', err);
      setError('An unexpected error occurred: ' + (err?.message || 'Unknown error'));
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Don't automatically fetch data on mount - let the parent component control this
  // This prevents race conditions with the connection check

  return { 
    videos, 
    pillars, 
    refetchData: fetchData, 
    isLoading, 
    error 
  };
};
