
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';

export type AlertType = 'youtube_connection' | 'pillar_balance' | 'strategy_drift' | 'trial_ending';

export const useDismissedAlerts = () => {
  const { user } = useAuth();
  const [dismissedAlerts, setDismissedAlerts] = useState<Set<AlertType>>(new Set());
  const [loading, setLoading] = useState(true);

  // Fetch dismissed alerts on mount
  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }

    const fetchDismissedAlerts = async () => {
      try {
        const { data, error } = await supabase
          .from('dismissed_alerts')
          .select('alert_type')
          .eq('user_id', user.id);

        if (error) {
          console.warn('❌ Error fetching dismissed alerts:', error);
          // Continue with empty set if table doesn't exist
          setDismissedAlerts(new Set());
          return;
        }

        const dismissedSet = new Set(data?.map(item => item.alert_type as AlertType) || []);
        setDismissedAlerts(dismissedSet);
      } catch (error) {
        console.error('Error fetching dismissed alerts:', error);
        // Continue with empty set on error
        setDismissedAlerts(new Set());
      } finally {
        setLoading(false);
      }
    };

    fetchDismissedAlerts();
  }, [user]);

  const dismissAlert = async (alertType: AlertType) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('dismissed_alerts')
        .upsert({
          user_id: user.id,
          alert_type: alertType,
          dismissed_at: new Date().toISOString()
        });

      if (error) {
        console.warn('❌ Error dismissing alert:', error);
        // Still update local state even if database fails
        setDismissedAlerts(prev => new Set([...prev, alertType]));
        return;
      }

      setDismissedAlerts(prev => new Set([...prev, alertType]));
    } catch (error) {
      console.error('Error dismissing alert:', error);
      // Still update local state even if database fails
      setDismissedAlerts(prev => new Set([...prev, alertType]));
    }
  };

  const undismissAlert = async (alertType: AlertType) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('dismissed_alerts')
        .delete()
        .eq('user_id', user.id)
        .eq('alert_type', alertType);

      if (error) {
        console.warn('❌ Error undismissing alert:', error);
        // Still update local state even if database fails
        setDismissedAlerts(prev => {
          const newSet = new Set(prev);
          newSet.delete(alertType);
          return newSet;
        });
        return;
      }

      setDismissedAlerts(prev => {
        const newSet = new Set(prev);
        newSet.delete(alertType);
        return newSet;
      });
    } catch (error) {
      console.error('Error undismissing alert:', error);
      // Still update local state even if database fails
      setDismissedAlerts(prev => {
        const newSet = new Set(prev);
        newSet.delete(alertType);
        return newSet;
      });
    }
  };

  const isAlertDismissed = (alertType: AlertType): boolean => {
    return dismissedAlerts.has(alertType);
  };

  return {
    dismissAlert,
    undismissAlert,
    isAlertDismissed,
    loading
  };
};
