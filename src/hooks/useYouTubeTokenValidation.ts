
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { YOUTUBE_CONFIG, validateYouTubeConfig, verifyConfigurationConsistency } from '@/config/youtube';

interface TokenValidationResult {
  isValid: boolean;
  needsRefresh: boolean;
  error?: string;
}

export const useYouTubeTokenValidation = (user: any) => {
  const [tokenStatus, setTokenStatus] = useState<TokenValidationResult>({
    isValid: false,
    needsRefresh: false
  });
  const [isValidating, setIsValidating] = useState(false);

  const validateToken = async (accessToken: string): Promise<TokenValidationResult> => {
    try {
      console.log('=== VALIDATING YOUTUBE TOKEN ===');
      console.log('Token length:', accessToken?.length || 0);

      const response = await fetch(
        `${YOUTUBE_CONFIG.ENDPOINTS.TOKEN_INFO}?access_token=${accessToken}`
      );

      const data = await response.json();
      console.log('Token validation response:', response.status, data);

      if (response.ok && data.scope?.includes('youtube')) {
        return { isValid: true, needsRefresh: false };
      } else if (response.status === 400 && data.error === 'invalid_token') {
        return { isValid: false, needsRefresh: true, error: 'Token expired' };
      } else {
        return { isValid: false, needsRefresh: false, error: 'Invalid token scope' };
      }
    } catch (error) {
      console.error('Token validation error:', error);
      return { isValid: false, needsRefresh: true, error: 'Validation failed' };
    }
  };

  const refreshAccessToken = async (refreshToken: string): Promise<string | null> => {
    try {
      // Validate configuration before proceeding
      validateYouTubeConfig();
      verifyConfigurationConsistency();

      console.log('=== REFRESHING YOUTUBE TOKEN ===');
      console.log('Using client ID:', YOUTUBE_CONFIG.CLIENT_ID);
      console.log('Using client secret:', YOUTUBE_CONFIG.CLIENT_SECRET);
      console.log('Refresh token length:', refreshToken?.length || 0);

      const response = await fetch(YOUTUBE_CONFIG.ENDPOINTS.TOKEN_REFRESH, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: YOUTUBE_CONFIG.CLIENT_ID,
          client_secret: YOUTUBE_CONFIG.CLIENT_SECRET,
          refresh_token: refreshToken,
          grant_type: 'refresh_token',
        }),
      });

      const data = await response.json();
      console.log('Token refresh response:', response.status, data);

      if (response.ok && data.access_token) {
        // Update the access token in the database
        const { error } = await supabase
          .from('users')
          .update({ youtube_access_token: data.access_token })
          .eq('id', user.id);

        if (error) {
          console.error('Failed to update access token:', error);
          return null;
        }

        console.log('✅ Access token refreshed successfully');
        return data.access_token;
      } else {
        console.error('❌ Token refresh failed:', {
          status: response.status,
          statusText: response.statusText,
          error: data.error,
          errorDescription: data.error_description,
          fullResponse: data
        });
        return null;
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      return null;
    }
  };

  const checkTokenStatus = async () => {
    if (!user?.youtube_access_token || !user?.youtube_channel_id) {
      setTokenStatus({
        isValid: false,
        needsRefresh: false,
        error: 'No YouTube connection found'
      });
      return;
    }

    setIsValidating(true);

    try {
      let result = await validateToken(user.youtube_access_token);

      if (!result.isValid && result.needsRefresh && user.youtube_refresh_token) {
        console.log('Attempting to refresh expired token...');
        const newToken = await refreshAccessToken(user.youtube_refresh_token);
        
        if (newToken) {
          result = await validateToken(newToken);
          if (result.isValid) {
            toast.success('YouTube token refreshed successfully');
          }
        } else {
          result.error = 'Failed to refresh token - please reconnect';
        }
      }

      setTokenStatus(result);
    } catch (error) {
      console.error('Token status check failed:', error);
      setTokenStatus({
        isValid: false,
        needsRefresh: false,
        error: 'Failed to check token status'
      });
    } finally {
      setIsValidating(false);
    }
  };

  useEffect(() => {
    if (user?.youtube_access_token) {
      checkTokenStatus();
    }
  }, [user?.youtube_access_token]);

  return {
    tokenStatus,
    isValidating,
    checkTokenStatus,
    refreshAccessToken: () => refreshAccessToken(user?.youtube_refresh_token)
  };
};
