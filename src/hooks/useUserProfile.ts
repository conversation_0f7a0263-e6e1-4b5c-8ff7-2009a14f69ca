import { useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { useQuery } from '@tanstack/react-query';

// Define a type for the profile
interface UserProfile {
  id: string;
  email: string;
  name?: string;
  subscription_tier?: string;
  subscription_status?: string;
  [key: string]: any; // For other properties
}

export function useUserProfile() {
  const { user } = useAuth();
  
  const {
    data: profile,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['user-profile', user?.id],
    queryFn: async (): Promise<UserProfile | null> => {
      if (!user) return null;

      console.log('Fetching profile for user:', user.id);

      try {
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Supabase error fetching profile:', error);
          console.error('Error details:', JSON.stringify(error));
          throw error;
        }

        console.log('Profile data received:', data);

        // Merge with auth user data
        return {
          ...data,
          id: user.id,
          email: user.email
        };
      } catch (error) {
        console.error('Exception in useUserProfile:', error);
        throw error;
      }
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });

  // Handle errors using useEffect instead of onError callback
  useEffect(() => {
    if (error) {
      console.error('Query error in useUserProfile:', error);
      toast.error('Failed to load profile');
    }
  }, [error]);

  return {
    profile,
    isLoading,
    error,
    refetch
  };
}
