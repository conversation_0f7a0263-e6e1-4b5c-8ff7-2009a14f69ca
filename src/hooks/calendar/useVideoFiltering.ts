
import { useMemo } from 'react';
import { VideoIdea } from '@/components/Ideas/types';

export const useVideoFiltering = (
  videos: VideoIdea[],
  visiblePillars: Set<string>,
  showPublished: boolean
) => {
  return useMemo(() => {
    const getVideosForDate = (date: Date) => {
      const dateString = date.toISOString().split('T')[0];
      return videos.filter(video => {
        // Filter by pillar visibility
        if (video.pillar_id && !visiblePillars.has(video.pillar_id)) return false;
        
        // Filter by published visibility
        if (!showPublished && video.status === 'published') return false;
        
        // Check both scheduled_date and published_at
        if (video.scheduled_date === dateString) return true;
        if (video.published_at) {
          const publishedDate = new Date(video.published_at).toISOString().split('T')[0];
          return publishedDate === dateString;
        }
        return false;
      });
    };

    return {
      getVideosForDate
    };
  }, [videos, visiblePillars, showPublished]);
};
