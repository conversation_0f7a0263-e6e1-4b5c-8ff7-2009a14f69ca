
import { useState, useEffect } from 'react';

interface ContentPillar {
  id: string;
  name: string;
  color: string;
  target_percentage: number;
}

export const useCalendarState = (pillars: ContentPillar[]) => {
  const [visiblePillars, setVisiblePillars] = useState<Set<string>>(new Set());
  const [showPublished, setShowPublished] = useState(true);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Update visible pillars when pillars change - default to all visible
  useEffect(() => {
    if (pillars.length > 0) {
      setVisiblePillars(new Set(pillars.map(p => p.id)));
    }
  }, [pillars]);

  const handleTogglePillar = (pillarId: string) => {
    const newVisiblePillars = new Set(visiblePillars);
    if (newVisiblePillars.has(pillarId)) {
      newVisiblePillars.delete(pillarId);
    } else {
      newVisiblePillars.add(pillarId);
    }
    setVisiblePillars(newVisiblePillars);
  };

  const handleToggleAll = () => {
    if (visiblePillars.size === pillars.length) {
      setVisiblePillars(new Set());
    } else {
      setVisiblePillars(new Set(pillars.map(p => p.id)));
    }
  };

  const handleTogglePublished = () => {
    setShowPublished(!showPublished);
  };

  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return {
    visiblePillars,
    showPublished,
    sidebarCollapsed,
    handleTogglePillar,
    handleToggleAll,
    handleTogglePublished,
    handleToggleSidebar
  };
};
