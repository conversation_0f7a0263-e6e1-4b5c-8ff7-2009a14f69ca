
import { useState } from 'react';
import { VideoIdea } from '@/components/Ideas/types';

export const useDragAndDrop = (onVideoUpdated: () => void) => {
  const [draggedVideo, setDraggedVideo] = useState<VideoIdea | null>(null);

  const handleDragStart = (e: React.DragEvent, video: VideoIdea) => {
    setDraggedVideo(video);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = async (e: React.DragEvent, date: Date) => {
    e.preventDefault();
    if (!draggedVideo) return;

    try {
      // Format the date as YYYY-MM-DD
      const formattedDate = date.toISOString().split('T')[0];

      // Update the video's scheduled date
      // This is a placeholder - you would implement your actual update logic here
      console.log(`Moving video ${draggedVideo.id} to ${formattedDate}`);

      // Call the callback to refresh data
      onVideoUpdated();
    } catch (error) {
      console.error('Error updating video date:', error);
    } finally {
      setDraggedVideo(null);
    }
  };

  return {
    draggedVideo,
    handleDragStart,
    handleDragOver,
    handleDrop
  };
};
