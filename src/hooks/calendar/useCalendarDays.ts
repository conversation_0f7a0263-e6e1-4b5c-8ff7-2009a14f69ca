
import { useMemo } from 'react';
import { 
  startOfMonth, 
  endOfMonth, 
  startOfWeek, 
  endOfWeek, 
  eachDayOfInterval,
  isSameMonth
} from 'date-fns';

export const useCalendarDays = (currentDate: Date) => {
  return useMemo(() => {
    // Get the start and end of the month
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    
    // Get the start and end of the calendar (including days from prev/next months)
    const calendarStart = startOfWeek(monthStart);
    const calendarEnd = endOfWeek(monthEnd);
    
    // Generate all days in the calendar view
    const calendarDays = eachDayOfInterval({
      start: calendarStart,
      end: calendarEnd
    });
    
    // Add metadata to each day
    const daysWithMetadata = calendarDays.map(date => ({
      date,
      isCurrentMonth: isSameMonth(date, currentDate)
    }));
    
    return daysWithMetadata;
  }, [currentDate]);
};
