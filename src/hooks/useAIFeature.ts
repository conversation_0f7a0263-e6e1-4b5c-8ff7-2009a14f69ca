import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';

export function useAIFeature() {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  const logAIUsage = async (featureType, creditsUsed, metadata = {}) => {
    if (!user) return false;

    try {
      const usageData = {
        user_id: user.id,
        feature_type: featureType,
        credits_used: creditsUsed,
        metadata
      };

      console.log('🔄 useAIFeature: Using direct Supabase query for logging');
      const { error } = await supabase
        .from('ai_usage_logs')
        .insert(usageData);

      return !error;
    } catch (error) {
      console.error('Error logging AI usage:', error);
      return false;
    }
  };

  // Example AI feature with usage logging
  const generateTitle = async (topic, options = {}) => {
    if (!user) {
      console.error('You must be logged in to use AI features');
      return null;
    }
    
    setIsLoading(true);
    try {
      // Log the usage first (1 credit for title generation)
      await logAIUsage('title_generation', 1, { topic });
      
      // Call your AI endpoint
      const response = await fetch('/api/generate-title', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic,
          ...options
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate title');
      }
      
      const data = await response.json();
      return data.title;
    } catch (error) {
      console.error('Error generating title:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    logAIUsage,
    generateTitle,
    // Add other AI features here
  };
}
