import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { ContentPillar } from '@/types/pillar';

export const useContentPillars = () => {
  const [pillars, setPillars] = useState<ContentPillar[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    const fetchPillars = async () => {
      if (!user) {
        setPillars([]);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('content_pillars')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: true });

        if (error) throw error;

        // Transform database pillars to match ContentPillar interface
        const transformedPillars = (data || []).map(pillar => ({
          ...pillar,
          actual_percentage: 0,
          video_count: 0,
          avg_views: 0,
          best_day: 'Monday',
          best_day_boost: 0
        }));

        setPillars(transformedPillars);
      } catch (error) {
        console.error('Error fetching content pillars:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPillars();
  }, [user]);

  return { pillars, loading };
};