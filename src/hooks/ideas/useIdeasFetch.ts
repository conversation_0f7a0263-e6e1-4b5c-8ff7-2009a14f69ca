
import { useCallback, useEffect } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { VideoIdea, ContentPillar } from '@/components/Ideas/types';

export const useIdeasFetch = (
  user: User | null,
  setIdeas: (ideas: VideoIdea[]) => void,
  setPillars: (pillars: ContentPillar[]) => void
) => {
  const fetchData = useCallback(async () => {
    if (!user) {
      setIdeas([]);
      setPillars([]);
      return;
    }
    
    try {
      // Fetch pillars first
      const { data: pillarsData, error: pillarsError } = await supabase
        .from('content_pillars')
        .select('*')
        .eq('user_id', user.id);
      
      if (pillarsError && pillarsError.code !== 'PGRST116') {
        console.error('Error fetching pillars:', pillarsError);
        // Don't show error toast, just continue with empty pillars
        setPillars([]);
      }
      
      // Log pillars data for debugging
      console.log('Fetched pillars:', pillarsData);

      // Transform database pillars to match ContentPillar interface
      const transformedPillars = (pillarsData || []).map(pillar => ({
        ...pillar,
        actual_percentage: 0,
        video_count: 0,
        avg_views: 0,
        best_day: 'Monday',
        best_day_boost: 0
      }));

      setPillars(transformedPillars);
      
      // Then fetch ideas
      const { data: ideasData, error: ideasError } = await supabase
        .from('videos')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'idea')
        .order('created_at', { ascending: false });
      
      if (ideasError) {
        console.error('Error fetching ideas:', ideasError);
        toast.error('Failed to load ideas');
        return;
      }
      
      setIdeas(ideasData || []);
    } catch (error) {
      console.error('Exception in fetchData:', error);
      toast.error('Failed to load data');
    }
  }, [user, setIdeas, setPillars]);

  // Fetch data on component mount and when user changes
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { fetchData };
};
