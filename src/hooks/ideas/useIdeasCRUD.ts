import { User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { VideoIdea, NewIdea } from '@/components/Ideas/types';

export const useIdeasCRUD = (
  user: User | null,
  newIdea: NewIdea,
  setNewIdea: (idea: NewIdea) => void,
  setIsDialogOpen: (open: boolean) => void,
  setEditingIdea: (idea: VideoIdea | null) => void,
  fetchData: () => void
) => {
  const addIdea = async (): Promise<boolean> => {
    if (!user || !newIdea.title || !newIdea.pillar_id) {
      toast.error('Please fill in all required fields');
      return false;
    }

    try {
      const { error } = await supabase
        .from('videos')
        .insert({
          user_id: user.id,
          title: newIdea.title,
          description: newIdea.description,
          pillar_id: newIdea.pillar_id,
          status: 'idea',
          priority: newIdea.priority,
          scheduled_date: newIdea.scheduled_date || null
        });

      if (error) {
        console.error('Error creating idea:', error);
        toast.error('Failed to create idea');
        return false;
      } else {
        toast.success('Video idea created successfully!');
        setNewIdea({ title: '', description: '', pillar_id: '', priority: 'Medium', scheduled_date: '' });
        setIsDialogOpen(false);
        fetchData();
        return true;
      }
    } catch (err) {
      console.error('Exception creating idea:', err);
      toast.error('Failed to create idea');
      return false;
    }
  };

  const addGeneratedIdea = async (ideaData: {
    title: string;
    description: string;
    pillar_id: string;
    topic?: string;
    hook?: string;
    script?: string;
    seo_tags?: string[];
    hashtags?: string;
  }) => {
    if (!user) return;

    const { error } = await supabase
      .from('videos')
      .insert({
        user_id: user.id,
        title: ideaData.title,
        description: ideaData.description,
        pillar_id: ideaData.pillar_id,
        status: 'idea',
        priority: 'Medium'
      });

    if (error) {
      console.error('Error saving AI-generated idea:', error);
      toast.error('Failed to save AI-generated idea');
    } else {
      toast.success('AI-generated video idea saved successfully!');
      fetchData();
    }
  };

  const updateIdea = async (ideaId: string, updates: Partial<VideoIdea>): Promise<void> => {
    if (!user) {
      console.error('❌ No user found for update');
      throw new Error('User not authenticated');
    }

    console.log('🔄 Updating idea:', { ideaId, updates });

    try {
      const { data, error } = await supabase
        .from('videos')
        .update(updates)
        .eq('id', ideaId)
        .eq('user_id', user.id)
        .select(); // Add select to get the updated data back

      if (error) {
        console.error('❌ Database error updating idea:', error);
        toast.error(`Failed to update idea: ${error.message}`);
        throw error; // Throw error so it can be caught upstream
      } else {
        console.log('✅ Idea updated successfully:', data);
        toast.success('Idea updated successfully');
        setEditingIdea(null);
        fetchData(); // Refresh the data
      }
    } catch (error) {
      console.error('❌ Exception updating idea:', error);
      toast.error('An unexpected error occurred');
      throw error; // Re-throw to allow handling in the component
    }
  };

  const updateIdeaTitle = async (ideaId: string, newTitle: string) => {
    await updateIdea(ideaId, { title: newTitle });
  };

  const deleteIdea = async (ideaId: string, ideaTitle: string) => {
    if (!user) return;

    const { error } = await supabase
      .from('videos')
      .delete()
      .eq('id', ideaId)
      .eq('user_id', user.id);

    if (error) {
      console.error('Error deleting idea:', error);
      toast.error('Failed to delete idea');
    } else {
      toast.success(`"${ideaTitle}" deleted successfully`);
      fetchData();
    }
  };

  const moveToCalendar = async (ideaId: string, ideaTitle: string) => {
    await updateIdea(ideaId, { status: 'planned' });
    toast.success(`"${ideaTitle}" moved to calendar`);
  };

  return {
    addIdea,
    addGeneratedIdea,
    updateIdea,
    updateIdeaTitle,
    deleteIdea,
    moveToCalendar
  };
};
