
import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { AIContentService } from '@/services/aiContentService';
import { ContentPillar } from '@/components/Ideas/types';

export const useAIContentGeneration = (
  pillars: ContentPillar[],
  setLoading: (loading: boolean) => void,
  setGeneratedTopics: (topics: any[]) => void,
  setGeneratedTitles: (titles: string[]) => void,
  logAIUsage: (type: string, credits: number) => void
) => {
  const { user } = useAuth();

  const parseTopicsResponse = (topics: any): any[] => {
    console.log('Parsing topics response:', topics);
    
    // If it's already an array, return it
    if (Array.isArray(topics)) {
      return topics;
    }
    
    // If it's a string, try to parse it as JSON
    if (typeof topics === 'string') {
      try {
        // Remove markdown code blocks if present
        const cleanedTopics = topics.replace(/```json\n?|\n?```/g, '').trim();
        const parsed = JSON.parse(cleanedTopics);
        console.log('Successfully parsed JSON topics:', parsed);
        return Array.isArray(parsed) ? parsed : [];
      } catch (error) {
        console.error('Failed to parse topics JSON:', error);
        // Try to extract topics from text format
        const lines = topics.split('\n').filter(line => line.trim());
        return lines.map((line, index) => ({
          title: line.replace(/^\d+\.\s*/, '').trim(),
          description: `Topic ${index + 1}`,
          reason: 'Generated topic'
        }));
      }
    }
    
    // If it's an object, wrap it in an array
    if (typeof topics === 'object' && topics !== null) {
      return [topics];
    }
    
    console.warn('Unknown topics format:', topics);
    return [];
  };

  const generateTopics = async (pillarId: string) => {
    if (!user || !pillarId) {
      console.log('Cannot generate topics: missing user or pillarId', { user: !!user, pillarId });
      return;
    }

    console.log('Starting topic generation for pillar:', pillarId);
    setLoading(true);
    
    try {
      const rawTopics = await AIContentService.generateTopics({ pillarId, pillars }, user.id);
      console.log('Raw topics from service:', rawTopics);
      
      // Parse and ensure topics is always an array
      const parsedTopics = parseTopicsResponse(rawTopics);
      console.log('Parsed topics:', parsedTopics);
      
      setGeneratedTopics(parsedTopics);
      
      // Log AI usage
      logAIUsage('topic-generation', 3);
    } catch (error) {
      console.error('Topic generation failed:', error);
      setGeneratedTopics([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  const generateContent = async (type: string, prompt: string, creditsUsed: number): Promise<string[]> => {
    if (!user) {
      console.log('Cannot generate content: no user');
      return [];
    }

    console.log('Starting content generation:', { type, prompt: prompt.slice(0, 100) + '...' });
    setLoading(true);
    
    try {
      const content = await AIContentService.generateContent({
        type,
        prompt,
        creditsUsed,
        userId: user.id
      });
      
      console.log('Content generated successfully:', { type, contentLength: content.length });
      
      // Ensure content is always an array
      const safeContent = Array.isArray(content) ? content : [];
      return safeContent;
    } catch (error) {
      console.error('Content generation failed:', error);
      return []; // Return empty array on error
    } finally {
      setLoading(false);
    }
  };

  const generateScript = async (formData: any, videoLength: string): Promise<string[]> => {
    if (!user) {
      console.log('Cannot generate script: no user');
      return [];
    }

    console.log('Starting script generation with length:', videoLength);
    setLoading(true);
    
    try {
      const pillar = pillars.find(p => p.id === formData.pillar_id);
      
      const result = await AIContentService.generateScript({
        title: formData.title,
        description: formData.topic,
        pillar: pillar?.name || '',
        videoLength,
        tone: 'engaging'
      }, user.id);
      
      console.log('Script generated successfully:', result);
      
      // Log AI usage
      logAIUsage('script-generation', 5);
      
      // Return script as array
      return [result.script];
    } catch (error) {
      console.error('Script generation failed:', error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const generateSEOTags = async (formData: any, pillars: ContentPillar[]) => {
    if (!user) {
      console.log('Cannot generate SEO tags: no user');
      return { tags: [], hashtags: '' };
    }

    console.log('Starting SEO tags generation for:', formData.title);
    setLoading(true);
    
    try {
      const result = await AIContentService.generateSEOTags({
        title: formData.title,
        topic: formData.topic,
        pillarId: formData.pillar_id,
        pillars
      }, user.id);
      
      console.log('SEO tags generated successfully:', result);
      return result;
    } catch (error) {
      console.error('SEO generation failed:', error);
      return { tags: [], hashtags: '' };
    } finally {
      setLoading(false);
    }
  };

  return {
    generateTopics,
    generateContent,
    generateScript,
    generateSEOTags
  };
};
