
import { useAIWizard } from '@/hooks/ideas/useAIWizard';
import { useAIContentGeneration } from '@/hooks/ideas/useAIContentGeneration';
import { ContentPillar } from '@/components/Ideas/types';

export const useEnhancedAIWizard = (
  pillars: ContentPillar[],
  onIdeaGenerated: (ideaData: any) => void,
  onClose: () => void
) => {
  const wizardState = useAIWizard();
  const contentGeneration = useAIContentGeneration(
    pillars,
    wizardState.setLoading,
    wizardState.setGeneratedTopics,
    wizardState.setGeneratedTitles,
    wizardState.logAIUsage
  );

  const steps = [
    { id: 0, title: 'Topic Selection', icon: 'Lightbulb', description: 'Choose or generate video topics' },
    { id: 1, title: 'Title & Hook', icon: 'Target', description: 'Create compelling titles and hooks' },
    { id: 2, title: 'SEO & Tags', icon: 'Hash', description: 'Optimize for search and discovery' }
  ];

  const handleClose = () => {
    wizardState.resetWizard();
    onClose();
  };

  const handleNext = () => {
    console.log('handleNext called, current step:', wizardState.currentStep);
    console.log('Current form data:', wizardState.formData);
    
    if (wizardState.currentStep < steps.length - 1) {
      const newCompletedSteps = [...wizardState.completedSteps, wizardState.currentStep];
      const newCurrentStep = wizardState.currentStep + 1;
      
      console.log('Moving to step:', newCurrentStep);
      console.log('Completed steps will be:', newCompletedSteps);
      
      wizardState.setCompletedSteps(newCompletedSteps);
      wizardState.setCurrentStep(newCurrentStep);
    } else {
      console.log('Already at last step, should finish');
      handleFinish();
    }
  };

  const handleBack = () => {
    if (wizardState.currentStep > 0) {
      wizardState.setCurrentStep(wizardState.currentStep - 1);
    }
  };

  const handleFinish = async () => {
    console.log('handleFinish called with formData:', wizardState.formData);
    
    // Generate SEO tags if not already done
    let finalFormData = { ...wizardState.formData };
    
    if (!finalFormData.tags || finalFormData.tags.length === 0) {
      console.log('Generating SEO tags before finishing...');
      const { tags, hashtags } = await contentGeneration.generateSEOTags(wizardState.formData, pillars);
      finalFormData = {
        ...finalFormData,
        tags,
        hashtags
      };
    }

    console.log('Final form data:', finalFormData);
    onIdeaGenerated(finalFormData);
    handleClose();
  };

  const handleTopicSelect = (topic: string, pillarId: string) => {
    console.log('handleTopicSelect called:', { topic, pillarId });
    const updatedFormData = { 
      ...wizardState.formData, 
      topic, 
      pillar_id: pillarId 
    };
    wizardState.setFormData(updatedFormData);
    console.log('Updated form data after topic select:', updatedFormData);
    handleNext();
  };

  const handleTitleSelect = (title: string) => {
    console.log('handleTitleSelect called:', title);
    const updatedFormData = { 
      ...wizardState.formData, 
      title 
    };
    wizardState.setFormData(updatedFormData);
    console.log('Updated form data after title select:', updatedFormData);
  };

  const handleHookSelect = (hook: string) => {
    console.log('handleHookSelect called:', hook);
    const updatedFormData = { 
      ...wizardState.formData, 
      hook 
    };
    wizardState.setFormData(updatedFormData);
    console.log('Updated form data after hook select:', updatedFormData);
  };

  return {
    wizardState,
    contentGeneration,
    steps,
    handleClose,
    handleNext,
    handleBack,
    handleFinish,
    handleTopicSelect,
    handleTitleSelect,
    handleHookSelect
  };
};
