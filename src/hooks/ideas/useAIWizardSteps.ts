
import { useState } from 'react';

export interface WizardStep {
  id: number;
  title: string;
  icon: string;
  description: string;
}

export const wizardSteps: WizardStep[] = [
  { id: 0, title: 'Starting Point', icon: 'Lightbulb', description: 'Choose your starting point' },
  { id: 1, title: 'Title & Hook', icon: 'Target', description: 'Create compelling titles and hooks' },
  { id: 2, title: 'Script Creation', icon: 'Edit', description: 'Generate script content' },
  { id: 3, title: 'Publishing Essentials', icon: 'Hash', description: 'Description and SEO optimization' }
];

export const useAIWizardSteps = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [skippedSteps, setSkippedSteps] = useState<number[]>([]);

  const goToNextStep = () => {
    if (currentStep < wizardSteps.length - 1) {
      setCompletedSteps(prev => [...prev, currentStep]);
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (stepIndex: number) => {
    if (stepIndex <= Math.max(...completedSteps, -1) + 1 && stepIndex >= 0) {
      setCurrentStep(stepIndex);
    }
  };

  const skipStep = (stepIndex: number) => {
    setSkippedSteps(prev => [...prev, stepIndex]);
    setCompletedSteps(prev => [...prev, stepIndex]);
    if (stepIndex === currentStep) {
      goToNextStep();
    }
  };

  const completeStep = (stepIndex: number) => {
    setCompletedSteps(prev => [...prev, stepIndex]);
  };

  const resetSteps = () => {
    setCurrentStep(0);
    setCompletedSteps([]);
    setSkippedSteps([]);
  };

  const isStepCompleted = (stepIndex: number) => {
    return completedSteps.includes(stepIndex);
  };

  const isStepSkipped = (stepIndex: number) => {
    return skippedSteps.includes(stepIndex);
  };

  const getProgress = () => {
    return ((currentStep + 1) / wizardSteps.length) * 100;
  };

  return {
    currentStep,
    setCurrentStep,
    completedSteps,
    setCompletedSteps,
    skippedSteps,
    setSkippedSteps,
    steps: wizardSteps,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    skipStep,
    completeStep,
    resetSteps,
    isStepCompleted,
    isStepSkipped,
    getProgress
  };
};
