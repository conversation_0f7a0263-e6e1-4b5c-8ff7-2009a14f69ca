
import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useWizardFormData } from '@/components/Ideas/WizardFormData';
import { useAIWizardSteps } from './useAIWizardSteps';

export const useAIWizard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  
  // Starting Point options
  const [startingPointOption, setStartingPointOption] = useState('generate');
  const [selectedPillarForTopics, setSelectedPillarForTopics] = useState('');
  const [manualTopic, setManualTopic] = useState('');
  const [videoTitle, setVideoTitle] = useState('');
  const [roughIdea, setRoughIdea] = useState('');
  
  // Generated content arrays
  const [generatedTopics, setGeneratedTopics] = useState<any[]>([]);
  const [generatedTitles, setGeneratedTitles] = useState<string[]>([]);
  const [generatedHooks, setGeneratedHooks] = useState<string[]>([]);
  const [generatedScripts, setGeneratedScripts] = useState<string[]>([]);
  const [generatedDescriptions, setGeneratedDescriptions] = useState<string[]>([]);

  const { formData, setFormData, resetFormData } = useWizardFormData();
  const stepManager = useAIWizardSteps();

  const logAIUsage = async (type: string, creditsUsed: number) => {
    console.log(`AI Usage: ${type} - ${creditsUsed} credits`);
  };

  const resetWizard = () => {
    resetFormData();
    stepManager.resetSteps();
    setStartingPointOption('generate');
    setSelectedPillarForTopics('');
    setManualTopic('');
    setVideoTitle('');
    setRoughIdea('');
    setGeneratedTopics([]);
    setGeneratedTitles([]);
    setGeneratedHooks([]);
    setGeneratedScripts([]);
    setGeneratedDescriptions([]);
  };

  const clearGeneratedContent = (contentType: string) => {
    switch (contentType) {
      case 'titles':
        setGeneratedTitles([]);
        break;
      case 'hooks':
        setGeneratedHooks([]);
        break;
      case 'scripts':
        setGeneratedScripts([]);
        break;
      case 'descriptions':
        setGeneratedDescriptions([]);
        break;
    }
  };

  return {
    user,
    loading,
    setLoading,
    
    // Starting Point State
    startingPointOption,
    setStartingPointOption,
    selectedPillarForTopics,
    setSelectedPillarForTopics,
    manualTopic,
    setManualTopic,
    videoTitle,
    setVideoTitle,
    roughIdea,
    setRoughIdea,
    
    // Generated Content
    generatedTopics,
    setGeneratedTopics,
    generatedTitles,
    setGeneratedTitles,
    generatedHooks,
    setGeneratedHooks,
    generatedScripts,
    setGeneratedScripts,
    generatedDescriptions,
    setGeneratedDescriptions,
    
    // Form and Step Management
    formData,
    setFormData,
    resetFormData,
    logAIUsage,
    resetWizard,
    clearGeneratedContent,
    ...stepManager
  };
};
