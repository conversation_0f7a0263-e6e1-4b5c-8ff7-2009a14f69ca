
import { useState } from 'react';
import { useIdeasData } from '@/hooks/useIdeasData';
import { useIdeasFilters } from '@/hooks/useIdeasFilters';

export const useIdeasPageData = () => {
  const [activeTab, setActiveTab] = useState('all-ideas');
  
  const {
    ideas,
    pillars,
    isDialogOpen,
    setIsDialogOpen,
    editingIdea,
    setEditingIdea,
    newIdea,
    setNewIdea,
    addIdea,
    updateIdea,
    updateIdeaTitle,
    deleteIdea,
    moveToCalendar
  } = useIdeasData();

  const {
    searchTerm,
    setSearchTerm,
    filterPillar,
    setFilterPillar,
    filterPriority,
    setFilterPriority,
    filterStatus,
    setFilterStatus,
    groupedIdeas
  } = useIdeasFilters(ideas, pillars);

  const handleStatusChange = (ideaId: string, status: string) => {
    updateIdea(ideaId, { status });
  };

  return {
    activeTab,
    setActiveTab,
    pillars,
    isDialogOpen,
    setIsDialogOpen,
    editingIdea,
    setEditingIdea,
    newIdea,
    setNewIdea,
    addIdea,
    updateIdea,
    updateIdeaTitle,
    deleteIdea,
    moveToCalendar,
    searchTerm,
    setSearchTerm,
    filterPillar,
    setFilterPillar,
    filterPriority,
    setFilterPriority,
    filterStatus,
    setFilterStatus,
    groupedIdeas,
    handleStatusChange
  };
};
