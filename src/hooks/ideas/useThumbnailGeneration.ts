
import { useState } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';

interface GeneratedThumbnail {
  id: string;
  imageUrl: string;
  text: string;
  template: string;
}

export const useThumbnailGeneration = () => {
  const { user } = useAuth();
  const [videoTitle, setVideoTitle] = useState('');
  const [category, setCategory] = useState('');
  const [colorScheme, setColorScheme] = useState('');
  const [thumbnails, setThumbnails] = useState<GeneratedThumbnail[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [copiedThumbnail, setCopiedThumbnail] = useState<string | null>(null);

  const generateThumbnails = async () => {
    if (!videoTitle.trim() || !category || !colorScheme || !user) return;

    setIsGenerating(true);
    try {
      const { data, error } = await supabase.functions.invoke('generate-thumbnails', {
        body: { 
          title: videoTitle.trim(),
          category,
          colorScheme,
          user_id: user.id 
        }
      });

      if (error) throw error;

      setThumbnails(data.thumbnails);
      toast.success('Thumbnails generated successfully!');
    } catch (error) {
      console.error('Error generating thumbnails:', error);
      toast.error('Failed to generate thumbnails');
    } finally {
      setIsGenerating(false);
    }
  };

  const resetGenerator = () => {
    setVideoTitle('');
    setCategory('');
    setColorScheme('');
    setThumbnails([]);
    setCopiedThumbnail(null);
  };

  return {
    videoTitle,
    setVideoTitle,
    category,
    setCategory,
    colorScheme,
    setColorScheme,
    thumbnails,
    isGenerating,
    copiedThumbnail,
    setCopiedThumbnail,
    generateThumbnails,
    resetGenerator
  };
};
