
import { useState } from 'react';

export const useCreationToolsState = () => {
  // Core tool states
  const [showScriptGenerator, setShowScriptGenerator] = useState(false);
  const [showScriptWriter, setShowScriptWriter] = useState(false);
  const [showTitleOptimizer, setShowTitleOptimizer] = useState(false);
  const [showDescriptionGenerator, setShowDescriptionGenerator] = useState(false);

  return {
    showScriptGenerator,
    setShowScriptGenerator,
    showScriptWriter,
    setShowScriptWriter,
    showTitleOptimizer,
    setShowTitleOptimizer,
    showDescriptionGenerator,
    setShowDescriptionGenerator
  };
};
