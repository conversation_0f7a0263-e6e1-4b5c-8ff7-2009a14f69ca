
import { toast } from 'sonner';
import { VideoIdea, ContentPillar } from '@/components/Ideas/types';

interface GeneratedThumbnail {
  id: string;
  imageUrl: string;
  text: string;
  template: string;
}

export const useThumbnailActions = (
  videoTitle: string,
  pillars: ContentPillar[],
  onAddIdea: (ideas: Array<{title: string; description: string}>, pillarId: string) => void,
  setCopiedThumbnail: (id: string | null) => void
) => {
  const downloadThumbnail = async (thumbnail: GeneratedThumbnail) => {
    try {
      const link = document.createElement('a');
      link.href = thumbnail.imageUrl;
      link.download = `thumbnail-${thumbnail.text.replace(/\s+/g, '-').toLowerCase()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success('Thumbnail downloaded!');
    } catch (error) {
      toast.error('Failed to download thumbnail');
    }
  };

  const saveToIdea = async (thumbnail: GeneratedThumbnail) => {
    if (pillars.length === 0) {
      toast.error('Please create content pillars first');
      return;
    }

    try {
      const defaultPillar = pillars[0];
      const newIdeas = [{
        title: videoTitle,
        description: `Thumbnail concept: ${thumbnail.text}`
      }];
      
      await onAddIdea(newIdeas, defaultPillar.id);
      toast.success('Saved to Ideas Bank!');
    } catch (error) {
      toast.error('Failed to save to ideas');
    }
  };

  const copyThumbnailUrl = async (thumbnail: GeneratedThumbnail) => {
    try {
      await navigator.clipboard.writeText(thumbnail.imageUrl);
      setCopiedThumbnail(thumbnail.id);
      toast.success('Thumbnail URL copied!');
      setTimeout(() => setCopiedThumbnail(null), 2000);
    } catch (error) {
      toast.error('Failed to copy URL');
    }
  };

  return {
    downloadThumbnail,
    saveToIdea,
    copyThumbnailUrl
  };
};
