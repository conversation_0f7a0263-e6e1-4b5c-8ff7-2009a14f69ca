
import { useState } from 'react';
import { supabase } from '@/lib/supabase';
import { youtubeApiService, YouTubeSyncResult } from '@/services/youtubeApiService';

interface SyncStats {
  subscribersUpdated: boolean;
  totalViewsUpdated: boolean;
  videosProcessed: number;
  monthlyViewsCalculated: number;
  avgWatchTimeUpdated: string;
}

export const useYouTubeSyncEnhanced = () => {
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncProgress, setSyncProgress] = useState<string>('');
  const [lastSyncStats, setLastSyncStats] = useState<SyncStats | null>(null);

  const performFullSync = async (user: any): Promise<SyncStats | null> => {
    if (!user?.youtube_access_token || !user?.youtube_channel_id) {
      toast.error('YouTube connection required for sync');
      return null;
    }

    setIsSyncing(true);
    setSyncProgress('Starting YouTube data sync...');

    try {
      console.log('=== STARTING ENHANCED YOUTUBE SYNC ===');
      console.log('User:', user.id, 'Stored Channel:', user.youtube_channel_id);
      console.log('Token available:', !!user.youtube_access_token);

      setSyncProgress('Validating YouTube connection...');
      
      // Test the token first with a simple API call
      const testResponse = await fetch(
        `https://www.googleapis.com/youtube/v3/channels?part=snippet&mine=true`,
        {
          headers: {
            'Authorization': `Bearer ${user.youtube_access_token}`,
            'User-Agent': 'MyContentHub/1.0'
          },
        }
      );

      if (!testResponse.ok) {
        const errorText = await testResponse.text();
        console.error('❌ YouTube API test failed:', testResponse.status, errorText);
        
        if (testResponse.status === 401) {
          throw new Error('YouTube token expired. Please reconnect your channel.');
        } else if (testResponse.status === 403) {
          throw new Error('YouTube API access denied. Please check your permissions.');
        } else {
          throw new Error(`YouTube API error (${testResponse.status}). Please try again.`);
        }
      }

      console.log('✅ Token validation successful');
      setSyncProgress('Fetching YouTube channel data...');
      
      // Perform comprehensive sync using the authenticated user's channel
      const syncResult: YouTubeSyncResult = await youtubeApiService.syncChannelData(
        user.youtube_access_token,
        user.youtube_channel_id
      );

      setSyncProgress('Processing video statistics...');

      // Calculate additional metrics
      const monthlyViews = youtubeApiService.calculateMonthlyViews(syncResult.recentVideos);
      const avgWatchTime = youtubeApiService.calculateAverageWatchTime(syncResult.recentVideos);

      setSyncProgress('Updating database with real YouTube metrics...');

      console.log('💾 Storing real YouTube channel statistics:', {
        subscriberCount: syncResult.channelStats.subscriberCount,
        viewCount: syncResult.channelStats.viewCount,
        videoCount: syncResult.channelStats.videoCount,
        monthlyViews,
        avgWatchTime
      });

      // Update user profile with comprehensive stats from YouTube API
      const { error: updateError } = await supabase
        .from('users')
        .update({
          youtube_subscriber_baseline: syncResult.channelStats.subscriberCount,
          last_youtube_sync: syncResult.syncTimestamp,
          // Store additional YouTube API metrics in preferences
          preferences: {
            ...user.preferences,
            youtube_total_views: syncResult.channelStats.viewCount,
            youtube_video_count: syncResult.channelStats.videoCount,
            youtube_monthly_views: monthlyViews,
            youtube_avg_watch_time: avgWatchTime,
            last_full_sync: syncResult.syncTimestamp,
            sync_source: 'youtube_api'
          }
        })
        .eq('id', user.id);

      if (updateError) {
        console.error('❌ Database update error:', updateError);
        throw updateError;
      }

      console.log('✅ Database updated successfully');
      setSyncProgress('Updating video data from YouTube...');

      // Now update individual videos with YouTube data
      let videosUpdated = 0;
      for (const video of syncResult.recentVideos) {
        try {
          // Try to match videos by title or create new ones
          const { data: existingVideos, error: fetchError } = await supabase
            .from('videos')
            .select('*')
            .eq('user_id', user.id)
            .or(`title.ilike.%${video.title.substring(0, 50)}%,youtube_video_id.eq.${video.id}`);

          if (fetchError) {
            console.error('❌ Error fetching existing videos:', fetchError);
            continue;
          }

          if (existingVideos && existingVideos.length > 0) {
            // Update existing video
            const existingVideo = existingVideos[0];
            const { error: updateVideoError } = await supabase
              .from('videos')
              .update({
                youtube_video_id: video.id,
                youtube_thumbnail_url: video.thumbnailUrl,
                views: video.viewCount,
                like_count: video.likeCount,
                comment_count: video.commentCount,
                published_at: video.publishedAt,
                status: 'published'
              })
              .eq('id', existingVideo.id);

            if (updateVideoError) {
              console.error('❌ Error updating video:', updateVideoError);
            } else {
              videosUpdated++;
              console.log(`✅ Updated video: ${video.title} (${video.viewCount} views)`);
            }
          } else {
            // Create new video entry
            const { error: insertVideoError } = await supabase
              .from('videos')
              .insert({
                user_id: user.id,
                title: video.title,
                description: video.description,
                youtube_video_id: video.id,
                youtube_thumbnail_url: video.thumbnailUrl,
                views: video.viewCount,
                like_count: video.likeCount,
                comment_count: video.commentCount,
                published_at: video.publishedAt,
                status: 'published'
              });

            if (insertVideoError) {
              console.error('❌ Error inserting video:', insertVideoError);
            } else {
              videosUpdated++;
              console.log(`✅ Inserted new video: ${video.title} (${video.viewCount} views)`);
            }
          }
        } catch (videoError) {
          console.error('❌ Error processing video:', video.title, videoError);
        }
      }

      setSyncProgress('Sync completed successfully!');

      const stats: SyncStats = {
        subscribersUpdated: true,
        totalViewsUpdated: true,
        videosProcessed: videosUpdated,
        monthlyViewsCalculated: monthlyViews,
        avgWatchTimeUpdated: avgWatchTime
      };

      setLastSyncStats(stats);

      console.log('=== SYNC COMPLETED SUCCESSFULLY ===');
      console.log('📊 Final YouTube API stats stored:', {
        subscribers: syncResult.channelStats.subscriberCount,
        totalViews: syncResult.channelStats.viewCount,
        videoCount: syncResult.channelStats.videoCount,
        monthlyViews,
        avgWatchTime,
        videosUpdated
      });

      toast.success(`YouTube data synced! Updated ${videosUpdated} videos with real metrics from YouTube API`);
      
      return stats;

    } catch (error) {
      console.error('❌ Enhanced YouTube sync failed:', error);
      setSyncProgress('Sync failed');
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      toast.error('Failed to sync YouTube data: ' + errorMessage);
      
      return null;
    } finally {
      setIsSyncing(false);
      setTimeout(() => setSyncProgress(''), 3000);
    }
  };

  return {
    isSyncing,
    syncProgress,
    lastSyncStats,
    performFullSync
  };
};
