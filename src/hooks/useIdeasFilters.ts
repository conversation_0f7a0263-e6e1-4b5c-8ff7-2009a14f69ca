
import { useState, useMemo } from 'react';
import { VideoIdea, ContentPillar } from '@/components/Ideas/types';

export const useIdeasFilters = (ideas: VideoIdea[], pillars: ContentPillar[]) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPillar, setFilterPillar] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  const filteredIdeas = useMemo(() => {
    return ideas.filter(idea => {
      // Exclude published videos - Ideas Bank should only show future/planned content
      if (idea.status === 'published') return false;
      
      const matchesSearch = idea.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           idea.description?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesPillar = filterPillar === 'all' || idea.pillar_id === filterPillar;
      const matchesPriority = filterPriority === 'all' || idea.priority === filterPriority;
      const matchesStatus = filterStatus === 'all' || idea.status === filterStatus;
      
      return matchesSearch && matchesPillar && matchesPriority && matchesStatus;
    });
  }, [ideas, searchTerm, filterPillar, filterPriority, filterStatus]);

  const groupedIdeas = useMemo(() => {
    const grouped: { [key: string]: VideoIdea[] } = {};
    
    // Group by pillar
    pillars.forEach(pillar => {
      grouped[pillar.id] = filteredIdeas.filter(idea => idea.pillar_id === pillar.id);
    });
    
    // Unassigned ideas
    grouped['unassigned'] = filteredIdeas.filter(idea => !idea.pillar_id);
    
    return grouped;
  }, [filteredIdeas, pillars]);

  return {
    searchTerm,
    setSearchTerm,
    filterPillar,
    setFilterPillar,
    filterPriority,
    setFilterPriority,
    filterStatus,
    setFilterStatus,
    filteredIdeas,
    groupedIdeas
  };
};
