
import { useMemo } from 'react';
import { useDismissedAlerts } from './useDismissedAlerts';

interface UseInsightsNotificationsProps {
  pillars: any[];
  videoCount: number;
  user?: any;
}

export const useInsightsNotifications = ({ pillars, videoCount, user }: UseInsightsNotificationsProps) => {
  const { isAlertDismissed, loading } = useDismissedAlerts();

  const hasNewInsights = useMemo(() => {
    if (loading) return false;

    // Calculate conditions for each alert type
    const driftPillars = pillars.filter(pillar => 
      Math.abs(pillar.actual_percentage - pillar.target_percentage) > 10
    );
    const totalPercentage = pillars.reduce((sum, pillar) => sum + pillar.target_percentage, 0);
    const hasBalanceIssue = Math.abs(totalPercentage - 100) > 5;
    const isYouTubeConnected = !!user?.youtube_channel_id;

    const getTrialDaysLeft = () => {
      if (!user?.current_period_end) return 7;
      const trialEnd = new Date(user.current_period_end);
      const now = new Date();
      const diffTime = trialEnd.getTime() - now.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return Math.max(0, diffDays);
    };

    const shouldShowTrialAlert = user && 
      user.subscription_status === 'trialing' && 
      (!user.subscription_tier || user.subscription_tier === 'starter') && 
      getTrialDaysLeft() > 0;

    // Check each alert type - only count as "new" if not dismissed
    const activeAlerts = [
      // Trial ending alert
      shouldShowTrialAlert && !isAlertDismissed('trial_ending'),
      // YouTube connection alert
      !isYouTubeConnected && !isAlertDismissed('youtube_connection'),
      // Pillar balance alert
      hasBalanceIssue && pillars.length > 0 && !isAlertDismissed('pillar_balance'),
      // Strategy drift alert
      driftPillars.length > 0 && !isAlertDismissed('strategy_drift')
    ].filter(Boolean);

    return activeAlerts.length > 0;
  }, [pillars, videoCount, user, isAlertDismissed, loading]);

  return { hasNewInsights, loading };
};
