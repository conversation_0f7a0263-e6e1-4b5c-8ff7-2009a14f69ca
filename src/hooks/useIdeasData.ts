
import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { VideoIdea, NewIdea } from '@/components/Ideas/types';
import { ContentPillar } from '@/types/pillar';
import { useIdeasCRUD } from './ideas/useIdeasCRUD';
import { useIdeasFetch } from './ideas/useIdeasFetch';

export const useIdeasData = () => {
  const { user } = useAuth();
  const [ideas, setIdeas] = useState<VideoIdea[]>([]);
  const [pillars, setPillars] = useState<ContentPillar[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingIdea, setEditingIdea] = useState<VideoIdea | null>(null);
  const [newIdea, setNewIdea] = useState<NewIdea>({
    title: '',
    description: '',
    pillar_id: '',
    priority: 'Medium',
    scheduled_date: ''
  });

  const { fetchData } = useIdeasFetch(user, setIdeas, setPillars);
  const { 
    addIdea, 
    addGeneratedIdea, 
    updateIdea, 
    updateIdeaTitle, 
    deleteIdea, 
    moveToCalendar 
  } = useIdeasCRUD(
    user, 
    newIdea, 
    setNewIdea, 
    setIsDialogOpen, 
    setEditingIdea, 
    fetchData
  );

  useEffect(() => {
    fetchData();
  }, [user, fetchData]);

  return {
    ideas,
    pillars,
    isDialogOpen,
    setIsDialogOpen,
    editingIdea,
    setEditingIdea,
    newIdea,
    setNewIdea,
    addIdea,
    addGeneratedIdea,
    updateIdea,
    updateIdeaTitle,
    deleteIdea,
    moveToCalendar,
    fetchData
  };
};
