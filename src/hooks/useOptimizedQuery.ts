
import { useCallback, useRef } from 'react'
import { supabase } from '@/lib/supabase'

interface QueryOptions {
  cacheKey?: string
  cacheTime?: number // milliseconds
  retry?: number
  retryDelay?: number
}

interface CacheEntry {
  data: any
  timestamp: number
  expiresAt: number
}

// Simple in-memory cache
const queryCache = new Map<string, CacheEntry>()

export const useOptimizedQuery = () => {
  const abortControllerRef = useRef<AbortController>()

  const executeQuery = useCallback(async (
    queryFn: () => Promise<any>,
    options: QueryOptions = {}
  ) => {
    const {
      cacheKey,
      cacheTime = 60000, // 1 minute default
      retry = 2,
      retryDelay = 1000
    } = options

    // Check cache first
    if (cacheKey) {
      const cached = queryCache.get(cacheKey)
      if (cached && Date.now() < cached.expiresAt) {
        console.log('Returning cached result for:', cacheKey)
        return cached.data
      }
    }

    // Cancel previous request if still pending
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController()

    let lastError: Error
    
    for (let attempt = 0; attempt <= retry; attempt++) {
      try {
        const result = await queryFn()
        
        // Cache the result
        if (cacheKey && result) {
          queryCache.set(cacheKey, {
            data: result,
            timestamp: Date.now(),
            expiresAt: Date.now() + cacheTime
          })
        }

        return result
      } catch (error) {
        lastError = error as Error
        console.warn(`Query attempt ${attempt + 1} failed:`, error)
        
        // Don't retry if aborted
        if (error instanceof Error && error.name === 'AbortError') {
          throw error
        }
        
        // Don't retry on final attempt
        if (attempt === retry) {
          break
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)))
      }
    }

    throw lastError!
  }, [])

  const invalidateCache = useCallback((cacheKey?: string) => {
    if (cacheKey) {
      queryCache.delete(cacheKey)
    } else {
      queryCache.clear()
    }
  }, [])

  const prefetchQuery = useCallback(async (
    queryFn: () => Promise<any>,
    cacheKey: string,
    cacheTime: number = 300000 // 5 minutes for prefetch
  ) => {
    try {
      const result = await queryFn()
      queryCache.set(cacheKey, {
        data: result,
        timestamp: Date.now(),
        expiresAt: Date.now() + cacheTime
      })
    } catch (error) {
      console.warn('Prefetch failed:', error)
    }
  }, [])

  return {
    executeQuery,
    invalidateCache,
    prefetchQuery
  }
}

// Optimized database queries
export const optimizedQueries = {
  getUserProfile: (userId: string) => 
    supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single(),

  getUserPillars: (userId: string) =>
    supabase
      .from('content_pillars')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: true }),

  getUserVideos: (userId: string, limit?: number) => {
    let query = supabase
      .from('videos')
      .select('id, title, status, pillar_id, views, published_at, scheduled_date')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
    
    if (limit) {
      query = query.limit(limit)
    }
    
    return query
  },

  getUserGoals: (userId: string) =>
    supabase
      .from('goals')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false }),
}
