import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { getAICreditsForTier } from '@/config/subscriptionTiers';

export const useAICredits = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [creditsUsed, setCreditsUsed] = useState(0);
  const [maxCredits, setMaxCredits] = useState(50);
  const [userTier, setUserTier] = useState('analytics_only');
  const [resetDate, setResetDate] = useState<Date | null>(null);

  useEffect(() => {
    if (user) {
      fetchUserTier();
      fetchCreditsUsed();
    }
  }, [user]);

  const fetchUserTier = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('users')
        .select('subscription_tier, ai_credits_reset_date, subscription_credits')
        .eq('id', user.id)
        .single();

      if (error) throw error;

      const tier = data?.subscription_tier || 'analytics_only';
      setUserTier(tier);

      // Set max credits based on tier and subscription credits
      setMaxCredits(data?.subscription_credits || getAICreditsForTier(tier));
      
      if (data?.ai_credits_reset_date) {
        setResetDate(new Date(data.ai_credits_reset_date));
      }
    } catch (error) {
      console.error('Error fetching user tier:', error);
    }
  };

  const fetchCreditsUsed = async () => {
    if (!user) return;

    try {
      // Get current month's usage based on reset date
      const now = new Date();
      let startDate = resetDate || new Date(now.getFullYear(), now.getMonth(), 1);
      
      const { data, error } = await supabase
        .from('ai_usage_logs')
        .select('credits_used')
        .eq('user_id', user.id)
        .gte('created_at', startDate.toISOString());

      if (error) throw error;

      const totalUsed = data?.reduce((sum, log) => sum + log.credits_used, 0) || 0;
      setCreditsUsed(totalUsed);
    } catch (error) {
      console.error('Error fetching AI credits:', error);
    }
  };

  const hasCreditsAvailable = (creditsNeeded: number = 1) => {
    if (userTier === 'ai_pro' && maxCredits >= 500) return true; // High limit for ai_pro
    return (creditsUsed + creditsNeeded) <= maxCredits;
  };

  const useCredits = async (creditsToUse: number, featureType: string, promptUsed?: string, responseReceived?: string) => {
    if (!user) {
      toast.error('Please sign in to use AI features');
      return false;
    }

    if (!hasCreditsAvailable(creditsToUse)) {
      toast.error(
        `Not enough AI credits. You need ${creditsToUse} credits but only have ${maxCredits - creditsUsed} remaining.`,
        {
          action: {
            label: 'Upgrade',
            onClick: () => navigate('/pricing')
          }
        }
      );
      return false;
    }

    try {
      const { error } = await supabase
        .from('ai_usage_logs')
        .insert({
          user_id: user.id,
          credits_used: creditsToUse,
          feature_type: featureType,
          prompt_used: promptUsed,
          response_received: responseReceived
        });

      if (error) throw error;

      // Update local state
      setCreditsUsed(prev => prev + creditsToUse);
      return true;
    } catch (error) {
      console.error('Error logging AI usage:', error);
      toast.error('Failed to process AI request');
      return false;
    }
  };

  const getRemainingCredits = () => {
    if (userTier === 'ai_pro' && maxCredits >= 500) return 'High Limit';
    return Math.max(0, maxCredits - creditsUsed);
  };

  const getResetInfo = () => {
    if (!resetDate) return 'Next month';
    
    const nextReset = new Date(resetDate);
    nextReset.setMonth(nextReset.getMonth() + 1);
    
    return nextReset.toLocaleDateString();
  };

  return {
    creditsUsed,
    maxCredits,
    userTier,
    hasCreditsAvailable,
    useCredits,
    getRemainingCredits,
    getResetInfo,
    refetchCredits: fetchCreditsUsed
  };
};
