import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';

export function useVideosData() {
  const [videos, setVideos] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  const fetchVideos = async () => {
    if (!user) {
      setVideos([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      console.log('🔄 useVideosData: Using direct Supabase query');
      const { data, error } = await supabase
        .from('videos')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setVideos(data || []);
    } catch (error) {
      console.error('Error fetching videos:', error);
      setVideos([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchPublishedVideos = async () => {
    if (!user) {
      setVideos([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      console.log('🔄 useVideosData: Using direct Supabase query for published videos');
      const { data, error } = await supabase
        .from('videos')
        .select('id, title, views, published_at, youtube_video_id, like_count, comment_count, youtube_thumbnail_url')
        .eq('user_id', user.id)
        .eq('status', 'published')
        .not('youtube_video_id', 'is', null)
        .order('published_at', { ascending: false });

      if (error) throw error;

      setVideos(data || []);
    } catch (error) {
      console.error('Error fetching published videos:', error);
      setVideos([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchVideosByPillar = async (pillarId) => {
    if (!user || !pillarId) {
      setVideos([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      console.log('🔄 useVideosData: Using direct Supabase query for pillar videos');
      const { data, error } = await supabase
        .from('videos')
        .select('*')
        .eq('user_id', user.id)
        .eq('pillar_id', pillarId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setVideos(data || []);
    } catch (error) {
      console.error('Error fetching pillar videos:', error);
      setVideos([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchVideos();
    }
  }, [user]);

  return {
    videos,
    isLoading,
    fetchVideos,
    fetchPublishedVideos,
    fetchVideosByPillar
  };
}
