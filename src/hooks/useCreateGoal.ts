import { useState } from 'react';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

interface CreateGoalParams {
  user_id: string;
  name: string;
  metric_type: string;
  target_value: number;
  deadline: string | null;
}

export function useCreateGoal() {
  const [isLoading, setIsLoading] = useState(false);

  const createGoal = async (params: CreateGoalParams) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('goals')
        .insert({
          user_id: params.user_id,
          name: params.name,
          metric_type: params.metric_type,
          target_value: params.target_value,
          deadline: params.deadline,
        })
        .select()
        .single();

      if (error) throw error;
      
      toast.success('Goal created successfully!');
      return data;
    } catch (error) {
      console.error('Error creating goal:', error);
      toast.error('Failed to create goal');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { createGoal, isLoading };
}