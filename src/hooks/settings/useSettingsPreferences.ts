
import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

export interface SettingsPreferences {
  timezone: string;
  email_notifications: boolean;
  marketing_emails: boolean;
  theme: string;
  auto_sync: boolean;
}

const DEFAULT_PREFERENCES: SettingsPreferences = {
  timezone: 'Australia/Brisbane',
  email_notifications: true,
  marketing_emails: false,
  theme: 'dark',
  auto_sync: true
};

export const useSettingsPreferences = () => {
  const { user: authUser } = useAuth();
  const [preferences, setPreferences] = useState<SettingsPreferences>(DEFAULT_PREFERENCES);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (authUser) {
      fetchPreferences();
    }
  }, [authUser]);

  const fetchPreferences = async () => {
    if (!authUser) return;

    try {
      const { data, error } = await supabase
        .from('users')
        .select('preferences')
        .eq('id', authUser.id)
        .single();

      if (error) throw error;
      
      if (data?.preferences && typeof data.preferences === 'object') {
        setPreferences({ ...DEFAULT_PREFERENCES, ...data.preferences });
      }
    } catch (error) {
      console.error('Error fetching preferences:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updatePreference = async (key: keyof SettingsPreferences, value: any) => {
    if (!authUser) return;

    const newPreferences = { ...preferences, [key]: value };
    setPreferences(newPreferences);

    try {
      const { error } = await supabase
        .from('users')
        .update({ preferences: newPreferences })
        .eq('id', authUser.id);

      if (error) throw error;
      toast.success('Preference updated successfully');
    } catch (error) {
      console.error('Error updating preference:', error);
      toast.error('Failed to update preference');
      setPreferences(preferences);
    }
  };

  return {
    preferences,
    isLoading,
    updatePreference
  };
};
