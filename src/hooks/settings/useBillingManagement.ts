import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { UserProfile } from './useUserProfile';
import { getTierById } from '@/config/subscriptionTiers';

export const useBillingManagement = (profile: UserProfile | null) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const getTierInfo = (tierId: string) => {
    const tier = getTierById(tierId);
    
    return {
      price: `$${tier.monthlyPrice}/month or $${tier.annualPrice}/year`,
      color: tierId === 'analytics_only' ? 'bg-teal-500' : 
             tierId === 'ai_lite' ? 'bg-orange' : 'bg-green-500',
      features: tier.features
    };
  };

  const getTrialDaysLeft = () => {
    if (!profile?.subscription_end_date) return null;
    const trialEnd = new Date(profile.subscription_end_date);
    const now = new Date();
    const diffTime = trialEnd.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const handleManageSubscription = async () => {
    if (!profile?.stripe_customer_id) {
      navigate('/pricing');
      return;
    }

    setIsLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('customer-portal');
      if (error) throw error;
      if (data.url) {
        window.open(data.url, '_blank');
      }
    } catch (error) {
      console.error('Portal error:', error);
      toast.error('Failed to open billing portal');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshSubscription = async (refetchProfile: () => Promise<void>) => {
    setIsRefreshing(true);
    try {
      const { data, error } = await supabase.functions.invoke('check-subscription');
      if (error) throw error;
      await refetchProfile();
      toast.success('Subscription status refreshed successfully');
    } catch (error) {
      console.error('Error refreshing subscription:', error);
      toast.error('Failed to refresh subscription status');
    } finally {
      setIsRefreshing(false);
    }
  };

  return {
    isLoading,
    isRefreshing,
    getTierInfo,
    getTrialDaysLeft,
    handleManageSubscription,
    refreshSubscription
  };
};
