
import { useState } from 'react';
import { enhancedToast } from '@/components/ui/enhanced-toast';
import { useFormValidation, commonRules } from '@/hooks/useFormValidation';
import { ContentPillar } from '@/types/pillar';

interface UsePillarFormManagementProps {
  pillars: ContentPillar[];
  addPillar: (pillar: { name: string; target_percentage: number; color: string }) => Promise<boolean>;
  updatePillar: (pillar: ContentPillar) => Promise<boolean>;
  deletePillar: (pillarId: string, pillarName: string) => void;
}

export const usePillarFormManagement = ({
  pillars,
  addPillar,
  updatePillar,
  deletePillar
}: UsePillarFormManagementProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingPillar, setEditingPillar] = useState<ContentPillar | null>(null);
  const [newPillar, setNewPillar] = useState({ name: '', target_percentage: 25, color: '#37BEB0' });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form validation
  const { validateForm, getFieldError, touchField, clearErrors } = useFormValidation({
    name: commonRules.pillarName,
    target_percentage: commonRules.percentage
  });

  const handleAddPillar = async (): Promise<boolean> => {
    const isValid = validateForm(newPillar);
    if (!isValid) {
      enhancedToast.error('Please fix the validation errors before submitting');
      return false;
    }

    // Check if pillar name already exists
    const existingPillar = pillars.find(p => 
      p.name.toLowerCase() === newPillar.name.toLowerCase()
    );
    if (existingPillar) {
      enhancedToast.error('A pillar with this name already exists');
      return false;
    }

    setIsSubmitting(true);
    const loadingToast = enhancedToast.loading('Creating pillar...');

    try {
      const success = await addPillar(newPillar);
      if (success) {
        setNewPillar({ name: '', target_percentage: 25, color: '#37BEB0' });
        setIsDialogOpen(false);
        clearErrors();
        enhancedToast.dismiss(loadingToast);
        enhancedToast.success('Pillar created successfully!');
        return true;
      }
      enhancedToast.dismiss(loadingToast);
      return false;
    } catch (error) {
      enhancedToast.dismiss(loadingToast);
      enhancedToast.error('Failed to create pillar. Please try again.');
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdatePillar = async (pillar: ContentPillar): Promise<boolean> => {
    const isValid = validateForm(pillar);
    if (!isValid) {
      enhancedToast.error('Please fix the validation errors before submitting');
      return false;
    }

    setIsSubmitting(true);
    const loadingToast = enhancedToast.loading('Updating pillar...');

    try {
      const success = await updatePillar(pillar);
      if (success) {
        setEditingPillar(null);
        enhancedToast.dismiss(loadingToast);
        enhancedToast.success('Pillar updated successfully!');
        return true;
      }
      enhancedToast.dismiss(loadingToast);
      return false;
    } catch (error) {
      enhancedToast.dismiss(loadingToast);
      enhancedToast.error('Failed to update pillar. Please try again.');
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditPillar = (pillar: ContentPillar) => {
    setEditingPillar(pillar);
    clearErrors();
  };

  const handleDeletePillar = (pillarId: string, pillarName: string) => {
    enhancedToast.info(
      `Are you sure you want to delete "${pillarName}"?`,
      {
        action: {
          label: 'Delete',
          onClick: () => {
            deletePillar(pillarId, pillarName);
            enhancedToast.success('Pillar deleted successfully');
          }
        },
        duration: 8000
      }
    );
  };

  return {
    isDialogOpen,
    setIsDialogOpen,
    editingPillar,
    setEditingPillar,
    newPillar,
    setNewPillar,
    isSubmitting,
    handleAddPillar,
    handleUpdatePillar,
    handleEditPillar,
    handleDeletePillar,
    getFieldError,
    touchField,
    clearErrors
  };
};
