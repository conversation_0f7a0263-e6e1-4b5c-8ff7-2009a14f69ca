
import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

interface Video {
  id: string;
  title: string;
  description?: string;
  status: string;
  scheduled_date: string;
  calendar_notes?: string;
  pillar_id: string;
}

interface FormData {
  title: string;
  description: string;
  status: string;
  scheduled_date: string;
  calendar_notes: string;
  pillar_id: string;
}

export const useVideoEditForm = (video: Video | null, onVideoUpdated: () => void, onClose: () => void) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    status: '',
    scheduled_date: '',
    calendar_notes: '',
    pillar_id: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (video) {
      setFormData({
        title: video.title || '',
        description: video.description || '',
        status: video.status || '',
        scheduled_date: video.scheduled_date || '',
        calendar_notes: video.calendar_notes || '',
        pillar_id: video.pillar_id || ''
      });
    }
  }, [video]);

  const updateFormField = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !video) return;

    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('videos')
        .update({
          title: formData.title,
          description: formData.description,
          status: formData.status,
          scheduled_date: formData.scheduled_date || null,
          calendar_notes: formData.calendar_notes,
          pillar_id: formData.pillar_id || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', video.id)
        .eq('user_id', user.id);

      if (error) throw error;

      toast.success('Video updated successfully');
      onVideoUpdated(); // Trigger refresh of calendar data
      onClose();
    } catch (error) {
      console.error('Error updating video:', error);
      toast.error('Failed to update video');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = () => {
    handleSubmit(new Event('submit') as any);
  };

  return {
    formData,
    isLoading,
    updateFormField,
    handleSubmit,
    handleSave
  };
};
