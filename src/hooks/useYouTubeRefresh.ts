import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/useAuth';

/**
 * Hook to provide YouTube connection refresh functionality
 * This can be used from anywhere in the app to refresh YouTube data after OAuth completion
 */
export const useYouTubeRefresh = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  const refreshYouTubeConnection = async () => {
    if (!user?.id) {
      console.warn('❌ Cannot refresh YouTube connection: no user ID');
      return;
    }

    console.log('🔄 Starting YouTube connection cache refresh for user:', user.id);

    // First, remove all cached data to force fresh fetch
    console.log('🗑️ Removing youtube-connection cache...');
    queryClient.removeQueries({
      queryKey: ['youtube-connection', user.id]
    });

    // Also remove any user data that might contain YouTube info
    console.log('🗑️ Removing user cache...');
    queryClient.removeQueries({
      queryKey: ['user']
    });

    // Then invalidate to trigger fresh fetch
    console.log('🔄 Invalidating youtube-connection cache...');
    await queryClient.invalidateQueries({
      queryKey: ['youtube-connection', user.id]
    });

    console.log('🔄 Invalidating user cache...');
    await queryClient.invalidateQueries({
      queryKey: ['user']
    });

    // Force a longer delay to ensure database propagation
    console.log('⏱️ Waiting for database propagation...');
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('✅ YouTube connection cache refresh completed');
  };

  return {
    refreshYouTubeConnection
  };
};
