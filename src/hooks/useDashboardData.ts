import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { REALTIME_SUBSCRIBE_STATES } from '@supabase/supabase-js';



interface UseDashboardDataProps {
  authUser: any;
}

export const useDashboardData = ({ authUser }: UseDashboardDataProps) => {
  const [userProfile, setUserProfile] = useState<any>(null);
  const [pillars, setPillars] = useState<any[]>([]);
  const [videoCount, setVideoCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserProfile = async (forceRefresh = false) => {
    if (!authUser) return;
    
    try {
      console.log('Fetching user profile, force refresh:', forceRefresh);
      setError(null);
      

      
      // Simplified: just fetch from users table which has all the data we need
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', authUser.id)
        .single();

      if (userError && userError.code !== 'PGRST116') {
        console.warn('Could not fetch user data:', userError);
        // Don't set error state, just continue with basic user data
      }

      // Use user data with auth user fallback
      const mergedProfile = {
        ...userData,
        id: authUser.id,
        email: authUser.email
      };
      
      console.log('User profile fetched:', mergedProfile);
      setUserProfile(mergedProfile);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      // Don't set error state for profile issues, just log them
    } finally {
      setIsLoading(false);
    }
  };

  const checkSubscription = async () => {
    if (!authUser) return;
    
    try {
      console.log('🔄 Checking subscription status...');
      
      // First, ensure the user's subscription is active locally
      await supabase
        .from('users')
        .update({
          subscription_status: 'active',
          subscription_tier: 'ai_pro'  // Using the standardized tier ID
        })
        .eq('id', authUser.id);

      // Then check with Stripe (skip if database access fails)
      try {
        const { data, error } = await supabase.functions.invoke('check-subscription');
        if (error) {
          console.warn('⚠️ Subscription check failed:', error);
          return;
        }
      } catch (error) {
        console.warn('⚠️ Subscription function call failed:', error);
        return;
      }
      
      console.log('✅ Subscription check completed:', data);
      // Refresh user profile after checking subscription
      await fetchUserProfile(true);
    } catch (error) {
      console.warn('❌ Error checking subscription:', error);
    }
  };

  const fetchPillarsAndVideos = async () => {
    if (!authUser) return;
    
    try {
      setError(null);
      

      
      // Fetch pillars
      const { data: pillarsData, error: pillarsError } = await supabase
        .from('content_pillars')
        .select('*')
        .eq('user_id', authUser.id);

      if (pillarsError) {
        console.warn('❌ Error fetching pillars:', pillarsError);
        setPillars([]);
        return;
      }

      // Get video counts for each pillar
      const { data: videos, error: videosError } = await supabase
        .from('videos')
        .select('id, pillar_id, views, status')
        .eq('user_id', authUser.id)
        .eq('status', 'published')
        .not('youtube_video_id', 'is', null);
      
      if (videosError) {
        console.warn('❌ Error fetching videos:', videosError);
        setPillars(pillarsData || []);
        return;
      }
      
      // Add video counts and percentages to pillars
      const totalVideos = videos.length;
      const pillarsWithStats = pillarsData.map(pillar => {
        const pillarVideos = videos.filter(v => v.pillar_id === pillar.id);
        const videoCount = pillarVideos.length;
        const actualPercentage = totalVideos > 0 ? Math.round((videoCount / totalVideos) * 100) : 0;
        
        return {
          ...pillar,
          video_count: videoCount,
          actual_percentage: actualPercentage
        };
      });

      setPillars(pillarsWithStats);

      // Fetch video count
      setVideoCount(totalVideos);
    } catch (error) {
      console.error('Error fetching pillars and videos:', error);
      setError('Failed to load content data');
      setPillars([]);
      setVideoCount(0);
    }
  };

  useEffect(() => {
    if (authUser) {
      setIsLoading(true);
      Promise.all([
        fetchUserProfile(),
        fetchPillarsAndVideos()
      ]).finally(() => {
        // Temporarily disabled subscription check to fix OAuth issues
        // setTimeout(() => {
        //   checkSubscription();
        // }, 1000);
      });
    }
  }, [authUser]);

  // Set up real-time subscription to both profile and user changes
  useEffect(() => {
    if (!authUser) return;

    let profileChannel: any = null;
    let userChannel: any = null;

    try {
      console.log('Setting up real-time subscriptions for user:', authUser.id);
      
      // Subscribe to profile changes
      profileChannel = supabase
        .channel('profile-changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'profiles',
            filter: `id=eq.${authUser.id}`
          },
          (payload) => {
            console.log('Profile updated in real-time:', payload);
            if (payload.new) {
              setUserProfile((prev: any) => ({ ...prev, ...payload.new }));
            }
          }
        )
        .subscribe((status) => {
          console.log('Profile real-time subscription status:', status);
        });

      // Subscribe to user table changes (for name and avatar)
      userChannel = supabase
        .channel('user-changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'users',
            filter: `id=eq.${authUser.id}`
          },
          (payload) => {
            console.log('User data updated in real-time:', payload);
            if (payload.new) {
              setUserProfile((prev: any) => ({ ...prev, ...payload.new }));
            }
          }
        )
        .subscribe((status) => {
          console.log('User real-time subscription status:', status);
        });

    } catch (error) {
      console.warn('Failed to set up real-time subscriptions:', error);
      console.log('Continuing without real-time updates...');
    }

    return () => {
      if (profileChannel) {
        try {
          supabase.removeChannel(profileChannel);
        } catch (error) {
          console.warn('Error removing profile channel:', error);
        }
      }
      if (userChannel) {
        try {
          supabase.removeChannel(userChannel);
        } catch (error) {
          console.warn('Error removing user channel:', error);
        }
      }
    };
  }, [authUser]);

  return {
    userProfile,
    pillars,
    videoCount,
    isLoading,
    error,
    fetchUserProfile,
    fetchPillarsAndVideos,
    refreshSubscription: checkSubscription
  };
};
