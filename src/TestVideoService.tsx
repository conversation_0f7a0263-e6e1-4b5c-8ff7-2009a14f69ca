import React, { useState } from 'react';
import { supabase } from '@/lib/supabase';

export default function TestVideoService() {
  const [result, setResult] = useState<string>('No test run yet');
  const [isLoading, setIsLoading] = useState(false);

  const runTest = async () => {
    setIsLoading(true);
    setResult('Running test...');
    
    try {
      // Simple test to check Supabase connection
      const { data, error } = await supabase
        .from('videos')
        .select('count', { count: 'exact', head: true });
        
      if (error) {
        setResult(`Error: ${error.message}`);
      } else {
        setResult(`Success! Found ${data.count} videos.`);
      }
    } catch (error) {
      setResult(`Exception: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1 style={{ marginBottom: '20px' }}>Video Service Test</h1>
      
      <button 
        onClick={runTest} 
        disabled={isLoading}
        style={{
          padding: '10px 20px',
          backgroundColor: isLoading ? '#ccc' : '#4CAF50',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: isLoading ? 'not-allowed' : 'pointer'
        }}
      >
        {isLoading ? 'Testing...' : 'Run Test'}
      </button>
      
      <div style={{ marginTop: '20px', padding: '10px', border: '1px solid #ddd' }}>
        <h3>Result:</h3>
        <pre>{result}</pre>
      </div>
    </div>
  );
}