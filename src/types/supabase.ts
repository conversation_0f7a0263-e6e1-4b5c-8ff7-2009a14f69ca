export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      whiteboard_projects: {
        Row: {
          id: string
          name: string
          data: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          data?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          data?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
      }
      whiteboard_nodes: {
        Row: {
          id: string
          project_id: string
          type: string
          position_x: number
          position_y: number
          data: <PERSON><PERSON>
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          type: string
          position_x: number
          position_y: number
          data?: <PERSON><PERSON>
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          type?: string
          position_x?: number
          position_y?: number
          data?: <PERSON><PERSON>
          created_at?: string
        }
      }
      whiteboard_connections: {
        Row: {
          id: string
          project_id: string
          from_node: string
          to_node: string
          type: string
          style: string
          color: string
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          from_node: string
          to_node: string
          type?: string
          style?: string
          color?: string
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          from_node?: string
          to_node?: string
          type?: string
          style?: string
          color?: string
          created_at?: string
        }
      }
      user_credits: {
        Row: {
          user_id: string
          credits_used: number
          credits_total: number
          reset_date: string
        }
        Insert: {
          user_id: string
          credits_used?: number
          credits_total?: number
          reset_date?: string
        }
        Update: {
          user_id?: string
          credits_used?: number
          credits_total?: number
          reset_date?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      ping: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      cleanup_mock_data: {
        Args: {
          user_id: string
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}
