@layer base {
  /* Basic animations only - no hover effects */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Basic Drag and Drop Animations */
  .kanban-card-dragging {
    animation: cardLift 0.2s ease-out forwards;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.1);
    z-index: 1000;
  }

  .kanban-dropzone-active {
    animation: pulseGlow 1.5s ease-in-out infinite;
  }

  @keyframes cardLift {
    from {
      transform: scale(1) rotate(0deg);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    to {
      transform: scale(1.05) rotate(2deg);
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    }
  }

  /* Add the gradient animation keyframes */
  @keyframes gradientAnimation {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Keep existing animations */
  @keyframes pulseGlow {
    0%,
    100% {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
      border-color: rgba(59, 130, 246, 0.3);
    }
    50% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.6),
        0 0 40px rgba(59, 130, 246, 0.3);
      border-color: rgba(59, 130, 246, 0.6);
    }
  }

  /* Basic shimmer effect for loading states */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  .shimmer-effect {
    position: relative;
    overflow: hidden;
  }

  .shimmer-effect::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    animation: shimmer 2s infinite;
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-6px);
    }
  }

  .float-animation {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes glow {
    0%,
    100% {
      box-shadow: 0 0 5px rgba(55, 190, 176, 0.5);
    }
    50% {
      box-shadow: 0 0 20px rgba(55, 190, 176, 0.8),
        0 0 30px rgba(55, 190, 176, 0.4);
    }
  }

  .glow-effect {
    animation: glow 2s ease-in-out infinite;
  }

  /* Drag handle improvements */
  .cursor-grab {
    cursor: grab;
  }

  .cursor-grab:active {
    cursor: grabbing;
  }

  /* Enhanced card entrance animations */
  .stagger-animation {
    animation: staggerIn 0.6s ease-out forwards;
  }

  @keyframes staggerIn {
    from {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
}
