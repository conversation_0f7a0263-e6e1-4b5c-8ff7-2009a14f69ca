@layer base {
  /* Apply font smoothing globally */
  html,
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Make all text white by default */
  body,
  p,
  span,
  div,
  a,
  button,
  input,
  textarea,
  select,
  label {
    color: #ffffff !important;
  }

  /* Enhanced text visibility on dark background */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-montserrat;
    color: #ffffff !important;
    font-weight: 600;
  }

  /* Ensure all text in the sidebar is white */
  [data-sidebar] *,
  [data-sidebar="sidebar"] *,
  .sidebar-container * {
    color: #ffffff !important;
  }

  /* Override any muted text colors */
  .text-muted,
  .text-muted-foreground,
  .text-gray-400,
  .text-gray-500,
  .text-gray-600 {
    color: #ffffff !important;
  }
}
