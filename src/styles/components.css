/* Create a gradient mesh using all logo colors */
body {
  background: #0f172a !important;
  background-image: radial-gradient(
      circle at 15% 15%,
      rgba(48, 145, 249, 0.6),
      transparent 40%
    ),
    radial-gradient(circle at 85% 15%, rgba(229, 57, 181, 0.5), transparent 40%),
    radial-gradient(circle at 75% 75%, rgba(17, 193, 76, 0.4), transparent 40%),
    radial-gradient(circle at 25% 75%, rgba(249, 115, 22, 0.4), transparent 40%) !important;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* Main container styling */
#root,
.app-container,
main {
  background: transparent !important;
}

@layer base {
  /* Base glass-effect styling for content cards */
  .content-card {
    background: rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border-radius: 16px;
    transition: all 0.2s ease-out;
    padding: 20px;
  }

  .content-card:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  }

  /* All dashboard-card containers use a lighter purple than Goals wrapper */
  .dashboard-card {
    background: rgba(
      126,
      34,
      206,
      0.7
    ) !important; /* bg-purple-700/70 - Lighter than Goals */
    color: #ffffff !important;
    border: 1px solid rgb(147, 51, 234); /* border-purple-600 - Lighter border */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border-radius: 16px;
    transition: all 0.2s ease-out;
    padding: 20px;
  }

  /* All tiers use the same lighter purple for consistency */
  .dashboard-card.tier-1,
  .dashboard-card.tier-2,
  .dashboard-card.tier-3,
  .dashboard-card.tier-4 {
    background: rgba(
      126,
      34,
      206,
      0.7
    ) !important; /* bg-purple-700/70 - Lighter than Goals */
    border: 1px solid rgb(147, 51, 234); /* border-purple-600 - Lighter border */
  }

  /* Consistent hover effects for all dashboard containers */
  .dashboard-card:hover,
  .dashboard-card.tier-1:hover,
  .dashboard-card.tier-2:hover,
  .dashboard-card.tier-3:hover,
  .dashboard-card.tier-4:hover {
    background: rgba(
      126,
      34,
      206,
      0.8
    ) !important; /* Slightly more opaque on hover */
    border: 1px solid rgb(147, 51, 234);
    box-shadow: 0 8px 30px rgba(126, 34, 206, 0.3);
  }

  /* Ensure inner content elements have different colors from wrapper */
  .dashboard-card .bg-purple-900 {
    background: rgba(126, 34, 206, 0.6) !important; /* bg-purple-700/60 */
  }

  .dashboard-card .border-purple-800 {
    border-color: rgba(109, 40, 217, 0.5) !important; /* border-purple-600/50 */
  }

  /* Chart container styling with accent colors */
  .chart-container {
    padding: 15px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Calendar grid styling for shorter height and thin lines */
  .calendar-day-cell {
    background: transparent;
    transition: all 0.2s ease;
  }

  .calendar-day-cell:hover {
    background: rgba(255, 255, 255, 0.05) !important;
  }
}

.btn-gradient {
  background: linear-gradient(135deg, #0cc0df, #e76f51);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(12, 192, 223, 0.3);
}

.btn-gradient:hover {
  background: linear-gradient(135deg, #0cc0df, #0ba5ba);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(12, 192, 223, 0.4);
}

/* Button styling with brand accent colors */
.btn-primary {
  background: rgba(229, 57, 181, 0.8); /* Pink from logo */
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: rgba(229, 57, 181, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.35);
  transform: translateY(-1px);
}

/* Date range selector styling */
.date-selector {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 8px 12px;
  color: white;
  font-weight: 500;
}
