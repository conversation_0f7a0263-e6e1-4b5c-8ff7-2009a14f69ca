/* ... keep existing code (all previous styles) the same ... */

/* Global text color override */
.sidebar-container *,
[data-sidebar] *,
[data-sidebar="sidebar"] *,
[data-sidebar="inset"] *,
[data-sidebar="menu"] *,
[data-sidebar="menu-label"] *,
[data-sidebar="menu-badge"] * {
  color: #ffffff !important;
}

/* Override any muted text in the sidebar */
.sidebar-container .text-muted,
.sidebar-container .text-muted-foreground,
.sidebar-container .text-gray-400,
.sidebar-container .text-gray-500,
.sidebar-container .text-gray-600,
[data-sidebar] .text-muted,
[data-sidebar] .text-muted-foreground,
[data-sidebar] .text-gray-400,
[data-sidebar] .text-gray-500,
[data-sidebar] .text-gray-600 {
  color: #ffffff !important;
}

/* Brand accent on page headers */
.page-header h1 {
  position: relative;
  padding-left: 16px;
  display: flex;
  align-items: center;
}

.page-header h1::before {
  content: "";
  width: 4px;
  height: 32px;
  background: #dc2626;
  display: inline-block;
  margin-right: 12px;
  vertical-align: middle;
  border-radius: 2px;
}

/* Enhanced brand presence for main content headers */
.brand-header {
  position: relative;
  padding-left: 16px;
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.brand-header::before {
  content: "";
  width: 4px;
  height: 32px;
  background: linear-gradient(180deg, #dc2626, #3b82f6);
  display: inline-block;
  margin-right: 12px;
  border-radius: 2px;
}

.brand-header h1,
.brand-header h2 {
  margin: 0;
  color: #ffffff !important;
  font-weight: 600;
}

/* Brand button utility classes - apply these to specific buttons that need brand colors */
.btn-brand {
  background: #dc2626 !important;
  border: 1px solid #dc2626 !important;
  color: #ffffff !important;
}

.btn-brand:hover {
  background: #991b1b !important;
  border-color: #991b1b !important;
}

/* Brand outline button utility classes */
.btn-brand-outline,
.disconnect-button {
  background: transparent !important;
  border: 1px solid #3b82f6 !important;
  color: #3b82f6 !important;
}

.btn-brand-outline:hover,
.disconnect-button:hover {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* Ghost buttons */
button[variant="ghost"] {
  background: transparent !important;
  border: none !important;
  color: #ffffff !important;
}

button[variant="ghost"]:hover {
  background: #4b5563 !important;
  color: #ffffff !important;
}

/* Link buttons */
button[variant="link"] {
  background: transparent !important;
  border: none !important;
  color: #dc2626 !important;
  text-decoration: underline;
}

button[variant="link"]:hover {
  color: #991b1b !important;
}

/* All select dropdowns */
select,
.form-select,
.custom-select,
.pillar-dropdown {
  background: #374151 !important;
  border: 1px solid #4b5563 !important;
  color: #ffffff !important;
}

/* Date inputs */
input[type="date"],
.date-input {
  background: #374151 !important;
  border: 1px solid #4b5563 !important;
  color: #ffffff !important;
}

/* Any remaining form controls */
.form-control {
  background: #374151 !important;
  border: 1px solid #4b5563 !important;
  color: #ffffff !important;
}

/* Focus states for all inputs */
input:focus,
select:focus,
textarea:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
  outline: none !important;
}

/* Placeholder text */
::placeholder {
  color: #9ca3af !important;
}

/* Override any remaining white backgrounds in buttons */
button.text-red-600,
.text-red-600 {
  color: #ffffff !important;
}

/* Ensure dropdown menus have proper black styling */
.bg-popover,
[data-radix-popper-content-wrapper] {
  background: #000000 !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

/* Select content styling */
[role="listbox"],
[role="option"] {
  background: #000000 !important;
  color: #ffffff !important;
}

[role="option"]:hover {
  background: #333333 !important;
}
