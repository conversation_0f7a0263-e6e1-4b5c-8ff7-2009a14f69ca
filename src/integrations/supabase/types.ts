export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      ai_prompt_templates: {
        Row: {
          category: string
          created_at: string | null
          id: string
          name: string
          template: string
          updated_at: string | null
        }
        Insert: {
          category: string
          created_at?: string | null
          id?: string
          name: string
          template: string
          updated_at?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          id?: string
          name?: string
          template?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      ai_usage_logs: {
        Row: {
          created_at: string | null
          credits_used: number
          feature_type: string
          id: string
          prompt_used: string | null
          response_received: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          credits_used?: number
          feature_type: string
          id?: string
          prompt_used?: string | null
          response_received?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          credits_used?: number
          feature_type?: string
          id?: string
          prompt_used?: string | null
          response_received?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_usage_logs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      competitor_analyses: {
        Row: {
          analysis_data: Json
          channel_id: string
          channel_name: string
          created_at: string
          id: string
          subscriber_count: number
          updated_at: string
          user_id: string
        }
        Insert: {
          analysis_data: Json
          channel_id: string
          channel_name: string
          created_at?: string
          id?: string
          subscriber_count?: number
          updated_at?: string
          user_id: string
        }
        Update: {
          analysis_data?: Json
          channel_id?: string
          channel_name?: string
          created_at?: string
          id?: string
          subscriber_count?: number
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      content_pillars: {
        Row: {
          color: string | null
          created_at: string | null
          id: string
          name: string
          target_percentage: number | null
          user_id: string
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          id?: string
          name: string
          target_percentage?: number | null
          user_id: string
        }
        Update: {
          color?: string | null
          created_at?: string | null
          id?: string
          name?: string
          target_percentage?: number | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "content_pillars_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      credit_packs: {
        Row: {
          created_at: string | null
          credits_remaining: number
          id: string
          pack_size: number
          price_paid: number
          purchased_at: string | null
          stripe_payment_intent_id: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          credits_remaining: number
          id?: string
          pack_size: number
          price_paid: number
          purchased_at?: string | null
          stripe_payment_intent_id?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          credits_remaining?: number
          id?: string
          pack_size?: number
          price_paid?: number
          purchased_at?: string | null
          stripe_payment_intent_id?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      dismissed_alerts: {
        Row: {
          alert_type: string
          created_at: string
          dismissed_at: string
          id: string
          user_id: string
        }
        Insert: {
          alert_type: string
          created_at?: string
          dismissed_at?: string
          id?: string
          user_id: string
        }
        Update: {
          alert_type?: string
          created_at?: string
          dismissed_at?: string
          id?: string
          user_id?: string
        }
        Relationships: []
      }
      goal_progress: {
        Row: {
          created_at: string
          goal_id: string
          id: string
          recorded_at: string
          user_id: string
          value: number
        }
        Insert: {
          created_at?: string
          goal_id: string
          id?: string
          recorded_at?: string
          user_id: string
          value: number
        }
        Update: {
          created_at?: string
          goal_id?: string
          id?: string
          recorded_at?: string
          user_id?: string
          value?: number
        }
        Relationships: [
          {
            foreignKeyName: "goal_progress_goal_id_fkey"
            columns: ["goal_id"]
            isOneToOne: false
            referencedRelation: "goals"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "goal_progress_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      goals: {
        Row: {
          created_at: string | null
          current_value: number | null
          end_date: string | null
          id: string
          target_value: number
          type: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          current_value?: number | null
          end_date?: string | null
          id?: string
          target_value: number
          type: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          current_value?: number | null
          end_date?: string | null
          id?: string
          target_value?: number
          type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "goals_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          additional_seats: number | null
          created_at: string | null
          current_period_end: string | null
          email: string
          id: string
          purchased_credits: number | null
          stripe_customer_id: string | null
          subscription_credits: number | null
          subscription_status: string | null
          subscription_tier: string | null
          updated_at: string | null
        }
        Insert: {
          additional_seats?: number | null
          created_at?: string | null
          current_period_end?: string | null
          email: string
          id: string
          purchased_credits?: number | null
          stripe_customer_id?: string | null
          subscription_credits?: number | null
          subscription_status?: string | null
          subscription_tier?: string | null
          updated_at?: string | null
        }
        Update: {
          additional_seats?: number | null
          created_at?: string | null
          current_period_end?: string | null
          email?: string
          id?: string
          purchased_credits?: number | null
          stripe_customer_id?: string | null
          subscription_credits?: number | null
          subscription_status?: string | null
          subscription_tier?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      strategy_sprints: {
        Row: {
          completed_challenges: number[] | null
          created_at: string
          current_day: number
          id: string
          start_date: string
          status: string
          total_points: number
          updated_at: string
          user_id: string
        }
        Insert: {
          completed_challenges?: number[] | null
          created_at?: string
          current_day?: number
          id?: string
          start_date: string
          status?: string
          total_points?: number
          updated_at?: string
          user_id: string
        }
        Update: {
          completed_challenges?: number[] | null
          created_at?: string
          current_day?: number
          id?: string
          start_date?: string
          status?: string
          total_points?: number
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      subscribers: {
        Row: {
          created_at: string
          email: string
          id: string
          stripe_customer_id: string | null
          subscribed: boolean
          subscription_end: string | null
          subscription_tier: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          email: string
          id?: string
          stripe_customer_id?: string | null
          subscribed?: boolean
          subscription_end?: string | null
          subscription_tier?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          email?: string
          id?: string
          stripe_customer_id?: string | null
          subscribed?: boolean
          subscription_end?: string | null
          subscription_tier?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      team_members: {
        Row: {
          created_at: string | null
          id: string
          invited_at: string | null
          joined_at: string | null
          member_email: string
          member_id: string | null
          monthly_credits: number | null
          status: string | null
          stripe_subscription_id: string | null
          team_owner_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          invited_at?: string | null
          joined_at?: string | null
          member_email: string
          member_id?: string | null
          monthly_credits?: number | null
          status?: string | null
          stripe_subscription_id?: string | null
          team_owner_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          invited_at?: string | null
          joined_at?: string | null
          member_email?: string
          member_id?: string | null
          monthly_credits?: number | null
          status?: string | null
          stripe_subscription_id?: string | null
          team_owner_id?: string | null
        }
        Relationships: []
      }
      users: {
        Row: {
          additional_seats: number | null
          ai_credits_reset_date: string | null
          ai_credits_used: number | null
          avatar_url: string | null
          billing_period: string | null
          created_at: string | null
          email: string
          id: string
          last_youtube_sync: string | null
          name: string | null
          onboarding_completed: boolean | null
          preferences: Json | null
          purchased_credits: number | null
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          subscription_credits: number | null
          subscription_end_date: string | null
          subscription_status: string | null
          subscription_tier: string | null
          trial_end_date: string | null
          youtube_access_token: string | null
          youtube_channel_id: string | null
          youtube_channel_name: string | null
          youtube_refresh_token: string | null
          youtube_subscriber_baseline: number | null
          youtube_thumbnail_url: string | null
        }
        Insert: {
          additional_seats?: number | null
          ai_credits_reset_date?: string | null
          ai_credits_used?: number | null
          avatar_url?: string | null
          billing_period?: string | null
          created_at?: string | null
          email: string
          id?: string
          last_youtube_sync?: string | null
          name?: string | null
          onboarding_completed?: boolean | null
          preferences?: Json | null
          purchased_credits?: number | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          subscription_credits?: number | null
          subscription_end_date?: string | null
          subscription_status?: string | null
          subscription_tier?: string | null
          trial_end_date?: string | null
          youtube_access_token?: string | null
          youtube_channel_id?: string | null
          youtube_channel_name?: string | null
          youtube_refresh_token?: string | null
          youtube_subscriber_baseline?: number | null
          youtube_thumbnail_url?: string | null
        }
        Update: {
          additional_seats?: number | null
          ai_credits_reset_date?: string | null
          ai_credits_used?: number | null
          avatar_url?: string | null
          billing_period?: string | null
          created_at?: string | null
          email?: string
          id?: string
          last_youtube_sync?: string | null
          name?: string | null
          onboarding_completed?: boolean | null
          preferences?: Json | null
          purchased_credits?: number | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          subscription_credits?: number | null
          subscription_end_date?: string | null
          subscription_status?: string | null
          subscription_tier?: string | null
          trial_end_date?: string | null
          youtube_access_token?: string | null
          youtube_channel_id?: string | null
          youtube_channel_name?: string | null
          youtube_refresh_token?: string | null
          youtube_subscriber_baseline?: number | null
          youtube_thumbnail_url?: string | null
        }
        Relationships: []
      }
      videos: {
        Row: {
          calendar_notes: string | null
          comment_count: number | null
          created_at: string | null
          description: string | null
          id: string
          like_count: number | null
          pillar_id: string | null
          priority: string | null
          published_at: string | null
          reminder_sent: boolean | null
          scheduled_date: string | null
          status: string | null
          title: string
          user_id: string
          views: number | null
          youtube_thumbnail_url: string | null
          youtube_video_id: string | null
        }
        Insert: {
          calendar_notes?: string | null
          comment_count?: number | null
          created_at?: string | null
          description?: string | null
          id?: string
          like_count?: number | null
          pillar_id?: string | null
          priority?: string | null
          published_at?: string | null
          reminder_sent?: boolean | null
          scheduled_date?: string | null
          status?: string | null
          title: string
          user_id: string
          views?: number | null
          youtube_thumbnail_url?: string | null
          youtube_video_id?: string | null
        }
        Update: {
          calendar_notes?: string | null
          comment_count?: number | null
          created_at?: string | null
          description?: string | null
          id?: string
          like_count?: number | null
          pillar_id?: string | null
          priority?: string | null
          published_at?: string | null
          reminder_sent?: boolean | null
          scheduled_date?: string | null
          status?: string | null
          title?: string
          user_id?: string
          views?: number | null
          youtube_thumbnail_url?: string | null
          youtube_video_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "videos_pillar_id_fkey"
            columns: ["pillar_id"]
            isOneToOne: false
            referencedRelation: "content_pillars"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "videos_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      ping: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      cleanup_mock_data: {
        Args: {
          user_id: string
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
