-- Fix the goals_type_check constraint to include monthly_views
-- Run this in Supabase SQL Editor

-- First, let's see the current constraint
SELECT 
    tc.constraint_name,
    cc.check_clause
FROM information_schema.table_constraints tc
JOIN information_schema.check_constraints cc 
    ON tc.constraint_name = cc.constraint_name
WHERE tc.table_name = 'goals'
AND tc.constraint_type = 'CHECK';

-- Drop the existing constraint
ALTER TABLE goals DROP CONSTRAINT IF EXISTS goals_type_check;

-- Add the updated constraint that includes all three goal types
ALTER TABLE goals ADD CONSTRAINT goals_type_check 
CHECK (type IN ('subscribers', 'monthly_views', 'revenue'));

-- Verify the constraint was updated
SELECT 
    tc.constraint_name,
    cc.check_clause
FROM information_schema.table_constraints tc
JOIN information_schema.check_constraints cc 
    ON tc.constraint_name = cc.constraint_name
WHERE tc.table_name = 'goals'
AND tc.constraint_type = 'CHECK';

-- Test that monthly_views now works (optional - uncomment to test)
-- INSERT INTO goals (user_id, type, current_value, target_value, end_date)
-- VALUES (auth.uid(), 'monthly_views', 100, 1000, '2025-01-31');

SELECT 'Goals type constraint updated successfully!' as result;
