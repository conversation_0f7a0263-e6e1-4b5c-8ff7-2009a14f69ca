const { execSync } = require('child_process');
const fs = require('fs');

// Get the current date for the migration filename
const date = new Date().toISOString().split('T')[0].replace(/-/g, '');
const migrationName = `${date}000000_add_missing_foreign_key_indexes.sql`;
const migrationPath = `./supabase/migrations/${migrationName}`;

// SQL to identify and fix all missing foreign key indexes
const sql = `
-- Automatically create indexes for all foreign keys without indexes
DO $$
DECLARE
  r RECORD;
BEGIN
  FOR r IN (
    SELECT
      tc.table_schema, 
      tc.table_name, 
      kcu.column_name,
      ccu.table_name AS foreign_table_name,
      ccu.column_name AS foreign_column_name,
      'idx_' || tc.table_name || '_' || kcu.column_name AS index_name
    FROM 
      information_schema.table_constraints AS tc 
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
    WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND NOT EXISTS (
      SELECT 1
      FROM pg_indexes
      WHERE tablename = tc.table_name
      AND indexdef LIKE '%' || kcu.column_name || '%'
    )
  ) LOOP
    EXECUTE format('CREATE INDEX %I ON %I.%I(%I)', 
                  r.index_name, 
                  r.table_schema, 
                  r.table_name, 
                  r.column_name);
    
    RAISE NOTICE 'Created index % on %.%.%', 
                r.index_name, 
                r.table_schema, 
                r.table_name, 
                r.column_name;
  END LOOP;
END $$;
`

// Write the migration file
fs.writeFileSync(migrationPath, sql);
console.log(`Created migration: ${migrationPath}`);

// Run the migration
try {
  console.log('Applying migration...');
  execSync('supabase migration up', { stdio: 'inherit' });
  console.log('Migration applied successfully!');
} catch (error) {
  console.error('Error applying migration:', error.message);
}