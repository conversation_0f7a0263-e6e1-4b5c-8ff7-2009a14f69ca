// This script can be run from the command line to clean up mock data
// Usage: node scripts/cleanup_mock_data.js

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function cleanupMockData() {
  console.log('Starting mock data cleanup...');
  
  try {
    // Delete mock videos - only very specific mock indicators
    const mockTitles = [
      'React Server Components: Complete Guide',
      'React Hooks Complete Guide',
      'Building a Full Stack React App',
      'React State Management Deep Dive',
      'Home Office Setup Tour',
      'AI Tools Revolutionizing Content Creation',
      'Boost Your Work From Home Productivity',
      'How to Grow Your Channel Fast',
      'Beginner\'s Guide to YouTube Success',
      'Top 10 Camera Tips for Creators',
      'YouTube Algorithm Explained',
      'Camera Settings for Beginners'
    ];

    const { data: deletedVideos, error: videosError } = await supabase
      .from('videos')
      .delete()
      .or(`title.ilike.%mock%,title.ilike.%demo%,title.ilike.%test%,title.ilike.%sample%,title.in.(${mockTitles.map(t => `"${t}"`).join(',')})`)
      .select();
      
    if (videosError) {
      console.error('Error deleting mock videos:', videosError);
    } else {
      console.log(`Deleted ${deletedVideos?.length || 0} mock videos`);
    }
    
    // Delete mock pillars - only obvious mock indicators
    const { data: deletedPillars, error: pillarsError } = await supabase
      .from('content_pillars')
      .delete()
      .or('name.ilike.%demo%,name.ilike.%mock%,name.ilike.%test%,name.ilike.%sample%')
      .select();
      
    if (pillarsError) {
      console.error('Error deleting mock pillars:', pillarsError);
    } else {
      console.log(`Deleted ${deletedPillars?.length || 0} mock pillars`);
    }
    
    console.log('Mock data cleanup completed');
  } catch (error) {
    console.error('Error during cleanup:', error);
  }
}

cleanupMockData();