-- Identify mock content pillars
SELECT * FROM content_pillars 
WHERE name IN ('Entertainment', 'Education', 'Behind The Scenes')
AND (description LIKE '%mock%' OR description LIKE '%demo%' OR description LIKE '%example%');

-- Delete mock content pillars
DELETE FROM content_pillars 
WHERE name IN ('Entertainment', 'Education', 'Behind The Scenes')
AND (description LIKE '%mock%' OR description LIKE '%demo%' OR description LIKE '%example%');

-- Identify mock videos
SELECT * FROM videos 
WHERE title LIKE '%React Server Components%'
OR title LIKE '%React Hooks Complete Guide%'
OR title LIKE '%Building a Full Stack React App%'
OR title LIKE '%React State Management%'
OR title LIKE '%Home Office Setup%'
OR title LIKE '%AI Tools Revolutionizing%'
OR title LIKE '%Boost Your Work From Home%'
OR title LIKE '%How to Grow Your Channel%'
OR title LIKE '%Beginner''s Guide to YouTube%'
OR title LIKE '%Top 10 Camera Tips%'
OR title LIKE '%YouTube Algorithm Explained%'
OR title LIKE '%Camera Settings for Beginners%';

-- Delete mock videos
DELETE FROM videos 
WHERE title LIKE '%React Server Components%'
OR title LIKE '%React Hooks Complete Guide%'
OR title LIKE '%Building a Full Stack React App%'
OR title LIKE '%React State Management%'
OR title LIKE '%Home Office Setup%'
OR title LIKE '%AI Tools Revolutionizing%'
OR title LIKE '%Boost Your Work From Home%'
OR title LIKE '%How to Grow Your Channel%'
OR title LIKE '%Beginner''s Guide to YouTube%'
OR title LIKE '%Top 10 Camera Tips%'
OR title LIKE '%YouTube Algorithm Explained%'
OR title LIKE '%Camera Settings for Beginners%';

-- Identify any videos without a valid YouTube ID (likely mock)
SELECT * FROM videos WHERE youtube_video_id IS NULL OR youtube_video_id = '';

-- Delete videos without a valid YouTube ID
DELETE FROM videos WHERE youtube_video_id IS NULL OR youtube_video_id = '';

-- Identify any content pillars with no associated videos
SELECT cp.* FROM content_pillars cp
LEFT JOIN videos v ON cp.id = v.pillar_id
WHERE v.id IS NULL;

-- Optional: Delete content pillars with no associated videos
-- DELETE FROM content_pillars WHERE id IN (
--   SELECT cp.id FROM content_pillars cp
--   LEFT JOIN videos v ON cp.id = v.pillar_id
--   WHERE v.id IS NULL
-- );