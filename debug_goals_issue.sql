-- Debug script to identify the monthly_views goal issue
-- Run this in Supabase SQL Editor

-- 1. Check current table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'goals' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Check for any constraints on the goals table
SELECT 
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name,
    cc.check_clause
FROM information_schema.table_constraints tc
LEFT JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
LEFT JOIN information_schema.check_constraints cc 
    ON tc.constraint_name = cc.constraint_name
WHERE tc.table_name = 'goals'
AND tc.table_schema = 'public';

-- 3. Check existing goals to see what types are already in the database
SELECT 
    type,
    COUNT(*) as count,
    MIN(current_value) as min_current,
    MAX(current_value) as max_current,
    MIN(target_value) as min_target,
    MAX(target_value) as max_target
FROM goals 
GROUP BY type;

-- 4. Check RLS policies
SELECT 
    policyname,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'goals';

-- 5. Test insert with monthly_views (replace with actual user_id)
-- This will help identify the exact error
-- INSERT INTO goals (user_id, type, current_value, target_value, end_date)
-- VALUES ('your-user-id-here', 'monthly_views', 100, 1000, '2025-01-31');

-- 6. Check for any triggers on the goals table
SELECT 
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'goals';

-- 7. Check if there are any functions that might be affecting inserts
SELECT 
    routine_name,
    routine_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_name LIKE '%goal%' 
OR routine_definition LIKE '%goals%';
