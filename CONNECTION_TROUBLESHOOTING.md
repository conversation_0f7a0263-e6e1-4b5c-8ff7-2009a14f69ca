# Connection Troubleshooting Guide

If you're experiencing connection issues between the frontend and backend, follow these steps:

## 1. Check if both servers are running

Make sure both the frontend and backend servers are running:

```bash
# Start both servers with a single command
npm run start

# Or start them separately
npm run backend  # Terminal 1
npm run dev      # Terminal 2
```

## 2. Verify server ports

- Frontend should be running on: http://localhost:8080
- Backend should be running on: http://localhost:3000

## 3. Check for errors in the console

- Check the browser console for frontend errors
- Check the terminal running the backend for server errors

## 4. Test the backend directly

Try accessing the backend health endpoint directly in your browser:
http://localhost:3000/health

## 5. Check environment variables

Make sure you have the required environment variables set:

- Create a `backend/.env` file with your OpenAI API key:
  ```
  OPENAI_API_KEY=sk-your-key-here
  ```

## 6. Restart both servers

Sometimes a clean restart resolves connection issues:

```bash
# Kill both servers (Ctrl+C)
# Then restart
npm run start
```

## 7. Clear browser cache

Try clearing your browser cache or opening the app in an incognito window.