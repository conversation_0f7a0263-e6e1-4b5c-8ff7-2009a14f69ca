-- Comprehensive script to fix all unindexed foreign keys
DO $$
DECLARE
  r RECORD;
BEGIN
  FOR r IN (
    SELECT
      tc.table_schema, 
      tc.table_name, 
      kcu.column_name,
      tc.constraint_name,
      ccu.table_name AS foreign_table_name,
      ccu.column_name AS foreign_column_name
    FROM 
      information_schema.table_constraints AS tc 
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
    WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND NOT EXISTS (
      SELECT 1
      FROM pg_indexes
      WHERE tablename = tc.table_name
      AND indexdef LIKE '%' || kcu.column_name || '%'
    )
  ) LOOP
    EXECUTE format('CREATE INDEX idx_%s_%s ON %I.%I(%I)', 
                  r.table_name, 
                  r.column_name,
                  r.table_schema, 
                  r.table_name, 
                  r.column_name);
    
    RAISE NOTICE 'Created index idx_%s_%s on %.%.%', 
                r.table_name, 
                r.column_name,
                r.table_schema, 
                r.table_name, 
                r.column_name;
  END LOOP;
END $$;

-- Force a database statistics update
ANALYZE;