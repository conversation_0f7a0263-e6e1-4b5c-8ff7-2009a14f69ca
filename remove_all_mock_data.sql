-- COMPREHENSIVE MOCK DATA REMOVAL SCRIPT
-- Run this in Supabase SQL Editor

-- 1. First, let's see what mock data exists
SELECT 'CHECKING FOR MOCK DATA' as status;

-- Check for Entertainment and Education pillars
SELECT 
    'Mock Pillars (Entertainment/Education):' as type,
    id, name, description, user_id, created_at
FROM content_pillars 
WHERE name IN ('Entertainment', 'Education', 'Behind The Scenes')
   OR name LIKE '%mock%'
   OR name LIKE '%demo%'
   OR name LIKE '%test%'
   OR description LIKE '%mock%'
   OR description LIKE '%demo%'
   OR description LIKE '%example%';

-- Check for mock videos
SELECT 
    'Mock Videos:' as type,
    COUNT(*) as count
FROM videos 
WHERE title LIKE '%React Server Components%'
   OR title LIKE '%React Hooks Complete Guide%'
   OR title LIKE '%Building a Full Stack React App%'
   OR title LIKE '%React State Management%'
   OR title LIKE '%Home Office Setup%'
   OR title LIKE '%AI Tools Revolutionizing%'
   OR title LIKE '%Boost Your Work From Home%'
   OR title LIKE '%How to Grow Your Channel%'
   OR title LIKE '%Beginner''s Guide to YouTube%'
   OR title LIKE '%Top 10 Camera Tips%'
   OR title LIKE '%YouTube Algorithm Explained%'
   OR title LIKE '%Camera Settings for Beginners%'
   OR title LIKE '%mock%'
   OR title LIKE '%demo%'
   OR title LIKE '%test%'
   OR youtube_video_id IS NULL 
   OR youtube_video_id = '';

-- Check for mock goals
SELECT 
    'Mock Goals:' as type,
    COUNT(*) as count
FROM goals 
WHERE type LIKE '%mock%'
   OR type LIKE '%demo%'
   OR type LIKE '%test%';

-- 2. Now remove all mock data
SELECT 'REMOVING MOCK DATA' as status;

-- Delete Entertainment and Education pillars specifically
DELETE FROM content_pillars 
WHERE name IN ('Entertainment', 'Education', 'Behind The Scenes')
   OR name LIKE '%mock%'
   OR name LIKE '%demo%'
   OR name LIKE '%test%'
   OR description LIKE '%mock%'
   OR description LIKE '%demo%'
   OR description LIKE '%example%';

-- Delete mock videos
DELETE FROM videos 
WHERE title LIKE '%React Server Components%'
   OR title LIKE '%React Hooks Complete Guide%'
   OR title LIKE '%Building a Full Stack React App%'
   OR title LIKE '%React State Management%'
   OR title LIKE '%Home Office Setup%'
   OR title LIKE '%AI Tools Revolutionizing%'
   OR title LIKE '%Boost Your Work From Home%'
   OR title LIKE '%How to Grow Your Channel%'
   OR title LIKE '%Beginner''s Guide to YouTube%'
   OR title LIKE '%Top 10 Camera Tips%'
   OR title LIKE '%YouTube Algorithm Explained%'
   OR title LIKE '%Camera Settings for Beginners%'
   OR title LIKE '%mock%'
   OR title LIKE '%demo%'
   OR title LIKE '%test%'
   OR youtube_video_id IS NULL 
   OR youtube_video_id = '';

-- Delete mock goals
DELETE FROM goals 
WHERE type LIKE '%mock%'
   OR type LIKE '%demo%'
   OR type LIKE '%test%';

-- Clean up AI usage logs for mock content
DELETE FROM ai_usage_logs 
WHERE metadata::text LIKE '%mock%'
   OR metadata::text LIKE '%demo%'
   OR metadata::text LIKE '%test%';

-- Clean up whiteboard projects that might be mock data
DELETE FROM whiteboard_projects 
WHERE name LIKE '%mock%'
   OR name LIKE '%demo%'
   OR name LIKE '%test%'
   OR description LIKE '%mock%'
   OR description LIKE '%demo%'
   OR description LIKE '%test%';

-- 3. Final verification
SELECT 'VERIFICATION - REMAINING DATA' as status;

-- Count remaining content pillars
SELECT 
    'Remaining Content Pillars:' as type,
    COUNT(*) as count
FROM content_pillars;

-- Show remaining pillar names
SELECT 
    'Remaining Pillar Names:' as type,
    string_agg(name, ', ') as names
FROM content_pillars;

-- Count remaining videos
SELECT 
    'Remaining Videos:' as type,
    COUNT(*) as count
FROM videos;

-- Count remaining goals
SELECT 
    'Remaining Goals:' as type,
    COUNT(*) as count
FROM goals;

SELECT 'Mock data removal completed!' as result;
