-- Quick test to identify the monthly_views goal issue
-- Run this in Supabase SQL Editor

-- 1. Check if the goals table structure is correct
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'goals' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Check existing goals to see what's working
SELECT 
    id,
    type,
    current_value,
    target_value,
    end_date,
    created_at
FROM goals 
ORDER BY created_at DESC 
LIMIT 10;

-- 3. Check for any constraints that might be failing
SELECT 
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name,
    cc.check_clause
FROM information_schema.table_constraints tc
LEFT JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
LEFT JOIN information_schema.check_constraints cc 
    ON tc.constraint_name = cc.constraint_name
WHERE tc.table_name = 'goals'
AND tc.table_schema = 'public';

-- 4. Test a simple monthly_views insert (replace 'your-user-id' with your actual user ID)
-- You can get your user ID by running: SELECT auth.uid();
-- INSERT INTO goals (user_id, type, current_value, target_value, end_date)
-- VALUES (auth.uid(), 'monthly_views', 100, 1000, '2025-01-31');

-- 5. Check RLS policies
SELECT 
    policyname,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'goals';

-- 6. Check for any triggers that might be interfering
SELECT 
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'goals';
