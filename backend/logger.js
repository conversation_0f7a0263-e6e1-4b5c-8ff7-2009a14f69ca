import winston from 'winston';
import { randomUUID } from 'crypto';
import config from './config.js';

// Create a Winston logger
const logger = winston.createLogger({
  level: config.logging.level,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'api-service' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
    // Add file transport for production
    ...(config.nodeEnv === 'production'
      ? [
          new winston.transports.File({ filename: 'error.log', level: 'error' }),
          new winston.transports.File({ filename: 'combined.log' }),
        ]
      : []),
  ],
});

// Error handling middleware
export const errorHandler = (err, req, res, next) => {
  const statusCode = err.statusCode || 500;
  const requestId = req.id || 'unknown';
  
  logger.error({
    message: err.message,
    stack: err.stack,
    requestId,
    path: req.path,
    method: req.method,
  });
  
  res.status(statusCode).json({
    success: false,
    error: {
      message: config.nodeEnv === 'production' && statusCode === 500
        ? 'Internal Server Error'
        : err.message,
      code: err.code || 'server_error',
      requestId,
    },
  });
};

// Request ID middleware
export const requestIdMiddleware = (req, res, next) => {
  req.id = randomUUID();
  res.setHeader('X-Request-ID', req.id);
  next();
};

// Standardized API response formats
export const apiResponse = {
  success: (data, message = 'Success', meta = {}) => ({
    success: true,
    message,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      ...meta,
    },
  }),

  error: (message, code = 'error', statusCode = 500, details = null) => ({
    success: false,
    error: {
      message,
      code,
      statusCode,
      details,
      timestamp: new Date().toISOString(),
    },
  }),

  paginated: (data, pagination, message = 'Success') => ({
    success: true,
    message,
    data,
    pagination: {
      page: pagination.page || 1,
      limit: pagination.limit || 10,
      total: pagination.total || 0,
      totalPages: Math.ceil((pagination.total || 0) / (pagination.limit || 10)),
    },
    meta: {
      timestamp: new Date().toISOString(),
    },
  }),
};

export default logger;