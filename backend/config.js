import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables from multiple possible locations
const environment = process.env.NODE_ENV || 'development';

// Try to load from parent directory first (where .env.local is located)
const parentEnvLocal = path.resolve(process.cwd(), '../.env.local');
const parentEnv = path.resolve(process.cwd(), '../.env');
const localEnvPath = path.resolve(process.cwd(), `.env.${environment}`);
const localEnv = path.resolve(process.cwd(), '.env');

if (fs.existsSync(parentEnvLocal)) {
  console.log(`Loading environment variables from ${parentEnvLocal}`);
  dotenv.config({ path: parentEnvLocal });
} else if (fs.existsSync(parentEnv)) {
  console.log(`Loading environment variables from ${parentEnv}`);
  dotenv.config({ path: parentEnv });
} else if (fs.existsSync(localEnvPath)) {
  console.log(`Loading environment variables from ${localEnvPath}`);
  dotenv.config({ path: localEnvPath });
} else if (fs.existsSync(localEnv)) {
  console.log(`Loading environment variables from ${localEnv}`);
  dotenv.config({ path: localEnv });
} else {
  console.log('No .env file found, using system environment variables');
}

// Required environment variables
const requiredEnvVars = [
  'OPENAI_API_KEY'
];

// Optional environment variables with defaults
const optionalEnvVars = [
  'SUPABASE_URL',
  'SUPABASE_SERVICE_KEY'
];

// Validate required environment variables
const missingEnvVars = requiredEnvVars.filter(
  envVar => !process.env[envVar]
);

if (missingEnvVars.length > 0) {
  throw new Error(
    `Missing required environment variables: ${missingEnvVars.join(', ')}`
  );
}

// Log warnings for missing optional environment variables
const missingOptionalVars = optionalEnvVars.filter(
  envVar => !process.env[envVar]
);

if (missingOptionalVars.length > 0) {
  console.warn(
    `Optional environment variables not set: ${missingOptionalVars.join(', ')}`
  );
}

// Export configuration
export default {
  port: parseInt(process.env.PORT || '3000', 10),
  nodeEnv: environment,
  openai: {
    apiKey: process.env.OPENAI_API_KEY,
    defaultModel: process.env.OPENAI_DEFAULT_MODEL || 'gpt-4o-mini',
    timeout: parseInt(process.env.OPENAI_TIMEOUT || '30000', 10),
  },
  supabase: {
    url: process.env.SUPABASE_URL,
    serviceKey: process.env.SUPABASE_SERVICE_KEY,
    anonKey: process.env.SUPABASE_ANON_KEY,
  },
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:8080',
    credentials: process.env.CORS_CREDENTIALS === 'true',
  },
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info',
  },
  server: {
    timeout: parseInt(process.env.SERVER_TIMEOUT || '30000', 10),
    keepAliveTimeout: parseInt(process.env.KEEP_ALIVE_TIMEOUT || '5000', 10),
  },
};