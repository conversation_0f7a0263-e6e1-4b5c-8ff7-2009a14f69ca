import express from 'express';
import cors from 'cors';
import OpenAI from 'openai';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import morgan from 'morgan';
import config from './config.js';
import logger, { errorHandler, requestIdMiddleware, apiResponse } from './logger.js';

const app = express();

// Request ID middleware (should be first)
app.use(requestIdMiddleware);

// Security middleware
app.use(helmet());

// CORS with configuration
app.use(cors({
  origin: config.cors.origin || 'http://localhost:8080',
  credentials: config.cors.credentials,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Request size limits
app.use(express.json({ limit: '1mb' }));

// Request logging with Winston
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.info(message.trim())
  }
}));

// Rate limiting with configuration
const apiLimiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.max,
  standardHeaders: true,
  legacyHeaders: false,
  message: apiResponse.error('Too many requests', 'rate_limit_exceeded', 429)
});
app.use('/api/', apiLimiter);

// Initialize OpenAI with configuration
const openai = new OpenAI({
  apiKey: config.openai.apiKey,
  timeout: config.openai.timeout,
});

// Middleware for input validation
const validateOpenAIRequest = (req, res, next) => {
  const { model, messages } = req.body;

  if (!model || !messages || !Array.isArray(messages)) {
    return res.status(400).json(
      apiResponse.error('Invalid request parameters', 'validation_error', 400)
    );
  }

  next();
};

// OpenAI endpoint with improved error handling
app.post('/api/openai', validateOpenAIRequest, async (req, res) => {
  const requestId = req.id;
  logger.info(`OpenAI request received`, { requestId });

  try {
    const { model, messages, ...rest } = req.body;

    const completion = await openai.chat.completions.create({
      model: model || config.openai.defaultModel,
      messages,
      ...rest
    });

    logger.info(`OpenAI request successful`, { requestId });
    res.json(apiResponse.success(completion, 'OpenAI request completed successfully'));
  } catch (err) {
    logger.error(`OpenAI request failed`, { requestId, error: err.message });

    if (err.response) {
      // OpenAI API error
      return res.status(err.response.status).json(
        apiResponse.error(err.response.data.error.message, 'openai_error', err.response.status)
      );
    }

    if (err.code === 'ECONNABORTED') {
      return res.status(408).json(
        apiResponse.error('Request timeout', 'timeout_error', 408)
      );
    }

    res.status(500).json(
      apiResponse.error('Internal server error', 'server_error', 500)
    );
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Supabase health check endpoint
app.get('/supabase-health', async (req, res) => {
  try {
    // Simple check to see if Supabase is configured
    const supabaseUrl = process.env.SUPABASE_URL || config.supabase?.url;
    const supabaseKey = process.env.SUPABASE_ANON_KEY || config.supabase?.anonKey;
    
    if (!supabaseUrl || !supabaseKey) {
      return res.status(500).json({
        status: 'error',
        message: 'Supabase not configured'
      });
    }
    
    // Return success
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Supabase health check failed:', error);
    res.status(500).json({
      status: 'error',
      message: error.message || 'Unknown error'
    });
  }
});

// 404 handler for undefined routes
app.use('*', (req, res) => {
  res.status(404).json(
    apiResponse.error(`Route ${req.originalUrl} not found`, 'route_not_found', 404)
  );
});

// Global error handler (should be last middleware)
app.use(errorHandler);

// Graceful shutdown
const gracefulShutdown = (signal) => {
  logger.info(`${signal} received, shutting down gracefully`);

  // Close server
  server.close(() => {
    logger.info('HTTP server closed');
    process.exit(0);
  });

  // Force close after timeout
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 10000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server
const PORT = config.port;
const server = app.listen(PORT, () => {
  logger.info(`Server running on port ${PORT}`, {
    environment: config.nodeEnv,
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Set server timeouts
server.timeout = config.server.timeout;
server.keepAliveTimeout = config.server.keepAliveTimeout;
