-- COMPREHENSIVE RLS POLICY FIX
-- Run this in your Supabase Dashboard SQL Editor
-- This will fix all the RLS warnings you're seeing

-- 1. GOALS TABLE
ALTER TABLE goals ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own goals" ON goals;
DROP POLICY IF EXISTS "Users can insert their own goals" ON goals;
DROP POLICY IF EXISTS "Users can update their own goals" ON goals;
DROP POLICY IF EXISTS "Users can delete their own goals" ON goals;

-- Create RLS policies for goals table
CREATE POLICY "Users can view their own goals"
    ON goals FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own goals"
    ON goals FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own goals"
    ON goals FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own goals"
    ON goals FOR DELETE
    USING (auth.uid() = user_id);

-- 2. GOAL_PROGRESS TABLE
ALTER TABLE goal_progress ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own goal progress" ON goal_progress;
DROP POLICY IF EXISTS "Users can insert their own goal progress" ON goal_progress;
DROP POLICY IF EXISTS "Users can update their own goal progress" ON goal_progress;
DROP POLICY IF EXISTS "Users can delete their own goal progress" ON goal_progress;

-- Create RLS policies for goal_progress table
CREATE POLICY "Users can view their own goal progress"
    ON goal_progress FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own goal progress"
    ON goal_progress FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own goal progress"
    ON goal_progress FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own goal progress"
    ON goal_progress FOR DELETE
    USING (auth.uid() = user_id);

-- 3. VIDEOS TABLE
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own videos" ON videos;
DROP POLICY IF EXISTS "Users can insert their own videos" ON videos;
DROP POLICY IF EXISTS "Users can update their own videos" ON videos;
DROP POLICY IF EXISTS "Users can delete their own videos" ON videos;

-- Create RLS policies for videos table
CREATE POLICY "Users can view their own videos"
    ON videos FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own videos"
    ON videos FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own videos"
    ON videos FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own videos"
    ON videos FOR DELETE
    USING (auth.uid() = user_id);

-- 4. STRATEGY_SPRINTS TABLE
ALTER TABLE strategy_sprints ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own strategy sprints" ON strategy_sprints;
DROP POLICY IF EXISTS "Users can insert their own strategy sprints" ON strategy_sprints;
DROP POLICY IF EXISTS "Users can update their own strategy sprints" ON strategy_sprints;
DROP POLICY IF EXISTS "Users can delete their own strategy sprints" ON strategy_sprints;

-- Create RLS policies for strategy_sprints table
CREATE POLICY "Users can view their own strategy sprints"
    ON strategy_sprints FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own strategy sprints"
    ON strategy_sprints FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own strategy sprints"
    ON strategy_sprints FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own strategy sprints"
    ON strategy_sprints FOR DELETE
    USING (auth.uid() = user_id);

-- 5. COMPETITOR_ANALYSES TABLE
ALTER TABLE competitor_analyses ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own competitor analyses" ON competitor_analyses;
DROP POLICY IF EXISTS "Users can insert their own competitor analyses" ON competitor_analyses;
DROP POLICY IF EXISTS "Users can update their own competitor analyses" ON competitor_analyses;
DROP POLICY IF EXISTS "Users can delete their own competitor analyses" ON competitor_analyses;

-- Create RLS policies for competitor_analyses table
CREATE POLICY "Users can view their own competitor analyses"
    ON competitor_analyses FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own competitor analyses"
    ON competitor_analyses FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own competitor analyses"
    ON competitor_analyses FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own competitor analyses"
    ON competitor_analyses FOR DELETE
    USING (auth.uid() = user_id);

-- 6. USERS TABLE (if you have a custom users table)
-- Note: If you're using auth.users, you don't need RLS policies for it
-- But if you have a custom users table, uncomment below:

-- ALTER TABLE users ENABLE ROW LEVEL SECURITY;
-- CREATE POLICY "Users can view their own profile"
--     ON users FOR SELECT
--     USING (auth.uid() = id);
-- CREATE POLICY "Users can update their own profile"
--     ON users FOR UPDATE
--     USING (auth.uid() = id);

-- Verify all policies are created
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;
