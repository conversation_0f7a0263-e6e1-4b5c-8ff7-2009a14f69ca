-- Add this to your manual-fixes.sql file
-- Run this periodically (monthly) to maintain database health

-- 1. Create indexes for any unindexed foreign keys
DO $$
DECLARE
  r RECORD;
BEGIN
  FOR r IN (
    SELECT
      tc.table_schema, 
      tc.table_name, 
      kcu.column_name,
      'idx_' || tc.table_name || '_' || kcu.column_name AS index_name
    FROM 
      information_schema.table_constraints AS tc 
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND NOT EXISTS (
      SELECT 1
      FROM pg_indexes
      WHERE tablename = tc.table_name
      AND indexdef LIKE '%' || kcu.column_name || '%'
    )
  ) LOOP
    EXECUTE format('CREATE INDEX %I ON %I.%I(%I)', 
                  r.index_name, 
                  r.table_schema, 
                  r.table_name, 
                  r.column_name);
  END LOOP;
END $$;

-- 2. Update database statistics
ANALYZE;

-- 3. Identify and optionally remove unused indexes
-- CAUTION: Only remove indexes after verifying they are truly not needed
DO $$
DECLARE
  r RECORD;
BEGIN
  -- This query finds indexes that haven't been used since the last stats reset
  -- and don't support primary keys or unique constraints
  FOR r IN (
    SELECT
      schemaname,
      relname as table_name,
      indexrelname as index_name,
      idx_scan,
      pg_size_pretty(pg_relation_size(i.indexrelid)) as index_size
    FROM
      pg_stat_user_indexes ui
      JOIN pg_index i ON ui.indexrelid = i.indexrelid
    WHERE
      idx_scan = 0
      AND NOT indisprimary
      AND NOT indisunique
      AND NOT EXISTS (
        -- Don't include indexes that support foreign keys
        SELECT 1
        FROM pg_constraint c
        WHERE c.conindid = i.indexrelid
          AND c.contype = 'f'
      )
    ORDER BY
      pg_relation_size(i.indexrelid) DESC
  ) LOOP
    -- Log the unused index (consider removing it if consistently unused)
    RAISE NOTICE 'Unused index: %.% (size: %) - Consider removing with: DROP INDEX %;',
      r.schemaname, r.index_name, r.index_size, r.index_name;
    
    -- Uncomment the line below to actually drop the unused indexes
    -- EXECUTE format('DROP INDEX %I.%I', r.schemaname, r.index_name);
  END LOOP;
END $$;
